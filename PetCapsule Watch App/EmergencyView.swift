//
//  EmergencyView.swift
//  PetCapsule Watch App
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import SwiftUI

struct EmergencyView: View {
    @StateObject private var connectivity = WatchConnectivityManager.shared
    @State private var showingEmergencyAlert = false
    @State private var selectedEmergencyType: EmergencyType = .general
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // Emergency Header
                VStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.largeTitle)
                        .foregroundColor(.red)
                    
                    Text("Pet Emergency")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Text("Quick access to emergency contacts and actions")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Emergency Contacts
                EmergencyContactsSection()
                
                // Quick Emergency Actions
                QuickEmergencyActionsSection()
                
                // Emergency Information
                EmergencyInfoSection()
            }
        }
        .navigationTitle("Emergency")
        .alert("Emergency Contact", isPresented: $showingEmergencyAlert) {
            Button("Call Now") {
                initiateEmergencyCall()
            }
            But<PERSON>("Cancel", role: .cancel) { }
        } message: {
            Text("This will contact your emergency veterinarian immediately.")
        }
    }
    
    private func initiateEmergencyCall() {
        connectivity.triggerEmergencyContact()
    }
}

// MARK: - Emergency Contacts Section
struct EmergencyContactsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Emergency Contacts")
                .font(.headline)
                .padding(.horizontal)
            
            VStack(spacing: 8) {
                EmergencyContactCard(
                    name: "24/7 Emergency Vet",
                    phone: "(*************",
                    type: .emergency,
                    icon: "cross.fill"
                )
                
                EmergencyContactCard(
                    name: "Poison Control",
                    phone: "(*************",
                    type: .poison,
                    icon: "exclamationmark.shield.fill"
                )
                
                EmergencyContactCard(
                    name: "Primary Vet",
                    phone: "(*************",
                    type: .general,
                    icon: "stethoscope"
                )
            }
        }
    }
}

struct EmergencyContactCard: View {
    let name: String
    let phone: String
    let type: EmergencyType
    let icon: String
    
    var body: some View {
        Button(action: {
            callEmergencyContact(type: type)
        }) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(type.color)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(phone)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "phone.fill")
                    .font(.caption)
                    .foregroundColor(.green)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(.plain)
    }
    
    private func callEmergencyContact(type: EmergencyType) {
        let message = [
            "action": "emergency",
            "type": type.rawValue
        ]
        
        WatchConnectivityManager.shared.session?.sendMessage(message, replyHandler: nil) { error in
            print("Failed to initiate emergency call: \(error)")
        }
    }
}

// MARK: - Quick Emergency Actions
struct QuickEmergencyActionsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .padding(.horizontal)
            
            VStack(spacing: 8) {
                EmergencyActionButton(
                    title: "Pet Injury",
                    subtitle: "Cuts, wounds, limping",
                    icon: "bandage.fill",
                    color: .red,
                    action: { reportEmergency(.injury) }
                )
                
                EmergencyActionButton(
                    title: "Poisoning",
                    subtitle: "Toxic ingestion",
                    icon: "exclamationmark.triangle.fill",
                    color: .orange,
                    action: { reportEmergency(.poisoning) }
                )
                
                EmergencyActionButton(
                    title: "Breathing Issues",
                    subtitle: "Difficulty breathing",
                    icon: "lungs.fill",
                    color: .blue,
                    action: { reportEmergency(.breathing) }
                )
                
                EmergencyActionButton(
                    title: "Other Emergency",
                    subtitle: "General emergency",
                    icon: "cross.circle.fill",
                    color: .purple,
                    action: { reportEmergency(.general) }
                )
            }
        }
    }
    
    private func reportEmergency(_ type: EmergencyType) {
        let message = [
            "action": "reportEmergency",
            "emergencyType": type.rawValue,
            "timestamp": Date().timeIntervalSince1970
        ] as [String: Any]
        
        WatchConnectivityManager.shared.session?.sendMessage(message, replyHandler: nil) { error in
            print("Failed to report emergency: \(error)")
        }
    }
}

struct EmergencyActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Emergency Information
struct EmergencyInfoSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Emergency Tips")
                .font(.headline)
                .padding(.horizontal)
            
            VStack(spacing: 8) {
                EmergencyTipCard(
                    icon: "clock.fill",
                    title: "Stay Calm",
                    description: "Keep your pet calm and assess the situation"
                )
                
                EmergencyTipCard(
                    icon: "phone.fill",
                    title: "Call First",
                    description: "Contact your vet before heading to the clinic"
                )
                
                EmergencyTipCard(
                    icon: "car.fill",
                    title: "Safe Transport",
                    description: "Secure your pet safely for transport"
                )
            }
        }
    }
}

struct EmergencyTipCard: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 25)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Emergency Types
// EmergencyType is defined in Services/EmergencyCallService.swift

#Preview {
    EmergencyView()
}
