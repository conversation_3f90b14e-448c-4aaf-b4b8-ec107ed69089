//
//  HealthMonitoringView.swift
//  PetCapsule Watch App
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import SwiftUI
import HealthKit

struct HealthMonitoringView: View {
    let pet: Pet?
    @StateObject private var healthManager = WatchHealthManager.shared
    @State private var showingWalkDetail = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                // Walk Tracking Section
                WalkTrackingCard()
                
                // Recent Walks
                if !healthManager.walkingData.isEmpty {
                    RecentWalksCard()
                }
                
                // Heart Rate Monitoring
                HeartRateCard()
                
                // Health Summary
                if let pet = pet {
                    HealthSummaryCard(pet: pet)
                }
            }
            .padding(.horizontal, 4)
        }
        .navigationTitle("Health")
    }
}

// MARK: - Walk Tracking Card
struct WalkTrackingCard: View {
    @StateObject private var healthManager = WatchHealthManager.shared
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "figure.walk")
                    .foregroundColor(.blue)
                Text("Walk Tracking")
                    .font(.headline)
                Spacer()
            }
            
            if healthManager.isTrackingWalk {
                // Active Walk Display
                VStack(spacing: 8) {
                    if let session = healthManager.currentWalkSession {
                        HStack {
                            VStack {
                                Text("Distance")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text(session.formattedDistance)
                                    .font(.title3)
                                    .fontWeight(.semibold)
                            }
                            
                            Spacer()
                            
                            VStack {
                                Text("Duration")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text(session.formattedDuration)
                                    .font(.title3)
                                    .fontWeight(.semibold)
                            }
                        }
                        
                        if session.averageHeartRate > 0 {
                            HStack {
                                Image(systemName: "heart.fill")
                                    .foregroundColor(.red)
                                    .font(.caption)
                                Text("\(Int(session.averageHeartRate)) BPM")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                Spacer()
                                Text("\(Int(session.caloriesBurned)) cal")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                        }
                    }
                    
                    Button("Stop Walk") {
                        healthManager.stopWalkTracking()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                }
            } else {
                // Start Walk Button
                Button("Start Walk") {
                    healthManager.startWalkTracking()
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.regular)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Recent Walks Card
struct RecentWalksCard: View {
    @StateObject private var healthManager = WatchHealthManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "clock")
                    .foregroundColor(.green)
                Text("Recent Walks")
                    .font(.headline)
                Spacer()
            }
            
            ForEach(healthManager.walkingData.prefix(3)) { walk in
                WalkSummaryRow(walk: walk)
            }
            
            if healthManager.walkingData.count > 3 {
                Button("View All") {
                    // Navigate to full walk history
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct WalkSummaryRow: View {
    let walk: WalkingSession
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(walk.formattedDistance)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(walk.startTime, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(walk.formattedDuration)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                if walk.averageHeartRate > 0 {
                    Text("\(Int(walk.averageHeartRate)) BPM")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Heart Rate Card
struct HeartRateCard: View {
    @StateObject private var healthManager = WatchHealthManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.red)
                Text("Heart Rate")
                    .font(.headline)
                Spacer()
            }
            
            if let latestReading = healthManager.heartRateData.last {
                HStack {
                    VStack(alignment: .leading) {
                        Text("Current")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(Int(latestReading.heartRate)) BPM")
                            .font(.title3)
                            .fontWeight(.semibold)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing) {
                        Text("Updated")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(latestReading.timestamp, style: .time)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
            } else {
                Text("No recent data")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Button("Start Monitoring") {
                healthManager.startHeartRateMonitoring()
            }
            .buttonStyle(.bordered)
            .controlSize(.small)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Health Summary Card
struct HealthSummaryCard: View {
    let pet: Pet
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.purple)
                Text("Health Summary")
                    .font(.headline)
                Spacer()
            }
            
            VStack(spacing: 6) {
                HealthMetricRow(
                    title: "Activity Level",
                    value: "Good",
                    color: .green
                )
                
                HealthMetricRow(
                    title: "Weight Trend",
                    value: "Stable",
                    color: .blue
                )
                
                HealthMetricRow(
                    title: "Last Vet Visit",
                    value: "2 weeks ago",
                    color: .orange
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct HealthMetricRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
}

#Preview {
    HealthMonitoringView(pet: nil)
}
