//
//  PetCapsuleWatchApp.swift
//  PetCapsule Watch App
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import SwiftUI

@main
struct PetCapsuleWatchApp: App {
    var body: some Scene {
        WindowGroup {
            WatchContentView()
                .onAppear {
                    setupWatchApp()
                }
        }
    }
    
    private func setupWatchApp() {
        // Initialize Watch Connectivity
        WatchConnectivityManager.shared.activate()
        
        // Request Health permissions
        WatchHealthManager.shared.requestHealthPermissions()
    }
}
