//
//  QuickActionsView.swift
//  PetCapsule Watch App
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import SwiftUI

struct QuickActionsView: View {
    let pet: Pet?
    @StateObject private var connectivity = WatchConnectivityManager.shared
    @State private var showingMemoryInput = false
    @State private var memoryTitle = ""
    @State private var memoryContent = ""
    
    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                // Quick Memory
                QuickMemoryCard(
                    showingInput: $showingMemoryInput,
                    title: $memoryTitle,
                    content: $memoryContent
                )
                
                // Feeding Reminder
                FeedingReminderCard()
                
                // Medication Reminder
                MedicationReminderCard()
                
                // Vet Appointment
                VetAppointmentCard()
                
                // Walk Reminder
                WalkReminderCard()
            }
            .padding(.horizontal, 4)
        }
        .navigationTitle("Actions")
        .sheet(isPresented: $showingMemoryInput) {
            MemoryInputView(
                title: $memoryTitle,
                content: $memoryContent,
                onSave: saveMemory
            )
        }
    }
    
    private func saveMemory() {
        guard !memoryTitle.isEmpty || !memoryContent.isEmpty else { return }
        
        connectivity.addQuickMemory(
            title: memoryTitle.isEmpty ? "Quick Note" : memoryTitle,
            content: memoryContent.isEmpty ? memoryTitle : memoryContent
        )
        
        memoryTitle = ""
        memoryContent = ""
        showingMemoryInput = false
    }
}

// MARK: - Quick Memory Card
struct QuickMemoryCard: View {
    @Binding var showingInput: Bool
    @Binding var title: String
    @Binding var content: String
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "camera.fill")
                    .foregroundColor(.blue)
                Text("Quick Memory")
                    .font(.headline)
                Spacer()
            }
            
            Button("Add Memory") {
                showingInput = true
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.regular)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Feeding Reminder Card
struct FeedingReminderCard: View {
    @State private var lastFeedingTime: Date?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "bowl.fill")
                    .foregroundColor(.orange)
                Text("Feeding")
                    .font(.headline)
                Spacer()
            }
            
            if let lastFeeding = lastFeedingTime {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Last fed:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(lastFeeding, style: .relative)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            } else {
                Text("No recent feeding recorded")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Button("Mark as Fed") {
                lastFeedingTime = Date()
                sendFeedingUpdate()
            }
            .buttonStyle(.bordered)
            .controlSize(.small)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func sendFeedingUpdate() {
        let message = [
            "action": "feeding",
            "timestamp": Date().timeIntervalSince1970
        ] as [String: Any]
        
        WatchConnectivityManager.shared.session?.sendMessage(message, replyHandler: nil) { error in
            print("Failed to send feeding update: \(error)")
        }
    }
}

// MARK: - Medication Reminder Card
struct MedicationReminderCard: View {
    @State private var nextMedicationDue = "Evening dose in 3 hours"
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "pills.fill")
                    .foregroundColor(.red)
                Text("Medication")
                    .font(.headline)
                Spacer()
            }
            
            Text(nextMedicationDue)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Button("Mark as Given") {
                markMedicationGiven()
            }
            .buttonStyle(.bordered)
            .controlSize(.small)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func markMedicationGiven() {
        let message = [
            "action": "medication",
            "timestamp": Date().timeIntervalSince1970
        ] as [String: Any]
        
        WatchConnectivityManager.shared.session?.sendMessage(message, replyHandler: nil) { error in
            print("Failed to send medication update: \(error)")
        }
    }
}

// MARK: - Vet Appointment Card
struct VetAppointmentCard: View {
    @State private var nextAppointment = "Checkup in 5 days"
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "stethoscope")
                    .foregroundColor(.green)
                Text("Vet Appointment")
                    .font(.headline)
                Spacer()
            }
            
            Text(nextAppointment)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Button("Call Vet") {
                callVeterinarian()
            }
            .buttonStyle(.bordered)
            .controlSize(.small)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func callVeterinarian() {
        let message = ["action": "callVet"]
        
        WatchConnectivityManager.shared.session?.sendMessage(message, replyHandler: nil) { error in
            print("Failed to initiate vet call: \(error)")
        }
    }
}

// MARK: - Walk Reminder Card
struct WalkReminderCard: View {
    @State private var lastWalkTime: Date?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "figure.walk")
                    .foregroundColor(.blue)
                Text("Walk Reminder")
                    .font(.headline)
                Spacer()
            }
            
            if let lastWalk = lastWalkTime {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Last walk:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(lastWalk, style: .relative)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            } else {
                Text("No recent walk recorded")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Button("Start Walk") {
                WatchHealthManager.shared.startWalkTracking()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.small)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Memory Input View
struct MemoryInputView: View {
    @Binding var title: String
    @Binding var content: String
    let onSave: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                TextField("Memory title", text: $title)
                    .textFieldStyle(.roundedBorder)
                
                TextField("What happened?", text: $content, axis: .vertical)
                    .textFieldStyle(.roundedBorder)
                    .lineLimit(3...6)
                
                Spacer()
            }
            .padding()
            .navigationTitle("New Memory")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        onSave()
                    }
                    .disabled(title.isEmpty && content.isEmpty)
                }
            }
        }
    }
}

#Preview {
    QuickActionsView(pet: nil)
}
