//
//  WatchConnectivityManager.swift
//  PetCapsule Watch App
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import WatchConnectivity
import Combine

@MainActor
class WatchConnectivityManager: NSObject, ObservableObject {
    static let shared = WatchConnectivityManager()
    
    @Published var isConnected = false
    @Published var receivedPetData: Pet?
    @Published var receivedHealthData: [String: Any] = [:]
    @Published var receivedMemories: [Memory] = []
    @Published var connectionStatus = "Disconnected"
    
    private var session: WCSession?
    
    override init() {
        super.init()
        setupWatchConnectivity()
    }
    
    private func setupWatchConnectivity() {
        if WCSession.isSupported() {
            session = WCSession.default
            session?.delegate = self
        }
    }
    
    func activate() {
        session?.activate()
    }
    
    // MARK: - Data Requests
    
    func requestPetData() {
        guard let session = session, session.isReachable else {
            connectionStatus = "iPhone not reachable"
            return
        }
        
        let message = ["request": "petData"]
        session.sendMessage(message, replyHandler: { [weak self] reply in
            DispatchQueue.main.async {
                self?.handlePetDataReply(reply)
            }
        }) { [weak self] error in
            DispatchQueue.main.async {
                self?.connectionStatus = "Error: \(error.localizedDescription)"
            }
        }
    }
    
    func requestHealthData() {
        guard let session = session, session.isReachable else { return }
        
        let message = ["request": "healthData"]
        session.sendMessage(message, replyHandler: { [weak self] reply in
            DispatchQueue.main.async {
                self?.handleHealthDataReply(reply)
            }
        }) { error in
            print("Health data request failed: \(error)")
        }
    }
    
    func requestMemories() {
        guard let session = session, session.isReachable else { return }
        
        let message = ["request": "memories", "limit": 10]
        session.sendMessage(message, replyHandler: { [weak self] reply in
            DispatchQueue.main.async {
                self?.handleMemoriesReply(reply)
            }
        }) { error in
            print("Memories request failed: \(error)")
        }
    }
    
    // MARK: - Emergency Actions
    
    func triggerEmergencyContact() {
        guard let session = session, session.isReachable else { return }
        
        let message = ["action": "emergency", "type": "veterinary"]
        session.sendMessage(message, replyHandler: nil) { error in
            print("Emergency contact failed: \(error)")
        }
    }
    
    func addQuickMemory(title: String, content: String) {
        guard let session = session, session.isReachable else { return }
        
        let message = [
            "action": "addMemory",
            "title": title,
            "content": content,
            "timestamp": Date().timeIntervalSince1970
        ] as [String : Any]
        
        session.sendMessage(message, replyHandler: nil) { error in
            print("Add memory failed: \(error)")
        }
    }
    
    // MARK: - Reply Handlers
    
    private func handlePetDataReply(_ reply: [String: Any]) {
        if let petDataDict = reply["petData"] as? [String: Any] {
            receivedPetData = Pet.fromDictionary(petDataDict)
            connectionStatus = "Pet data received"
        }
    }
    
    private func handleHealthDataReply(_ reply: [String: Any]) {
        if let healthData = reply["healthData"] as? [String: Any] {
            receivedHealthData = healthData
            connectionStatus = "Health data received"
        }
    }
    
    private func handleMemoriesReply(_ reply: [String: Any]) {
        if let memoriesData = reply["memories"] as? [[String: Any]] {
            receivedMemories = memoriesData.compactMap { Memory.fromDictionary($0) }
            connectionStatus = "Memories received"
        }
    }
}

// MARK: - WCSessionDelegate
extension WatchConnectivityManager: WCSessionDelegate {
    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        DispatchQueue.main.async {
            switch activationState {
            case .activated:
                self.isConnected = true
                self.connectionStatus = "Connected"
            case .inactive:
                self.isConnected = false
                self.connectionStatus = "Inactive"
            case .notActivated:
                self.isConnected = false
                self.connectionStatus = "Not Activated"
            @unknown default:
                self.isConnected = false
                self.connectionStatus = "Unknown"
            }
            
            if let error = error {
                self.connectionStatus = "Error: \(error.localizedDescription)"
            }
        }
    }
    
    func session(_ session: WCSession, didReceiveMessage message: [String : Any]) {
        DispatchQueue.main.async {
            self.handleReceivedMessage(message)
        }
    }
    
    func session(_ session: WCSession, didReceiveMessage message: [String : Any], replyHandler: @escaping ([String : Any]) -> Void) {
        DispatchQueue.main.async {
            self.handleReceivedMessage(message)
            replyHandler(["status": "received"])
        }
    }
    
    func session(_ session: WCSession, didReceiveApplicationContext applicationContext: [String : Any]) {
        DispatchQueue.main.async {
            self.handleReceivedMessage(applicationContext)
        }
    }
    
    private func handleReceivedMessage(_ message: [String: Any]) {
        // Handle real-time updates from iPhone
        if let petUpdate = message["petUpdate"] as? [String: Any] {
            receivedPetData = Pet.fromDictionary(petUpdate)
        }
        
        if let healthUpdate = message["healthUpdate"] as? [String: Any] {
            receivedHealthData = healthUpdate
        }
        
        if let memoriesUpdate = message["memoriesUpdate"] as? [[String: Any]] {
            receivedMemories = memoriesUpdate.compactMap { Memory.fromDictionary($0) }
        }
    }
}

// MARK: - Data Model Extensions
extension Pet {
    static func fromDictionary(_ dict: [String: Any]) -> Pet? {
        guard let name = dict["name"] as? String,
              let species = dict["species"] as? String,
              let breed = dict["breed"] as? String,
              let age = dict["age"] as? Int else {
            return nil
        }
        
        // Create a simplified Pet object for Watch
        // Note: This would need to match your actual Pet model structure
        return Pet(
            name: name,
            species: species,
            breed: breed,
            age: age,
            weight: dict["weight"] as? Double ?? 0.0,
            color: dict["color"] as? String ?? "",
            gender: dict["gender"] as? String ?? "",
            isNeutered: dict["isNeutered"] as? Bool ?? false,
            microchipId: dict["microchipId"] as? String,
            notes: dict["notes"] as? String ?? ""
        )
    }
    
    func toDictionary() -> [String: Any] {
        return [
            "name": name,
            "species": species,
            "breed": breed,
            "age": age,
            "weight": weight,
            "color": color,
            "gender": gender,
            "isNeutered": isNeutered,
            "microchipId": microchipId ?? "",
            "notes": notes
        ]
    }
}

extension Memory {
    static func fromDictionary(_ dict: [String: Any]) -> Memory? {
        guard let title = dict["title"] as? String,
              let content = dict["content"] as? String,
              let typeString = dict["type"] as? String,
              let type = MemoryType(rawValue: typeString) else {
            return nil
        }
        
        return Memory(
            title: title,
            content: content,
            type: type,
            mediaURL: dict["mediaURL"] as? String,
            thumbnailURL: dict["thumbnailURL"] as? String,
            duration: dict["duration"] as? TimeInterval,
            milestone: dict["milestone"] as? String,
            sentiment: dict["sentiment"] as? String,
            tags: dict["tags"] as? [String] ?? [],
            isPublic: dict["isPublic"] as? Bool ?? false,
            isFavorite: dict["isFavorite"] as? Bool ?? false
        )
    }
}
