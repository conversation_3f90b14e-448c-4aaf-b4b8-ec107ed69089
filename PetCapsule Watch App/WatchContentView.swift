//
//  ContentView.swift
//  PetCapsule Watch App
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import SwiftUI
import WatchKit
import HealthKit
import WatchConnectivity

struct WatchContentView: View {
    @StateObject private var watchConnectivity = WatchConnectivityManager.shared
    @StateObject private var healthManager = WatchHealthManager.shared
    @State private var selectedTab = 0
    @State private var currentPet: Pet?
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Pet Dashboard
            PetDashboardView(pet: currentPet)
                .tabItem {
                    Image(systemName: "pawprint.fill")
                    Text("Pet")
                }
                .tag(0)
            
            // Health Monitoring
            HealthMonitoringView(pet: currentPet)
                .tabItem {
                    Image(systemName: "heart.fill")
                    Text("Health")
                }
                .tag(1)
            
            // Quick Actions
            QuickActionsView(pet: currentPet)
                .tabItem {
                    Image(systemName: "bolt.fill")
                    Text("Actions")
                }
                .tag(2)
            
            // Emergency
            EmergencyView()
                .tabItem {
                    Image(systemName: "exclamationmark.triangle.fill")
                    Text("Emergency")
                }
                .tag(3)
        }
        .onAppear {
            setupWatch()
        }
        .onReceive(watchConnectivity.$receivedPetData) { petData in
            if let petData = petData {
                currentPet = petData
            }
        }
    }
    
    private func setupWatch() {
        // Request health permissions
        healthManager.requestHealthPermissions()
        
        // Setup watch connectivity
        watchConnectivity.activate()
        
        // Request pet data from iPhone
        watchConnectivity.requestPetData()
    }
}

// MARK: - Pet Dashboard View
struct PetDashboardView: View {
    let pet: Pet?
    @StateObject private var healthManager = WatchHealthManager.shared
    
    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                if let pet = pet {
                    // Pet Info Header
                    VStack(spacing: 4) {
                        Text(pet.name)
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        Text("\(pet.species) • \(pet.breed)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)
                    
                    // Health Status
                    HealthStatusCard(pet: pet)
                    
                    // Activity Summary
                    ActivitySummaryCard(pet: pet)
                    
                    // Next Care Item
                    NextCareCard(pet: pet)
                    
                } else {
                    // No Pet Data
                    VStack(spacing: 8) {
                        Image(systemName: "pawprint")
                            .font(.largeTitle)
                            .foregroundColor(.secondary)
                        
                        Text("No Pet Data")
                            .font(.headline)
                        
                        Text("Open PetCapsule on iPhone")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                }
            }
        }
        .navigationTitle("PetCapsule")
    }
}

// MARK: - Health Status Card
struct HealthStatusCard: View {
    let pet: Pet
    @StateObject private var healthManager = WatchHealthManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.red)
                Text("Health Status")
                    .font(.headline)
                Spacer()
            }
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Overall")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("Good")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Last Check")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("2 days ago")
                        .font(.caption)
                        .fontWeight(.medium)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Activity Summary Card
struct ActivitySummaryCard: View {
    let pet: Pet
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "figure.walk")
                    .foregroundColor(.blue)
                Text("Today's Activity")
                    .font(.headline)
                Spacer()
            }
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Walks")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("2")
                        .font(.title3)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .center) {
                    Text("Duration")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("45 min")
                        .font(.title3)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Distance")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("2.1 mi")
                        .font(.title3)
                        .fontWeight(.semibold)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Next Care Card
struct NextCareCard: View {
    let pet: Pet
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.orange)
                Text("Next Care")
                    .font(.headline)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Vaccination Due")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("Rabies booster in 5 days")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    WatchContentView()
}
