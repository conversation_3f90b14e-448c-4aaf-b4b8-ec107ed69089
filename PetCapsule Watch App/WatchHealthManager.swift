//
//  WatchHealthManager.swift
//  PetCapsule Watch App
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import HealthKit
import Combine

@MainActor
class WatchHealthManager: ObservableObject {
    static let shared = WatchHealthManager()
    
    @Published var isAuthorized = false
    @Published var walkingData: [WalkingSession] = []
    @Published var heartRateData: [HeartRateReading] = []
    @Published var currentWalkSession: WalkingSession?
    @Published var isTrackingWalk = false
    
    private let healthStore = HKHealthStore()
    private var workoutSession: HKWorkoutSession?
    private var builder: HKLiveWorkoutBuilder?
    
    private init() {}
    
    // MARK: - Health Permissions
    
    func requestHealthPermissions() {
        guard HKHealthStore.isHealthDataAvailable() else {
            print("HealthKit not available")
            return
        }
        
        // Safely create HKObjectTypes with nil handling
        var typesToRead: Set<HKObjectType> = [HKObjectType.workoutType()]
        
        if let heartRateType = HKObjectType.quantityType(forIdentifier: .heartRate) {
            typesToRead.insert(heartRateType)
        }
        
        if let distanceType = HKObjectType.quantityType(forIdentifier: .distanceWalkingRunning) {
            typesToRead.insert(distanceType)
        }
        
        if let energyType = HKObjectType.quantityType(forIdentifier: .activeEnergyBurned) {
            typesToRead.insert(energyType)
        }
        
        healthStore.requestAuthorization(toShare: nil, read: typesToRead) { [weak self] success, error in
            DispatchQueue.main.async {
                self?.isAuthorized = success
                if let error = error {
                    print("HealthKit authorization error: \(error)")
                }
            }
        }
    }
    
    // MARK: - Walk Tracking
    
    func startWalkTracking() {
        guard isAuthorized else {
            print("HealthKit not authorized")
            return
        }
        
        let configuration = HKWorkoutConfiguration()
        configuration.activityType = .walking
        configuration.locationType = .outdoor
        
        do {
            workoutSession = try HKWorkoutSession(healthStore: healthStore, configuration: configuration)
            builder = workoutSession?.associatedWorkoutBuilder()
            
            builder?.dataSource = HKLiveWorkoutDataSource(healthStore: healthStore, workoutConfiguration: configuration)
            
            workoutSession?.delegate = self
            builder?.delegate = self
            
            let startDate = Date()
            workoutSession?.startActivity(with: startDate)
            builder?.beginCollection(withStart: startDate) { [weak self] success, error in
                DispatchQueue.main.async {
                    if success {
                        self?.isTrackingWalk = true
                        self?.currentWalkSession = WalkingSession(
                            startTime: startDate,
                            endTime: nil,
                            distance: 0,
                            duration: 0,
                            averageHeartRate: 0,
                            caloriesBurned: 0
                        )
                    }
                }
            }
        } catch {
            print("Failed to start workout session: \(error)")
        }
    }
    
    func stopWalkTracking() {
        guard let workoutSession = workoutSession,
              let builder = builder else { return }
        
        let endDate = Date()
        workoutSession.end()
        
        builder.endCollection(withEnd: endDate) { [weak self] success, error in
            if success {
                builder.finishWorkout { [weak self] workout, error in
                    DispatchQueue.main.async {
                        self?.isTrackingWalk = false
                        self?.currentWalkSession?.endTime = endDate
                        
                        if let session = self?.currentWalkSession {
                            self?.walkingData.append(session)
                            self?.sendWalkDataToPhone(session)
                        }
                        
                        self?.currentWalkSession = nil
                        self?.workoutSession = nil
                        self?.builder = nil
                    }
                }
            }
        }
    }
    
    private func sendWalkDataToPhone(_ session: WalkingSession) {
        // Send walk data to iPhone via Watch Connectivity
        let walkData = session.toDictionary()
        WatchConnectivityManager.shared.session?.sendMessage(
            ["walkData": walkData],
            replyHandler: nil
        ) { error in
            print("Failed to send walk data: \(error)")
        }
    }
    
    // MARK: - Heart Rate Monitoring
    
    func startHeartRateMonitoring() {
        guard isAuthorized else { return }
        
        guard let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate) else {
            print("❌ Unable to get heart rate type")
            return
        }
        
        let query = HKAnchoredObjectQuery(
            type: heartRateType,
            predicate: nil,
            anchor: nil,
            limit: HKObjectQueryNoLimit
        ) { [weak self] query, samples, deletedObjects, anchor, error in
            self?.processHeartRateSamples(samples)
        }
        
        query.updateHandler = { [weak self] query, samples, deletedObjects, anchor, error in
            self?.processHeartRateSamples(samples)
        }
        
        healthStore.execute(query)
    }
    
    private func processHeartRateSamples(_ samples: [HKSample]?) {
        guard let heartRateSamples = samples as? [HKQuantitySample] else { return }
        
        DispatchQueue.main.async {
            let readings = heartRateSamples.map { sample in
                HeartRateReading(
                    timestamp: sample.startDate,
                    heartRate: sample.quantity.doubleValue(for: HKUnit.count().unitDivided(by: .minute()))
                )
            }
            
            self.heartRateData.append(contentsOf: readings)
            
            // Keep only last 100 readings
            if self.heartRateData.count > 100 {
                self.heartRateData = Array(self.heartRateData.suffix(100))
            }
        }
    }
}

// MARK: - HKWorkoutSessionDelegate
extension WatchHealthManager: HKWorkoutSessionDelegate {
    func workoutSession(_ workoutSession: HKWorkoutSession, didChangeTo toState: HKWorkoutSessionState, from fromState: HKWorkoutSessionState, date: Date) {
        DispatchQueue.main.async {
            switch toState {
            case .running:
                print("Workout session started")
            case .ended:
                print("Workout session ended")
            default:
                break
            }
        }
    }
    
    func workoutSession(_ workoutSession: HKWorkoutSession, didFailWithError error: Error) {
        print("Workout session failed: \(error)")
    }
}

// MARK: - HKLiveWorkoutBuilderDelegate
extension WatchHealthManager: HKLiveWorkoutBuilderDelegate {
    func workoutBuilder(_ workoutBuilder: HKLiveWorkoutBuilder, didCollectDataOf collectedTypes: Set<HKSampleType>) {
        for type in collectedTypes {
            guard let quantityType = type as? HKQuantityType else { continue }
            
            let statistics = workoutBuilder.statistics(for: quantityType)
            
            DispatchQueue.main.async {
                self.updateWorkoutStatistics(for: quantityType, statistics: statistics)
            }
        }
    }
    
    func workoutBuilderDidCollectEvent(_ workoutBuilder: HKLiveWorkoutBuilder) {
        // Handle workout events
    }
    
    private func updateWorkoutStatistics(for quantityType: HKQuantityType, statistics: HKStatistics?) {
        guard let statistics = statistics else { return }
        
        switch quantityType.identifier {
        case HKQuantityTypeIdentifier.distanceWalkingRunning.rawValue:
            let distance = statistics.sumQuantity()?.doubleValue(for: .meter()) ?? 0
            currentWalkSession?.distance = distance
            
        case HKQuantityTypeIdentifier.activeEnergyBurned.rawValue:
            let calories = statistics.sumQuantity()?.doubleValue(for: .kilocalorie()) ?? 0
            currentWalkSession?.caloriesBurned = calories
            
        case HKQuantityTypeIdentifier.heartRate.rawValue:
            let heartRate = statistics.averageQuantity()?.doubleValue(for: HKUnit.count().unitDivided(by: .minute())) ?? 0
            currentWalkSession?.averageHeartRate = heartRate
            
        default:
            break
        }
        
        // Update duration
        if let startTime = currentWalkSession?.startTime {
            currentWalkSession?.duration = Date().timeIntervalSince(startTime)
        }
    }
}

// MARK: - Data Models
struct WalkingSession: Identifiable {
    let id = UUID()
    let startTime: Date
    var endTime: Date?
    var distance: Double // meters
    var duration: TimeInterval // seconds
    var averageHeartRate: Double // BPM
    var caloriesBurned: Double // kcal
    
    var formattedDistance: String {
        let miles = distance * 0.000621371
        return String(format: "%.2f mi", miles)
    }
    
    var formattedDuration: String {
        let minutes = Int(duration / 60)
        let seconds = Int(duration.truncatingRemainder(dividingBy: 60))
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    func toDictionary() -> [String: Any] {
        return [
            "startTime": startTime.timeIntervalSince1970,
            "endTime": endTime?.timeIntervalSince1970 ?? Date().timeIntervalSince1970,
            "distance": distance,
            "duration": duration,
            "averageHeartRate": averageHeartRate,
            "caloriesBurned": caloriesBurned
        ]
    }
}

struct HeartRateReading: Identifiable {
    let id = UUID()
    let timestamp: Date
    let heartRate: Double
}
