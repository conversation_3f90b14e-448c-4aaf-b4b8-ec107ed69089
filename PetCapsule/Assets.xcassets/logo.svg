<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for the time capsule -->
    <linearGradient id="capsuleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradient for the paw print -->
    <linearGradient id="pawGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradient for the heart -->
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fecfef;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="95" fill="url(#capsuleGradient)" filter="url(#shadow)"/>
  
  <!-- Time capsule body -->
  <rect x="60" y="70" width="80" height="60" rx="30" ry="30" fill="#ffffff" opacity="0.9"/>
  
  <!-- Time capsule lid -->
  <ellipse cx="100" cy="70" rx="40" ry="8" fill="#e0e0e0"/>
  <ellipse cx="100" cy="68" rx="40" ry="8" fill="#f5f5f5"/>
  
  <!-- Paw print inside capsule -->
  <g transform="translate(100, 100)">
    <!-- Main pad -->
    <ellipse cx="0" cy="0" rx="12" ry="10" fill="url(#pawGradient)"/>
    
    <!-- Toes -->
    <ellipse cx="-8" cy="-12" rx="4" ry="6" fill="url(#pawGradient)"/>
    <ellipse cx="0" cy="-14" rx="4" ry="6" fill="url(#pawGradient)"/>
    <ellipse cx="8" cy="-12" rx="4" ry="6" fill="url(#pawGradient)"/>
  </g>
  
  <!-- Small heart floating above -->
  <g transform="translate(120, 50)">
    <path d="M0,5 C0,2 2,0 5,0 C8,0 10,2 10,5 C10,8 5,13 5,13 C5,13 0,8 0,5 Z" fill="url(#heartGradient)"/>
  </g>
  
  <!-- Sparkles around the capsule -->
  <g fill="#ffffff" opacity="0.8">
    <circle cx="70" cy="50" r="2"/>
    <circle cx="130" cy="45" r="1.5"/>
    <circle cx="150" cy="80" r="2"/>
    <circle cx="45" cy="90" r="1.5"/>
    <circle cx="155" cy="120" r="2"/>
    <circle cx="50" cy="140" r="1.5"/>
  </g>
  
  <!-- Clock elements to represent time -->
  <g transform="translate(140, 140)" fill="#ffffff" opacity="0.7">
    <circle cx="0" cy="0" r="15" fill="none" stroke="#ffffff" stroke-width="2"/>
    <line x1="0" y1="0" x2="0" y2="-8" stroke="#ffffff" stroke-width="2"/>
    <line x1="0" y1="0" x2="6" y2="0" stroke="#ffffff" stroke-width="2"/>
  </g>
</svg>
