//
//  APIKeys.swift
//  PetCapsule
//
//  Native Apple services configuration - No external API keys needed!
//  Uses Apple MapKit, WeatherKit, and other Apple frameworks
//

import Foundation

struct APIKeys {
    // MARK: - Native Apple Services (No Keys Required)
    
    // Apple MapKit - Built into iOS, no API key needed
    static let useNativeMapKit = true
    
    // Apple WeatherKit - Built into iOS, no API key needed  
    static let useNativeWeatherKit = true
    
    // Apple Core Location - Built into iOS, no API key needed
    static let useNativeCoreLocation = true
    
    // MARK: - Optional External Services
    
    // OpenWeather API (Backup option if needed)
    static let openWeatherAPIKey: String = {
        if let envKey = ProcessInfo.processInfo.environment["OPENWEATHER_API_KEY"], !envKey.isEmpty {
            return envKey
        }
        if let plistKey = Bundle.main.object(forInfoDictionaryKey: "OpenWeatherAPIKey") as? String, !plistKey.isEmpty {
            return plistKey
        }
        return "" // Optional - not required since we use Apple WeatherKit
    }()
    
    // MARK: - API Service Configuration
    
    static func isNativeService(_ service: APIService) -> Bool {
        switch service {
        case .appleMapKit, .appleWeatherKit, .appleCoreLocation:
            return true
        case .openWeather:
            return false
        }
    }
    
    // Check if optional external API is configured
    static func isConfigured(for service: APIService) -> Bool {
        switch service {
        case .appleMapKit, .appleWeatherKit, .appleCoreLocation:
            return true // Native services always available
        case .openWeather:
            return !openWeatherAPIKey.isEmpty
        }
    }
    
    // Log API configuration status
    static func logConfigurationStatus() {
        print("🍎 Native Apple Services Configuration:")
        print("   Apple MapKit: ✅ Native iOS service")
        print("   Apple WeatherKit: ✅ Native iOS service") 
        print("   Apple Core Location: ✅ Native iOS service")
        print("   OpenWeather (Backup): \(isConfigured(for: .openWeather) ? "✅ Configured" : "⚪ Optional")")
        print("")
        print("🎉 All core features use native Apple frameworks - no external API keys required!")
    }
}

enum APIService {
    case appleMapKit
    case appleWeatherKit
    case appleCoreLocation
    case openWeather // Optional backup
}

// MARK: - API Endpoints for External Services

struct APIEndpoints {
    // OpenWeather API (optional backup)
    static let openWeatherBase = "https://api.openweathermap.org/data/2.5"
    static let airQualityBase = "https://api.openweathermap.org/data/2.5/air_pollution"
    
    struct OpenWeather {
        static let currentWeather = "\(openWeatherBase)/weather"
        static let forecast = "\(openWeatherBase)/forecast"
        static let airPollution = "\(airQualityBase)/current"
        static let airPollutionForecast = "\(airQualityBase)/forecast"
    }
}

// MARK: - API Configuration

struct APIConfiguration {
    // Request timeouts
    static let defaultTimeout: TimeInterval = 30.0
    static let uploadTimeout: TimeInterval = 60.0
    
    // Rate limiting (for external services only)
    static let maxRequestsPerMinute = 60
    static let maxRequestsPerHour = 1000
    
    // Cache settings
    static let cacheExpirationTime: TimeInterval = 300 // 5 minutes
    static let maxCacheSize = 50 * 1024 * 1024 // 50MB
    
    // Native service preferences
    static let preferNativeServices = true
    static let fallbackToExternalServices = false
}

// MARK: - Migration Summary

/*
 🔄 API Migration Summary:
 
 ❌ REMOVED: Google Places API → ✅ Apple MapKit
 ❌ REMOVED: Google Maps API → ✅ Apple MapKit  
 ❌ REMOVED: Google Pollen API → ✅ Apple WeatherKit Air Quality
 ❌ REMOVED: Google Service Account → ✅ Native iOS Authentication
 
 🎯 BENEFITS:
 • No API keys required
 • Better iOS integration
 • No authentication errors
 • Native performance
 • Apple ecosystem consistency
 • Reduced dependencies
 • Enhanced privacy
 */
