import Foundation
/// Build configuration management for different development environments
struct BuildConfiguration {
    // MARK: - Environment Detection
    enum Environment: String, CaseIterable {
        case development = "Development"
        case staging = "Staging"
        case production = "Production"
        case featureTesting = "FeatureTesting"
        var isProduction: Bool {
            return self == .production
        }
        var isDevelopment: Bool {
            return self == .development || self == .featureTesting
        }
        var allowsExperimentalFeatures: Bool {
            return self != .production
        }
    }
    static var current: Environment {
        #if PRODUCTION
        return .production
        #elseif STAGING  
        return .staging
        #elseif FEATURE_TESTING
        return .featureTesting
        #else
        return .development
        #endif
    }
    // MARK: - Feature Isolation Settings
    /// Whether to load features from feature branches
    static var allowsFeatureBranches: Bool {
        return current.isDevelopment
    }
    /// Whether to show debug UI elements
    static var showsDebugUI: Bool {
        return current != .production
    }
    /// Whether to enable crash reporting
    static var enablesCrashReporting: Bool {
        return current.isProduction
    }
    /// Whether to use real payment processing
    static var usesRealPayments: Bool {
        return current.isProduction
    }
    /// Whether to allow feature flag overrides
    static var allowsFeatureFlagOverrides: Bool {
        return current.allowsExperimentalFeatures
    }
    // MARK: - API Configuration
    static var apiBaseURL: String {
        switch current {
        case .development, .featureTesting:
            return "https://dev-api.petcapsule.app"
        case .staging:
            return "https://staging-api.petcapsule.app"
        case .production:
            return "https://api.petcapsule.app"
        }
    }
    // MARK: - Logging Configuration
    static var logLevel: LogLevel {
        switch current {
        case .development, .featureTesting:
            return .verbose
        case .staging:
            return .debug
        case .production:
            return .warning
        }
    }
    enum LogLevel: Int, CaseIterable {
        case verbose = 0
        case debug = 1
        case info = 2
        case warning = 3
        case error = 4
        case silent = 5
        var description: String {
            switch self {
            case .verbose: return "VERBOSE"
            case .debug: return "DEBUG"
            case .info: return "INFO"
            case .warning: return "WARNING"
            case .error: return "ERROR"
            case .silent: return "SILENT"
            }
        }
    }
    // MARK: - Feature Development Helpers
    /// Get the current git branch name for feature isolation
    static var currentBranch: String {
        // This would typically be set at build time via build scripts
        return Bundle.main.object(forInfoDictionaryKey: "GitBranch") as? String ?? "unknown"
    }
    /// Get build number for tracking feature versions
    static var buildNumber: String {
        return Bundle.main.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "0"
    }
    /// Get app version
    static var appVersion: String {
        return Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0"
    }
    // MARK: - Debug Helpers
    static func printConfiguration() {
        guard showsDebugUI else { return }
        print("""
        🔧 Build Configuration:
        Environment: \(current.rawValue)
        Branch: \(currentBranch)
        Version: \(appVersion) (\(buildNumber))
        API URL: \(apiBaseURL)
        Log Level: \(logLevel.description)
        Feature Flags: \(allowsFeatureFlagOverrides ? "Enabled" : "Disabled")
        """)
    }
}
// MARK: - Preprocessor Helpers
/// Conditional compilation helpers for feature isolation
struct CompilerFlags {
    /// Check if specific feature is enabled at compile time
    static func isFeatureEnabled(_ feature: String) -> Bool {
        #if FEATURE_AI_CHAT
        if feature == "ai_chat" { return true }
        #endif
        #if FEATURE_MEMORY_FILTERS
        if feature == "memory_filters" { return true }
        #endif
        #if FEATURE_COMMUNITY_EVENTS
        if feature == "community_events" { return true }
        #endif
        return false
    }
    /// Development-only code execution
    static func executeInDevelopment<T>(_ closure: () -> T) -> T? {
        #if DEBUG
        return closure()
        #else
        return nil
        #endif
    }
}
// MARK: - Scheme Detection
extension BuildConfiguration {
    /// Detect which Xcode scheme is being used
    static var currentScheme: String {
        #if SCHEME_PETCAPSULE_DEV
        return "PetCapsule-Dev"
        #elseif SCHEME_PETCAPSULE_STAGING
        return "PetCapsule-Staging"
        #elseif SCHEME_PETCAPSULE_FEATURE
        return "PetCapsule-Feature"
        #else
        return "PetCapsule"
        #endif
    }
    /// Check if running in feature testing scheme
    static var isFeatureTestingScheme: Bool {
        return currentScheme.contains("Feature")
    }
} 