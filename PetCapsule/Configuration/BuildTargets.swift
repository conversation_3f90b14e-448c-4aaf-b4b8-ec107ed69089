import Foundation

/// Simple build-time feature detection
/// No runtime complexity, no UI needed
struct BuildTargets {
    
    // MARK: - Feature Availability
    
    /// AI-powered health recommendations and insights
    static var hasAIHealth: Bool {
        #if INCLUDE_AI_HEALTH
        return true
        #else
        return false
        #endif
    }
    
    /// Community events and social features
    static var hasCommunityEvents: Bool {
        #if INCLUDE_COMMUNITY_EVENTS
        return true
        #else
        return false
        #endif
    }
    
    /// Advanced memory filtering and search
    static var hasAdvancedMemoryFilters: Bool {
        #if INCLUDE_MEMORY_FILTERS
        return true
        #else
        return false
        #endif
    }
    
    /// Premium subscription features
    static var hasPremiumFeatures: Bool {
        #if INCLUDE_PREMIUM_FEATURES
        return true
        #else
        return false
        #endif
    }
    
    /// Social sharing and community features
    static var hasSocialFeatures: Bool {
        #if INCLUDE_SOCIAL_FEATURES
        return true
        #else
        return false
        #endif
    }
    
    // MARK: - Build Configuration Info
    
    static var currentConfiguration: String {
        #if DEBUG
        return "Development"
        #elseif STAGING
        return "Staging"
        #else
        return "Production"
        #endif
    }
    
    static var availableFeatures: [String] {
        var features: [String] = []
        
        if hasAIHealth { features.append("AI Health") }
        if hasCommunityEvents { features.append("Community Events") }
        if hasAdvancedMemoryFilters { features.append("Advanced Memory Filters") }
        if hasPremiumFeatures { features.append("Premium Features") }
        if hasSocialFeatures { features.append("Social Features") }
        
        return features
    }
    
    // MARK: - Debug Info
    
    static func printConfiguration() {
        #if DEBUG
        print("""
        🏗️ Build Configuration: \(currentConfiguration)
        📦 Available Features: \(availableFeatures.joined(separator: ", "))
        🎯 AI Health: \(hasAIHealth ? "✅" : "❌")
        🏘️ Community Events: \(hasCommunityEvents ? "✅" : "❌")
        🔍 Memory Filters: \(hasAdvancedMemoryFilters ? "✅" : "❌")
        💎 Premium Features: \(hasPremiumFeatures ? "✅" : "❌")
        👥 Social Features: \(hasSocialFeatures ? "✅" : "❌")
        """)
        #endif
    }
} 