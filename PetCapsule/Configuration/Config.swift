//
//  Config.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

struct Config {

    // MARK: - 🍎 Apple Intelligence Configuration (iOS 18+)
    struct AppleIntelligence {
        // Apple Intelligence uses local processing - no API keys needed
        static let modelName = "apple-intelligence-foundation"
        
        // Local processing capabilities
        static let supportsVision = true
        static let supportsSpeech = true
        static let supportsNLP = true
        static let supportsML = true
        
        // Device requirements
        static let minimumOSVersion = "18.0"
        static let requiresA17Pro = true // For optimal performance
        
        // Privacy-first configuration
        static let localProcessingOnly = true
        static let cloudFallbackEnabled = false // Always local
        
        // Availability check for Apple Intelligence
        static var isAvailable: Bool {
            if #available(iOS 18.0, *) {
                return true
            }
            return false
        }
        
        // Performance optimization settings
        static let maxContextLength = 4096
        static let preferredResponseLength = 1024
        static let enableDeviceOptimization = true
    }

    // MARK: - App Configuration
    struct App {
        static let name = "PetTime Capsule"
        static let version = "1.0.0"
        static let bundleId = "com.yourcompany.petcapsule"

        // Premium subscription configuration
        static let premiumMonthlyPrice = 9.99
        static let premiumYearlyPrice = 99.99
        static let premiumPlusMonthlyPrice = 19.99
        static let premiumPlusYearlyPrice = 199.99
    }

    // MARK: - Feature Flags
    struct Features {
        static let skipAuthentication = false // ✅ Using real Apple ID authentication
        static let enableAnalytics = true
        static let enablePushNotifications = true
        static let enableAIFeatures = true
    }

    // MARK: - Development Configuration
    struct Development {
        static let isDebugMode = true
        static let enableMockData = false // Using real SwiftData + CloudKit
        static let logLevel = "debug"
    }

    // MARK: - Storage Configuration (Apple Native)
    struct Storage {
        static let maxFileSize = 10 * 1024 * 1024 // 10MB
        static let maxVideoSize = 100 * 1024 * 1024 // 100MB
        static let maxImageDimension = 4096 // Max image width/height
        static let supportedImageFormats = ["jpg", "jpeg", "png", "heic", "heif"]
        static let supportedVideoFormats = ["mp4", "mov", "m4v"]
    }
}

// MARK: - Environment-based Configuration
extension Config {
    static var isProduction: Bool {
        #if DEBUG
        return false
        #else
        return true
        #endif
    }

    static var shouldUseMockData: Bool {
        return !isProduction && Development.enableMockData
    }
}
