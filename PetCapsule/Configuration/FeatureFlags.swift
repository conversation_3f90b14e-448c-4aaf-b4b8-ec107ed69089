import Foundation

/// Feature flag system for managing feature rollouts and A/B testing
@MainActor
class FeatureFlags: ObservableObject {
    static let shared = FeatureFlags()
    
    @Published private var flags: [String: Bool] = [:]
    
    private init() {
        loadDefaultFlags()
        loadRemoteFlags()
    }
    
    // MARK: - Feature Flag Definitions
    
    enum Feature: String, CaseIterable {
        // AI Features
        case enhancedAIChat = "enhanced_ai_chat"
        case aiHealthRecommendations = "ai_health_recommendations"
        case smartNotifications = "smart_notifications"
        
        // Memory Features
        case advancedMemoryFilters = "advanced_memory_filters"
        case memorySharing = "memory_sharing"
        case memoryTimeline = "memory_timeline"
        
        // Social Features
        case communityEvents = "community_events"
        case petPlaydates = "pet_playdates"
        case socialFeed = "social_feed"
        
        // Premium Features
        case premiumSubscription = "premium_subscription"
        case unlimitedMemories = "unlimited_memories"
        case advancedAnalytics = "advanced_analytics"
        
        // Experimental Features
        case experimentalUI = "experimental_ui"
        case betaFeatures = "beta_features"
        case debugMode = "debug_mode"
        
        var defaultValue: Bool {
            switch self {
            // Stable features - enabled by default
            case .enhancedAIChat, .memoryTimeline:
                return true
                
            // New features - disabled by default for gradual rollout
            case .advancedMemoryFilters, .smartNotifications, .communityEvents:
                return false
                
            // Premium features - disabled by default
            case .premiumSubscription, .unlimitedMemories, .advancedAnalytics:
                return false
                
            // Experimental - disabled by default
            case .experimentalUI, .betaFeatures:
                return false
                
            // Debug - only in debug builds
            case .debugMode:
                #if DEBUG
                return true
                #else
                return false
                #endif
                
            // Social features - gradual rollout
            case .aiHealthRecommendations, .memorySharing, .petPlaydates, .socialFeed:
                return false
            }
        }
        
        var description: String {
            switch self {
            case .enhancedAIChat:
                return "Enhanced AI chat with better responses"
            case .aiHealthRecommendations:
                return "AI-powered health recommendations"
            case .smartNotifications:
                return "Smart notification system"
            case .advancedMemoryFilters:
                return "Advanced memory filtering and search"
            case .memorySharing:
                return "Share memories with other pet owners"
            case .memoryTimeline:
                return "Interactive memory timeline view"
            case .communityEvents:
                return "Community pet events and meetups"
            case .petPlaydates:
                return "Schedule playdates with other pets"
            case .socialFeed:
                return "Social feed of pet activities"
            case .premiumSubscription:
                return "Premium subscription features"
            case .unlimitedMemories:
                return "Unlimited memory storage"
            case .advancedAnalytics:
                return "Advanced pet health analytics"
            case .experimentalUI:
                return "Experimental UI improvements"
            case .betaFeatures:
                return "Beta feature access"
            case .debugMode:
                return "Debug mode for development"
            }
        }
    }
    
    // MARK: - Public API
    
    /// Check if a feature is enabled
    func isEnabled(_ feature: Feature) -> Bool {
        return flags[feature.rawValue] ?? feature.defaultValue
    }
    
    /// Enable/disable a feature (for testing/admin)
    func setFlag(_ feature: Feature, enabled: Bool) {
        flags[feature.rawValue] = enabled
        saveFlags()
    }
    
    /// Reset all flags to defaults
    func resetToDefaults() {
        flags.removeAll()
        loadDefaultFlags()
        saveFlags()
    }
    
    /// Get all available features for admin panel
    func getAllFeatures() -> [(Feature, Bool)] {
        return Feature.allCases.map { ($0, isEnabled($0)) }
    }
    
    // MARK: - Persistence
    
    private func loadDefaultFlags() {
        for feature in Feature.allCases {
            if flags[feature.rawValue] == nil {
                flags[feature.rawValue] = feature.defaultValue
            }
        }
    }
    
    private func loadRemoteFlags() {
        // Load from UserDefaults first
        if let savedFlags = UserDefaults.standard.dictionary(forKey: "FeatureFlags") as? [String: Bool] {
            for (key, value) in savedFlags {
                flags[key] = value
            }
        }
        
        // TODO: Load from remote configuration service (Firebase Remote Config, etc.)
        // This allows you to enable/disable features without app updates
        loadRemoteConfiguration()
    }
    
    private func saveFlags() {
        UserDefaults.standard.set(flags, forKey: "FeatureFlags")
    }
    
    private func loadRemoteConfiguration() {
        // Placeholder for remote config integration
        // Could integrate with Firebase Remote Config, AWS AppConfig, etc.
        
        #if DEBUG
        // In debug builds, allow overrides via launch arguments
        loadDebugOverrides()
        #endif
    }
    
    private func loadDebugOverrides() {
        // Allow feature flags to be overridden via Xcode scheme arguments
        // Example: -feature_enhanced_ai_chat YES
        for feature in Feature.allCases {
            let argumentKey = "feature_\(feature.rawValue)"
            if let override = UserDefaults.standard.object(forKey: argumentKey) as? Bool {
                flags[feature.rawValue] = override
                print("🚩 Debug override: \(feature.rawValue) = \(override)")
            }
        }
    }
}

// MARK: - SwiftUI Helpers

import SwiftUI

/// View modifier to conditionally show views based on feature flags
struct FeatureFlag: ViewModifier {
    let feature: FeatureFlags.Feature
    let fallback: (() -> AnyView)?
    
    init(_ feature: FeatureFlags.Feature, fallback: (() -> AnyView)? = nil) {
        self.feature = feature
        self.fallback = fallback
    }
    
    func body(content: Content) -> some View {
        if FeatureFlags.shared.isEnabled(feature) {
            content
        } else if let fallback = fallback {
            fallback()
        } else {
            EmptyView()
        }
    }
}

extension View {
    /// Show this view only if the feature flag is enabled
    func featureFlag(_ feature: FeatureFlags.Feature) -> some View {
        self.modifier(FeatureFlag(feature))
    }
    
    /// Show this view if feature is enabled, otherwise show fallback
    func featureFlag(_ feature: FeatureFlags.Feature, fallback: @escaping () -> some View) -> some View {
        self.modifier(FeatureFlag(feature, fallback: { AnyView(fallback()) }))
    }
}

// MARK: - Admin Panel View

struct FeatureFlagsAdminView: View {
    @StateObject private var featureFlags = FeatureFlags.shared
    
    var body: some View {
        NavigationView {
            List {
                Section("🚩 Feature Flags") {
                    ForEach(FeatureFlags.Feature.allCases, id: \.rawValue) { feature in
                        FeatureFlagRow(feature: feature)
                    }
                }
                
                Section("🔧 Actions") {
                    Button("Reset to Defaults") {
                        featureFlags.resetToDefaults()
                    }
                    .foregroundColor(.orange)
                }
            }
            .navigationTitle("Feature Flags")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct FeatureFlagRow: View {
    let feature: FeatureFlags.Feature
    @StateObject private var featureFlags = FeatureFlags.shared
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(feature.rawValue.replacingOccurrences(of: "_", with: " ").capitalized)
                    .font(.headline)
                
                Text(feature.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: Binding(
                get: { featureFlags.isEnabled(feature) },
                set: { featureFlags.setFlag(feature, enabled: $0) }
            ))
        }
        .padding(.vertical, 2)
    }
}

#if DEBUG
// MARK: - Debug Preview
struct FeatureFlagsAdminView_Previews: PreviewProvider {
    static var previews: some View {
        FeatureFlagsAdminView()
    }
}
#endif 