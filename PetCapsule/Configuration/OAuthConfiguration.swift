//
//  OAuthConfiguration.swift
//  PetCapsule
//
//  OAuth Provider Configuration for Pet Lovers
//  Step-by-step setup guide for social authentication
//

import Foundation

struct OAuthConfiguration {
    
    // MARK: - Configuration Status
    static var isGoogleConfigured: Bool {
        return !googleClientId.isEmpty && !googleClientSecret.isEmpty
    }
    
    static var isFacebookConfigured: Bool {
        return !facebookAppId.isEmpty && !facebookAppSecret.isEmpty
    }
    
    // MARK: - Google OAuth Configuration
    static let googleClientId = ProcessInfo.processInfo.environment["GOOGLE_CLIENT_ID"] ?? ""
    static let googleClientSecret = ProcessInfo.processInfo.environment["GOOGLE_CLIENT_SECRET"] ?? ""
    
    // MARK: - Facebook OAuth Configuration  
    static let facebookAppId = ProcessInfo.processInfo.environment["FACEBOOK_APP_ID"] ?? ""
    static let facebookAppSecret = ProcessInfo.processInfo.environment["FACEBOOK_APP_SECRET"] ?? ""
    
    // MARK: - Setup Instructions
    
    static func printSetupInstructions() {
        print("""
        
        🐾 PetCapsule OAuth Setup Instructions for Pet Lovers
        =====================================================
        
        Your app currently has these authentication methods:
        ✅ Apple Sign-In (Built-in, no setup required)
        ✅ Email/Password (Built-in)
        ✅ Biometric Auth (TouchID/FaceID)
        
        To enable additional social logins for pet lovers:
        
        📱 GOOGLE SIGN-IN (Recommended for pet photo sharing)
        --------------------------------------------------
        1. Go to Google Cloud Console: https://console.cloud.google.com/
        2. Create a new project or select existing one
        3. Enable Google+ API and Google Sign-In API
        4. Go to Credentials → Create Credentials → OAuth 2.0 Client ID
        5. Select "iOS" application type
        6. Add your bundle ID: com.yourcompany.petcapsule
        7. Download the GoogleService-Info.plist file
        8. Add these environment variables:
           - GOOGLE_CLIENT_ID="your-client-id"
           - GOOGLE_CLIENT_SECRET="your-client-secret"
        
        📘 FACEBOOK SIGN-IN (Great for pet communities) 
        -----------------------------------------------
        1. Go to Facebook Developers: https://developers.facebook.com/
        2. Create a new app for "Consumer" use case
        3. Add "Facebook Login" product to your app
        4. Configure iOS settings with your bundle ID
        5. Add these environment variables:
           - FACEBOOK_APP_ID="your-app-id"
           - FACEBOOK_APP_SECRET="your-app-secret"
        
        🔐 APPLE NATIVE AUTHENTICATION
        ------------------------------
        Using Apple's native authentication services:
        - Apple Sign-In (primary)
        - CloudKit for data storage
        - Keychain for secure credential storage
        
        💡 WHY THESE PROVIDERS FOR PET LOVERS?
        -------------------------------------
        🍎 Apple Sign-In: Most secure, privacy-focused (already enabled)
        📧 Email/Password: Traditional, works everywhere (already enabled) 
        🔍 Google: Great for users who store pet photos in Google Photos
        📱 Facebook: Perfect for pet community sharing and groups
        
        Current Configuration Status:
        Apple Sign-In: ✅ Enabled
        Email/Password: ✅ Enabled  
        Google: \(isGoogleConfigured ? "✅ Configured" : "❌ Not configured")
        Facebook: \(isFacebookConfigured ? "✅ Configured" : "❌ Not configured")
        
        """)
    }
}

// MARK: - Apple Native Provider Mapping

extension OAuthConfiguration {
    static func getAppleProviderName(for provider: String) -> String {
        switch provider.lowercased() {
        case "apple": return "apple"
        default: return "apple" // Default to Apple Sign-In
        }
    }
}