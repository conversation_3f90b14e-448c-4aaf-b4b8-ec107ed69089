import Foundation
import SwiftUI

/// Production-ready feature flag system
/// - NO UI for end users
/// - Remote config integration ready  
/// - Debug overrides for development only
@MainActor
class ProductionFeatureFlags: ObservableObject {
    static let shared = ProductionFeatureFlags()
    
    @Published private var flags: [String: Bool] = [:]
    
    enum Feature: String, CaseIterable {
        case enhancedAIChat = "enhanced_ai_chat"
        case aiHealthRecommendations = "ai_health_recommendations"
        case communityEvents = "community_events"
        case advancedMemoryFilters = "advanced_memory_filters"
        
        var defaultValue: Bool {
            switch self {
            case .enhancedAIChat:
                return true  // Stable, always on
            case .aiHealthRecommendations, .communityEvents, .advancedMemoryFilters:
                return false // New features, off by default
            }
        }
    }
    
    private init() {
        loadFlags()
    }
    
    /// Check if feature is enabled - ONLY public method for production
    func isEnabled(_ feature: Feature) -> Bool {
        return flags[feature.rawValue] ?? feature.defaultValue
    }
    
    // MARK: - Private Implementation
    
    private func loadFlags() {
        loadDefaultFlags()
        
        #if DEBUG
        loadDebugOverrides()
        #else
        loadRemoteConfig()
        #endif
    }
    
    private func loadDefaultFlags() {
        for feature in Feature.allCases {
            flags[feature.rawValue] = feature.defaultValue
        }
    }
    
    private func loadRemoteConfig() {
        // In production, flags come from remote config service
        // Examples: Firebase Remote Config, AWS AppConfig, LaunchDarkly
        
        if let savedFlags = UserDefaults.standard.dictionary(forKey: "RemoteFeatureFlags") as? [String: Bool] {
            for (key, value) in savedFlags {
                flags[key] = value
            }
        }
    }
    
    #if DEBUG
    // Debug-only functionality - never ships to App Store
    private func loadDebugOverrides() {
        for feature in Feature.allCases {
            let argumentKey = "feature_\(feature.rawValue)"
            if let override = UserDefaults.standard.object(forKey: argumentKey) as? Bool {
                flags[feature.rawValue] = override
                print("🚩 Debug override: \(feature.rawValue) = \(override)")
            }
        }
    }
    
    func debugSetFlag(_ feature: Feature, enabled: Bool) {
        flags[feature.rawValue] = enabled
    }
    
    func debugResetToDefaults() {
        flags.removeAll()
        loadDefaultFlags()
    }
    #endif
}

// MARK: - SwiftUI Helper

extension View {
    func productionFeatureFlag(_ feature: ProductionFeatureFlags.Feature) -> some View {
        Group {
            if ProductionFeatureFlags.shared.isEnabled(feature) {
                self
            } else {
                EmptyView()
            }
        }
    }
} 