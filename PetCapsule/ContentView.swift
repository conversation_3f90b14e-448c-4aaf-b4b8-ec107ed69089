//
//  ContentView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct ContentView: View {
    // MARK: - Environment & State
    @StateObject private var authService = AuthenticationService()
    @StateObject private var appleIDAuthService = AppleIDAuthenticationService.shared
    @StateObject private var realDataService = RealDataService()
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var themeManager = ThemeManager.shared
    
    @State private var isAppReady = false
    @State private var showingOnboarding = false
    @State private var hasSkippedAuth = false
    @State private var animationScale: CGFloat = 1.0
    
    var body: some View {
        ZStack {
            if !isAppReady {
                // App Loading Screen
                loadingView
            } else if !authService.isAuthenticated {
                // Authentication View - Use the existing one from Views/Authentication/
                AuthenticationView()
                    .environmentObject(authService)
                    .environmentObject(appleIDAuthService)
            } else if shouldShowOnboarding {
                // Onboarding for new users
                OnboardingView(showOnboarding: $showingOnboarding, hasSkippedAuth: $hasSkippedAuth)
                    .environmentObject(authService)
                    .onReceive(NotificationCenter.default.publisher(for: .onboardingCompleted)) { _ in
                        showingOnboarding = false
                    }
            } else {
                // Main App Interface
                if #available(iOS 18.0, *) {
                    MainAppView()
                        .environmentObject(authService)
                        .environmentObject(realDataService)
                        .environmentObject(subscriptionService)
                        .environmentObject(themeManager)
                } else {
                    // Fallback for earlier iOS versions
                    Text("iOS 18.0+ Required for Full Features")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding()
                }
            }
        }
        .onAppear {
            setupApp()
        }
        .alert("Authentication Error", isPresented: .constant(authService.errorMessage != nil)) {
            Button("OK") {
                authService.errorMessage = nil
            }
        } message: {
            Text(authService.errorMessage ?? "")
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 30) {
            // App Logo with backwards compatible animation
            Image(systemName: "pawprint.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.blue)
                .scaleEffect(animationScale)
                .onAppear {
                    withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                        animationScale = 1.2
                    }
                }
            
            // App Name
            Text("PetCapsule")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text("Your pet's digital companion")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // Loading Indicator
            VStack(spacing: 15) {
                ProgressView()
                    .scaleEffect(1.2)
                
                Text("Preparing your experience...")
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }
    
    // MARK: - Setup Methods
    private func setupApp() {
        Task {
            // Initialize Apple native services
            await initializeServices()
            
            // Check authentication status
            authService.checkAuthenticationStatus()
            
            // Mark app as ready
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.5)) {
                    isAppReady = true
                }
            }
        }
    }
    
    private func initializeServices() async {
        // Apple data service initialization is handled in its init
        print("✅ Apple native services initialized")
        
        // Setup notification observers
        setupNotificationObservers()
    }
    
    private func setupNotificationObservers() {
        // Listen for authentication state changes
        NotificationCenter.default.addObserver(
            forName: .authenticationStateChanged,
            object: nil,
            queue: .main
        ) { _ in
            // Handle authentication state changes if needed
        }
        
        // Listen for data sync completion
        NotificationCenter.default.addObserver(
            forName: .dataSyncCompleted,
            object: nil,
            queue: .main
        ) { _ in
            // Handle data sync completion if needed
        }
    }
    
    // MARK: - Computed Properties
    private var shouldShowOnboarding: Bool {
        // Temporarily disabled onboarding check during migration
        // TODO: Add onboardingCompleted property to User model
        return false
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let authenticationStateChanged = Notification.Name("authenticationStateChanged")
    static let onboardingCompleted = Notification.Name("onboardingCompleted")
    static let dataSyncCompleted = Notification.Name("dataSyncCompleted")
}

// MARK: - Preview
struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
