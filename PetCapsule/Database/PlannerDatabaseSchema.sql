-- PetCapsule Planner Database Schema
-- PostgreSQL schema for planner features
-- Enhanced with RLS policies and real-time subscriptions
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
-- =============================================
-- PETS TABLE (Core pet data)
-- =============================================
CREATE TABLE IF NOT EXISTS pets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    -- Basic pet information
    name TEXT NOT NULL,
    species TEXT NOT NULL, -- 'dog', 'cat', 'bird', 'rabbit', etc.
    breed TEXT,
    birth_date DATE,
    adoption_date DATE,
    -- Physical characteristics
    weight DOUBLE PRECISION, -- in pounds
    gender TEXT, -- 'male', 'female', 'unknown'
    -- Care information
    activity_level TEXT DEFAULT 'moderate', -- 'low', 'moderate', 'high', 'very_high'
    personality_traits TEXT[], -- Array of trait strings
    health_conditions TEXT[], -- Array of condition strings
    medications TEXT[], -- Array of medication names
    vaccinations TEXT[], -- Array of vaccination names
    health_alerts TEXT[], -- Array of alert titles
    ai_recommendations TEXT[], -- Array of AI-generated recommendations
    -- Media and profile
    profile_image_url TEXT,
    -- Health tracking
    last_checkup_date DATE,
    -- Status
    is_active BOOLEAN DEFAULT true,
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Create indexes for pets
CREATE INDEX IF NOT EXISTS pets_user_id_idx ON pets (user_id);
CREATE INDEX IF NOT EXISTS pets_species_idx ON pets (species);
CREATE INDEX IF NOT EXISTS pets_active_idx ON pets (is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS pets_created_at_idx ON pets (created_at DESC);
-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE TRIGGER update_pets_updated_at BEFORE UPDATE ON pets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Walk Plans Table
CREATE TABLE IF NOT EXISTS walk_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    pet_id UUID REFERENCES pets(id) ON DELETE CASCADE,
    -- Location data
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_name TEXT,
    address TEXT,
    -- Environmental data
    temperature INTEGER,
    humidity INTEGER,
    air_quality_index INTEGER,
    wind_speed DECIMAL(5, 2),
    weather_condition TEXT,
    -- Pollen data
    tree_pollen_index INTEGER DEFAULT 0,
    grass_pollen_index INTEGER DEFAULT 0,
    weed_pollen_index INTEGER DEFAULT 0,
    overall_pollen_risk TEXT,
    -- Walk recommendation
    walk_score INTEGER NOT NULL CHECK (walk_score >= 0 AND walk_score <= 100),
    recommended_time_slot TEXT,
    recommended_duration TEXT,
    recommendation_reason TEXT,
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours')
);
-- Walk Locations Table (Pet-friendly places)
CREATE TABLE IF NOT EXISTS walk_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    -- Location details
    name TEXT NOT NULL,
    address TEXT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    -- Place information
    category TEXT NOT NULL, -- park, veterinary, pet_store, restaurant, etc.
    rating DECIMAL(3, 2) DEFAULT 0,
    phone_number TEXT,
    website TEXT,
    is_open BOOLEAN DEFAULT true,
    -- Environmental suitability
    air_quality_score INTEGER,
    pollen_safety_score INTEGER,
    overall_safety_score INTEGER,
    -- User interaction
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    is_favorite BOOLEAN DEFAULT false,
    visit_count INTEGER DEFAULT 0,
    last_visited_at TIMESTAMP WITH TIME ZONE,
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Walk Memories Table (Location-tagged memories)
CREATE TABLE IF NOT EXISTS walk_memories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    pet_id UUID REFERENCES pets(id) ON DELETE CASCADE,
    -- Memory content
    title TEXT NOT NULL,
    description TEXT,
    media_urls TEXT[], -- Array of image/video URLs
    -- Location data
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_name TEXT,
    address TEXT,
    -- Walk details
    duration_minutes INTEGER,
    distance_meters DECIMAL(10, 2),
    route_coordinates JSONB, -- Array of lat/lng coordinates
    -- Environmental context
    temperature INTEGER,
    humidity INTEGER,
    air_quality_index INTEGER,
    weather_condition TEXT,
    tree_pollen_index INTEGER,
    grass_pollen_index INTEGER,
    weed_pollen_index INTEGER,
    -- User interaction
    is_favorite BOOLEAN DEFAULT false,
    tags TEXT[],
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Community Events Table
CREATE TABLE IF NOT EXISTS community_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organizer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    -- Event details
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL, -- walks, training, social, emergency
    -- Location
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_name TEXT NOT NULL,
    address TEXT,
    -- Timing
    event_date TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    -- Capacity
    max_attendees INTEGER,
    current_attendees INTEGER DEFAULT 0,
    -- Environmental requirements
    min_air_quality_score INTEGER DEFAULT 0,
    max_pollen_level INTEGER DEFAULT 5,
    weather_requirements JSONB,
    -- Status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'completed')),
    is_public BOOLEAN DEFAULT true,
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Event Attendees Table
CREATE TABLE IF NOT EXISTS event_attendees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID REFERENCES community_events(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    pet_id UUID REFERENCES pets(id) ON DELETE CASCADE,
    -- RSVP details
    status TEXT DEFAULT 'attending' CHECK (status IN ('attending', 'maybe', 'not_attending')),
    rsvp_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Check-in
    checked_in BOOLEAN DEFAULT false,
    check_in_time TIMESTAMP WITH TIME ZONE,
    UNIQUE(event_id, user_id)
);
-- Environmental Alerts Table
CREATE TABLE IF NOT EXISTS environmental_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    pet_id UUID REFERENCES pets(id),
    -- Alert configuration
    alert_type TEXT NOT NULL, -- air_quality, pollen, weather, temperature
    threshold_value INTEGER NOT NULL,
    comparison_operator TEXT NOT NULL CHECK (comparison_operator IN ('>', '<', '>=', '<=', '=')),
    -- Location
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    radius_meters INTEGER DEFAULT 5000,
    -- Alert settings
    is_active BOOLEAN DEFAULT true,
    notification_enabled BOOLEAN DEFAULT true,
    notification_time_start TIME DEFAULT '06:00:00',
    notification_time_end TIME DEFAULT '22:00:00',
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Alert Notifications Table (Log of sent notifications)
CREATE TABLE IF NOT EXISTS alert_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alert_id UUID REFERENCES environmental_alerts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    -- Notification details
    alert_type TEXT NOT NULL,
    current_value INTEGER NOT NULL,
    threshold_value INTEGER NOT NULL,
    message TEXT NOT NULL,
    -- Delivery
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivery_status TEXT DEFAULT 'sent' CHECK (delivery_status IN ('sent', 'delivered', 'failed')),
    -- User interaction
    read_at TIMESTAMP WITH TIME ZONE,
    dismissed_at TIMESTAMP WITH TIME ZONE
);
-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_walk_plans_user_id ON walk_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_walk_plans_pet_id ON walk_plans(pet_id);
CREATE INDEX IF NOT EXISTS idx_walk_plans_location ON walk_plans(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_walk_plans_expires_at ON walk_plans(expires_at);
CREATE INDEX IF NOT EXISTS idx_walk_locations_category ON walk_locations(category);
CREATE INDEX IF NOT EXISTS idx_walk_locations_location ON walk_locations(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_walk_locations_user_favorites ON walk_locations(user_id, is_favorite);
CREATE INDEX IF NOT EXISTS idx_walk_memories_user_id ON walk_memories(user_id);
CREATE INDEX IF NOT EXISTS idx_walk_memories_pet_id ON walk_memories(pet_id);
CREATE INDEX IF NOT EXISTS idx_walk_memories_location ON walk_memories(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_walk_memories_created_at ON walk_memories(created_at);
CREATE INDEX IF NOT EXISTS idx_community_events_date ON community_events(event_date);
CREATE INDEX IF NOT EXISTS idx_community_events_location ON community_events(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_community_events_status ON community_events(status);
CREATE INDEX IF NOT EXISTS idx_environmental_alerts_user_id ON environmental_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_environmental_alerts_active ON environmental_alerts(is_active);
CREATE INDEX IF NOT EXISTS idx_environmental_alerts_location ON environmental_alerts(latitude, longitude);
-- Row Level Security (RLS) Policies
-- Walk Plans RLS
ALTER TABLE walk_plans ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own walk plans" ON walk_plans
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own walk plans" ON walk_plans
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own walk plans" ON walk_plans
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own walk plans" ON walk_plans
    FOR DELETE USING (auth.uid() = user_id);
-- Walk Locations RLS
ALTER TABLE walk_locations ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view all walk locations" ON walk_locations
    FOR SELECT USING (true);
CREATE POLICY "Users can insert walk locations" ON walk_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own walk locations" ON walk_locations
    FOR UPDATE USING (auth.uid() = user_id);
-- Walk Memories RLS
ALTER TABLE walk_memories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own walk memories" ON walk_memories
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own walk memories" ON walk_memories
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own walk memories" ON walk_memories
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own walk memories" ON walk_memories
    FOR DELETE USING (auth.uid() = user_id);
-- Community Events RLS
ALTER TABLE community_events ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view public events" ON community_events
    FOR SELECT USING (is_public = true OR auth.uid() = organizer_id);
CREATE POLICY "Users can create events" ON community_events
    FOR INSERT WITH CHECK (auth.uid() = organizer_id);
CREATE POLICY "Organizers can update their events" ON community_events
    FOR UPDATE USING (auth.uid() = organizer_id);
CREATE POLICY "Organizers can delete their events" ON community_events
    FOR DELETE USING (auth.uid() = organizer_id);
-- Event Attendees RLS
ALTER TABLE event_attendees ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view attendees of events they're part of" ON event_attendees
    FOR SELECT USING (
        auth.uid() = user_id OR 
        EXISTS (SELECT 1 FROM community_events WHERE id = event_id AND organizer_id = auth.uid())
    );
CREATE POLICY "Users can RSVP to events" ON event_attendees
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own RSVP" ON event_attendees
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can cancel their RSVP" ON event_attendees
    FOR DELETE USING (auth.uid() = user_id);
-- Environmental Alerts RLS
ALTER TABLE environmental_alerts ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own alerts" ON environmental_alerts
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own alerts" ON environmental_alerts
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own alerts" ON environmental_alerts
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own alerts" ON environmental_alerts
    FOR DELETE USING (auth.uid() = user_id);
-- Alert Notifications RLS
ALTER TABLE alert_notifications ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own notifications" ON alert_notifications
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can insert notifications" ON alert_notifications
    FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update their notification status" ON alert_notifications
    FOR UPDATE USING (auth.uid() = user_id);
-- Functions for automatic cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_walk_plans()
RETURNS void AS $$
BEGIN
    DELETE FROM walk_plans WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;
-- Trigger for updating updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
-- Apply updated_at triggers
CREATE TRIGGER update_walk_plans_updated_at BEFORE UPDATE ON walk_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_walk_locations_updated_at BEFORE UPDATE ON walk_locations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_walk_memories_updated_at BEFORE UPDATE ON walk_memories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_community_events_updated_at BEFORE UPDATE ON community_events
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_environmental_alerts_updated_at BEFORE UPDATE ON environmental_alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
