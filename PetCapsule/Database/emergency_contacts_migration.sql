-- Emergency Contacts Table Migration
-- Creates the emergency_contacts table with proper unique constraints
-- to prevent duplicate default contacts

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- EMERGENCY CONTACTS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS emergency_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Contact information
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    address TEXT,
    
    -- Relationship/Type
    relationship TEXT NOT NULL, -- 'veterinarian', 'emergency service', 'poison control', 'animal hospital', 'custom'
    
    -- Metadata
    is_primary BOOLEAN DEFAULT false,
    is_default BOOLEAN DEFAULT false, -- Mark default contacts to prevent duplication
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraints to prevent duplicates
    CONSTRAINT emergency_contacts_unique_user_name_phone UNIQUE (user_id, name, phone),
    CONSTRAINT emergency_contacts_unique_default UNIQUE (user_id, relationship, is_default) 
        DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS emergency_contacts_user_id_idx ON emergency_contacts (user_id);
CREATE INDEX IF NOT EXISTS emergency_contacts_relationship_idx ON emergency_contacts (relationship);
CREATE INDEX IF NOT EXISTS emergency_contacts_is_primary_idx ON emergency_contacts (user_id, is_primary) WHERE is_primary = true;
CREATE INDEX IF NOT EXISTS emergency_contacts_is_default_idx ON emergency_contacts (user_id, is_default) WHERE is_default = true;

-- Trigger for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_emergency_contacts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_emergency_contacts_updated_at 
    BEFORE UPDATE ON emergency_contacts
    FOR EACH ROW 
    EXECUTE FUNCTION update_emergency_contacts_updated_at();

-- Row Level Security (RLS) Policies
ALTER TABLE emergency_contacts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own emergency contacts" ON emergency_contacts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own emergency contacts" ON emergency_contacts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own emergency contacts" ON emergency_contacts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own emergency contacts" ON emergency_contacts
    FOR DELETE USING (auth.uid() = user_id);

-- Function to clean up duplicate emergency contacts
CREATE OR REPLACE FUNCTION cleanup_duplicate_emergency_contacts(p_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    contact_record RECORD;
    keep_id UUID;
BEGIN
    -- For each combination of name and phone, keep only the oldest record
    FOR contact_record IN
        SELECT name, phone, MIN(created_at) as oldest_created_at
        FROM emergency_contacts 
        WHERE user_id = p_user_id
        GROUP BY name, phone
        HAVING COUNT(*) > 1
    LOOP
        -- Get the ID of the oldest record to keep
        SELECT id INTO keep_id
        FROM emergency_contacts 
        WHERE user_id = p_user_id 
          AND name = contact_record.name 
          AND phone = contact_record.phone
          AND created_at = contact_record.oldest_created_at
        LIMIT 1;
        
        -- Delete all other records with the same name and phone
        DELETE FROM emergency_contacts 
        WHERE user_id = p_user_id 
          AND name = contact_record.name 
          AND phone = contact_record.phone
          AND id != keep_id;
          
        GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;
    END LOOP;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Success message
SELECT 'Emergency contacts table created successfully with unique constraints!' as status; 