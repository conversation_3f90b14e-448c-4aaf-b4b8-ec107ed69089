-- Enhanced Memory System Database Schema
-- This schema supports all advanced memory features including video support,
-- secure vault, timeline events, measurements, and memorial functionality

-- Drop existing tables if they exist (for development)
-- DROP TABLE IF EXISTS memory_media_items CASCADE;
-- DROP TABLE IF EXISTS memory_measurements CASCADE;
-- DROP TABLE IF EXISTS memory_locations CASCADE;
-- DROP TABLE IF EXISTS memory_pet_associations CASCADE;
-- DROP TABLE IF EXISTS enhanced_memories CASCADE;

-- Enhanced Memories Table
CREATE TABLE enhanced_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    thumbnail_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Event and categorization
    event_type TEXT NOT NULL DEFAULT 'general',
    mood TEXT NOT NULL DEFAULT 'happy',
    tags TEXT[] DEFAULT '{}',
    
    -- Security and privacy
    is_secure_vault BOOLEAN DEFAULT FALSE,
    is_memorial BOOLEAN DEFAULT FALSE,
    is_protected BOOLEAN DEFAULT FALSE,
    
    -- Additional features
    voice_note_url TEXT,
    reminder_date TIMESTAMP WITH TIME ZONE,
    
    -- Milestone data (JSON for flexibility)
    milestone_data JSONB,
    
    -- Share settings (JSON for flexibility)
    share_settings JSONB DEFAULT '{
        "isPublic": false,
        "sharedWith": [],
        "allowComments": true,
        "allowDownload": false
    }',
    
    -- Weather data (JSON for flexibility)
    weather_data JSONB,
    
    -- Metadata
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    CONSTRAINT valid_event_type CHECK (event_type IN (
        'general', 'daily', 'special', 'adoption', 'birthday', 'gotcha_day', 
        'first_day', 'veterinary', 'vaccination', 'grooming', 'dental', 
        'medication', 'training', 'achievement', 'socialization', 'behavior', 
        'exercise', 'play', 'adventure', 'travel', 'walk', 'family', 
        'friends', 'other_pets', 'visitors', 'memorial', 'remembrance', 
        'final_moments'
    )),
    CONSTRAINT valid_mood CHECK (mood IN (
        'ecstatic', 'happy', 'content', 'calm', 'excited', 'playful', 
        'sleepy', 'curious', 'proud', 'nostalgic'
    ))
);

-- Memory Media Items Table
CREATE TABLE memory_media_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES enhanced_memories(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    thumbnail_url TEXT,
    media_type TEXT NOT NULL,
    duration_seconds INTEGER, -- For video/audio
    file_size_bytes BIGINT,
    width INTEGER,
    height INTEGER,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Media metadata (JSON for flexibility)
    metadata JSONB,
    
    -- Display order
    display_order INTEGER DEFAULT 0,
    
    -- Constraints
    CONSTRAINT valid_media_type CHECK (media_type IN ('photo', 'video', 'audio')),
    CONSTRAINT valid_dimensions CHECK (
        (width IS NULL AND height IS NULL) OR 
        (width > 0 AND height > 0)
    )
);

-- Memory Pet Associations Table
CREATE TABLE memory_pet_associations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES enhanced_memories(id) ON DELETE CASCADE,
    pet_id UUID NOT NULL REFERENCES pets(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique associations
    UNIQUE(memory_id, pet_id)
);

-- Memory Locations Table
CREATE TABLE memory_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES enhanced_memories(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    place_type TEXT NOT NULL DEFAULT 'other',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_place_type CHECK (place_type IN (
        'home', 'park', 'beach', 'vet', 'groomer', 'training', 'other'
    )),
    CONSTRAINT valid_coordinates CHECK (
        (latitude IS NULL AND longitude IS NULL) OR 
        (latitude BETWEEN -90 AND 90 AND longitude BETWEEN -180 AND 180)
    )
);

-- Memory Measurements Table
CREATE TABLE memory_measurements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES enhanced_memories(id) ON DELETE CASCADE,
    pet_id UUID NOT NULL REFERENCES pets(id) ON DELETE CASCADE,
    measurement_type TEXT NOT NULL,
    value DECIMAL(10, 4) NOT NULL,
    unit TEXT NOT NULL,
    measurement_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_measurement_type CHECK (measurement_type IN (
        'weight', 'height', 'length', 'temperature'
    )),
    CONSTRAINT positive_value CHECK (value > 0)
);

-- Memory Collections Table (for organizing memories)
CREATE TABLE memory_collections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    color TEXT,
    is_system_collection BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique collection names per user
    UNIQUE(user_id, name)
);

-- Memory Collection Items Table
CREATE TABLE memory_collection_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    collection_id UUID NOT NULL REFERENCES memory_collections(id) ON DELETE CASCADE,
    memory_id UUID NOT NULL REFERENCES enhanced_memories(id) ON DELETE CASCADE,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique items per collection
    UNIQUE(collection_id, memory_id)
);

-- Memory Favorites Table
CREATE TABLE memory_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    memory_id UUID NOT NULL REFERENCES enhanced_memories(id) ON DELETE CASCADE,
    favorited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique favorites per user
    UNIQUE(user_id, memory_id)
);

-- Memory Comments Table (for shared memories)
CREATE TABLE memory_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES enhanced_memories(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Soft delete support
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Memory Shares Table (for tracking shared memories)
CREATE TABLE memory_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES enhanced_memories(id) ON DELETE CASCADE,
    shared_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    shared_with UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    share_url TEXT,
    access_level TEXT NOT NULL DEFAULT 'view',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_access_level CHECK (access_level IN ('view', 'comment', 'download'))
);

-- Memorial Garden Table (special memorial management)
CREATE TABLE memorial_garden (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    pet_id UUID NOT NULL REFERENCES pets(id) ON DELETE CASCADE,
    memorial_date DATE NOT NULL,
    tribute_message TEXT,
    memorial_image_url TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique memorial per pet per user
    UNIQUE(user_id, pet_id)
);

-- Indexes for performance optimization
CREATE INDEX idx_enhanced_memories_created_at ON enhanced_memories(created_at DESC);
CREATE INDEX idx_enhanced_memories_event_type ON enhanced_memories(event_type);
CREATE INDEX idx_enhanced_memories_mood ON enhanced_memories(mood);
CREATE INDEX idx_enhanced_memories_is_secure_vault ON enhanced_memories(is_secure_vault);
CREATE INDEX idx_enhanced_memories_is_memorial ON enhanced_memories(is_memorial);
CREATE INDEX idx_enhanced_memories_created_by ON enhanced_memories(created_by);
CREATE INDEX idx_enhanced_memories_tags ON enhanced_memories USING GIN(tags);
CREATE INDEX idx_enhanced_memories_milestone_data ON enhanced_memories USING GIN(milestone_data);

CREATE INDEX idx_memory_media_items_memory_id ON memory_media_items(memory_id);
CREATE INDEX idx_memory_media_items_media_type ON memory_media_items(media_type);
CREATE INDEX idx_memory_media_items_display_order ON memory_media_items(memory_id, display_order);

CREATE INDEX idx_memory_pet_associations_memory_id ON memory_pet_associations(memory_id);
CREATE INDEX idx_memory_pet_associations_pet_id ON memory_pet_associations(pet_id);

CREATE INDEX idx_memory_locations_memory_id ON memory_locations(memory_id);
CREATE INDEX idx_memory_locations_coordinates ON memory_locations(latitude, longitude);

CREATE INDEX idx_memory_measurements_memory_id ON memory_measurements(memory_id);
CREATE INDEX idx_memory_measurements_pet_id ON memory_measurements(pet_id);
CREATE INDEX idx_memory_measurements_type ON memory_measurements(measurement_type);

CREATE INDEX idx_memory_collections_user_id ON memory_collections(user_id);
CREATE INDEX idx_memory_collection_items_collection_id ON memory_collection_items(collection_id);
CREATE INDEX idx_memory_collection_items_memory_id ON memory_collection_items(memory_id);

CREATE INDEX idx_memory_favorites_user_id ON memory_favorites(user_id);
CREATE INDEX idx_memory_favorites_memory_id ON memory_favorites(memory_id);

CREATE INDEX idx_memory_comments_memory_id ON memory_comments(memory_id);
CREATE INDEX idx_memory_comments_user_id ON memory_comments(user_id);

CREATE INDEX idx_memory_shares_memory_id ON memory_shares(memory_id);
CREATE INDEX idx_memory_shares_shared_by ON memory_shares(shared_by);
CREATE INDEX idx_memory_shares_shared_with ON memory_shares(shared_with);

CREATE INDEX idx_memorial_garden_user_id ON memorial_garden(user_id);
CREATE INDEX idx_memorial_garden_pet_id ON memorial_garden(pet_id);

-- Triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_enhanced_memories_updated_at BEFORE UPDATE ON enhanced_memories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_memory_collections_updated_at BEFORE UPDATE ON memory_collections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_memory_comments_updated_at BEFORE UPDATE ON memory_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_memorial_garden_updated_at BEFORE UPDATE ON memorial_garden
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE enhanced_memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_media_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_pet_associations ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_measurements ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_collection_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE memorial_garden ENABLE ROW LEVEL SECURITY;

-- RLS Policies for enhanced_memories
CREATE POLICY "Users can view their own memories" ON enhanced_memories
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can view shared memories" ON enhanced_memories
    FOR SELECT USING (
        id IN (
            SELECT memory_id FROM memory_shares 
            WHERE shared_with = auth.uid() 
            AND (expires_at IS NULL OR expires_at > NOW())
        )
    );

CREATE POLICY "Users can insert their own memories" ON enhanced_memories
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their own memories" ON enhanced_memories
    FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Users can delete their own memories" ON enhanced_memories
    FOR DELETE USING (created_by = auth.uid());

-- RLS Policies for memory_media_items
CREATE POLICY "Users can view media for their memories" ON memory_media_items
    FOR SELECT USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "Users can insert media for their memories" ON memory_media_items
    FOR INSERT WITH CHECK (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "Users can update media for their memories" ON memory_media_items
    FOR UPDATE USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "Users can delete media for their memories" ON memory_media_items
    FOR DELETE USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

-- RLS Policies for memory_pet_associations
CREATE POLICY "Users can view pet associations for their memories" ON memory_pet_associations
    FOR SELECT USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "Users can manage pet associations for their memories" ON memory_pet_associations
    FOR ALL USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

-- RLS Policies for memory_locations
CREATE POLICY "Users can view locations for their memories" ON memory_locations
    FOR SELECT USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "Users can manage locations for their memories" ON memory_locations
    FOR ALL USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

-- RLS Policies for memory_measurements
CREATE POLICY "Users can view measurements for their memories" ON memory_measurements
    FOR SELECT USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "Users can manage measurements for their memories" ON memory_measurements
    FOR ALL USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

-- RLS Policies for memory_collections
CREATE POLICY "Users can view their own collections" ON memory_collections
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own collections" ON memory_collections
    FOR ALL USING (user_id = auth.uid());

-- RLS Policies for memory_collection_items
CREATE POLICY "Users can view items in their collections" ON memory_collection_items
    FOR SELECT USING (
        collection_id IN (
            SELECT id FROM memory_collections 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage items in their collections" ON memory_collection_items
    FOR ALL USING (
        collection_id IN (
            SELECT id FROM memory_collections 
            WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for memory_favorites
CREATE POLICY "Users can view their own favorites" ON memory_favorites
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own favorites" ON memory_favorites
    FOR ALL USING (user_id = auth.uid());

-- RLS Policies for memory_comments
CREATE POLICY "Users can view comments on accessible memories" ON memory_comments
    FOR SELECT USING (
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        ) OR 
        memory_id IN (
            SELECT memory_id FROM memory_shares 
            WHERE shared_with = auth.uid() 
            AND (expires_at IS NULL OR expires_at > NOW())
        )
    );

CREATE POLICY "Users can insert comments on accessible memories" ON memory_comments
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND (
            memory_id IN (
                SELECT id FROM enhanced_memories 
                WHERE created_by = auth.uid()
            ) OR 
            memory_id IN (
                SELECT memory_id FROM memory_shares 
                WHERE shared_with = auth.uid() 
                AND (expires_at IS NULL OR expires_at > NOW())
            )
        )
    );

CREATE POLICY "Users can update their own comments" ON memory_comments
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own comments" ON memory_comments
    FOR DELETE USING (user_id = auth.uid());

-- RLS Policies for memory_shares
CREATE POLICY "Users can view shares for their memories" ON memory_shares
    FOR SELECT USING (
        shared_by = auth.uid() OR shared_with = auth.uid()
    );

CREATE POLICY "Users can create shares for their memories" ON memory_shares
    FOR INSERT WITH CHECK (
        shared_by = auth.uid() AND
        memory_id IN (
            SELECT id FROM enhanced_memories 
            WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "Users can update their own shares" ON memory_shares
    FOR UPDATE USING (shared_by = auth.uid());

CREATE POLICY "Users can delete their own shares" ON memory_shares
    FOR DELETE USING (shared_by = auth.uid());

-- RLS Policies for memorial_garden
CREATE POLICY "Users can view their own memorial garden" ON memorial_garden
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can view public memorials" ON memorial_garden
    FOR SELECT USING (is_public = TRUE);

CREATE POLICY "Users can manage their own memorial garden" ON memorial_garden
    FOR ALL USING (user_id = auth.uid());

-- Create default system collections
INSERT INTO memory_collections (user_id, name, description, icon, color, is_system_collection)
SELECT 
    auth.uid(),
    'Favorites',
    'Your favorite memories',
    'heart.fill',
    'red',
    TRUE
WHERE auth.uid() IS NOT NULL;

INSERT INTO memory_collections (user_id, name, description, icon, color, is_system_collection)
SELECT 
    auth.uid(),
    'Secure Vault',
    'Protected memories',
    'lock.shield',
    'orange',
    TRUE
WHERE auth.uid() IS NOT NULL;

INSERT INTO memory_collections (user_id, name, description, icon, color, is_system_collection)
SELECT 
    auth.uid(),
    'Memorial Garden',
    'Memorial memories',
    'heart.circle',
    'purple',
    TRUE
WHERE auth.uid() IS NOT NULL;

-- Views for easier querying
CREATE VIEW memory_with_media AS
SELECT 
    m.*,
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', mi.id,
                'url', mi.url,
                'thumbnail_url', mi.thumbnail_url,
                'media_type', mi.media_type,
                'duration_seconds', mi.duration_seconds,
                'file_size_bytes', mi.file_size_bytes,
                'width', mi.width,
                'height', mi.height,
                'display_order', mi.display_order,
                'metadata', mi.metadata
            ) ORDER BY mi.display_order
        ) FILTER (WHERE mi.id IS NOT NULL),
        '[]'
    ) AS media_items
FROM enhanced_memories m
LEFT JOIN memory_media_items mi ON m.id = mi.memory_id
GROUP BY m.id;

CREATE VIEW memory_with_pets AS
SELECT 
    m.*,
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', p.id,
                'name', p.name,
                'species', p.species,
                'breed', p.breed,
                'profile_image_url', p.profile_image_url
            )
        ) FILTER (WHERE p.id IS NOT NULL),
        '[]'
    ) AS associated_pets
FROM enhanced_memories m
LEFT JOIN memory_pet_associations mpa ON m.id = mpa.memory_id
LEFT JOIN pets p ON mpa.pet_id = p.id
GROUP BY m.id;

CREATE VIEW memory_full_details AS
SELECT 
    m.*,
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', mi.id,
                'url', mi.url,
                'thumbnail_url', mi.thumbnail_url,
                'media_type', mi.media_type,
                'duration_seconds', mi.duration_seconds,
                'file_size_bytes', mi.file_size_bytes,
                'width', mi.width,
                'height', mi.height,
                'display_order', mi.display_order,
                'metadata', mi.metadata
            ) ORDER BY mi.display_order
        ) FILTER (WHERE mi.id IS NOT NULL),
        '[]'
    ) AS media_items,
    COALESCE(
        JSON_AGG(
            DISTINCT JSON_BUILD_OBJECT(
                'id', p.id,
                'name', p.name,
                'species', p.species,
                'breed', p.breed,
                'profile_image_url', p.profile_image_url
            )
        ) FILTER (WHERE p.id IS NOT NULL),
        '[]'
    ) AS associated_pets,
    ml.name AS location_name,
    ml.address AS location_address,
    ml.latitude AS location_latitude,
    ml.longitude AS location_longitude,
    ml.place_type AS location_place_type,
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', mm.id,
                'pet_id', mm.pet_id,
                'measurement_type', mm.measurement_type,
                'value', mm.value,
                'unit', mm.unit,
                'measurement_date', mm.measurement_date,
                'notes', mm.notes
            )
        ) FILTER (WHERE mm.id IS NOT NULL),
        '[]'
    ) AS measurements
FROM enhanced_memories m
LEFT JOIN memory_media_items mi ON m.id = mi.memory_id
LEFT JOIN memory_pet_associations mpa ON m.id = mpa.memory_id
LEFT JOIN pets p ON mpa.pet_id = p.id
LEFT JOIN memory_locations ml ON m.id = ml.memory_id
LEFT JOIN memory_measurements mm ON m.id = mm.memory_id
GROUP BY m.id, ml.name, ml.address, ml.latitude, ml.longitude, ml.place_type;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated; 