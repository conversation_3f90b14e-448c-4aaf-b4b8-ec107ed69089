-- PetCapsule Missing Tables Migration
-- Fixes for missing database elements causing app errors
-- 
-- INSTRUCTIONS:
-- 1. Copy this entire SQL script
-- 2. Go to your database dashboard  
-- 3. Navigate to SQL Editor
-- 4. Paste and run this script
-- 5. This will create the missing database elements
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- =============================================
-- AI INSIGHTS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS ai_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    pet_id UUID NOT NULL REFERENCES pets(id) ON DELETE CASCADE,
    -- Insight content
    insight_type TEXT NOT NULL, -- 'health', 'nutrition', 'exercise', 'training', 'general'
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    recommendations TEXT[],
    -- Confidence and priority
    confidence_score DOUBLE PRECISION DEFAULT 0.5 CHECK (confidence_score >= 0 AND confidence_score <= 1),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    -- Data used for insight
    data_sources JSONB DEFAULT '[]'::jsonb, -- Array of data sources used
    analysis_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- User interaction
    is_read BOOLEAN DEFAULT false,
    is_dismissed BOOLEAN DEFAULT false,
    user_feedback TEXT, -- 'helpful', 'not_helpful', 'irrelevant'
    -- Scheduling and expiry
    scheduled_for TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Create indexes for ai_insights
CREATE INDEX IF NOT EXISTS ai_insights_user_id_idx ON ai_insights (user_id);
CREATE INDEX IF NOT EXISTS ai_insights_pet_id_idx ON ai_insights (pet_id);
CREATE INDEX IF NOT EXISTS ai_insights_type_idx ON ai_insights (insight_type);
CREATE INDEX IF NOT EXISTS ai_insights_priority_idx ON ai_insights (priority);
CREATE INDEX IF NOT EXISTS ai_insights_unread_idx ON ai_insights (user_id, is_read) WHERE is_read = false;
CREATE INDEX IF NOT EXISTS ai_insights_analysis_date_idx ON ai_insights (analysis_date DESC);
-- =============================================
-- SUBSCRIPTIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    -- Subscription details
    plan_name TEXT NOT NULL, -- 'free', 'premium', 'professional'
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'expired', 'past_due')),
    -- Pricing
    amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00, -- Monthly amount in USD
    currency TEXT NOT NULL DEFAULT 'USD',
    billing_cycle TEXT NOT NULL DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
    -- Dates
    starts_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    ends_at TIMESTAMP WITH TIME ZONE,
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    -- Payment information
    payment_method TEXT, -- 'stripe', 'apple_pay', 'google_pay'
    external_subscription_id TEXT, -- Stripe subscription ID or similar
    -- Features and limits
    max_pets INTEGER DEFAULT 1,
    max_storage_gb INTEGER DEFAULT 1,
    features JSONB DEFAULT '[]'::jsonb, -- Array of enabled features
    -- Trial information
    trial_start TIMESTAMP WITH TIME ZONE,
    trial_end TIMESTAMP WITH TIME ZONE,
    is_trial BOOLEAN DEFAULT false,
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure one active subscription per user
    UNIQUE(user_id) DEFERRABLE INITIALLY DEFERRED
);
-- Create indexes for subscriptions
CREATE INDEX IF NOT EXISTS subscriptions_user_id_idx ON subscriptions (user_id);
CREATE INDEX IF NOT EXISTS subscriptions_status_idx ON subscriptions (status);
CREATE INDEX IF NOT EXISTS subscriptions_plan_idx ON subscriptions (plan_name);
CREATE INDEX IF NOT EXISTS subscriptions_ends_at_idx ON subscriptions (ends_at);
CREATE INDEX IF NOT EXISTS subscriptions_external_id_idx ON subscriptions (external_subscription_id);
-- =============================================
-- ADD DISPLAY_NAME TO USER PROFILES
-- =============================================
-- First, let's check if profiles table exists and add display_name if missing
DO $$
BEGIN
    -- Add display_name column to profiles table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'display_name'
    ) THEN
        ALTER TABLE profiles ADD COLUMN display_name TEXT;
        RAISE NOTICE 'Added display_name column to profiles table';
    END IF;
    -- If profiles table doesn't exist, create it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'profiles'
    ) THEN
        CREATE TABLE profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            -- Profile information
            display_name TEXT,
            full_name TEXT,
            avatar_url TEXT,
            bio TEXT,
            -- Contact information
            phone_number TEXT,
            location TEXT,
            -- Preferences
            timezone TEXT DEFAULT 'UTC',
            language TEXT DEFAULT 'en',
            notification_preferences JSONB DEFAULT '{
                "email": true,
                "push": true,
                "marketing": false
            }'::jsonb,
            -- Privacy settings
            profile_visibility TEXT DEFAULT 'private' CHECK (profile_visibility IN ('public', 'friends', 'private')),
            location_sharing BOOLEAN DEFAULT false,
            -- Metadata
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            -- Ensure one profile per user
            UNIQUE(user_id)
        );
        -- Create indexes for profiles
        CREATE INDEX IF NOT EXISTS profiles_user_id_idx ON profiles (user_id);
        CREATE INDEX IF NOT EXISTS profiles_display_name_idx ON profiles (display_name);
        CREATE INDEX IF NOT EXISTS profiles_visibility_idx ON profiles (profile_visibility);
        RAISE NOTICE 'Created profiles table with display_name column';
    END IF;
END $$;
-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================
-- AI Insights RLS
ALTER TABLE ai_insights ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own AI insights" ON ai_insights
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can insert AI insights" ON ai_insights
    FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update their AI insights" ON ai_insights
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their AI insights" ON ai_insights
    FOR DELETE USING (auth.uid() = user_id);
-- Subscriptions RLS
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own subscription" ON subscriptions
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can manage subscriptions" ON subscriptions
    FOR ALL WITH CHECK (true);
-- Profiles RLS (if table was created)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'profiles'
    ) THEN
        ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
        DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
        DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
        DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
        CREATE POLICY "Users can view their own profile" ON profiles
            FOR SELECT USING (auth.uid() = user_id);
        CREATE POLICY "Users can update their own profile" ON profiles
            FOR UPDATE USING (auth.uid() = user_id);
        CREATE POLICY "Users can insert their own profile" ON profiles
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;
END $$;
-- =============================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================
-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
-- Create triggers for updated_at columns
CREATE TRIGGER update_ai_insights_updated_at BEFORE UPDATE ON ai_insights
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Create trigger for profiles if table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'profiles'
    ) THEN
        DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
        CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;
-- =============================================
-- ADD COMPREHENSIVE FIELDS TO PETS TABLE
-- =============================================
-- Add missing comprehensive fields to pets table
DO $$
BEGIN
    -- Add missing fields one by one with error handling
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS is_spayed_neutered BOOLEAN;
        RAISE NOTICE 'Added is_spayed_neutered to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'is_spayed_neutered already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS microchip_id TEXT;
        RAISE NOTICE 'Added microchip_id to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'microchip_id already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS current_food TEXT;
        RAISE NOTICE 'Added current_food to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'current_food already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS feeding_schedule TEXT;
        RAISE NOTICE 'Added feeding_schedule to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'feeding_schedule already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS water_intake_ml INTEGER;
        RAISE NOTICE 'Added water_intake_ml to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'water_intake_ml already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS dietary_restrictions TEXT[];
        RAISE NOTICE 'Added dietary_restrictions to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'dietary_restrictions already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS exercise_minutes_daily INTEGER;
        RAISE NOTICE 'Added exercise_minutes_daily to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'exercise_minutes_daily already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS walking_frequency TEXT;
        RAISE NOTICE 'Added walking_frequency to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'walking_frequency already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS favorite_activities TEXT[];
        RAISE NOTICE 'Added favorite_activities to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'favorite_activities already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS training_level TEXT;
        RAISE NOTICE 'Added training_level to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'training_level already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS known_commands TEXT[];
        RAISE NOTICE 'Added known_commands to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'known_commands already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS behavior_issues TEXT[];
        RAISE NOTICE 'Added behavior_issues to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'behavior_issues already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS social_behavior TEXT;
        RAISE NOTICE 'Added social_behavior to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'social_behavior already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS veterinarian_info TEXT;
        RAISE NOTICE 'Added veterinarian_info to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'veterinarian_info already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS insurance_info TEXT;
        RAISE NOTICE 'Added insurance_info to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'insurance_info already exists or error occurred';
    END;
    BEGIN
        ALTER TABLE pets ADD COLUMN IF NOT EXISTS emergency_contacts TEXT;
        RAISE NOTICE 'Added emergency_contacts to pets table';
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'emergency_contacts already exists or error occurred';
    END;
END $$;
-- =============================================
-- SUCCESS MESSAGE
-- =============================================
SELECT 'Missing database tables and columns have been successfully created! 
✅ ai_insights table created
✅ subscriptions table created with amount column  
✅ display_name field added to profiles
✅ Comprehensive pet fields added
✅ RLS policies configured
✅ Indexes optimized
Your app should now work without database-related errors.' as status; 