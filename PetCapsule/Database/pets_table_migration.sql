-- PetCapsule Pets Table Migration
-- CRITICAL FIX for "Could not find the 'age' column of 'pets' in the schema cache"
-- 
-- INSTRUCTIONS:
-- 1. Copy this entire SQL script
-- 2. Go to your database dashboard
-- 3. Navigate to SQL Editor  
-- 4. Paste and run this script
-- 5. This will create the missing pets table structure
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- =============================================
-- PETS TABLE (Core pet data) - CRITICAL FIX
-- =============================================
CREATE TABLE IF NOT EXISTS pets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    -- Basic pet information
    name TEXT NOT NULL,
    species TEXT NOT NULL, -- 'dog', 'cat', 'bird', 'rabbit', etc.
    breed TEXT,
    birth_date DATE,
    adoption_date DATE,
    -- Physical characteristics  
    weight DOUBLE PRECISION, -- in pounds
    gender TEXT, -- 'male', 'female', 'unknown'
    -- Care information
    activity_level TEXT DEFAULT 'moderate', -- 'low', 'moderate', 'high', 'very_high'
    personality_traits TEXT[], -- Array of trait strings
    health_conditions TEXT[], -- Array of condition strings
    medications TEXT[], -- Array of medication names
    vaccinations TEXT[], -- Array of vaccination names
    health_alerts TEXT[], -- Array of alert titles
    ai_recommendations TEXT[], -- Array of AI-generated recommendations
    -- Media and profile
    profile_image_url TEXT,
    -- Health tracking
    last_checkup_date DATE,
    -- Status
    is_active BOOLEAN DEFAULT true,
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Create indexes for performance
CREATE INDEX IF NOT EXISTS pets_user_id_idx ON pets (user_id);
CREATE INDEX IF NOT EXISTS pets_species_idx ON pets (species);  
CREATE INDEX IF NOT EXISTS pets_active_idx ON pets (is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS pets_created_at_idx ON pets (created_at DESC);
-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
-- Create trigger for pets
CREATE TRIGGER update_pets_updated_at BEFORE UPDATE ON pets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Enable Row Level Security
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;
-- RLS Policies for pets table
CREATE POLICY "Users can view their own pets" ON pets
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own pets" ON pets  
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own pets" ON pets
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own pets" ON pets
    FOR DELETE USING (auth.uid() = user_id);
-- Success message
SELECT 'Pets table created successfully! Your app should now work correctly.' as status; 