//
//  MemoryTestView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

@available(iOS 18.0, *)
struct MemoryTestView: View {
    @StateObject private var storageService = AppleNativeStorageService.shared
    @StateObject private var dataService = AppleNativeDataService.shared
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Memory Testing")
                    .font(.title)
                    .padding()
                
                Text("Using Apple Native Storage")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("Memory Test")
        }
    }
}
