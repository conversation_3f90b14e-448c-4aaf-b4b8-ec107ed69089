//
//  DesignSystem.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Colors
extension Color {
    static let petPrimary = Color(red: 0.95, green: 0.85, blue: 0.95)
    static let petSecondary = Color(red: 0.85, green: 0.95, blue: 0.95)
    static let petAccent = Color.pink
    static let petAccentSecondary = Color.purple

    static let petBackground = Color(UIColor.systemBackground)
    static let petSecondaryBackground = Color(UIColor.secondarySystemBackground)
    static let petTertiaryBackground = Color(UIColor.tertiarySystemBackground)

    static let petText = Color.primary
    static let petSecondaryText = Color.secondary
    static let petTertiaryText = Color(UIColor.tertiaryLabel)

    static let petSuccess = Color.green
    static let petWarning = Color.orange
    static let petError = Color.red

    // Basic colors using system colors to avoid asset catalog warnings
    static let systemBlue = Color(UIColor.systemBlue)
    static let systemGray = Color(UIColor.systemGray)
    static let systemOrange = Color(UIColor.systemOrange)

    // Gem colors
    static let gemGold = Color(red: 1.0, green: 0.84, blue: 0.0)
    static let gemSilver = Color(red: 0.75, green: 0.75, blue: 0.75)
    static let gemBronze = Color(red: 0.8, green: 0.5, blue: 0.2)
    
    // MARK: - Glassmorphism Colors
    static let glassLight = Color.white.opacity(0.25)
    static let glassDark = Color.black.opacity(0.15)
    static let glassBorder = Color.white.opacity(0.18)
    static let glassBorderDark = Color.white.opacity(0.08)
    static let glassShadow = Color.black.opacity(0.1)
    static let glassShadowDark = Color.black.opacity(0.3)
    
    // Gradient Glass Colors
    static let glassAccentPrimary = Color(red: 0.5, green: 0.4, blue: 1.0) // Purple
    static let glassAccentSecondary = Color(red: 0.2, green: 0.8, blue: 1.0) // Cyan
    static let glassAccentTertiary = Color(red: 1.0, green: 0.4, blue: 0.8) // Pink
    static let glassAccentQuaternary = Color(red: 1.0, green: 0.6, blue: 0.2) // Orange
    
    // Glass Background Variants
    static let glassBackground = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark
            ? UIColor(red: 0.1, green: 0.1, blue: 0.12, alpha: 0.7)
            : UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 0.7)
    })
    
    static let glassSecondaryBackground = Color(UIColor { traitCollection in
        return traitCollection.userInterfaceStyle == .dark
            ? UIColor(red: 0.15, green: 0.15, blue: 0.17, alpha: 0.8)
            : UIColor(red: 0.98, green: 0.98, blue: 1.0, alpha: 0.8)
    })
}

// MARK: - Gradients
extension LinearGradient {
    static let petPrimaryGradient = LinearGradient(
        colors: [Color.petPrimary, Color.petSecondary],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    static let petAccentGradient = LinearGradient(
        colors: [Color.petAccent, Color.petAccentSecondary],
        startPoint: .leading,
        endPoint: .trailing
    )

    static let gemGradient = LinearGradient(
        colors: [Color.gemGold, Color.gemBronze],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    // MARK: - Glassmorphism Gradients
    static let glassGradient = LinearGradient(
        colors: [Color.glassLight, Color.glassLight.opacity(0.1)],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let glassDarkGradient = LinearGradient(
        colors: [Color.glassDark, Color.glassDark.opacity(0.05)],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let glassAccentGradient = LinearGradient(
        colors: [
            Color.glassAccentPrimary.opacity(0.8),
            Color.glassAccentSecondary.opacity(0.6),
            Color.glassAccentTertiary.opacity(0.4)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let glassCardGradient = LinearGradient(
        colors: [
            Color.white.opacity(0.2),
            Color.white.opacity(0.1),
            Color.white.opacity(0.05)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let glassBorderGradient = LinearGradient(
        colors: [
            Color.white.opacity(0.5),
            Color.white.opacity(0.2),
            Color.clear
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    // Animated Background Gradients
    static let glassAnimatedBackground = LinearGradient(
        colors: [
            Color.glassAccentPrimary.opacity(0.1),
            Color.glassAccentSecondary.opacity(0.1),
            Color.glassAccentTertiary.opacity(0.1),
            Color.glassAccentQuaternary.opacity(0.1)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
}

// MARK: - RadialGradients for Glass Effects
extension RadialGradient {
    static let glassSpotlight = RadialGradient(
        colors: [
            Color.white.opacity(0.3),
            Color.white.opacity(0.1),
            Color.clear
        ],
        center: .topLeading,
        startRadius: 0,
        endRadius: 300
    )
    
    static let glassGlow = RadialGradient(
        colors: [
            Color.glassAccentPrimary.opacity(0.6),
            Color.glassAccentSecondary.opacity(0.3),
            Color.clear
        ],
        center: .center,
        startRadius: 0,
        endRadius: 150
    )
}

// MARK: - Typography
extension Font {
    static let petLargeTitle = Font.largeTitle.weight(.bold)
    static let petTitle = Font.title.weight(.semibold)
    static let petTitle2 = Font.title2.weight(.semibold)
    static let petTitle3 = Font.title3.weight(.medium)
    static let petHeadline = Font.headline.weight(.medium)
    static let petSubheadline = Font.subheadline.weight(.regular)
    static let petBody = Font.body.weight(.regular)
    static let petCallout = Font.callout.weight(.regular)
    static let petFootnote = Font.footnote.weight(.regular)
    static let petCaption = Font.caption.weight(.regular)
    static let petCaption2 = Font.caption2.weight(.regular)
    
    // Glassmorphism Typography
    static let glassTitle = Font.system(.title, design: .rounded).weight(.bold)
    static let glassHeadline = Font.system(.headline, design: .rounded).weight(.semibold)
    static let glassBody = Font.system(.body, design: .rounded).weight(.medium)
    static let glassCaption = Font.system(.caption, design: .rounded).weight(.regular)
}

// MARK: - Spacing
struct Spacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
    
    // Glass-specific spacing
    static let glassInset: CGFloat = 12
    static let glassBlur: CGFloat = 20
    static let glassBorder: CGFloat = 1
    static let glassShadow: CGFloat = 8
}

// MARK: - Corner Radius
struct CornerRadius {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 24
    
    // Glass-specific corner radius
    static let glass: CGFloat = 20
    static let glassCard: CGFloat = 24
    static let glassButton: CGFloat = 16
}

// MARK: - Glassmorphism View Modifiers
struct GlassCardStyle: ViewModifier {
    @Environment(\.colorScheme) var colorScheme
    let intensity: Double
    
    init(intensity: Double = 1.0) {
        self.intensity = intensity
    }
    
    func body(content: Content) -> some View {
        content
            .background {
                // Main glass background
                RoundedRectangle(cornerRadius: CornerRadius.glassCard)
                    .fill(
                        colorScheme == .dark
                        ? Color.glassDark.opacity(0.6 * intensity)
                        : Color.glassLight.opacity(0.7 * intensity)
                    )
                    .background(
                        // Gradient overlay
                        RoundedRectangle(cornerRadius: CornerRadius.glassCard)
                            .fill(LinearGradient.glassCardGradient.opacity(intensity))
                    )
                    .overlay(
                        // Border gradient
                        RoundedRectangle(cornerRadius: CornerRadius.glassCard)
                            .stroke(
                                LinearGradient.glassBorderGradient.opacity(0.5 * intensity),
                                lineWidth: Spacing.glassBorder
                            )
                    )
                    .shadow(
                        color: colorScheme == .dark
                        ? Color.glassShadowDark.opacity(0.8 * intensity)
                        : Color.glassShadow.opacity(0.6 * intensity),
                        radius: Spacing.glassShadow,
                        x: 0,
                        y: 4
                    )
            }
            .overlay(
                // Inner highlight
                RoundedRectangle(cornerRadius: CornerRadius.glassCard)
                    .fill(
                        RadialGradient.glassSpotlight.opacity(0.4 * intensity)
                    )
                    .allowsHitTesting(false)
            )
    }
}

struct GlassButtonModifier: ViewModifier {
    @Environment(\.colorScheme) var colorScheme
    let style: GlassButtonType
    @State private var isPressed = false
    
    func body(content: Content) -> some View {
        content
            .padding(.horizontal, Spacing.lg)
            .padding(.vertical, Spacing.md)
            .background {
                RoundedRectangle(cornerRadius: CornerRadius.glassButton)
                    .fill(style.backgroundGradient(colorScheme: colorScheme))
                    .overlay(
                        RoundedRectangle(cornerRadius: CornerRadius.glassButton)
                            .stroke(style.borderColor(colorScheme: colorScheme), lineWidth: 1)
                    )
                    .shadow(
                        color: style.shadowColor(colorScheme: colorScheme),
                        radius: isPressed ? 2 : 6,
                        x: 0,
                        y: isPressed ? 1 : 3
                    )
            }
            .foregroundStyle(style.foregroundColor(colorScheme: colorScheme))
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
            }, perform: {})
    }
}

struct GlassBackgroundStyle: ViewModifier {
    @Environment(\.colorScheme) var colorScheme
    let animated: Bool
    @State private var animationOffset: CGFloat = 0
    
    init(animated: Bool = true) {
        self.animated = animated
    }
    
    func body(content: Content) -> some View {
        content
            .background {
                // Base animated gradient
                LinearGradient.glassAnimatedBackground
                    .ignoresSafeArea()
                    .offset(x: animated ? animationOffset : 0, y: animated ? animationOffset * 0.5 : 0)
                    .onAppear {
                        if animated {
                            withAnimation(.linear(duration: 20).repeatForever(autoreverses: true)) {
                                animationOffset = 100
                            }
                        }
                    }
                
                // Overlay pattern
                VStack(spacing: 0) {
                    ForEach(0..<10, id: \.self) { _ in
                        HStack(spacing: 0) {
                            ForEach(0..<10, id: \.self) { _ in
                                Circle()
                                    .fill(
                                        RadialGradient(
                                            colors: [
                                                Color.white.opacity(0.05),
                                Color.clear
                                            ],
                                            center: .center,
                                            startRadius: 0,
                                            endRadius: 30
                                        )
                                    )
                                    .frame(width: 60, height: 60)
                            }
                        }
                    }
                }
                .ignoresSafeArea()
                .opacity(0.3)
            }
    }
}

// MARK: - Glass Button Types
enum GlassButtonType {
    case primary
    case secondary
    case accent
    case destructive
    case ghost
    
    func backgroundGradient(colorScheme: ColorScheme) -> LinearGradient {
        switch self {
        case .primary:
            return LinearGradient.glassAccentGradient
        case .secondary:
            return colorScheme == .dark
                ? LinearGradient.glassDarkGradient
                : LinearGradient.glassGradient
        case .accent:
            return LinearGradient(
                colors: [
                    Color.glassAccentTertiary.opacity(0.8),
                    Color.glassAccentQuaternary.opacity(0.6)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .destructive:
            return LinearGradient(
                colors: [
                    Color.red.opacity(0.8),
                    Color.red.opacity(0.6)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .ghost:
            return LinearGradient(
                colors: [Color.clear, Color.clear],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    func borderColor(colorScheme: ColorScheme) -> Color {
        switch self {
        case .primary, .accent:
            return Color.white.opacity(0.4)
        case .secondary:
            return colorScheme == .dark ? Color.glassBorderDark : Color.glassBorder
        case .destructive:
            return Color.red.opacity(0.5)
        case .ghost:
            return Color.clear
        }
    }
    
    func foregroundColor(colorScheme: ColorScheme) -> Color {
        switch self {
        case .primary, .accent, .destructive:
            return .white
        case .secondary:
            return Color.petText
        case .ghost:
            return Color.petAccent
        }
    }
    
    func shadowColor(colorScheme: ColorScheme) -> Color {
        switch self {
        case .primary:
            return Color.glassAccentPrimary.opacity(0.3)
        case .accent:
            return Color.glassAccentTertiary.opacity(0.3)
        case .destructive:
            return Color.red.opacity(0.3)
        default:
            return colorScheme == .dark ? Color.glassShadowDark : Color.glassShadow
        }
    }
}

struct GlassTextFieldModifier: ViewModifier {
    @Environment(\.colorScheme) var colorScheme
    @FocusState private var isFocused: Bool
    
    func body(content: Content) -> some View {
        content
            .padding(Spacing.md)
            .background {
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .fill(
                        colorScheme == .dark
                        ? Color.glassDark.opacity(0.6)
                        : Color.glassLight.opacity(0.7)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: CornerRadius.md)
                            .stroke(
                                isFocused
                                ? LinearGradient.glassAccentGradient.opacity(0.8)
                                : LinearGradient.glassBorderGradient.opacity(0.3),
                                lineWidth: isFocused ? 2 : 1
                            )
                    )
                    .shadow(
                        color: isFocused
                        ? Color.glassAccentPrimary.opacity(0.2)
                        : (colorScheme == .dark ? Color.glassShadowDark : Color.glassShadow),
                        radius: isFocused ? 8 : 4,
                        x: 0,
                        y: 2
                    )
            }
            .focused($isFocused)
            .animation(.easeInOut(duration: 0.2), value: isFocused)
    }
}

// MARK: - Legacy View Modifiers (for backward compatibility)
struct PetCardStyle: ViewModifier {
    func body(content: Content) -> some View {
        content
            .glassCard()
    }
}

struct PetButtonStyle: ViewModifier {
    let style: ButtonStyleType

    func body(content: Content) -> some View {
        content
            .glassButton(style.toGlassButtonType())
    }
}

struct PetTextFieldStyle: ViewModifier {
    func body(content: Content) -> some View {
        content
            .glassTextField()
    }
}

// MARK: - ButtonStyleType Extension (removed duplicate - now in ThemeManager.swift)

// MARK: - View Extensions
extension View {
    // Legacy support
    func petCardStyle() -> some View {
        modifier(PetCardStyle())
    }

    func petButtonStyle(_ style: ButtonStyleType = .primary) -> some View {
        modifier(PetButtonStyle(style: style))
    }

    func petTextFieldStyle() -> some View {
        modifier(PetTextFieldStyle())
    }
    
    // New glassmorphism styles
    func glassCard(intensity: Double = 1.0) -> some View {
        modifier(GlassCardStyle(intensity: intensity))
    }
    
    func glassButton(_ style: GlassButtonType = .primary) -> some View {
        modifier(GlassButtonModifier(style: style))
    }
    
    func glassTextField() -> some View {
        modifier(GlassTextFieldModifier())
    }
    
    func glassBackground(animated: Bool = true) -> some View {
        modifier(GlassBackgroundStyle(animated: animated))
    }
}

// MARK: - Custom Components
struct PetGemView: View {
    let count: Int
    let size: CGFloat

    init(count: Int, size: CGFloat = 20) {
        self.count = count
        self.size = size
    }

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "diamond.fill")
                .font(.system(size: size))
                .foregroundStyle(LinearGradient.gemGradient)

            Text("\(count)")
                .font(.system(size: size * 0.8, weight: .semibold))
                .foregroundColor(.petText)
        }
        .glassCard(intensity: 0.7)
        .padding(.horizontal, Spacing.sm)
        .padding(.vertical, Spacing.xs)
    }
}

struct PetLoadingView: View {
    let message: String

    init(_ message: String = "Loading...") {
        self.message = message
    }

    var body: some View {
        VStack(spacing: Spacing.md) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .petAccent))
                .scaleEffect(1.2)

            Text(message)
                .font(.petCallout)
                .foregroundColor(.petSecondaryText)
        }
        .padding(Spacing.xl)
        .glassCard()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .glassBackground()
    }
}

struct PetEmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    let actionTitle: String?
    let action: (() -> Void)?

    init(
        icon: String,
        title: String,
        subtitle: String,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.actionTitle = actionTitle
        self.action = action
    }

    var body: some View {
        VStack(spacing: Spacing.lg) {
            Image(systemName: icon)
                .font(.system(size: 60))
                .foregroundStyle(LinearGradient.glassAccentGradient)

            VStack(spacing: Spacing.sm) {
                Text(title)
                    .font(.glassTitle)
                    .foregroundColor(.petText)

                Text(subtitle)
                    .font(.glassBody)
                    .foregroundColor(.petSecondaryText)
                    .multilineTextAlignment(.center)
            }

            if let actionTitle = actionTitle, let action = action {
                Button(action: action) {
                    Text(actionTitle)
                        .font(.glassHeadline)
                }
                .glassButton(.primary)
            }
        }
        .padding(Spacing.xl)
        .glassCard()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .glassBackground()
    }
}

// MARK: - Glass Navigation Components
struct GlassNavigationBar: View {
    let title: String
    let leadingAction: (() -> Void)?
    let trailingAction: (() -> Void)?
    let leadingIcon: String?
    let trailingIcon: String?
    
    init(
        title: String,
        leadingAction: (() -> Void)? = nil,
        trailingAction: (() -> Void)? = nil,
        leadingIcon: String? = nil,
        trailingIcon: String? = nil
    ) {
        self.title = title
        self.leadingAction = leadingAction
        self.trailingAction = trailingAction
        self.leadingIcon = leadingIcon
        self.trailingIcon = trailingIcon
    }
    
    var body: some View {
        HStack {
            if let leadingIcon = leadingIcon, let leadingAction = leadingAction {
                Button(action: leadingAction) {
                    Image(systemName: leadingIcon)
                        .font(.glassHeadline)
                }
                .glassButton(.ghost)
            }
            
            Spacer()
            
            Text(title)
                .font(.glassTitle)
                .foregroundColor(.petText)
            
            Spacer()
            
            if let trailingIcon = trailingIcon, let trailingAction = trailingAction {
                Button(action: trailingAction) {
                    Image(systemName: trailingIcon)
                        .font(.glassHeadline)
                }
                .glassButton(.ghost)
            }
        }
        .padding(.horizontal, Spacing.lg)
        .padding(.vertical, Spacing.md)
        .glassCard(intensity: 0.8)
    }
}

struct GlassTabBar: View {
    @Binding var selectedTab: Int
    let tabs: [TabItem]
    
    struct TabItem {
        let icon: String
        let title: String
        let index: Int
    }
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(tabs, id: \.index) { tab in
                Button(action: {
                    selectedTab = tab.index
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.icon)
                            .font(.system(size: 20, weight: .medium))
                        
                        Text(tab.title)
                            .font(.glassCaption)
                    }
                    .foregroundStyle(
                        selectedTab == tab.index
                        ? LinearGradient.glassAccentGradient
                        : LinearGradient(colors: [Color.petSecondaryText], startPoint: .top, endPoint: .bottom)
                    )
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.sm)
                }
                .glassButton(selectedTab == tab.index ? .accent : .ghost)
            }
        }
        .padding(.horizontal, Spacing.md)
        .padding(.vertical, Spacing.sm)
        .glassCard(intensity: 0.9)
    }
}
