//
//  EuropeanDesignSystem.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - European Design System
struct EuropeanDesign {
    
    // MARK: - European Color Palette (Theme-Aware)
    struct Colors {
        // Sophisticated European Primary Colors (Theme-Aware)
        static var primary: Color {
            Color(UIColor { traitCollection in
                traitCollection.userInterfaceStyle == .dark ?
                UIColor(red: 0.85, green: 0.85, blue: 0.87, alpha: 1.0) :  // Light text for dark mode
                UIColor(red: 0.15, green: 0.25, blue: 0.35, alpha: 1.0)    // Deep Charcoal Blue for light mode
            })
        }

        static var primaryLight: Color {
            Color(UIColor { traitCollection in
                traitCollection.userInterfaceStyle == .dark ?
                UIColor(red: 0.75, green: 0.75, blue: 0.77, alpha: 1.0) :
                UIColor(red: 0.25, green: 0.35, blue: 0.45, alpha: 1.0)
            })
        }

        static var primaryDark: Color {
            Color(UIColor { traitCollection in
                traitCollection.userInterfaceStyle == .dark ?
                UIColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1.0) :
                UIColor(red: 0.08, green: 0.15, blue: 0.22, alpha: 1.0)
            })
        }

        // Elegant Accent Colors (Consistent across themes)
        static let accent = Color(red: 0.85, green: 0.65, blue: 0.45)         // Warm Champagne Gold
        static let accentLight = Color(red: 0.92, green: 0.78, blue: 0.65)    // Light Champagne
        static let accentDark = Color(red: 0.75, green: 0.55, blue: 0.35)     // Deep Gold

        // Sophisticated Neutrals (Theme-Aware)
        static var background: Color {
            Color(UIColor.systemBackground)
        }

        static var surface: Color {
            Color(UIColor.secondarySystemBackground)
        }

        static var surfaceSecondary: Color {
            Color(UIColor.tertiarySystemBackground)
        }

        static var surfaceTertiary: Color {
            Color(UIColor { traitCollection in
                traitCollection.userInterfaceStyle == .dark ?
                UIColor.systemGray5 :
                UIColor(red: 0.88, green: 0.90, blue: 0.92, alpha: 1.0)
            })
        }

        // European Text Colors (Theme-Aware)
        static var textPrimary: Color {
            Color(UIColor.label)
        }

        static var textSecondary: Color {
            Color(UIColor.secondaryLabel)
        }

        static var textTertiary: Color {
            Color(UIColor.tertiaryLabel)
        }

        static var textQuaternary: Color {
            Color(UIColor.quaternaryLabel)
        }
        
        // Elegant Status Colors (WCAG AA Compliant)
        static let success = Color(red: 0.15, green: 0.50, blue: 0.30)        // Forest Green (Enhanced contrast)
        static let warning = Color(red: 0.80, green: 0.50, blue: 0.20)        // Amber (Enhanced contrast)
        static let error = Color(red: 0.70, green: 0.20, blue: 0.20)          // Deep Red (Enhanced contrast)
        static let info = Color(red: 0.20, green: 0.40, blue: 0.70)           // Royal Blue (Enhanced contrast)
        
        // Pet-specific European Colors
        static let petHealthy = Color(red: 0.25, green: 0.65, blue: 0.45)     // Sage Green
        static let petWarning = Color(red: 0.85, green: 0.65, blue: 0.35)     // Warm Amber
        static let petCritical = Color(red: 0.75, green: 0.35, blue: 0.35)    // Muted Red
        static let petNeutral = Color(red: 0.65, green: 0.70, blue: 0.75)     // Cool Grey
        
        // Premium European Colors
        static let premium = Color(red: 0.55, green: 0.35, blue: 0.75)        // Royal Purple
        static let premiumLight = Color(red: 0.70, green: 0.55, blue: 0.85)   // Light Purple
        static let premiumGold = Color(red: 0.85, green: 0.75, blue: 0.45)    // Elegant Gold
    }
    
    // MARK: - European Typography
    struct Typography {
        static let largeTitle = Font.system(size: 34, weight: .light, design: .serif)
        static let title1 = Font.system(size: 28, weight: .light, design: .serif)
        static let title2 = Font.system(size: 22, weight: .regular, design: .serif)
        static let title3 = Font.system(size: 20, weight: .regular, design: .serif)
        static let headline = Font.system(size: 17, weight: .medium, design: .default)
        static let body = Font.system(size: 17, weight: .regular, design: .default)
        static let bodyEmphasized = Font.system(size: 17, weight: .medium, design: .default)
        static let callout = Font.system(size: 16, weight: .regular, design: .default)
        static let subheadline = Font.system(size: 15, weight: .regular, design: .default)
        static let footnote = Font.system(size: 13, weight: .regular, design: .default)
        static let caption = Font.system(size: 12, weight: .regular, design: .default)
        static let caption2 = Font.system(size: 11, weight: .regular, design: .default)
        
        // European-specific styles
        static let elegantTitle = Font.system(size: 24, weight: .light, design: .serif)
        static let sophisticatedBody = Font.system(size: 16, weight: .regular, design: .default)
        static let refinedCaption = Font.system(size: 12, weight: .medium, design: .default)
    }
    
    // MARK: - European Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
        static let xxxl: CGFloat = 64
        
        // European-specific spacing
        static let elegant: CGFloat = 20
        static let sophisticated: CGFloat = 28
        static let luxurious: CGFloat = 36
    }
    
    // MARK: - European Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 6
        static let medium: CGFloat = 12
        static let large: CGFloat = 18
        static let extraLarge: CGFloat = 24
        static let elegant: CGFloat = 16
        static let sophisticated: CGFloat = 20
    }
    
    // MARK: - European Shadows (Theme-Aware)
    struct Shadows {
        static var subtle: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :  // No shadows in dark mode
                    UIColor.black.withAlphaComponent(0.05)
                }),
                radius: 2, x: 0, y: 1
            )
        }

        static var soft: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :
                    UIColor.black.withAlphaComponent(0.08)
                }),
                radius: 4, x: 0, y: 2
            )
        }

        static var elegant: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :
                    UIColor.black.withAlphaComponent(0.12)
                }),
                radius: 8, x: 0, y: 4
            )
        }

        static var sophisticated: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :
                    UIColor.black.withAlphaComponent(0.15)
                }),
                radius: 12, x: 0, y: 6
            )
        }

        static var luxurious: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :
                    UIColor.black.withAlphaComponent(0.18)
                }),
                radius: 16, x: 0, y: 8
            )
        }

        static var premiumGlow: EuropeanShadow {
            EuropeanShadow(
                color: Colors.premium.opacity(0.3),
                radius: 8, x: 0, y: 4
            )
        }

        static var accentGlow: EuropeanShadow {
            EuropeanShadow(
                color: Colors.accent.opacity(0.4),
                radius: 6, x: 0, y: 3
            )
        }
    }
    
    // MARK: - European Gradients
    struct Gradients {
        static let elegantBackground = LinearGradient(
            colors: [Colors.background, Colors.surface],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        static let sophisticatedCard = LinearGradient(
            colors: [Colors.surface, Colors.surfaceSecondary],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        static let accentShimmer = LinearGradient(
            colors: [Colors.accent.opacity(0.3), Colors.accentLight, Colors.accent.opacity(0.3)],
            startPoint: .leading,
            endPoint: .trailing
        )
        
        static let premiumGradient = LinearGradient(
            colors: [Colors.premium, Colors.premiumLight],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - European Animations
    struct Animations {
        static let gentle = Animation.easeInOut(duration: 0.3)
        static let elegant = Animation.easeInOut(duration: 0.4)
        static let sophisticated = Animation.easeInOut(duration: 0.5)
        static let luxurious = Animation.easeInOut(duration: 0.6)
        static let smooth = Animation.easeOut(duration: 0.25)
    }
}

// MARK: - European Shadow Helper
struct EuropeanShadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - View Extensions for European Design
extension View {
    func europeanCardStyle() -> some View {
        self
            .padding(EuropeanDesign.Spacing.elegant)
            .background(
                RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.elegant)
                    .fill(EuropeanDesign.Gradients.sophisticatedCard)
                    .shadow(
                        color: EuropeanDesign.Shadows.elegant.color,
                        radius: EuropeanDesign.Shadows.elegant.radius,
                        x: EuropeanDesign.Shadows.elegant.x,
                        y: EuropeanDesign.Shadows.elegant.y
                    )
            )
    }
    
    func europeanButtonStyle() -> some View {
        self
            .padding(.horizontal, EuropeanDesign.Spacing.elegant)
            .padding(.vertical, EuropeanDesign.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: EuropeanDesign.CornerRadius.medium)
                    .fill(EuropeanDesign.Colors.accent)
            )
            .foregroundColor(.white)
            .font(EuropeanDesign.Typography.bodyEmphasized)
    }
} 