//
//  EuropeanDesignSystem.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - European Design System
struct EuropeanDesign {
    
    // MARK: - European Color Palette (Theme-Aware)
    struct Colors {
        // Sophisticated European Primary Colors (Theme-Aware)
        static var primary: Color {
            Color(UIColor { traitCollection in
                traitCollection.userInterfaceStyle == .dark ?
                UIColor(red: 0.85, green: 0.85, blue: 0.87, alpha: 1.0) :  // Light text for dark mode
                UIColor(red: 0.15, green: 0.25, blue: 0.35, alpha: 1.0)    // Deep Charcoal Blue for light mode
            })
        }

        static var primaryLight: Color {
            Color(UIColor { traitCollection in
                traitCollection.userInterfaceStyle == .dark ?
                UIColor(red: 0.75, green: 0.75, blue: 0.77, alpha: 1.0) :
                UIColor(red: 0.25, green: 0.35, blue: 0.45, alpha: 1.0)
            })
        }

        static var primaryDark: Color {
            Color(UIColor { traitCollection in
                traitCollection.userInterfaceStyle == .dark ?
                UIColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1.0) :
                UIColor(red: 0.08, green: 0.15, blue: 0.22, alpha: 1.0)
            })
        }

        // Elegant Accent Colors (Consistent across themes)
        static let accent = Color(red: 0.85, green: 0.65, blue: 0.45)         // Warm Champagne Gold
        static let accentLight = Color(red: 0.92, green: 0.78, blue: 0.65)    // Light Champagne
        static let accentDark = Color(red: 0.75, green: 0.55, blue: 0.35)     // Deep Gold

        // Sophisticated Neutrals (Theme-Aware)
        static var background: Color {
            Color(UIColor.systemBackground)
        }

        static var surface: Color {
            Color(UIColor.secondarySystemBackground)
        }

        static var surfaceSecondary: Color {
            Color(UIColor.tertiarySystemBackground)
        }

        static var surfaceTertiary: Color {
            Color(UIColor { traitCollection in
                traitCollection.userInterfaceStyle == .dark ?
                UIColor.systemGray5 :
                UIColor(red: 0.88, green: 0.90, blue: 0.92, alpha: 1.0)
            })
        }

        // European Text Colors (Theme-Aware)
        static var textPrimary: Color {
            Color(UIColor.label)
        }

        static var textSecondary: Color {
            Color(UIColor.secondaryLabel)
        }

        static var textTertiary: Color {
            Color(UIColor.tertiaryLabel)
        }

        static var textQuaternary: Color {
            Color(UIColor.quaternaryLabel)
        }
        
        // Elegant Status Colors
        static let success = Color(red: 0.20, green: 0.55, blue: 0.35)        // Forest Green
        static let warning = Color(red: 0.85, green: 0.55, blue: 0.25)        // Amber
        static let error = Color(red: 0.75, green: 0.25, blue: 0.25)          // Deep Red
        static let info = Color(red: 0.25, green: 0.45, blue: 0.75)           // Royal Blue
        
        // Pet-specific European Colors
        static let petHealthy = Color(red: 0.25, green: 0.65, blue: 0.45)     // Sage Green
        static let petWarning = Color(red: 0.85, green: 0.65, blue: 0.35)     // Warm Amber
        static let petCritical = Color(red: 0.75, green: 0.35, blue: 0.35)    // Muted Red
        static let petNeutral = Color(red: 0.65, green: 0.70, blue: 0.75)     // Cool Grey
        
        // Premium European Colors
        static let premium = Color(red: 0.55, green: 0.35, blue: 0.75)        // Royal Purple
        static let premiumLight = Color(red: 0.70, green: 0.55, blue: 0.85)   // Light Purple
        static let premiumGold = Color(red: 0.85, green: 0.75, blue: 0.45)    // Elegant Gold
    }
    
    // MARK: - European Typography
    struct Typography {
        static let largeTitle = Font.system(size: 34, weight: .light, design: .serif)
        static let title1 = Font.system(size: 28, weight: .light, design: .serif)
        static let title2 = Font.system(size: 22, weight: .regular, design: .serif)
        static let title3 = Font.system(size: 20, weight: .regular, design: .serif)
        static let headline = Font.system(size: 17, weight: .medium, design: .default)
        static let body = Font.system(size: 17, weight: .regular, design: .default)
        static let bodyEmphasized = Font.system(size: 17, weight: .medium, design: .default)
        static let callout = Font.system(size: 16, weight: .regular, design: .default)
        static let subheadline = Font.system(size: 15, weight: .regular, design: .default)
        static let footnote = Font.system(size: 13, weight: .regular, design: .default)
        static let caption = Font.system(size: 12, weight: .regular, design: .default)
        static let caption2 = Font.system(size: 11, weight: .regular, design: .default)
        
        // European-specific styles
        static let elegantTitle = Font.system(size: 24, weight: .light, design: .serif)
        static let sophisticatedBody = Font.system(size: 16, weight: .regular, design: .default)
        static let refinedCaption = Font.system(size: 12, weight: .medium, design: .default)
    }
    
    // MARK: - European Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
        static let xxxl: CGFloat = 64
        
        // European-specific spacing
        static let elegant: CGFloat = 20
        static let sophisticated: CGFloat = 28
        static let luxurious: CGFloat = 36
    }
    
    // MARK: - European Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 6
        static let medium: CGFloat = 12
        static let large: CGFloat = 18
        static let extraLarge: CGFloat = 24
        static let elegant: CGFloat = 16
        static let sophisticated: CGFloat = 20
    }
    
    // MARK: - European Shadows (Theme-Aware)
    struct Shadows {
        static var subtle: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :  // No shadows in dark mode
                    UIColor.black.withAlphaComponent(0.05)
                }),
                radius: 2, x: 0, y: 1
            )
        }

        static var soft: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :
                    UIColor.black.withAlphaComponent(0.08)
                }),
                radius: 4, x: 0, y: 2
            )
        }

        static var elegant: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :
                    UIColor.black.withAlphaComponent(0.12)
                }),
                radius: 8, x: 0, y: 4
            )
        }

        static var sophisticated: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :
                    UIColor.black.withAlphaComponent(0.15)
                }),
                radius: 12, x: 0, y: 6
            )
        }

        static var luxurious: EuropeanShadow {
            EuropeanShadow(
                color: Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark ?
                    UIColor.clear :
                    UIColor.black.withAlphaComponent(0.18)
                }),
                radius: 16, x: 0, y: 8
            )
        }
        
        // Colored shadows for premium elements
        static let premiumGlow = EuropeanShadow(color: Colors.premium.opacity(0.25), radius: 8, x: 0, y: 4)
        static let accentGlow = EuropeanShadow(color: Colors.accent.opacity(0.20), radius: 6, x: 0, y: 3)
    }
    
    // MARK: - European Animations
    struct Animations {
        static let subtle = Animation.easeInOut(duration: 0.3)
        static let elegant = Animation.easeInOut(duration: 0.5)
        static let sophisticated = Animation.spring(response: 0.6, dampingFraction: 0.8)
        static let luxurious = Animation.spring(response: 0.8, dampingFraction: 0.9)
        static let gentle = Animation.easeOut(duration: 0.4)
        static let smooth = Animation.interpolatingSpring(stiffness: 300, damping: 30)
    }
    
    // MARK: - European Gradients (Theme-Aware)
    struct Gradients {
        static var elegantBackground: LinearGradient {
            LinearGradient(
                colors: [Colors.background, Colors.surface],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }

        static var sophisticatedCard: LinearGradient {
            LinearGradient(
                colors: [Colors.surface, Colors.surfaceSecondary],
                startPoint: .top,
                endPoint: .bottom
            )
        }
        
        static let premiumGlow = LinearGradient(
            colors: [Colors.premium.opacity(0.1), Colors.premiumLight.opacity(0.05)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        static let accentShimmer = LinearGradient(
            colors: [Colors.accent.opacity(0.8), Colors.accentLight.opacity(0.6), Colors.accent.opacity(0.8)],
            startPoint: .leading,
            endPoint: .trailing
        )
    }
}

struct EuropeanShadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - European View Modifiers
extension View {
    func europeanCard() -> some View {
        self
            .background(EuropeanDesign.Gradients.sophisticatedCard)
            .cornerRadius(EuropeanDesign.CornerRadius.elegant)
            .shadow(
                color: EuropeanDesign.Shadows.elegant.color,
                radius: EuropeanDesign.Shadows.elegant.radius,
                x: EuropeanDesign.Shadows.elegant.x,
                y: EuropeanDesign.Shadows.elegant.y
            )
    }
    
    func europeanButton() -> some View {
        self
            .background(EuropeanDesign.Colors.accent)
            .foregroundColor(EuropeanDesign.Colors.textPrimary)
            .cornerRadius(EuropeanDesign.CornerRadius.medium)
            .shadow(
                color: EuropeanDesign.Shadows.soft.color,
                radius: EuropeanDesign.Shadows.soft.radius,
                x: EuropeanDesign.Shadows.soft.x,
                y: EuropeanDesign.Shadows.soft.y
            )
    }
    
    func europeanPremiumCard() -> some View {
        self
            .background(EuropeanDesign.Gradients.premiumGlow)
            .cornerRadius(EuropeanDesign.CornerRadius.sophisticated)
            .shadow(
                color: EuropeanDesign.Shadows.premiumGlow.color,
                radius: EuropeanDesign.Shadows.premiumGlow.radius,
                x: EuropeanDesign.Shadows.premiumGlow.x,
                y: EuropeanDesign.Shadows.premiumGlow.y
            )
    }
}

// MARK: - European Color Palette
extension Color {
    // European Glass Palette
    static let europeanGlassPrimary = Color(red: 0.4, green: 0.3, blue: 0.9)      // Deep violet
    static let europeanGlassSecondary = Color(red: 0.1, green: 0.7, blue: 0.9)    // Ocean blue
    static let europeanGlassTertiary = Color(red: 0.9, green: 0.3, blue: 0.6)     // Rose pink
    static let europeanGlassQuaternary = Color(red: 0.9, green: 0.6, blue: 0.1)   // Amber gold
    
    // Nordic Glass Colors
    static let nordicIce = Color(red: 0.85, green: 0.95, blue: 1.0)
    static let nordicFrost = Color(red: 0.9, green: 0.95, blue: 0.98)
    static let nordicDepth = Color(red: 0.2, green: 0.3, blue: 0.4)
    
    // Mediterranean Glass Colors
    static let mediterraneanTurquoise = Color(red: 0.2, green: 0.8, blue: 0.8)
    static let mediterraneanCoral = Color(red: 1.0, green: 0.5, blue: 0.4)
    static let mediterraneanGold = Color(red: 0.9, green: 0.7, blue: 0.2)
    
    // Glass Surface Colors
    static let glassUltraLight = Color.white.opacity(0.15)
    static let glassUltraDark = Color.black.opacity(0.25)
    static let glassMisted = Color.white.opacity(0.08)
    static let glassCrystal = Color.white.opacity(0.35)
}

// MARK: - Advanced Gradients
extension LinearGradient {
    // Liquid Glass Gradients
    static let liquidGlass = LinearGradient(
        colors: [
            Color.europeanGlassPrimary.opacity(0.9),
            Color.europeanGlassSecondary.opacity(0.7),
            Color.europeanGlassTertiary.opacity(0.5),
            Color.europeanGlassQuaternary.opacity(0.3)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let nordicGlass = LinearGradient(
        colors: [
            Color.nordicIce.opacity(0.8),
            Color.nordicFrost.opacity(0.6),
            Color.nordicDepth.opacity(0.2)
        ],
        startPoint: .top,
        endPoint: .bottom
    )
    
    static let mediterraneanGlass = LinearGradient(
        colors: [
            Color.mediterraneanTurquoise.opacity(0.7),
            Color.mediterraneanCoral.opacity(0.5),
            Color.mediterraneanGold.opacity(0.3)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    // Aurora Glass Effect
    static let auroraGlass = LinearGradient(
        colors: [
            Color.green.opacity(0.6),
            Color.blue.opacity(0.5),
            Color.purple.opacity(0.4),
            Color.pink.opacity(0.3)
        ],
        startPoint: UnitPoint(x: 0, y: 0),
        endPoint: UnitPoint(x: 1, y: 1)
    )
    
    // Prismatic Glass
    static let prismaticGlass = LinearGradient(
        colors: [
            Color.red.opacity(0.3),
            Color.orange.opacity(0.4),
            Color.yellow.opacity(0.3),
            Color.green.opacity(0.4),
            Color.blue.opacity(0.3),
            Color.purple.opacity(0.4)
        ],
        startPoint: .leading,
        endPoint: .trailing
    )
}

// MARK: - Advanced Radial Gradients
extension RadialGradient {
    static let glassOrb = RadialGradient(
        colors: [
            Color.white.opacity(0.8),
            Color.europeanGlassPrimary.opacity(0.6),
            Color.europeanGlassSecondary.opacity(0.3),
            Color.clear
        ],
        center: .center,
        startRadius: 0,
        endRadius: 200
    )
    
    static let crystalCore = RadialGradient(
        colors: [
            Color.glassCrystal,
            Color.glassUltraLight,
            Color.glassMisted,
            Color.clear
        ],
        center: .topLeading,
        startRadius: 10,
        endRadius: 150
    )
    
    static let glassDistortion = RadialGradient(
        colors: [
            Color.white.opacity(0.4),
            Color.white.opacity(0.2),
            Color.white.opacity(0.05),
            Color.clear
        ],
        center: .center,
        startRadius: 0,
        endRadius: 100
    )
}

// MARK: - Angular Gradients for Glass Refraction
extension AngularGradient {
    static let glassRefraction = AngularGradient(
        colors: [
            Color.europeanGlassPrimary.opacity(0.6),
            Color.europeanGlassSecondary.opacity(0.5),
            Color.europeanGlassTertiary.opacity(0.4),
            Color.europeanGlassQuaternary.opacity(0.5),
            Color.europeanGlassPrimary.opacity(0.6)
        ],
        center: .center,
        startAngle: .degrees(0),
        endAngle: .degrees(360)
    )
    
    static let prismSpectrum = AngularGradient(
        colors: [
            Color.red.opacity(0.7),
            Color.orange.opacity(0.6),
            Color.yellow.opacity(0.5),
            Color.green.opacity(0.6),
            Color.blue.opacity(0.7),
            Color.purple.opacity(0.6),
            Color.red.opacity(0.7)
        ],
        center: .center,
        startAngle: .degrees(0),
        endAngle: .degrees(360)
    )
}

// MARK: - Liquid Glass View Modifiers
struct LiquidGlassCard: ViewModifier {
    @Environment(\.colorScheme) var colorScheme
    let distortion: Double
    let liquidity: Double
    @State private var animationPhase: Double = 0
    
    init(distortion: Double = 1.0, liquidity: Double = 1.0) {
        self.distortion = distortion
        self.liquidity = liquidity
    }
    
    func body(content: Content) -> some View {
        content
            .background {
                // Base liquid glass background
                RoundedRectangle(cornerRadius: 28)
                    .fill(
                        colorScheme == .dark
                        ? Color.glassUltraDark.opacity(0.8)
                        : Color.glassUltraLight.opacity(0.9)
                    )
                    .background(
                        // Liquid distortion layer
                        RoundedRectangle(cornerRadius: 28)
                            .fill(LinearGradient.liquidGlass.opacity(liquidity * 0.6))
                            .scaleEffect(1.0 + sin(animationPhase) * 0.02 * liquidity)
                            .rotationEffect(.degrees(sin(animationPhase * 0.7) * 2 * distortion))
                    )
                    .background(
                        // Prismatic refraction
                        RoundedRectangle(cornerRadius: 28)
                            .fill(AngularGradient.glassRefraction.opacity(0.3 * liquidity))
                            .blur(radius: 8)
                    )
                    .overlay(
                        // Crystal highlights
                        RoundedRectangle(cornerRadius: 28)
                            .fill(RadialGradient.crystalCore.opacity(0.7))
                            .blendMode(.overlay)
                    )
                    .overlay(
                        // Border glow
                        RoundedRectangle(cornerRadius: 28)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.6),
                                        Color.europeanGlassPrimary.opacity(0.4),
                                        Color.europeanGlassSecondary.opacity(0.3),
                                        Color.white.opacity(0.2)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                    )
                    .shadow(
                        color: colorScheme == .dark
                        ? Color.black.opacity(0.4)
                        : Color.gray.opacity(0.2),
                        radius: 12,
                        x: 0,
                        y: 6
                    )
                    .onAppear {
                        withAnimation(.easeInOut(duration: 8).repeatForever(autoreverses: true)) {
                            animationPhase = .pi * 2
                        }
                    }
            }
    }
}

struct CrystalButton: ViewModifier {
    @Environment(\.colorScheme) var colorScheme
    let style: CrystalButtonStyle
    @State private var isPressed = false
    @State private var crystalGlow: Double = 0
    
    func body(content: Content) -> some View {
        content
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background {
                // Main crystal structure
                RoundedRectangle(cornerRadius: 20)
                    .fill(style.primaryGradient(colorScheme: colorScheme))
                    .overlay(
                        // Inner crystal core
                        RoundedRectangle(cornerRadius: 20)
                            .fill(RadialGradient.glassOrb.opacity(0.4))
                            .blendMode(.overlay)
                    )
                    .overlay(
                        // Prismatic overlay
                        RoundedRectangle(cornerRadius: 20)
                            .fill(AngularGradient.prismSpectrum.opacity(0.2))
                            .blur(radius: 3)
                    )
                    .overlay(
                        // Crystal facets
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.8),
                                        Color.clear,
                                        Color.white.opacity(0.3),
                                        Color.clear
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1.5
                            )
                    )
                    .scaleEffect(isPressed ? 0.96 : 1.0)
                    .shadow(
                        color: style.shadowColor(colorScheme: colorScheme),
                        radius: isPressed ? 4 : 8,
                        x: 0,
                        y: isPressed ? 2 : 4
                    )
                    .overlay(
                        // Glow effect
                        RoundedRectangle(cornerRadius: 20)
                            .fill(RadialGradient.glassGlow.opacity(crystalGlow))
                            .blur(radius: 6)
                            .allowsHitTesting(false)
                    )
            }
            .foregroundStyle(style.foregroundGradient(colorScheme: colorScheme))
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
                withAnimation(.easeInOut(duration: 0.3)) {
                    crystalGlow = pressing ? 0.6 : 0
                }
            }, perform: {})
    }
}

enum CrystalButtonStyle {
    case aurora
    case nordic
    case mediterranean
    case prismatic
    
    func primaryGradient(colorScheme: ColorScheme) -> LinearGradient {
        switch self {
        case .aurora:
            return LinearGradient.auroraGlass
        case .nordic:
            return LinearGradient.nordicGlass
        case .mediterranean:
            return LinearGradient.mediterraneanGlass
        case .prismatic:
            return LinearGradient.prismaticGlass
        }
    }
    
    func foregroundGradient(colorScheme: ColorScheme) -> LinearGradient {
        switch self {
        case .aurora, .mediterranean, .prismatic:
            return LinearGradient(colors: [Color.white], startPoint: .top, endPoint: .bottom)
        case .nordic:
            return LinearGradient(colors: [Color.nordicDepth], startPoint: .top, endPoint: .bottom)
        }
    }
    
    func shadowColor(colorScheme: ColorScheme) -> Color {
        switch self {
        case .aurora:
            return Color.green.opacity(0.3)
        case .nordic:
            return Color.nordicDepth.opacity(0.2)
        case .mediterranean:
            return Color.mediterraneanTurquoise.opacity(0.3)
        case .prismatic:
            return Color.purple.opacity(0.3)
        }
    }
}

struct GlassMotion: ViewModifier {
    @State private var motionOffset: CGSize = .zero
    @State private var rotationAngle: Double = 0
    let intensity: Double
    
    init(intensity: Double = 1.0) {
        self.intensity = intensity
    }
    
    func body(content: Content) -> some View {
        content
            .offset(motionOffset)
            .rotationEffect(.degrees(rotationAngle))
            .onAppear {
                startMotion()
            }
    }
    
    private func startMotion() {
        withAnimation(.easeInOut(duration: 6).repeatForever(autoreverses: true)) {
            motionOffset = CGSize(
                width: sin(.pi / 3) * 8 * intensity,
                height: cos(.pi / 4) * 6 * intensity
            )
        }
        
        withAnimation(.easeInOut(duration: 8).repeatForever(autoreverses: true)) {
            rotationAngle = 3 * intensity
        }
    }
}

struct GlassDistortionEffect: ViewModifier {
    @State private var distortionScale: CGFloat = 1.0
    @State private var distortionRotation: Double = 0
    let strength: Double
    
    init(strength: Double = 1.0) {
        self.strength = strength
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(distortionScale)
            .rotationEffect(.degrees(distortionRotation))
            .onAppear {
                withAnimation(.easeInOut(duration: 4).repeatForever(autoreverses: true)) {
                    distortionScale = 1.0 + (0.05 * strength)
                    distortionRotation = 2 * strength
                }
            }
    }
}

// MARK: - Liquid Glass Components
struct LiquidGlassNavigationBar: View {
    let title: String
    let leadingAction: (() -> Void)?
    let trailingAction: (() -> Void)?
    let leadingIcon: String?
    let trailingIcon: String?
    
    var body: some View {
        HStack {
            if let leadingIcon = leadingIcon, let leadingAction = leadingAction {
                Button(action: leadingAction) {
                    Image(systemName: leadingIcon)
                        .font(.system(size: 18, weight: .semibold))
                }
                .crystalButton(.nordic)
                .frame(width: 44, height: 44)
            }
            
            Spacer()
            
            Text(title)
                .font(.system(.title2, design: .rounded).weight(.bold))
                .foregroundStyle(LinearGradient.liquidGlass)
            
            Spacer()
            
            if let trailingIcon = trailingIcon, let trailingAction = trailingAction {
                Button(action: trailingAction) {
                    Image(systemName: trailingIcon)
                        .font(.system(size: 18, weight: .semibold))
                }
                .crystalButton(.nordic)
                .frame(width: 44, height: 44)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .liquidGlassCard(distortion: 0.3, liquidity: 0.5)
    }
}

struct CrystalTabBar: View {
    @Binding var selectedTab: Int
    let tabs: [TabItem]
    
    struct TabItem {
        let icon: String
        let title: String
        let index: Int
        let style: CrystalButtonStyle
    }
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(tabs, id: \.index) { tab in
                Button(action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        selectedTab = tab.index
                    }
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: tab.icon)
                            .font(.system(size: 20, weight: .medium))
                        
                        Text(tab.title)
                            .font(.system(.caption, design: .rounded).weight(.medium))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
                .crystalButton(selectedTab == tab.index ? tab.style : .nordic)
                .opacity(selectedTab == tab.index ? 1.0 : 0.7)
                .scaleEffect(selectedTab == tab.index ? 1.0 : 0.95)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .liquidGlassCard(distortion: 0.2, liquidity: 0.3)
    }
}

struct GlassFloatingActionButton: View {
    let icon: String
    let action: () -> Void
    let style: CrystalButtonStyle
    @State private var isHovered = false
    
    init(icon: String, style: CrystalButtonStyle = .aurora, action: @escaping () -> Void) {
        self.icon = icon
        self.style = style
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .bold))
                .frame(width: 56, height: 56)
        }
        .crystalButton(style)
        .glassMotion(intensity: isHovered ? 1.5 : 1.0)
        .glassDistortion(strength: isHovered ? 1.2 : 0.8)
        .onHover { hovering in
            withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                isHovered = hovering
            }
        }
        .shadow(
            color: style.shadowColor(colorScheme: .light),
            radius: isHovered ? 12 : 8,
            x: 0,
            y: isHovered ? 6 : 4
        )
    }
}

struct LiquidGlassTextField: View {
    @Binding var text: String
    let placeholder: String
    let icon: String?
    @FocusState private var isFocused: Bool
    @Environment(\.colorScheme) var colorScheme
    
    init(_ placeholder: String, text: Binding<String>, icon: String? = nil) {
        self.placeholder = placeholder
        self._text = text
        self.icon = icon
    }
    
    var body: some View {
        HStack(spacing: 12) {
            if let icon = icon {
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundStyle(
                        isFocused
                        ? LinearGradient.liquidGlass
                        : LinearGradient(colors: [Color.secondary], startPoint: .top, endPoint: .bottom)
                    )
            }
            
            TextField(placeholder, text: $text)
                .font(.system(.body, design: .rounded).weight(.medium))
                .focused($isFocused)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 14)
        .background {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    colorScheme == .dark
                    ? Color.glassUltraDark.opacity(0.7)
                    : Color.glassUltraLight.opacity(0.8)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            isFocused
                            ? LinearGradient.liquidGlass.opacity(0.8)
                            : LinearGradient(
                                colors: [Color.white.opacity(0.3), Color.clear],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: isFocused ? 2 : 1
                        )
                )
                .shadow(
                    color: isFocused
                    ? Color.europeanGlassPrimary.opacity(0.2)
                    : Color.clear,
                    radius: 8,
                    x: 0,
                    y: 4
                )
        }
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isFocused)
    }
}

// MARK: - View Extensions for Liquid Glass
extension View {
    func liquidGlassCard(distortion: Double = 1.0, liquidity: Double = 1.0) -> some View {
        modifier(LiquidGlassCard(distortion: distortion, liquidity: liquidity))
    }
    
    func crystalButton(_ style: CrystalButtonStyle = .aurora) -> some View {
        modifier(CrystalButton(style: style))
    }
    
    func glassMotion(intensity: Double = 1.0) -> some View {
        modifier(GlassMotion(intensity: intensity))
    }
    
    func glassDistortion(strength: Double = 1.0) -> some View {
        modifier(GlassDistortionEffect(strength: strength))
    }
}

// MARK: - Preset Glass Themes
struct GlassTheme {
    static let aurora = GlassThemeConfig(
        primaryGradient: .auroraGlass,
        backgroundGradient: LinearGradient(
            colors: [
                Color.green.opacity(0.1),
                Color.blue.opacity(0.1),
                Color.purple.opacity(0.1)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        accentColor: Color.green
    )
    
    static let nordic = GlassThemeConfig(
        primaryGradient: .nordicGlass,
        backgroundGradient: LinearGradient(
            colors: [
                Color.nordicIce.opacity(0.3),
                Color.nordicFrost.opacity(0.2),
                Color.white.opacity(0.1)
            ],
            startPoint: .top,
            endPoint: .bottom
        ),
        accentColor: Color.nordicDepth
    )
    
    static let mediterranean = GlassThemeConfig(
        primaryGradient: .mediterraneanGlass,
        backgroundGradient: LinearGradient(
            colors: [
                Color.mediterraneanTurquoise.opacity(0.2),
                Color.mediterraneanCoral.opacity(0.15),
                Color.mediterraneanGold.opacity(0.1)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        accentColor: Color.mediterraneanTurquoise
    )
}

struct GlassThemeConfig {
    let primaryGradient: LinearGradient
    let backgroundGradient: LinearGradient
    let accentColor: Color
}

// MARK: - Glass Theme Environment
struct GlassThemeKey: EnvironmentKey {
    static let defaultValue = GlassTheme.aurora
}

extension EnvironmentValues {
    var glassTheme: GlassThemeConfig {
        get { self[GlassThemeKey.self] }
        set { self[GlassThemeKey.self] = newValue }
    }
}

extension View {
    func glassTheme(_ theme: GlassThemeConfig) -> some View {
        environment(\.glassTheme, theme)
    }
}
