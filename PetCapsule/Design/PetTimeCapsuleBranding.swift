//
//  PetTimeCapsuleBranding.swift
//  PetTime Capsule
//
//  Brand Colors and Visual Identity
//

import SwiftUI

// MARK: - Brand Colors
extension Color {
    // Primary Brand Colors
    static let petTimePrimary = Color(red: 0.4, green: 0.49, blue: 0.92) // #667eea
    static let petTimeSecondary = Color(red: 0.46, green: 0.29, blue: 0.64) // #764ba2
    static let petTimeAccent = Color(red: 0.94, green: 0.58, blue: 0.98) // #f093fb
    
    // Paw Print Colors
    static let pawPrimary = Color(red: 1.0, green: 0.42, blue: 0.42) // #ff6b6b
    static let pawSecondary = Color(red: 0.93, green: 0.35, blue: 0.14) // #ee5a24
    
    // Heart Colors (Love & Care)
    static let heartPrimary = Color(red: 1.0, green: 0.6, blue: 0.62) // #ff9a9e
    static let heartSecondary = Color(red: 0.99, green: 0.81, blue: 0.94) // #fecfef
    
    // Capsule Colors (Time & Memory)
    static let capsulePrimary = Color(red: 0.96, green: 0.97, blue: 0.98) // #f8f9fa
    static let capsuleSecondary = Color(red: 0.91, green: 0.93, blue: 0.94) // #e9ecef
    
    // Functional Colors
    static let petTimeSuccess = Color(red: 0.16, green: 0.8, blue: 0.45) // #28cd72
    static let petTimeWarning = Color(red: 1.0, green: 0.73, blue: 0.0) // #ffba00
    static let petTimeError = Color(red: 1.0, green: 0.23, blue: 0.19) // #ff3b30
    static let petTimeInfo = Color(red: 0.0, green: 0.48, blue: 1.0) // #007aff
}

// MARK: - Brand Gradients
struct PetTimeBrandGradients {
    // Primary brand gradient
    static let primary = LinearGradient(
        colors: [.petTimePrimary, .petTimeSecondary],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    // Accent gradient
    static let accent = LinearGradient(
        colors: [.petTimeAccent, .petTimePrimary],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    // Paw gradient
    static let paw = LinearGradient(
        colors: [.pawPrimary, .pawSecondary],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    // Heart gradient
    static let heart = LinearGradient(
        colors: [.heartPrimary, .heartSecondary],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    // Background gradient
    static let background = LinearGradient(
        colors: [
            .petTimePrimary.opacity(0.1),
            .petTimeSecondary.opacity(0.1),
            .petTimeAccent.opacity(0.05)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    // Card gradient
    static let card = LinearGradient(
        colors: [
            Color.white,
            Color.white.opacity(0.95)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
}

// MARK: - Typography
struct PetTimeBrandFonts {
    // Heading fonts
    static func largeTitle(weight: Font.Weight = .bold) -> Font {
        .system(size: 34, weight: weight, design: .rounded)
    }
    
    static func title1(weight: Font.Weight = .bold) -> Font {
        .system(size: 28, weight: weight, design: .rounded)
    }
    
    static func title2(weight: Font.Weight = .bold) -> Font {
        .system(size: 22, weight: weight, design: .rounded)
    }
    
    static func title3(weight: Font.Weight = .semibold) -> Font {
        .system(size: 20, weight: weight, design: .rounded)
    }
    
    // Body fonts
    static func headline(weight: Font.Weight = .semibold) -> Font {
        .system(size: 17, weight: weight, design: .default)
    }
    
    static func body(weight: Font.Weight = .regular) -> Font {
        .system(size: 17, weight: weight, design: .default)
    }
    
    static func callout(weight: Font.Weight = .regular) -> Font {
        .system(size: 16, weight: weight, design: .default)
    }
    
    static func subheadline(weight: Font.Weight = .regular) -> Font {
        .system(size: 15, weight: weight, design: .default)
    }
    
    static func footnote(weight: Font.Weight = .regular) -> Font {
        .system(size: 13, weight: weight, design: .default)
    }
    
    static func caption1(weight: Font.Weight = .regular) -> Font {
        .system(size: 12, weight: weight, design: .default)
    }
    
    static func caption2(weight: Font.Weight = .regular) -> Font {
        .system(size: 11, weight: weight, design: .default)
    }
}

// MARK: - Spacing System
struct PetTimeBrandSpacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
    static let xxxl: CGFloat = 64
}

// MARK: - Corner Radius
struct PetTimeBrandRadius {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 24
    static let xxl: CGFloat = 32
    static let round: CGFloat = 50 // For circular elements
}

// MARK: - Shadow Styles
struct PetTimeBrandShadows {
    static let small = Shadow(
        color: .black.opacity(0.1),
        radius: 2,
        x: 0,
        y: 1
    )
    
    static let medium = Shadow(
        color: .black.opacity(0.15),
        radius: 4,
        x: 0,
        y: 2
    )
    
    static let large = Shadow(
        color: .black.opacity(0.2),
        radius: 8,
        x: 0,
        y: 4
    )
    
    static let extraLarge = Shadow(
        color: .black.opacity(0.25),
        radius: 16,
        x: 0,
        y: 8
    )
}

struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - Brand Button Styles
struct PetTimeBrandButtonStyle: ButtonStyle {
    let variant: ButtonVariant
    let size: ButtonSize
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(size.font)
            .foregroundStyle(variant.foregroundColor)
            .padding(.horizontal, size.horizontalPadding)
            .padding(.vertical, size.verticalPadding)
            .background(variant.backgroundColor)
            .clipShape(RoundedRectangle(cornerRadius: PetTimeBrandRadius.md))
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: configuration.isPressed)
    }
}

enum ButtonVariant {
    case primary
    case secondary
    case accent
    case ghost
    
    var backgroundColor: AnyShapeStyle {
        switch self {
        case .primary:
            return AnyShapeStyle(PetTimeBrandGradients.primary)
        case .secondary:
            return AnyShapeStyle(Color.secondary.opacity(0.1))
        case .accent:
            return AnyShapeStyle(PetTimeBrandGradients.accent)
        case .ghost:
            return AnyShapeStyle(Color.clear)
        }
    }
    
    var foregroundColor: Color {
        switch self {
        case .primary, .accent:
            return .white
        case .secondary, .ghost:
            return .primary
        }
    }
}

enum ButtonSize {
    case small
    case medium
    case large
    
    var font: Font {
        switch self {
        case .small:
            return PetTimeBrandFonts.caption1(weight: .medium)
        case .medium:
            return PetTimeBrandFonts.callout(weight: .medium)
        case .large:
            return PetTimeBrandFonts.headline(weight: .semibold)
        }
    }
    
    var horizontalPadding: CGFloat {
        switch self {
        case .small:
            return PetTimeBrandSpacing.sm
        case .medium:
            return PetTimeBrandSpacing.md
        case .large:
            return PetTimeBrandSpacing.lg
        }
    }
    
    var verticalPadding: CGFloat {
        switch self {
        case .small:
            return PetTimeBrandSpacing.xs
        case .medium:
            return PetTimeBrandSpacing.sm
        case .large:
            return PetTimeBrandSpacing.md
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: PetTimeBrandSpacing.lg) {
        // Logo
        PetTimeCapsuleLogo(size: 100, showText: true)
        
        // Color palette
        VStack(spacing: PetTimeBrandSpacing.md) {
            Text("Brand Colors")
                .font(PetTimeBrandFonts.title2())
            
            HStack(spacing: PetTimeBrandSpacing.sm) {
                ColorSwatch(color: .petTimePrimary, name: "Primary")
                ColorSwatch(color: .petTimeSecondary, name: "Secondary")
                ColorSwatch(color: .petTimeAccent, name: "Accent")
                ColorSwatch(color: .pawPrimary, name: "Paw")
            }
        }
        
        // Buttons
        VStack(spacing: PetTimeBrandSpacing.md) {
            Text("Button Styles")
                .font(PetTimeBrandFonts.title2())
            
            VStack(spacing: PetTimeBrandSpacing.sm) {
                Button("Primary Button") {}
                    .buttonStyle(PetTimeBrandButtonStyle(variant: .primary, size: .medium))
                
                Button("Secondary Button") {}
                    .buttonStyle(PetTimeBrandButtonStyle(variant: .secondary, size: .medium))
                
                Button("Accent Button") {}
                    .buttonStyle(PetTimeBrandButtonStyle(variant: .accent, size: .medium))
            }
        }
    }
    .padding(PetTimeBrandSpacing.lg)
    .background(PetTimeBrandGradients.background)
}

struct ColorSwatch: View {
    let color: Color
    let name: String
    
    var body: some View {
        VStack(spacing: PetTimeBrandSpacing.xs) {
            RoundedRectangle(cornerRadius: PetTimeBrandRadius.sm)
                .fill(color)
                .frame(width: 60, height: 60)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            
            Text(name)
                .font(PetTimeBrandFonts.caption1(weight: .medium))
                .foregroundColor(.secondary)
        }
    }
}
