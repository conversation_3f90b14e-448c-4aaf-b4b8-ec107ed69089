//
//  UIImage+Extensions.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

#if canImport(UIKit)
import UIKit

extension UIImage {
    
    /// Resize image to specified size while maintaining aspect ratio
    func resized(to targetSize: CGSize) -> UIImage? {
        let size = self.size
        
        let widthRatio = targetSize.width / size.width
        let heightRatio = targetSize.height / size.height
        
        // Choose the smaller ratio to maintain aspect ratio
        let ratio = min(widthRatio, heightRatio)
        
        let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
        
        let renderer = UIGraphicsImageRenderer(size: newSize)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }
    
    /// Resize image with maximum dimension constraint
    func resized(maxDimension: CGFloat) -> UIImage? {
        let size = self.size
        
        // Check if resizing is needed
        if size.width <= maxDimension && size.height <= maxDimension {
            return self
        }
        
        // Calculate new size maintaining aspect ratio
        let aspectRatio = size.width / size.height
        let newSize: CGSize
        
        if size.width > size.height {
            // Landscape orientation
            newSize = CGSize(width: maxDimension, height: maxDimension / aspectRatio)
        } else {
            // Portrait orientation
            newSize = CGSize(width: maxDimension * aspectRatio, height: maxDimension)
        }
        
        let renderer = UIGraphicsImageRenderer(size: newSize)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }
    
    /// Create a thumbnail version of the image
    func thumbnail(size: CGSize = CGSize(width: 300, height: 300)) -> UIImage? {
        return resized(to: size)
    }
    
    /// Compress image data with quality
    func compressedData(quality: CGFloat = 0.8) -> Data? {
        return self.jpegData(compressionQuality: quality)
    }
    
    /// Get optimized data for upload (resized and compressed)
    func optimizedForUpload(maxDimension: CGFloat = 1920, quality: CGFloat = 0.8) -> Data? {
        guard let resizedImage = self.resized(maxDimension: maxDimension) else {
            return self.jpegData(compressionQuality: quality)
        }
        return resizedImage.jpegData(compressionQuality: quality)
    }
}
#endif
