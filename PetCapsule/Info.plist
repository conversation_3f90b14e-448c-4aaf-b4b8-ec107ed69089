<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>PetTime Capsule</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>background-processing</string>
		<string>background-fetch</string>
	</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
			<key>NSExceptionDomains</key>
	<dict>
		<key>googleapis.com</key>
		<dict>
			<key>NSExceptionAllowsInsecureHTTPLoads</key>
			<false/>
			<key>NSExceptionMinimumTLSVersion</key>
			<string>TLSv1.2</string>
			<key>NSExceptionRequiresForwardSecrecy</key>
			<true/>
		</dict>
	</dict>
	</dict>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSSupportsLiveActivities</key>
	<true/>
	<key>NSSupportsLiveActivitiesFrequentUpdates</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>PetTime Capsule needs camera access to capture precious moments with your pet.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>PetTime Capsule needs photo library access to save and organize your pet memories.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>PetTime Capsule needs microphone access to record voice notes and audio memories of your pet.</string>
	<key>NSContactsUsageDescription</key>
	<string>PetTime Capsule needs access to your contacts to help you easily import emergency contacts for your pet's safety.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>PetTime Capsule needs permission to save generated memory montages to your photo library.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>PetTime Capsule uses speech recognition to help you communicate with our AI pet care assistants through voice commands, making it easier to get expert advice about your pet's health, nutrition, and care.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>PetTime Capsule uses your location to find nearby pet services, veterinarians, and pet-friendly places recommended by our AI assistants.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>PetTime Capsule uses your location to provide location-based pet care reminders and find nearby emergency veterinary services when needed.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>PetTime Capsule accesses your calendar to schedule pet care reminders, veterinary appointments, and feeding schedules recommended by our AI health assistants.</string>
	<key>NSRemindersUsageDescription</key>
	<string>PetTime Capsule creates reminders for pet care tasks, medication schedules, and health checkups as recommended by our AI pet care assistants.</string>
	<key>NSHealthShareUsageDescription</key>
	<string>PetTime Capsule can integrate with HealthKit to track your pet's activity and health data, providing our AI assistants with better insights for personalized care recommendations.</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>PetTime Capsule updates HealthKit with your pet's health and activity data to maintain comprehensive health records for better AI-powered care recommendations.</string>
	<key>NSMotionUsageDescription</key>
	<string>PetTime Capsule uses motion data to track your pet's activity levels and provide AI-powered insights about their exercise needs and health patterns.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>PetTime Capsule uses Bluetooth to connect with pet health monitoring devices and smart collars to provide comprehensive care data to our AI assistants.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>PetTime Capsule uses Bluetooth to communicate with pet monitoring devices and smart accessories for enhanced AI-powered health tracking.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>PetTime Capsule uses Face ID to securely protect your pet's sensitive health information and AI chat history with biometric authentication.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>PetTime Capsule uses tracking to provide personalized AI recommendations and improve our pet care services. Your privacy is important to us and this data helps us serve you better.</string>

	<!-- iOS 18 Features -->
	<key>NSSupportsAlternateIcons</key>
	<true/>
	<key>CFBundleAlternateIcons</key>
	<dict>
		<key>AppIcon-Playful</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon-Playful</string>
			</array>
			<key>UIPrerenderedIcon</key>
			<false/>
		</dict>
		<key>AppIcon-Elegant</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon-Elegant</string>
			</array>
			<key>UIPrerenderedIcon</key>
			<false/>
		</dict>
		<key>AppIcon-Veterinary</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon-Veterinary</string>
			</array>
			<key>UIPrerenderedIcon</key>
			<false/>
		</dict>
		<key>AppIcon-Paw</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon-Paw</string>
			</array>
			<key>UIPrerenderedIcon</key>
			<false/>
		</dict>
		<key>AppIcon-Heart</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon-Heart</string>
			</array>
			<key>UIPrerenderedIcon</key>
			<false/>
		</dict>
	</dict>

	<!-- Apple Intelligence -->
	<key>NSAppleIntelligenceUsageDescription</key>
	<string>PetTime Capsule uses Apple Intelligence to enhance AI agent responses, improve memory descriptions, and provide better pet care recommendations with advanced writing tools and image generation.</string>

	<!-- Passkeys -->
	<key>NSSupportsPasskeys</key>
	<true/>
	<key>NSPasskeyDomain</key>
	<string>pettimecapsule.app</string>

	<!-- Controls -->
	<key>NSSupportsControlCenter</key>
	<true/>
	<key>NSSupportsLockScreenControls</key>
	<true/>

	<!-- Enhanced Machine Learning -->
	<key>NSCoreMLUsageDescription</key>
	<string>PetTime Capsule uses machine learning to analyze pet photos, detect health indicators, recognize emotions, and provide intelligent pet care insights.</string>

	<!-- Translation -->
	<key>NSTranslationUsageDescription</key>
	<string>PetTime Capsule uses translation services to provide pet care advice and AI responses in your preferred language.</string>

	<!-- App Intents -->
	<key>NSAppIntentsUsageDescription</key>
	<string>PetTime Capsule integrates with Siri and Shortcuts to provide voice-activated pet care assistance and quick access to AI experts.</string>

	<!-- Visual Intelligence -->
	<key>NSVisualIntelligenceUsageDescription</key>
	<string>PetTime Capsule uses Visual Intelligence to identify pet breeds, analyze health indicators, and provide contextual pet care information from photos and camera input.</string>

	<!-- HealthKit -->
	<key>NSHealthShareUsageDescription</key>
	<string>PetTime Capsule uses HealthKit to track your walking activities with your pet, monitor exercise data, and provide health insights for both you and your pet's wellbeing.</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>PetTime Capsule uses HealthKit to record walking sessions and exercise activities with your pet to help maintain both your health and your pet's fitness routine.</string>

	<!-- Widget Configuration -->
	<key>NSWidgetWantsLocation</key>
	<true/>

	<!-- Live Activities -->
	<key>NSSupportsLiveActivitiesFrequentUpdates</key>
	<true/>
	<key>NSLiveActivitiesUsageDescription</key>
	<string>PetTime Capsule uses Live Activities to provide real-time updates for pet walks, medication reminders, and vet appointments on your Lock Screen and Dynamic Island.</string>
	
	<!-- Notifications -->
	<key>NSUserNotificationsUsageDescription</key>
	<string>PetTime Capsule sends notifications for important pet care reminders, environmental alerts that may affect your pet's health, medication schedules, vet appointments, and emergency situations to keep your pet safe and healthy.</string>
</dict>
</plist>
