//
//  KnowledgeBaseIntents.swift
//  PetCapsule
//
//  App Intents for Knowledge Base functionality
//  Enables Siri Shortcuts for quick document access and creation
//

import Foundation
import AppIntents
import SwiftUI

// MARK: - Add Document Intent

@available(iOS 16.0, *)
struct AddDocumentIntent: AppIntent {
    static let title: LocalizedStringResource = "Add Document to Knowledge Base"
    static let description = IntentDescription("Quickly add a new document to your pet's knowledge base")
    
    @Parameter(title: "Document Title")
    var title: String
    
    @Parameter(title: "Document Content")
    var content: String
    
    @Parameter(title: "Folder", default: "📋 Care Instructions")
    var folderName: String
    
    @Parameter(title: "Tags (comma separated)")
    var tags: String?
    
    func perform() async throws -> some IntentResult {
        let knowledgeService = await KnowledgeBaseService.shared
        
        // Find the folder by name
        guard let folder = await knowledgeService.folders.first(where: { $0.name.contains(folderName) }) else {
            return .result(dialog: "Folder '\(folderName)' not found. Using default Care Instructions folder.")
        }
        
        let documentTags = tags?.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) } ?? []
        
        let document = KnowledgeDocument(
            id: UUID(),
            title: title,
            content: content,
            type: .text,
            fileURL: nil,
            tags: documentTags,
            createdAt: Date(),
            updatedAt: Date()
        )
        
        await knowledgeService.addDocument(to: folder, document: document)
        
        return .result(dialog: "Document '\(title)' added to \(folder.name) successfully!")
    }
}

// MARK: - Search Documents Intent

@available(iOS 16.0, *)
struct SearchDocumentsIntent: AppIntent {
    static let title: LocalizedStringResource = "Search Knowledge Base"
    static let description = IntentDescription("Search for documents in your pet's knowledge base")
    
    @Parameter(title: "Search Query")
    var searchQuery: String
    
    func perform() async throws -> some IntentResult & ReturnsValue<String> {
        let knowledgeService = await KnowledgeBaseService.shared
        let results = await knowledgeService.searchDocuments(query: searchQuery)
        
        if results.isEmpty {
            return .result(
                value: "No documents found for '\(searchQuery)'",
                dialog: "No documents found matching '\(searchQuery)'"
            )
        }
        
        let resultText = results.map { document in
            "📄 \(document.title)\n\(document.content.prefix(100))...\n"
        }.joined(separator: "\n")
        
        return .result(
            value: resultText,
            dialog: "Found \(results.count) document(s) matching '\(searchQuery)'"
        )
    }
}

// MARK: - Get Recipe Intent

@available(iOS 16.0, *)
struct GetRecipeIntent: AppIntent {
    static let title: LocalizedStringResource = "Get Pet Recipe"
    static let description = IntentDescription("Get a pet recipe from your knowledge base")
    
    func perform() async throws -> some IntentResult & ReturnsValue<String> {
        let knowledgeService = await KnowledgeBaseService.shared
        
        // Find recipes folder
        guard let recipesFolder = await knowledgeService.folders.first(where: { $0.name.contains("Recipes") }) else {
            return .result(
                value: "No recipes folder found",
                dialog: "No recipes folder found in your knowledge base"
            )
        }
        
        let recipes = recipesFolder.documents.filter { $0.type == .recipe }
        
        if recipes.isEmpty {
            return .result(
                value: "No recipes found",
                dialog: "No recipes found in your knowledge base"
            )
        }
        
        // Return a random recipe
        let randomRecipe = recipes.randomElement()!
        
        return .result(
            value: randomRecipe.content,
            dialog: "Here's the recipe for \(randomRecipe.title)"
        )
    }
}

// MARK: - Quick Note Intent

@available(iOS 16.0, *)
struct QuickNoteIntent: AppIntent {
    static let title: LocalizedStringResource = "Quick Pet Note"
    static let description = IntentDescription("Quickly add a note about your pet")
    
    @Parameter(title: "Note Content")
    var note: String
    
    func perform() async throws -> some IntentResult {
        let knowledgeService = await KnowledgeBaseService.shared
        
        // Find care instructions folder
        guard let careFolder = await knowledgeService.folders.first(where: { $0.name.contains("Care Instructions") }) else {
            return .result(dialog: "Care Instructions folder not found. Please check your knowledge base setup.")
        }
        
        let document = KnowledgeDocument(
            id: UUID(),
            title: "Quick Note - \(DateFormatter.shortFormatter.string(from: Date()))",
            content: note,
            type: .text,
            fileURL: nil,
            tags: ["quick-note", "siri"],
            createdAt: Date(),
            updatedAt: Date()
        )
        
        await knowledgeService.addDocument(to: careFolder, document: document)
        
        return .result(dialog: "Note added to your pet's care instructions!")
    }
}

// MARK: - Extensions

extension DateFormatter {
    static let shortFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter
    }()
}

// MARK: - Knowledge Base Shortcuts
// Note: These shortcuts are registered in PetCapsuleAppIntents.swift 
// to maintain single AppShortcutsProvider per app requirement 