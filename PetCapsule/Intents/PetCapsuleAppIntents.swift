//
//  PetCapsuleAppIntents.swift
//  PetCapsule
//
//  Enhanced App Intents for iOS 18 Siri integration
//  Supports Apple Intelligence orchestration and cross-app actions
//

import Foundation
import AppIntents
import SwiftUI

// Type alias to resolve ambiguity



// Type aliases to resolve ambiguity - using the specific types we need for App Intents
// These types are defined at file level in their respective service files
// We need to import the specific types we want to use for App Intents

// MARK: - App Shortcuts Provider

@available(iOS 18.0, *)
struct PetCapsuleShortcuts: AppShortcutsProvider {
    static var appShortcuts: [AppShortcut] {
        AppShortcut(
            intent: AskPetExpertIntent(),
            phrases: [
                "Ask \(.applicationName) about my pet",
                "Get pet advice from \(.applicationName)",
                "Ask the pet expert in \(.applicationName)"
            ],
            shortTitle: "Ask Pet Expert",
            systemImageName: "pawprint.circle"
        )

        AppShortcut(
            intent: AddPetMemoryIntent(),
            phrases: [
                "Add a pet memory to \(.applicationName)",
                "Save a pet moment in \(.applicationName)",
                "Create memory in \(.applicationName)"
            ],
            shortTitle: "Add Memory",
            systemImageName: "camera.circle"
        )

        AppShortcut(
            intent: CheckVaccinationStatusIntent(),
            phrases: [
                "Check vaccination status in \(.applicationName)",
                "Show pet vaccinations in \(.applicationName)",
                "Vaccination reminder in \(.applicationName)"
            ],
            shortTitle: "Check Vaccinations",
            systemImageName: "syringe.circle"
        )

        AppShortcut(
            intent: EmergencyVetContactIntent(),
            phrases: [
                "Emergency vet contact in \(.applicationName)",
                "Call emergency vet from \(.applicationName)",
                "Pet emergency in \(.applicationName)"
            ],
            shortTitle: "Emergency Vet",
            systemImageName: "phone.circle.fill"
        )

        // Visual Intelligence Shortcuts
        AppShortcut(
            intent: IdentifyPetBreedIntent(),
            phrases: [
                "Identify pet breed with \(.applicationName)",
                "What breed is this pet in \(.applicationName)",
                "Analyze pet breed in \(.applicationName)"
            ],
            shortTitle: "Identify Breed",
            systemImageName: "eye.circle"
        )

        AppShortcut(
            intent: AnalyzePetHealthIntent(),
            phrases: [
                "Analyze pet health with \(.applicationName)",
                "Check pet health in \(.applicationName)",
                "Visual health check in \(.applicationName)"
            ],
            shortTitle: "Health Analysis",
            systemImageName: "heart.circle"
        )

        AppShortcut(
            intent: SearchPetProductsIntent(),
            phrases: [
                "Find pet products with \(.applicationName)",
                "Search for pet items in \(.applicationName)",
                "Identify pet products in \(.applicationName)"
            ],
            shortTitle: "Find Products",
            systemImageName: "bag.circle"
        )
        
        AppShortcut(
            intent: PlanWalkIntent(),
            phrases: [
                "Plan a walk in \(.applicationName)",
                "Check walk weather in \(.applicationName)",
                "Schedule pet walk in \(.applicationName)"
            ],
            shortTitle: "Plan Walk",
            systemImageName: "figure.walk.circle"
        )
    }
}

// MARK: - Ask Pet Expert Intent

@available(iOS 18.0, *)
struct AskPetExpertIntent: AppIntent {
    static var title: LocalizedStringResource = "Ask Pet Expert"
    static var description = IntentDescription("Get expert advice for your pet from specialized AI agents")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Question", description: "What would you like to ask about your pet?")
    var question: String
    
    @Parameter(title: "Expert Type", description: "Which expert would you like to consult?")
    var expertType: PetExpertType
    
    @Parameter(title: "Pet", description: "Which pet is this about?", optionsProvider: PetOptionsProvider())
    var pet: PetEntity?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Ask \(\.$expertType) about \(\.$question) for \(\.$pet)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let aiService = await EnhancedAIAgentService.shared
        let appleIntelligence = await AppleIntelligenceService.shared
        
        // Get AI response
        let agentType = expertType.toAIAgentType()
        // Find the agent and send message
        let agents = await aiService.availableAgents
        let agent = agents.first { $0.specialty.lowercased() == agentType.rawValue.lowercased() } ?? agents.first ?? AIAgent.fallbackAgent
        let response = try await aiService.sendMessage(to: agent, message: question, pet: nil)
        
        // Enhance with Apple Intelligence if available
        let enhancedResponse = await withCheckedContinuation { continuation in
            if #available(iOS 18.0, *) {
                appleIntelligence.enhanceAIAgentResponse(response, agentType: agentType) { enhanced in
                    continuation.resume(returning: enhanced)
                }
            } else {
                continuation.resume(returning: response)
            }
        }
        
        return .result(
            dialog: IntentDialog("Here's what the \(expertType.displayName) says: \(enhancedResponse)"),
            view: PetExpertResponseView(
                expertType: expertType,
                question: question,
                response: enhancedResponse,
                pet: pet
            )
        )
    }
}

// MARK: - Add Pet Memory Intent

@available(iOS 18.0, *)
struct AddPetMemoryIntent: AppIntent {
    static var title: LocalizedStringResource = "Add Pet Memory"
    static var description = IntentDescription("Quickly add a new memory for your pet")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Memory Title", description: "What's this memory about?")
    var title: String
    
    @Parameter(title: "Description", description: "Describe this special moment")
    var description: String?
    
    @Parameter(title: "Pet", description: "Which pet is this memory for?", optionsProvider: PetOptionsProvider())
    var pet: PetEntity
    
    static var parameterSummary: some ParameterSummary {
        Summary("Add memory '\(\.$title)' for \(\.$pet)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let memoryService = await ProductionMemoryService.shared
        let appleIntelligence = await AppleIntelligenceService.shared
        
        // Enhance description with Apple Intelligence if available
        let enhancedDescription = await withCheckedContinuation { continuation in
            if let desc = description, #available(iOS 18.0, *) {
                appleIntelligence.enhanceMemoryDescription(desc) { enhanced in
                    continuation.resume(returning: enhanced)
                }
            } else {
                continuation.resume(returning: description ?? "")
            }
        }
        
        // Create memory
        let memory = Memory(
            title: title,
            content: enhancedDescription,
            type: .text,
            mediaURL: nil,
            thumbnailURL: nil,
            duration: nil,
            milestone: nil,
            sentiment: "positive",
            tags: [],
            isPublic: false,
            isFavorite: false
        )
        
        try await memoryService.saveMemory(memory)
        
        return .result(dialog: IntentDialog("Memory '\(title)' has been added for \(pet.name)! 🐾"))
    }
}

// MARK: - Check Vaccination Status Intent

@available(iOS 18.0, *)
struct CheckVaccinationStatusIntent: AppIntent {
    static var title: LocalizedStringResource = "Check Vaccination Status"
    static var description = IntentDescription("Check upcoming vaccinations and health status")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Pet", description: "Which pet to check?", optionsProvider: PetOptionsProvider())
    var pet: PetEntity?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Check vaccination status for \(\.$pet)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        // Mock vaccination data - in real app, fetch from database
        let upcomingVaccinations = [
            VaccinationRecord(
                vaccineName: "Bordetella",
                dateAdministered: Date(),
                nextDueDate: Calendar.current.date(byAdding: .day, value: 15, to: Date()),
                veterinarian: "Dr. Smith",
                clinic: "Pet Clinic",
                lotNumber: "LOT123",
                notes: "Annual booster"
            ),
            VaccinationRecord(
                vaccineName: "Flea & Tick Prevention",
                dateAdministered: Date(),
                nextDueDate: Calendar.current.date(byAdding: .day, value: 30, to: Date()),
                veterinarian: "Dr. Johnson",
                clinic: "Pet Clinic",
                lotNumber: "LOT456",
                notes: "Monthly prevention"
            )
        ]
        
        let appleIntelligence = await AppleIntelligenceService.shared
        
        // Generate summary with Apple Intelligence
        let summary = await withCheckedContinuation { continuation in
            if #available(iOS 18.0, *) {
                appleIntelligence.summarizeVaccinationHistory(upcomingVaccinations.map { $0.vaccineName }) { summary in
                    continuation.resume(returning: summary)
                }
            } else {
                continuation.resume(returning: "Vaccination status check complete")
            }
        }
        
        let petName = pet?.name ?? "your pet"
        let urgentCount = upcomingVaccinations.filter {
            guard let nextDueDate = $0.nextDueDate else { return false }
            return Calendar.current.dateComponents([.day], from: Date(), to: nextDueDate).day ?? 0 <= 7
        }.count
        
        let dialogText = urgentCount > 0 
            ? "\(petName) has \(urgentCount) vaccination(s) due soon! \(summary)"
            : "\(petName) is up to date with vaccinations. \(summary)"
        
        return .result(
            dialog: IntentDialog(stringLiteral: dialogText),
            view: VaccinationStatusView(
                pet: pet,
                upcomingVaccinations: upcomingVaccinations,
                summary: summary
            )
        )
    }
}

// MARK: - Emergency Vet Contact Intent

@available(iOS 18.0, *)
struct EmergencyVetContactIntent: AppIntent {
    static var title: LocalizedStringResource = "Emergency Vet Contact"
    static var description = IntentDescription("Quickly access emergency veterinary contacts")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Emergency Type", description: "What type of emergency?")
    var emergencyType: IntentEmergencyType?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Get emergency vet contact for \(\.$emergencyType)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let emergencyContacts = [
            EmergencyContact(
                id: UUID().uuidString,
                name: "24/7 Pet Emergency",
                phoneNumber: "555-PET-HELP",
                type: .emergency,
                country: "US",
                isEditable: false,
                description: "24/7 emergency pet services"
            ),
            EmergencyContact(
                id: UUID().uuidString,
                name: "Poison Control",
                phoneNumber: "555-POISON",
                type: .poisonControl,
                country: "US",
                isEditable: false,
                description: "Pet poison control hotline"
            ),
            EmergencyContact(
                id: UUID().uuidString,
                name: "Local Emergency Vet",
                phoneNumber: "555-VET-911",
                type: .veterinarian,
                country: "US",
                isEditable: false,
                description: "Local emergency veterinary services"
            )
        ]
        
        let relevantContact = emergencyContacts.first { contact in
            guard emergencyType != nil else { return true }
            return contact.type == .emergency || contact.type == .veterinarian
        } ?? emergencyContacts.first ?? EmergencyContact.fallbackContact
        
        return .result(
            dialog: IntentDialog("Emergency contact: \(relevantContact.name) at \(relevantContact.phoneNumber). Stay calm and call immediately if needed."),
            view: Text("Emergency: \(relevantContact.name) - \(relevantContact.phoneNumber)")
        )
    }
}

// MARK: - Plan Walk Intent

@available(iOS 18.0, *)
struct PlanWalkIntent: AppIntent {
    static var title: LocalizedStringResource = "Plan Pet Walk"
    static var description = IntentDescription("Check weather and plan the perfect walk for your pet")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Pet", description: "Which pet to walk?", optionsProvider: PetOptionsProvider())
    var pet: PetEntity?
    
    @Parameter(title: "Walk Duration", description: "How long should the walk be?")
    var duration: WalkDuration?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Plan a \(\.$duration) walk for \(\.$pet)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        _ = await PetPlannerService.shared
        let walkPlan = IntentWalkPlan(
            isRecommended: true,
            weatherDescription: "Perfect weather for walking",
            temperature: "72°F",
            recommendations: ["Bring water", "Use leash", "Watch for traffic"]
        )
        
        let petName = pet?.name ?? "your pet"
        let recommendation = walkPlan.isRecommended 
            ? "Perfect weather for a walk with \(petName)! \(walkPlan.weatherDescription)"
            : "Consider a shorter indoor activity for \(petName). \(walkPlan.weatherDescription)"
        
        return .result(
            dialog: IntentDialog(stringLiteral: recommendation),
            view: WalkPlanView(
                pet: pet,
                walkPlan: walkPlan,
                duration: duration
            )
        )
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
enum PetExpertType: String, CaseIterable, AppEnum {
    case healthAndEmergency = "health"
    case nutrition = "nutrition"
    case training = "training"
    case grooming = "grooming"
    case shopping = "shopping"
    case petMaster = "general"
    case insurance = "insurance"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Pet Expert Type")
    }
    
    static var caseDisplayRepresentations: [PetExpertType: DisplayRepresentation] {
        [
            .healthAndEmergency: DisplayRepresentation(title: "Health & Emergency Expert", subtitle: "Medical advice and emergency care"),
            .nutrition: DisplayRepresentation(title: "Dr. Nutrition", subtitle: "Diet and nutrition specialist"),
            .training: DisplayRepresentation(title: "Trainer Pro", subtitle: "Behavior and training expert"),
            .grooming: DisplayRepresentation(title: "Style Guru", subtitle: "Grooming and styling specialist"),
            .shopping: DisplayRepresentation(title: "Shopping Assistant", subtitle: "Product recommendations"),
            .petMaster: DisplayRepresentation(title: "Pet Master", subtitle: "General pet care guidance"),
            .insurance: DisplayRepresentation(title: "Insurance Advisor", subtitle: "Pet insurance guidance")
        ]
    }
    
    var displayName: String {
        switch self {
        case .healthAndEmergency: return "Health & Emergency Expert"
        case .nutrition: return "Dr. Nutrition"
        case .training: return "Trainer Pro"
        case .grooming: return "Style Guru"
        case .shopping: return "Shopping Assistant"
        case .petMaster: return "Pet Master"
        case .insurance: return "Insurance Advisor"
        }
    }
    
    func toAIAgentType() -> AIAgentType {
        switch self {
        case .healthAndEmergency: return .healthAndEmergency
        case .nutrition: return .nutrition
        case .training: return .training
        case .grooming: return .grooming
        case .shopping: return .shopping
        case .petMaster: return .petMaster
        case .insurance: return .insurance
        }
    }
}

@available(iOS 18.0, *)
enum IntentEmergencyType: String, CaseIterable, AppEnum {
    case general = "general"
    case poison = "poison"
    case injury = "injury"
    case breathing = "breathing"
    case seizure = "seizure"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Emergency Type")
    }
    
    static var caseDisplayRepresentations: [IntentEmergencyType: DisplayRepresentation] {
        [
            .general: DisplayRepresentation(title: "General Emergency"),
            .poison: DisplayRepresentation(title: "Poisoning"),
            .injury: DisplayRepresentation(title: "Injury"),
            .breathing: DisplayRepresentation(title: "Breathing Problems"),
            .seizure: DisplayRepresentation(title: "Seizure")
        ]
    }
}

@available(iOS 18.0, *)
enum WalkDuration: String, CaseIterable, AppEnum {
    case short = "short"
    case medium = "medium"
    case long = "long"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Walk Duration")
    }
    
    static var caseDisplayRepresentations: [WalkDuration: DisplayRepresentation] {
        [
            .short: DisplayRepresentation(title: "Short Walk", subtitle: "15-20 minutes"),
            .medium: DisplayRepresentation(title: "Medium Walk", subtitle: "30-45 minutes"),
            .long: DisplayRepresentation(title: "Long Walk", subtitle: "60+ minutes")
        ]
    }
    
    var minutes: Int {
        switch self {
        case .short: return 20
        case .medium: return 35
        case .long: return 60
        }
    }
}

// MARK: - Pet Options Provider

@available(iOS 18.0, *)
struct PetOptionsProvider: DynamicOptionsProvider {
    func results() async throws -> [PetEntity] {
        let petService = await RealDataService()
        let pets = await MainActor.run { petService.pets }
        
        // Extract data first to avoid Sendable conformance issues
        let petData = pets.map { (id: "\($0.persistentModelID)", name: $0.name, species: $0.species, breed: $0.breed, age: $0.age, weight: $0.weight) }
        
        return petData.map { petInfo in
            PetEntity(
                id: petInfo.id,
                name: petInfo.name,
                species: petInfo.species,
                breed: petInfo.breed,
                age: petInfo.age,
                weight: petInfo.weight
            )
        }
    }
}

// MARK: - Supporting Models (using existing types from EmergencyContactsService)

struct IntentWalkPlan {
    let isRecommended: Bool
    let weatherDescription: String
    let temperature: String
    let recommendations: [String]
}

// MARK: - Placeholder Views for AppIntents

@available(iOS 18.0, *)
struct PetExpertResponseView: View {
    let expertType: PetExpertType
    let question: String
    let response: String
    let pet: PetEntity?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(expertType.displayName)
                .font(.headline)
                .foregroundColor(.blue)

            Text("Q: \(question)")
                .font(.subheadline)
                .fontWeight(.medium)

            Text("A: \(response)")
                .font(.body)

            if let pet = pet {
                Text("For: \(pet.name)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct VaccinationStatusView: View {
    let pet: PetEntity?
    let upcomingVaccinations: [VaccinationRecord]
    let summary: String

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Vaccination Status")
                .font(.headline)

            if let pet = pet {
                Text("Pet: \(pet.name)")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }

            Text(summary)
                .font(.body)

            Text("\(upcomingVaccinations.count) upcoming vaccinations")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct EmergencyContactView: View {
    let contact: IntentsEmergencyContact
    let emergencyType: IntentEmergencyType?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Emergency Contact")
                .font(.headline)
                .foregroundColor(.red)

            Text(contact.name)
                .font(.subheadline)
                .fontWeight(.medium)

            Text(contact.phoneNumber)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.blue)

            if let emergencyType = emergencyType {
                Text("Emergency: \(emergencyType.rawValue)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
    }
}

@available(iOS 18.0, *)
struct WalkPlanView: View {
    let pet: PetEntity?
    let walkPlan: IntentWalkPlan
    let duration: WalkDuration?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Walk Plan")
                .font(.headline)

            if let pet = pet {
                Text("Pet: \(pet.name)")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }

            if let duration = duration {
                Text("Duration: \(duration.rawValue)")
                    .font(.subheadline)
            }

            Text(walkPlan.weatherDescription)
                .font(.body)

            Text("Temperature: \(walkPlan.temperature)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
}

// MARK: - Phase 3: Enhanced Siri Shortcuts Integration

@available(iOS 18.0, *)
struct VoiceCarePetIntent: AppIntent {
    static var title: LocalizedStringResource = "Voice Pet Care"
    static var description = IntentDescription("Ask your AI pet care assistant anything using natural voice commands")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Pet")
    var pet: PetEntity?
    
    @Parameter(title: "Voice Command")
    var voiceCommand: String
    
    func perform() async throws -> some IntentResult & ReturnsValue<String> {
        let aiService = await EnhancedAppleIntelligenceService.shared
        
        // Simplified voice command response since processVoiceCommand was removed
        return .result(value: "Pet voice command received: \(voiceCommand)")
    }
}

@available(iOS 18.0, *)
struct QuickHealthCheckIntent: AppIntent {
    static var title: LocalizedStringResource = "Quick Health Check"
    static var description = IntentDescription("Get instant health insights about your pet")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Pet")
    var pet: PetEntity?
    
    func perform() async throws -> some IntentResult & ReturnsValue<String> {
        guard let pet = pet else {
            return .result(value: "Please select a pet for the health check.")
        }
        
        let aiService = await EnhancedAppleIntelligenceService.shared
        let healthAgent = AIAgent.healthGuardian
        
        let healthPrompt = """
        Provide a quick health status check for \(pet.name). 
        Consider their recent activity, age (\(pet.age) years), and any patterns.
        Give a brief, voice-friendly response suitable for Siri.
        """
        
        let response = await aiService.sendMessage(
            to: healthAgent,
            message: healthPrompt,
            pet: nil // PetEntity conversion would require database lookup
        )
        
        return .result(value: response)
    }
}

@available(iOS 18.0, *)
struct SpatialAudioTrainingIntent: AppIntent {
    static var title: LocalizedStringResource = "Spatial Audio Training"
    static var description = IntentDescription("Start 3D audio-guided training session")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Pet")
    var pet: PetEntity?
    
    @Parameter(title: "Training Command", default: "sit")
    var trainingCommand: String
    
    func perform() async throws -> some IntentResult & ReturnsValue<String> {
        guard let pet = pet else {
            return .result(value: "Please select a pet for training.")
        }
        
        // Note: SpatialAudioTrainingService references removed for Apple-only build
        // TODO: Re-implement with pure Apple services
        return .result(value: "Spatial audio training requires full pet data. Please use the app directly for \(pet.name).")
    }
}

@available(iOS 18.0, *)
struct AppleWatchPetStatusIntent: AppIntent {
    static var title: LocalizedStringResource = "Pet Status on Watch"
    static var description = IntentDescription("Check your pet's status from Apple Watch")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Pet")
    var pet: PetEntity?
    
    func perform() async throws -> some IntentResult & ReturnsValue<String> {
        guard let pet = pet else {
            return .result(value: "No pet selected.")
        }
        
        _ = await AppleWatchPetService.shared
        // Note: We would need to convert PetEntity back to Pet with database lookup
        // For now, return a simple status based on PetEntity data
        return .result(value: "Status for \(pet.name): Monitoring from Apple Watch. Use the app for detailed health data.")
    }
}
