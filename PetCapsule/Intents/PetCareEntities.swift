//
//  PetCareEntities.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import Foundation
import AppIntents

// MARK: - Appointment Type Entity

@available(iOS 18.0, *)
enum AppointmentTypeEntity: String, CaseIterable, AppEnum {
    case checkup = "checkup"
    case vaccination = "vaccination"
    case emergency = "emergency"
    case surgery = "surgery"
    case dental = "dental"
    case grooming = "grooming"
    case consultation = "consultation"
    case followUp = "follow_up"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Appointment Type")
    }
    
    static var caseDisplayRepresentations: [Self: DisplayRepresentation] {
        [
            .checkup: DisplayRepresentation(title: "Health Checkup", subtitle: "Routine health examination"),
            .vaccination: DisplayRepresentation(title: "Vaccination", subtitle: "Vaccination appointment"),
            .emergency: DisplayRepresentation(title: "Emergency", subtitle: "Emergency veterinary visit"),
            .surgery: DisplayRepresentation(title: "Surgery", subtitle: "Surgical procedure"),
            .dental: DisplayRepresentation(title: "Dental Care", subtitle: "Dental examination or cleaning"),
            .grooming: DisplayRepresentation(title: "Grooming", subtitle: "Professional grooming session"),
            .consultation: DisplayRepresentation(title: "Consultation", subtitle: "Veterinary consultation"),
            .followUp: DisplayRepresentation(title: "Follow-up", subtitle: "Follow-up appointment")
        ]
    }
    
    var displayName: String {
        switch self {
        case .checkup: return "Health Checkup"
        case .vaccination: return "Vaccination"
        case .emergency: return "Emergency Visit"
        case .surgery: return "Surgery"
        case .dental: return "Dental Care"
        case .grooming: return "Grooming"
        case .consultation: return "Consultation"
        case .followUp: return "Follow-up"
        }
    }
}

// MARK: - Reminder Type Entity

@available(iOS 18.0, *)
enum ReminderTypeEntity: String, CaseIterable, AppEnum {
    case feeding = "feeding"
    case medication = "medication"
    case exercise = "exercise"
    case grooming = "grooming"
    case training = "training"
    case playtime = "playtime"
    case bathroom = "bathroom"
    case supplement = "supplement"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Reminder Type")
    }
    
    static var caseDisplayRepresentations: [Self: DisplayRepresentation] {
        [
            .feeding: DisplayRepresentation(title: "Feeding", subtitle: "Meal time reminders"),
            .medication: DisplayRepresentation(title: "Medication", subtitle: "Medicine administration"),
            .exercise: DisplayRepresentation(title: "Exercise", subtitle: "Physical activity time"),
            .grooming: DisplayRepresentation(title: "Grooming", subtitle: "Grooming and hygiene"),
            .training: DisplayRepresentation(title: "Training", subtitle: "Training sessions"),
            .playtime: DisplayRepresentation(title: "Playtime", subtitle: "Interactive play time"),
            .bathroom: DisplayRepresentation(title: "Bathroom", subtitle: "Bathroom breaks"),
            .supplement: DisplayRepresentation(title: "Supplement", subtitle: "Vitamin or supplement time")
        ]
    }
    
    var displayName: String {
        switch self {
        case .feeding: return "Feeding"
        case .medication: return "Medication"
        case .exercise: return "Exercise"
        case .grooming: return "Grooming"
        case .training: return "Training"
        case .playtime: return "Playtime"
        case .bathroom: return "Bathroom Break"
        case .supplement: return "Supplement"
        }
    }
}

// MARK: - Reminder Frequency Entity

@available(iOS 18.0, *)
enum ReminderFrequencyEntity: String, CaseIterable, AppEnum {
    case daily = "daily"
    case twiceDaily = "twice_daily"
    case weekly = "weekly"
    case monthly = "monthly"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Reminder Frequency")
    }
    
    static var caseDisplayRepresentations: [Self: DisplayRepresentation] {
        [
            .daily: DisplayRepresentation(title: "Daily", subtitle: "Once per day"),
            .twiceDaily: DisplayRepresentation(title: "Twice Daily", subtitle: "Morning and evening"),
            .weekly: DisplayRepresentation(title: "Weekly", subtitle: "Once per week"),
            .monthly: DisplayRepresentation(title: "Monthly", subtitle: "Once per month")
        ]
    }
    
    var displayName: String {
        switch self {
        case .daily: return "Daily"
        case .twiceDaily: return "Twice Daily"
        case .weekly: return "Weekly"
        case .monthly: return "Monthly"
        }
    }
}

// MARK: - Health Data Type Entity

@available(iOS 18.0, *)
enum HealthDataTypeEntity: String, CaseIterable, AppEnum {
    case weight = "weight"
    case temperature = "temperature"
    case medication = "medication"
    case symptoms = "symptoms"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Health Data Type")
    }
    
    static var caseDisplayRepresentations: [Self: DisplayRepresentation] {
        [
            .weight: DisplayRepresentation(title: "Weight", subtitle: "Body weight measurement"),
            .temperature: DisplayRepresentation(title: "Temperature", subtitle: "Body temperature"),
            .medication: DisplayRepresentation(title: "Medication", subtitle: "Medicine administration"),
            .symptoms: DisplayRepresentation(title: "Symptoms", subtitle: "Health symptoms or concerns")
        ]
    }
    
    var displayName: String {
        switch self {
        case .weight: return "Weight"
        case .temperature: return "Temperature"
        case .medication: return "Medication"
        case .symptoms: return "Symptoms"
        }
    }
}

// MARK: - Pet Data Topic Entity

@available(iOS 18.0, *)
enum PetDataTopicEntity: String, CaseIterable, AppEnum {
    case health = "health"
    case appointments = "appointments"
    case behavior = "behavior"
    case memories = "memories"
    case care = "care"
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Pet Data Topic")
    }
    
    static var caseDisplayRepresentations: [Self: DisplayRepresentation] {
        [
            .health: DisplayRepresentation(title: "Health", subtitle: "Health records and status"),
            .appointments: DisplayRepresentation(title: "Appointments", subtitle: "Scheduled appointments"),
            .behavior: DisplayRepresentation(title: "Behavior", subtitle: "Behavior and training"),
            .memories: DisplayRepresentation(title: "Memories", subtitle: "Photos and milestones"),
            .care: DisplayRepresentation(title: "Daily Care", subtitle: "Feeding and care routines")
        ]
    }
    
    var displayName: String {
        switch self {
        case .health: return "Health"
        case .appointments: return "Appointments"
        case .behavior: return "Behavior"
        case .memories: return "Memories"
        case .care: return "Daily Care"
        }
    }
}

// MARK: - Pet Data Discussion View

@available(iOS 18.0, *)
struct PetDataDiscussionView: View {
    let pet: AIPetEntity
    let topic: PetDataTopicEntity
    let initialData: [String: Any]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            Image(systemName: "pawprint.fill")
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 50, height: 50)
                .clipShape(Circle())
                
                VStack(alignment: .leading) {
                    Text(pet.name)
                        .font(.headline)
                    Text("Discussing \(topic.displayName)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            // Topic-specific content
            switch topic {
            case .health:
                healthSummaryView
            case .appointments:
                appointmentsView
            case .behavior:
                behaviorView
            case .memories:
                memoriesView
            case .care:
                careView
            }
            
            // Action buttons
            HStack {
                Button("Open Full Chat") {
                    // Open the full AI chat interface
                }
                .buttonStyle(.borderedProminent)
                
                Spacer()
                
                Button("View Details") {
                    // Open detailed view for this topic
                }
                .buttonStyle(.bordered)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    @ViewBuilder
    private var healthSummaryView: some View {
        if let healthSummary = initialData["healthSummary"] as? PetHealthSummary {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Health Score")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(Int(healthSummary.overallHealthScore * 100))%")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(healthSummary.overallHealthScore > 0.8 ? .green : .orange)
                }
                
                if !healthSummary.healthAlerts.isEmpty {
                    Text("\(healthSummary.healthAlerts.count) health alerts")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
            }
        }
    }
    
    @ViewBuilder
    private var appointmentsView: some View {
        if let appointments = initialData["appointments"] as? [PetAppointment] {
            VStack(alignment: .leading, spacing: 8) {
                Text("Upcoming Appointments")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                if appointments.isEmpty {
                    Text("No upcoming appointments")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else {
                    ForEach(appointments.prefix(3), id: \.id) { appointment in
                        HStack {
                            Text(appointment.title)
                                .font(.caption)
                            Spacer()
                            Text(appointment.scheduledDate, style: .date)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    private var behaviorView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Recent Behavior Notes")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text("Training progress and behavior patterns")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    @ViewBuilder
    private var memoriesView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Recent Memories")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text("Photos, videos, and milestones")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    @ViewBuilder
    private var careView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Daily Care Schedule")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text("Feeding, medication, and care routines")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

import SwiftUI
