//
//  PetCareIntents.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import Foundation
import AppIntents

// MARK: - Create Pet Appointment Intent

@available(iOS 18.0, *)
struct CreatePetAppointmentIntent: AppIntent {
    static var title: LocalizedStringResource = "Schedule Pet Appointment"
    static var description = IntentDescription("Schedule a veterinary or grooming appointment for your pet")
    
    @Parameter(title: "Pet", description: "Which pet is the appointment for?")
    var pet: AIPetEntity
    
    @Parameter(title: "Appointment Type", description: "What type of appointment?")
    var appointmentType: AppointmentTypeEntity
    
    @Parameter(title: "Date and Time", description: "When should the appointment be scheduled?")
    var scheduledDate: Date
    
    @Parameter(title: "Location", description: "Where is the appointment?")
    var location: String?
    
    @Parameter(title: "Notes", description: "Any additional notes for the appointment?")
    var notes: String?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Schedule a \(\.$appointmentType) appointment for \(\.$pet) on \(\.$scheduledDate)") {
            \.$location
            \.$notes
        }
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let _ = await AppleIntelligenceDataService.shared
        
        // Create the appointment
        let appointment = PetAppointment(
            id: UUID(),
            petId: pet.id,
            appointmentType: appointmentType.rawValue,
            title: "\(appointmentType.displayName) for \(pet.name)",
            description: notes,
            scheduledDate: scheduledDate,
            duration: 3600, // 1 hour default
            location: AppointmentLocation(
                name: location ?? "TBD",
                address: nil,
                phoneNumber: nil,
                coordinates: nil
            ),
            status: "scheduled",
            reminders: [],
            notes: notes
        )
        
        // Save to data manager
        await saveAppointment(appointment)
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .short
        
        return .result(
            dialog: "I've scheduled a \(appointmentType.displayName.lowercased()) appointment for \(pet.name) on \(dateFormatter.string(from: scheduledDate))"
        )
    }
    
    private func saveAppointment(_ appointment: PetAppointment) async {
        // Integration with existing PetDataManager
        let dataManager = await PetDataManager.shared

        // Convert to existing Appointment model and save
        let pets = await MainActor.run { dataManager.pets }
        if pets.first(where: { $0.id == appointment.petId.uuidString }) != nil {
            // TODO: Save appointment to pet's appointment list
            // TODO: Implement appointment saving
            /*
            let newAppointment = PetAppointment(
                id: appointment.id,
                title: appointment.title,
                description: appointment.description ?? "",
                date: appointment.scheduledDate,
                type: appointment.appointmentType.rawValue,
                location: appointment.location.name,
                address: appointment.location.address,
                phoneNumber: appointment.location.phoneNumber,
                status: appointment.status.rawValue,
                duration: appointment.duration,
                notes: appointment.notes
            )
            */

            // TODO: Implement appointment saving when Pet model supports appointments
        }
    }
}

// MARK: - Add Pet Reminder Intent

@available(iOS 18.0, *)
struct AddPetReminderIntent: AppIntent {
    static var title: LocalizedStringResource = "Add Pet Reminder"
    static var description = IntentDescription("Set up a reminder for pet care activities")
    
    @Parameter(title: "Pet", description: "Which pet is this reminder for?")
    var pet: AIPetEntity
    
    @Parameter(title: "Reminder Type", description: "What type of reminder?")
    var reminderType: ReminderTypeEntity
    
    @Parameter(title: "Frequency", description: "How often should this reminder occur?")
    var frequency: ReminderFrequencyEntity
    
    @Parameter(title: "Start Date", description: "When should the reminders start?")
    var startDate: Date?
    
    @Parameter(title: "Notes", description: "Any additional details for the reminder?")
    var notes: String?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Set up \(\.$frequency) \(\.$reminderType) reminders for \(\.$pet)") {
            \.$startDate
            \.$notes
        }
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let careSchedule = PetCareSchedule(
            petId: pet.id,
            scheduleType: CareScheduleType(rawValue: reminderType.rawValue) ?? .feeding,
            title: "\(reminderType.displayName) for \(pet.name)",
            description: notes,
            frequency: mapFrequency(frequency),
            scheduledTimes: generateScheduledTimes(for: frequency, startDate: startDate ?? Date())
        )
        
        await saveCareSchedule(careSchedule)
        
        return .result(
            dialog: "I've set up \(frequency.displayName.lowercased()) \(reminderType.displayName.lowercased()) reminders for \(pet.name)"
        )
    }
    
    private func mapFrequency(_ frequency: ReminderFrequencyEntity) -> CareFrequency {
        switch frequency {
        case .daily:
            return .daily(times: 1)
        case .twiceDaily:
            return .daily(times: 2)
        case .weekly:
            return .weekly(days: [1]) // Monday
        case .monthly:
            return .monthly(day: 1)
        }
    }
    
    private func generateScheduledTimes(for frequency: ReminderFrequencyEntity, startDate: Date) -> [Date] {
        var times: [Date] = []
        let calendar = Calendar.current
        
        switch frequency {
        case .daily, .twiceDaily:
            // Generate times for today
            if let morning = calendar.date(bySettingHour: 8, minute: 0, second: 0, of: startDate) {
                times.append(morning)
            }
            if frequency == .twiceDaily,
               let evening = calendar.date(bySettingHour: 18, minute: 0, second: 0, of: startDate) {
                times.append(evening)
            }
        case .weekly:
            // Generate time for next Monday
            if let nextMonday = calendar.nextDate(after: startDate, matching: DateComponents(weekday: 2), matchingPolicy: .nextTime) {
                times.append(nextMonday)
            }
        case .monthly:
            // Generate time for next month
            if let nextMonth = calendar.date(byAdding: .month, value: 1, to: startDate) {
                times.append(nextMonth)
            }
        }
        
        return times
    }
    
    private func saveCareSchedule(_ schedule: PetCareSchedule) async {
        // Integration with existing data management
        // This would integrate with your existing reminder/schedule system
    }
}

// MARK: - Log Pet Health Data Intent

@available(iOS 18.0, *)
struct LogPetHealthDataIntent: AppIntent {
    static var title: LocalizedStringResource = "Log Pet Health Data"
    static var description = IntentDescription("Record health information for your pet")
    
    @Parameter(title: "Pet", description: "Which pet is this health data for?")
    var pet: AIPetEntity
    
    @Parameter(title: "Health Data Type", description: "What type of health data are you logging?")
    var healthDataType: HealthDataTypeEntity
    
    @Parameter(title: "Value", description: "The health data value (e.g., weight, temperature)")
    var value: String
    
    @Parameter(title: "Notes", description: "Any additional notes about this health data?")
    var notes: String?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Log \(\.$healthDataType) of \(\.$value) for \(\.$pet)") {
            \.$notes
        }
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let healthRecord = PetHealthRecord(
            petId: pet.id,
            recordType: mapHealthDataType(healthDataType),
            title: "\(healthDataType.displayName) - \(value)",
            description: notes ?? "",
            date: Date(),
            tags: [value, healthDataType.rawValue]
        )
        
        await saveHealthRecord(healthRecord)
        
        return .result(
            dialog: "I've logged \(pet.name)'s \(healthDataType.displayName.lowercased()) as \(value)"
        )
    }
    
    private func mapHealthDataType(_ type: HealthDataTypeEntity) -> HealthRecordType {
        switch type {
        case .weight:
            return .weightCheck
        case .temperature:
            return .checkup
        case .medication:
            return .medication
        case .symptoms:
            return .illness
        }
    }
    
    private func saveHealthRecord(_ record: PetHealthRecord) async {
        // Integration with existing health record system
        let dataManager = await PetDataManager.shared

        let pets = await MainActor.run { dataManager.pets }
        if let pet = pets.first(where: { $0.id == record.petId.uuidString }) {
            let _ = HealthRecord(
                id: record.id,
                petId: UUID(uuidString: pet.id) ?? UUID(),
                type: SimpleHealthRecordType.examination,
                value: 1.0,
                unit: "record",
                notes: record.description,
                date: record.date
            )

            // TODO: Add health record to pet when model supports it
            // pet.healthRecords.append(newRecord)
            // dataManager.updatePet(pet)
        }
    }
}

// MARK: - Discuss Pet Data Intent

@available(iOS 18.0, *)
struct DiscussPetDataIntent: AppIntent {
    static var title: LocalizedStringResource = "Discuss Pet Data"
    static var description = IntentDescription("Start a conversation about your pet's data with an AI agent")
    
    @Parameter(title: "Pet", description: "Which pet would you like to discuss?")
    var pet: AIPetEntity
    
    @Parameter(title: "Topic", description: "What aspect of your pet's data would you like to discuss?")
    var topic: PetDataTopicEntity
    
    @Parameter(title: "Specific Question", description: "Any specific question you have?")
    var question: String?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Discuss \(\.$pet)'s \(\.$topic)") {
            \.$question
        }
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let _ = await AppleIntelligenceDataService.shared
        
        // Get relevant data based on topic
        let contextData = await getContextualData(for: pet.id, topic: topic)
        
        // Generate initial AI response
        let initialResponse = generateInitialResponse(for: pet, topic: topic, question: question, data: contextData)
        
        return .result(
            dialog: IntentDialog(stringLiteral: initialResponse),
            view: PetDataDiscussionView(pet: pet, topic: topic, initialData: contextData)
        )
    }
    
    private func getContextualData(for petId: UUID, topic: PetDataTopicEntity) async -> [String: Any] {
        let dataService = await AppleIntelligenceDataService.shared
        var data: [String: Any] = [:]

        switch topic {
        case .health:
            let healthSummary = await dataService.getPetHealthSummary(for: petId)
            data["healthSummary"] = healthSummary
        case .appointments:
            let appointments = await dataService.getUpcomingAppointments(for: petId)
            data["appointments"] = appointments
        case .behavior:
            let behaviorNotes = await dataService.getBehaviorNotes(for: petId)
            data["behaviorNotes"] = behaviorNotes
        case .memories:
            let memories = await dataService.getRecentMemories(for: petId)
            data["memories"] = memories
        case .care:
            let feedingSchedule = await dataService.getFeedingSchedule(for: petId)
            let medicationSchedule = await dataService.getMedicationSchedule(for: petId)
            data["feedingSchedule"] = feedingSchedule
            data["medicationSchedule"] = medicationSchedule
        }
        
        return data
    }
    
    private func generateInitialResponse(for pet: AIPetEntity, topic: PetDataTopicEntity, question: String?, data: [String: Any]) -> String {
        var response = "Let's discuss \(pet.name)'s \(topic.displayName.lowercased()). "
        
        // Add context-specific information
        switch topic {
        case .health:
            if let healthSummary = data["healthSummary"] as? PetHealthSummary {
                response += "Their current health score is \(Int(healthSummary.overallHealthScore * 100))%. "
                if !healthSummary.healthAlerts.isEmpty {
                    response += "I notice there are \(healthSummary.healthAlerts.count) health alerts to discuss. "
                }
            }
        case .appointments:
            if let appointments = data["appointments"] as? [PetAppointment] {
                if appointments.isEmpty {
                    response += "There are no upcoming appointments scheduled. "
                } else {
                    response += "There are \(appointments.count) upcoming appointments. "
                }
            }
        case .behavior:
            response += "I can help you analyze behavior patterns and training progress. "
        case .memories:
            response += "I can help you review recent memories and milestones. "
        case .care:
            response += "I can discuss feeding schedules, medication, and daily care routines. "
        }
        
        if let question = question {
            response += "You asked: '\(question)'. Let me help you with that."
        } else {
            response += "What would you like to know?"
        }
        
        return response
    }
}
