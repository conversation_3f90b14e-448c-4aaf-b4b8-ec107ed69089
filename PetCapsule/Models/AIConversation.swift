//
//  AIConversation.swift
//  PetCapsule
//
//  AI Conversation and Message Models
//  🤖 Models for AI agent conversation history
//

import Foundation

// MARK: - AI Conversation Models

struct AIConversation: Identifiable, Codable {
    let id: UUID
    let userId: UUID?
    let agentId: UUID
    let petId: UUID?
    var title: String
    var lastMessageAt: Date
    var messageCount: Int
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date
    let agent: AIAgentInfo?
    
    init?(from dbConversation: DatabaseAIConversation) {
        self.id = dbConversation.id
        self.userId = dbConversation.userId
        self.agentId = dbConversation.agentId
        self.petId = dbConversation.petId
        self.title = dbConversation.title ?? "Conversation"
        self.lastMessageAt = dbConversation.lastMessageAt ?? Date()
        self.messageCount = dbConversation.messageCount ?? 0
        self.isActive = dbConversation.isActive ?? true
        self.createdAt = dbConversation.createdAt ?? Date()
        self.updatedAt = dbConversation.updatedAt ?? Date()

        if let dbAgent = dbConversation.agent {
            self.agent = AIAgentInfo(from: dbAgent)
        } else {
            self.agent = nil
        }
    }

    init(
        id: UUID,
        agentId: UUID,
        agentName: String,
        petId: UUID? = nil,
        title: String? = nil,
        lastMessageAt: Date? = nil,
        messageCount: Int? = nil,
        isActive: Bool? = nil,
        createdAt: Date? = nil,
        updatedAt: Date? = nil
    ) {
        self.id = id
        self.userId = nil // Will be set by the service
        self.agentId = agentId
        self.petId = petId
        self.title = title ?? "New Conversation"
        self.lastMessageAt = lastMessageAt ?? Date()
        self.messageCount = messageCount ?? 0
        self.isActive = isActive ?? true
        self.createdAt = createdAt ?? Date()
        self.updatedAt = updatedAt ?? Date()

        // Create a basic agent info
        self.agent = AIAgentInfo(
            id: agentId,
            name: agentName,
            description: nil,
            specialization: nil,
            avatarUrl: nil
        )
    }
}

struct AIAgentInfo: Codable {
    let id: UUID
    let name: String
    let description: String?
    let specialization: String?
    let avatarUrl: String?
    
    init(from dbAgent: DatabaseAIAgent) {
        self.id = dbAgent.id
        self.name = dbAgent.name
        self.description = dbAgent.description
        self.specialization = dbAgent.specialization
        self.avatarUrl = dbAgent.avatarUrl
    }

    init(id: UUID, name: String, description: String?, specialization: String?, avatarUrl: String?) {
        self.id = id
        self.name = name
        self.description = description
        self.specialization = specialization
        self.avatarUrl = avatarUrl
    }
}

struct AIMessage: Identifiable, Codable {
    let id: UUID
    let conversationId: UUID
    let content: String
    let isFromUser: Bool
    let messageType: String
    let metadata: [String: Any]?
    let createdAt: Date
    
    init(from dbMessage: DatabaseAIMessage) {
        self.id = dbMessage.id
        self.conversationId = dbMessage.conversationId
        self.content = dbMessage.content
        self.isFromUser = dbMessage.isFromUser
        self.messageType = dbMessage.messageType ?? "text"
        self.metadata = dbMessage.metadata
        self.createdAt = dbMessage.createdAt ?? Date()
    }
    
    // For creating new messages locally
    init(
        conversationId: UUID,
        content: String,
        isFromUser: Bool,
        messageType: String = "text",
        metadata: [String: Any]? = nil
    ) {
        self.id = UUID()
        self.conversationId = conversationId
        self.content = content
        self.isFromUser = isFromUser
        self.messageType = messageType
        self.metadata = metadata
        self.createdAt = Date()
    }
    
    // Custom encoding to handle metadata
    enum CodingKeys: String, CodingKey {
        case id, conversationId, content, isFromUser, messageType, createdAt
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(conversationId, forKey: .conversationId)
        try container.encode(content, forKey: .content)
        try container.encode(isFromUser, forKey: .isFromUser)
        try container.encode(messageType, forKey: .messageType)
        try container.encode(createdAt, forKey: .createdAt)
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        conversationId = try container.decode(UUID.self, forKey: .conversationId)
        content = try container.decode(String.self, forKey: .content)
        isFromUser = try container.decode(Bool.self, forKey: .isFromUser)
        messageType = try container.decode(String.self, forKey: .messageType)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        metadata = nil // Will be handled separately if needed
    }
}

// MARK: - Conversation Extensions

extension AIConversation {
    var formattedLastMessageTime: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: lastMessageAt, relativeTo: Date())
    }
    
    var agentName: String {
        return agent?.name ?? "AI Agent"
    }
    
    var agentSpecialization: String {
        return agent?.specialization?.capitalized ?? "General"
    }
    
    var displayTitle: String {
        if title.isEmpty || title == "Conversation" || title == "New Conversation" {
            return "\(agentName) Chat"
        }
        return title
    }
}

extension AIMessage {
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    var isRecent: Bool {
        Date().timeIntervalSince(createdAt) < 300 // 5 minutes
    }
}

// MARK: - Helper Extensions

extension Array where Element == AIMessage {
    func toChatMessages() -> [ChatMessage] {
        return self.map { aiMessage in
            ChatMessage(
                content: aiMessage.content,
                isFromUser: aiMessage.isFromUser,
                agentId: nil // Will be set by the conversation context
            )
        }
    }
}

extension Array where Element == ChatMessage {
    func toAIMessages(conversationId: UUID) -> [AIMessage] {
        return self.map { chatMessage in
            AIMessage(
                conversationId: conversationId,
                content: chatMessage.content,
                isFromUser: chatMessage.isFromUser
            )
        }
    }
}
