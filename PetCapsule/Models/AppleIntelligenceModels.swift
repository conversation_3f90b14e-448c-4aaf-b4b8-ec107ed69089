//
//  AppleIntelligenceModels.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import Foundation
import AppIntents

// Type aliases to resolve ambiguity - EmergencyContact is defined in EmergencyContactsService.swift
// typealias AIEmergencyContact = EmergencyContact

// MARK: - Pet Data Models for Apple Intelligence Integration

/// Core pet entity that Apple Intelligence can understand and reference
@available(iOS 18.0, *)
struct AIPetEntity: AppEntity {
    let id: UUID
    let name: String
    let species: String
    let breed: String?
    let age: Int
    let weight: Double?
    let healthScore: Double
    let profileImageURL: String?
    
    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Pet")
    }
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(
            title: "\(name)",
            subtitle: "\(species.capitalized) • \(breed ?? "Mixed")"
        )
    }
    
    static var defaultQuery = AIPetEntityQuery()
}

/// Query handler for pet entities
@available(iOS 18.0, *)
struct AIPetEntityQuery: EntityQuery {
    func entities(for identifiers: [UUID]) async throws -> [AIPetEntity] {
        let pets = await MainActor.run { PetDataManager.shared.pets }
        return pets.compactMap { pet in
            guard let petUUID = UUID(uuidString: pet.id), identifiers.contains(petUUID) else { return nil }
            return AIPetEntity(
                id: UUID(uuidString: pet.id) ?? UUID(),
                name: pet.name,
                species: pet.species,
                breed: pet.breed,
                age: pet.age,
                weight: pet.weight,
                healthScore: pet.healthScore,
                profileImageURL: pet.profileImageURL
            )
        }
    }
    
    func entities(matching string: String) async throws -> [AIPetEntity] {
        let pets = await MainActor.run { PetDataManager.shared.pets }
        return pets.compactMap { pet in
            guard pet.name.localizedCaseInsensitiveContains(string) ||
                  pet.species.localizedCaseInsensitiveContains(string) ||
                  pet.breed?.localizedCaseInsensitiveContains(string) == true else { return nil }
            return AIPetEntity(
                id: UUID(uuidString: pet.id) ?? UUID(),
                name: pet.name,
                species: pet.species,
                breed: pet.breed,
                age: pet.age,
                weight: pet.weight,
                healthScore: pet.healthScore,
                profileImageURL: pet.profileImageURL
            )
        }
    }
    
    func suggestedEntities() async throws -> [AIPetEntity] {
        let pets = await MainActor.run { PetDataManager.shared.pets }
        return pets.map { pet in
            AIPetEntity(
                id: UUID(uuidString: pet.id) ?? UUID(),
                name: pet.name,
                species: pet.species,
                breed: pet.breed,
                age: pet.age,
                weight: pet.weight,
                healthScore: pet.healthScore,
                profileImageURL: pet.profileImageURL
            )
        }
    }
}

// MARK: - Health Data Models

/// Health record entity for AI access
@available(iOS 18.0, *)
struct PetHealthRecord: Codable, Identifiable, Sendable {
    let id: UUID
    let petId: UUID
    let recordType: HealthRecordType
    let title: String
    let description: String
    let date: Date
    let vetName: String?
    let severity: HealthSeverity
    let tags: [String]
    let attachments: [String] // URLs to documents/images
    
    init(id: UUID = UUID(), petId: UUID, recordType: HealthRecordType, title: String, description: String, date: Date, vetName: String? = nil, severity: HealthSeverity = .normal, tags: [String] = [], attachments: [String] = []) {
        self.id = id
        self.petId = petId
        self.recordType = recordType
        self.title = title
        self.description = description
        self.date = date
        self.vetName = vetName
        self.severity = severity
        self.tags = tags
        self.attachments = attachments
    }
}

enum HealthRecordType: String, CaseIterable, Codable {
    case vaccination = "vaccination"
    case checkup = "checkup"
    case illness = "illness"
    case injury = "injury"
    case surgery = "surgery"
    case medication = "medication"
    case labResults = "lab_results"
    case weightCheck = "weight_check"
    case dentalCare = "dental_care"
    case emergency = "emergency"
    
    var displayName: String {
        switch self {
        case .vaccination: return "Vaccination"
        case .checkup: return "Checkup"
        case .illness: return "Illness"
        case .injury: return "Injury"
        case .surgery: return "Surgery"
        case .medication: return "Medication"
        case .labResults: return "Lab Results"
        case .weightCheck: return "Weight Check"
        case .dentalCare: return "Dental Care"
        case .emergency: return "Emergency"
        }
    }
}

enum HealthSeverity: String, CaseIterable, Codable {
    case normal = "normal"
    case minor = "minor"
    case moderate = "moderate"
    case serious = "serious"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .normal: return "Normal"
        case .minor: return "Minor"
        case .moderate: return "Moderate"
        case .serious: return "Serious"
        case .critical: return "Critical"
        }
    }
}

// MARK: - Appointment Data Models

/// Appointment entity for AI access
@available(iOS 18.0, *)
struct PetAppointment: Codable, Identifiable, Sendable {
    let id: UUID
    let petId: UUID
    let appointmentType: String // Simplified to avoid type conflicts
    let title: String
    let description: String?
    let scheduledDate: Date
    let duration: TimeInterval
    let location: AppointmentLocation
    let status: String // Simplified to avoid type conflicts
    let reminders: [AppointmentReminder]
    let notes: String?
    
    init(id: UUID = UUID(), petId: UUID, appointmentType: String, title: String, description: String? = nil, scheduledDate: Date, duration: TimeInterval = 3600, location: AppointmentLocation, status: String = "scheduled", reminders: [AppointmentReminder] = [], notes: String? = nil) {
        self.id = id
        self.petId = petId
        self.appointmentType = appointmentType
        self.title = title
        self.description = description
        self.scheduledDate = scheduledDate
        self.duration = duration
        self.location = location
        self.status = status
        self.reminders = reminders
        self.notes = notes
    }
}

// AppointmentType is defined in ComprehensiveHealthMonitoringService.swift to avoid duplication

struct AppointmentLocation: Codable {
    let name: String
    let address: String?
    let phoneNumber: String?
    let coordinates: LocationCoordinates?
}

struct LocationCoordinates: Codable {
    let latitude: Double
    let longitude: Double
}

// AppointmentStatus is defined in ComprehensiveHealthMonitoringService.swift to avoid duplication

struct AppointmentReminder: Codable, Identifiable {
    let id: UUID
    let timeBeforeAppointment: TimeInterval
    let message: String
    let isEnabled: Bool
    
    init(id: UUID = UUID(), timeBeforeAppointment: TimeInterval, message: String, isEnabled: Bool = true) {
        self.id = id
        self.timeBeforeAppointment = timeBeforeAppointment
        self.message = message
        self.isEnabled = isEnabled
    }
}

// MARK: - Care Schedule Data Models

/// Daily care schedule for AI access
@available(iOS 18.0, *)
struct PetCareSchedule: Codable, Identifiable, Sendable {
    let id: UUID
    let petId: UUID
    let scheduleType: CareScheduleType
    let title: String
    let description: String?
    let frequency: CareFrequency
    let scheduledTimes: [Date]
    let isActive: Bool
    let lastCompleted: Date?
    let nextDue: Date?
    let tags: [String]
    
    init(id: UUID = UUID(), petId: UUID, scheduleType: CareScheduleType, title: String, description: String? = nil, frequency: CareFrequency, scheduledTimes: [Date] = [], isActive: Bool = true, lastCompleted: Date? = nil, nextDue: Date? = nil, tags: [String] = []) {
        self.id = id
        self.petId = petId
        self.scheduleType = scheduleType
        self.title = title
        self.description = description
        self.frequency = frequency
        self.scheduledTimes = scheduledTimes
        self.isActive = isActive
        self.lastCompleted = lastCompleted
        self.nextDue = nextDue
        self.tags = tags
    }
}

enum CareScheduleType: String, CaseIterable, Codable {
    case feeding = "feeding"
    case medication = "medication"
    case exercise = "exercise"
    case grooming = "grooming"
    case training = "training"
    case playtime = "playtime"
    case bathroom = "bathroom"
    case supplement = "supplement"
    
    var displayName: String {
        switch self {
        case .feeding: return "Feeding"
        case .medication: return "Medication"
        case .exercise: return "Exercise"
        case .grooming: return "Grooming"
        case .training: return "Training"
        case .playtime: return "Playtime"
        case .bathroom: return "Bathroom Break"
        case .supplement: return "Supplement"
        }
    }
}

enum CareFrequency: Codable {
    case daily(times: Int)
    case weekly(days: [Int]) // 0 = Sunday, 1 = Monday, etc.
    case monthly(day: Int)
    case custom(interval: TimeInterval)
    
    var displayName: String {
        switch self {
        case .daily(let times):
            return times == 1 ? "Daily" : "\(times) times daily"
        case .weekly(let days):
            return "Weekly on \(days.count) days"
        case .monthly(let day):
            return "Monthly on day \(day)"
        case .custom(let interval):
            let hours = Int(interval / 3600)
            return "Every \(hours) hours"
        }
    }
}

// MARK: - Behavior and Training Data Models

/// Behavior tracking for AI analysis
@available(iOS 18.0, *)
struct PetBehaviorRecord: Codable, Identifiable, Sendable {
    let id: UUID
    let petId: UUID
    let behaviorType: String // Simplified to avoid type conflicts
    let title: String
    let description: String
    let date: Date
    let severity: BehaviorSeverity
    let context: BehaviorContext
    let triggers: [String]
    let interventions: [String]
    let outcome: BehaviorOutcome?
    let tags: [String]

    init(id: UUID = UUID(), petId: UUID, behaviorType: String, title: String, description: String, date: Date, severity: BehaviorSeverity = .normal, context: BehaviorContext, triggers: [String] = [], interventions: [String] = [], outcome: BehaviorOutcome? = nil, tags: [String] = []) {
        self.id = id
        self.petId = petId
        self.behaviorType = behaviorType
        self.title = title
        self.description = description
        self.date = date
        self.severity = severity
        self.context = context
        self.triggers = triggers
        self.interventions = interventions
        self.outcome = outcome
        self.tags = tags
    }
}

// BehaviorType is defined in ActivityTrackingService.swift to avoid duplication

enum BehaviorSeverity: String, CaseIterable, Codable {
    case normal = "normal"
    case mild = "mild"
    case moderate = "moderate"
    case severe = "severe"

    var displayName: String {
        switch self {
        case .normal: return "Normal"
        case .mild: return "Mild"
        case .moderate: return "Moderate"
        case .severe: return "Severe"
        }
    }
}

struct BehaviorContext: Codable {
    let location: String
    let timeOfDay: String
    let peoplePresent: [String]
    let otherPetsPresent: [String]
    let environmentalFactors: [String]
}

enum BehaviorOutcome: String, CaseIterable, Codable {
    case improved = "improved"
    case unchanged = "unchanged"
    case worsened = "worsened"
    case resolved = "resolved"

    var displayName: String {
        switch self {
        case .improved: return "Improved"
        case .unchanged: return "Unchanged"
        case .worsened: return "Worsened"
        case .resolved: return "Resolved"
        }
    }
}

// MARK: - Memory and Milestone Data Models

/// Pet memory/milestone for AI access
@available(iOS 18.0, *)
struct PetMemory: Codable, Identifiable, Sendable {
    let id: UUID
    let petId: UUID
    let memoryType: AIMemoryType
    let title: String
    let description: String?
    let date: Date
    let location: String?
    let mediaURLs: [String] // Photos, videos
    let tags: [String]
    let milestone: Bool
    let shareStatus: ShareStatus
    let emotionalTone: EmotionalTone

    init(id: UUID = UUID(), petId: UUID, memoryType: AIMemoryType, title: String, description: String? = nil, date: Date, location: String? = nil, mediaURLs: [String] = [], tags: [String] = [], milestone: Bool = false, shareStatus: ShareStatus = .private, emotionalTone: EmotionalTone = .happy) {
        self.id = id
        self.petId = petId
        self.memoryType = memoryType
        self.title = title
        self.description = description
        self.date = date
        self.location = location
        self.mediaURLs = mediaURLs
        self.tags = tags
        self.milestone = milestone
        self.shareStatus = shareStatus
        self.emotionalTone = emotionalTone
    }
}

enum AIMemoryType: String, CaseIterable, Codable {
    case photo = "photo"
    case video = "video"
    case achievement = "achievement"
    case firstTime = "first_time"
    case funny = "funny"
    case adventure = "adventure"
    case training = "training"
    case health = "health"
    case social = "social"
    case daily = "daily"

    var displayName: String {
        switch self {
        case .photo: return "Photo Memory"
        case .video: return "Video Memory"
        case .achievement: return "Achievement"
        case .firstTime: return "First Time"
        case .funny: return "Funny Moment"
        case .adventure: return "Adventure"
        case .training: return "Training Success"
        case .health: return "Health Milestone"
        case .social: return "Social Moment"
        case .daily: return "Daily Life"
        }
    }
}

enum ShareStatus: String, CaseIterable, Codable {
    case `private` = "private"
    case family = "family"
    case friends = "friends"
    case `public` = "public"

    var displayName: String {
        switch self {
        case .`private`: return "Private"
        case .family: return "Family Only"
        case .friends: return "Friends"
        case .`public`: return "Public"
        }
    }
}

enum EmotionalTone: String, CaseIterable, Codable {
    case happy = "happy"
    case excited = "excited"
    case calm = "calm"
    case proud = "proud"
    case concerned = "concerned"
    case sad = "sad"
    case funny = "funny"
    case loving = "loving"

    var displayName: String {
        switch self {
        case .happy: return "Happy"
        case .excited: return "Excited"
        case .calm: return "Calm"
        case .proud: return "Proud"
        case .concerned: return "Concerned"
        case .sad: return "Sad"
        case .funny: return "Funny"
        case .loving: return "Loving"
        }
    }
}

// MARK: - Emergency Contact Data Models

/// Emergency contact information for AI access
@available(iOS 18.0, *)
struct AIEmergencyContact: Codable, Identifiable {
    let id: UUID
    let petId: UUID?
    let contactType: EmergencyContactType
    let name: String
    let phoneNumber: String
    let address: String?
    let email: String?
    let availability: ContactAvailability
    let specialties: [String]
    let notes: String?
    let isPrimary: Bool

    init(id: UUID = UUID(), petId: UUID? = nil, contactType: EmergencyContactType, name: String, phoneNumber: String, address: String? = nil, email: String? = nil, availability: ContactAvailability = .always, specialties: [String] = [], notes: String? = nil, isPrimary: Bool = false) {
        self.id = id
        self.petId = petId
        self.contactType = contactType
        self.name = name
        self.phoneNumber = phoneNumber
        self.address = address
        self.email = email
        self.availability = availability
        self.specialties = specialties
        self.notes = notes
        self.isPrimary = isPrimary
    }
}

// EmergencyContactType is defined in EmergencyContactsService.swift to avoid duplication

struct ContactAvailability: Codable {
    let isAlwaysAvailable: Bool
    let availableHours: [DayAvailability]
    let emergencyOnly: Bool

    init(isAlwaysAvailable: Bool = false, availableHours: [DayAvailability] = [], emergencyOnly: Bool = false) {
        self.isAlwaysAvailable = isAlwaysAvailable
        self.availableHours = availableHours
        self.emergencyOnly = emergencyOnly
    }

    static let always = ContactAvailability(isAlwaysAvailable: true)
    static let emergencyOnly = ContactAvailability(emergencyOnly: true)
}

struct DayAvailability: Codable {
    let dayOfWeek: Int // 0 = Sunday, 1 = Monday, etc.
    let startTime: Date
    let endTime: Date
}
