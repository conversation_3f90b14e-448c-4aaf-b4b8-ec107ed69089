//
//  ChatMessage.swift
//  PetCapsule
//
//  Enhanced chat message model for AI conversations with image support
//

import Foundation
import UIKit

struct ChatMessage: Identifiable, Codable {
    let id: UUID
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let agentId: UUID?
    let image: UIImage?
    
    init(content: String, isFromUser: Bool, timestamp: Date? = nil, agentId: UUID? = nil, image: UIImage? = nil) {
        self.id = UUID()
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = timestamp ?? Date()
        self.agentId = agentId
        self.image = image
    }
    
    // Custom coding to handle UIImage
    enum CodingKeys: String, CodingKey {
        case id, content, isFromUser, timestamp, agentId, imageData
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        content = try container.decode(String.self, forKey: .content)
        isFromUser = try container.decode(Bool.self, forKey: .isFromUser)
        timestamp = try container.decode(Date.self, forKey: .timestamp)
        agentId = try container.decodeIfPresent(UUID.self, forKey: .agentId)
        
        if let imageData = try container.decodeIfPresent(Data.self, forKey: .imageData) {
            image = UIImage(data: imageData)
        } else {
            image = nil
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(content, forKey: .content)
        try container.encode(isFromUser, forKey: .isFromUser)
        try container.encode(timestamp, forKey: .timestamp)
        try container.encodeIfPresent(agentId, forKey: .agentId)
        
        if let image = image, let imageData = image.jpegData(compressionQuality: 0.8) {
            try container.encode(imageData, forKey: .imageData)
        }
    }
}
