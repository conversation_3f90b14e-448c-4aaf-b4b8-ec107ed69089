//
//  DatabaseModels.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Database User Model

struct DatabaseUser: Codable {
    let id: UUID
    let email: String
    let fullName: String?
    let avatarUrl: String?
    let subscriptionTier: String?
    let subscriptionExpiresAt: Date?
    let totalPets: Int?
    let totalMemories: Int?
    let onboardingCompleted: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case fullName = "full_name"
        case avatarUrl = "avatar_url"
        case subscriptionTier = "subscription_tier"
        case subscriptionExpiresAt = "subscription_expires_at"
        case totalPets = "total_pets"
        case totalMemories = "total_memories"
        case onboardingCompleted = "onboarding_completed"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    init(id: UUID, email: String, fullName: String?, subscriptionTier: String? = "free", onboardingCompleted: Bool? = false) {
        self.id = id
        self.email = email
        self.fullName = fullName
        self.avatarUrl = nil
        self.subscriptionTier = subscriptionTier
        self.subscriptionExpiresAt = nil
        self.totalPets = 0
        self.totalMemories = 0
        self.onboardingCompleted = onboardingCompleted
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Database Pet Model

struct DatabasePet: Codable {
    let id: UUID
    let userId: UUID
    let name: String
    let species: String
    let breed: String?
    let birthDate: Date?
    let adoptionDate: Date?
    let weight: Double?
    let activityLevel: String?
    let personalityTraits: [String]?
    let healthConditions: [String]?
    let medications: [String]?
    let vaccinations: [String]?
    let healthAlerts: [String]?
    let aiRecommendations: [String]?
    let profileImageUrl: String?
    let isActive: Bool?
    let lastCheckupDate: Date?
    let createdAt: Date?
    let updatedAt: Date?
    
    // Comprehensive new fields
    let gender: String?
    let isSpayedNeutered: Bool?
    let microchipId: String?
    let currentFood: String?
    let feedingSchedule: String?
    let waterIntakeML: Int?
    let dietaryRestrictions: [String]?
    let exerciseMinutesDaily: Int?
    let walkingFrequency: String?
    let favoriteActivities: [String]?
    let trainingLevel: String?
    let knownCommands: [String]?
    let behaviorIssues: [String]?
    let socialBehavior: String?
    let veterinarianInfo: String? // JSON encoded VeterinarianInfo
    let insuranceInfo: String? // JSON encoded InsuranceInfo
    let emergencyContacts: String? // JSON encoded [EmergencyContact]

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case name
        case species
        case breed
        case birthDate = "birth_date"
        case adoptionDate = "adoption_date"
        case weight
        case activityLevel = "activity_level"
        case personalityTraits = "personality_traits"
        case healthConditions = "health_conditions"
        case medications
        case vaccinations
        case healthAlerts = "health_alerts"
        case aiRecommendations = "ai_recommendations"
        case profileImageUrl = "profile_image_url"
        case isActive = "is_active"
        case lastCheckupDate = "last_checkup_date"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Comprehensive new fields
        case gender
        case isSpayedNeutered = "is_spayed_neutered"
        case microchipId = "microchip_id"
        case currentFood = "current_food"
        case feedingSchedule = "feeding_schedule"
        case waterIntakeML = "water_intake_ml"
        case dietaryRestrictions = "dietary_restrictions"
        case exerciseMinutesDaily = "exercise_minutes_daily"
        case walkingFrequency = "walking_frequency"
        case favoriteActivities = "favorite_activities"
        case trainingLevel = "training_level"
        case knownCommands = "known_commands"
        case behaviorIssues = "behavior_issues"
        case socialBehavior = "social_behavior"
        case veterinarianInfo = "veterinarian_info"
        case insuranceInfo = "insurance_info"
        case emergencyContacts = "emergency_contacts"
    }

    init(id: UUID, userId: UUID, name: String, species: String, breed: String?, birthDate: Date?, adoptionDate: Date?, weight: Double?, activityLevel: String?, personalityTraits: [String]?, healthConditions: [String]?, medications: [String]?, vaccinations: [String]?, healthAlerts: [String]?, aiRecommendations: [String]?, profileImageUrl: String?, lastCheckupDate: Date?, gender: String? = nil, isSpayedNeutered: Bool? = nil, microchipId: String? = nil, currentFood: String? = nil, feedingSchedule: String? = nil, waterIntakeML: Int? = nil, dietaryRestrictions: [String]? = nil, exerciseMinutesDaily: Int? = nil, walkingFrequency: String? = nil, favoriteActivities: [String]? = nil, trainingLevel: String? = nil, knownCommands: [String]? = nil, behaviorIssues: [String]? = nil, socialBehavior: String? = nil, veterinarianInfo: String? = nil, insuranceInfo: String? = nil, emergencyContacts: String? = nil) {
        self.id = id
        self.userId = userId
        self.name = name
        self.species = species
        self.breed = breed
        self.birthDate = birthDate
        self.adoptionDate = adoptionDate
        self.weight = weight
        self.activityLevel = activityLevel
        self.personalityTraits = personalityTraits
        self.healthConditions = healthConditions
        self.medications = medications
        self.vaccinations = vaccinations
        self.healthAlerts = healthAlerts
        self.aiRecommendations = aiRecommendations
        self.profileImageUrl = profileImageUrl
        self.isActive = true
        self.lastCheckupDate = lastCheckupDate
        self.createdAt = Date()
        self.updatedAt = Date()
        // Comprehensive new fields
        self.gender = gender
        self.isSpayedNeutered = isSpayedNeutered
        self.microchipId = microchipId
        self.currentFood = currentFood
        self.feedingSchedule = feedingSchedule
        self.waterIntakeML = waterIntakeML
        self.dietaryRestrictions = dietaryRestrictions
        self.exerciseMinutesDaily = exerciseMinutesDaily
        self.walkingFrequency = walkingFrequency
        self.favoriteActivities = favoriteActivities
        self.trainingLevel = trainingLevel
        self.knownCommands = knownCommands
        self.behaviorIssues = behaviorIssues
        self.socialBehavior = socialBehavior
        self.veterinarianInfo = veterinarianInfo
        self.insuranceInfo = insuranceInfo
        self.emergencyContacts = emergencyContacts
    }

    // Custom decoder to handle date strings from database
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(UUID.self, forKey: .id)
        userId = try container.decode(UUID.self, forKey: .userId)
        name = try container.decode(String.self, forKey: .name)
        species = try container.decode(String.self, forKey: .species)
        breed = try container.decodeIfPresent(String.self, forKey: .breed)

        // Handle birth_date string format
        if let birthDateString = try container.decodeIfPresent(String.self, forKey: .birthDate) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            formatter.locale = Locale(identifier: "en_US_POSIX")
            formatter.timeZone = TimeZone(secondsFromGMT: 0)
            birthDate = formatter.date(from: birthDateString)
        } else {
            birthDate = try container.decodeIfPresent(Date.self, forKey: .birthDate)
        }

        // Handle adoption_date string format
        if let adoptionDateString = try container.decodeIfPresent(String.self, forKey: .adoptionDate) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            formatter.locale = Locale(identifier: "en_US_POSIX")
            formatter.timeZone = TimeZone(secondsFromGMT: 0)
            adoptionDate = formatter.date(from: adoptionDateString)
        } else {
            adoptionDate = try container.decodeIfPresent(Date.self, forKey: .adoptionDate)
        }

        weight = try container.decodeIfPresent(Double.self, forKey: .weight)
        activityLevel = try container.decodeIfPresent(String.self, forKey: .activityLevel)
        personalityTraits = try container.decodeIfPresent([String].self, forKey: .personalityTraits)
        healthConditions = try container.decodeIfPresent([String].self, forKey: .healthConditions)
        medications = try container.decodeIfPresent([String].self, forKey: .medications)
        vaccinations = try container.decodeIfPresent([String].self, forKey: .vaccinations)
        healthAlerts = try container.decodeIfPresent([String].self, forKey: .healthAlerts)
        aiRecommendations = try container.decodeIfPresent([String].self, forKey: .aiRecommendations)
        profileImageUrl = try container.decodeIfPresent(String.self, forKey: .profileImageUrl)
        isActive = try container.decodeIfPresent(Bool.self, forKey: .isActive)

        // Handle last_checkup_date string format
        if let lastCheckupDateString = try container.decodeIfPresent(String.self, forKey: .lastCheckupDate) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            formatter.locale = Locale(identifier: "en_US_POSIX")
            formatter.timeZone = TimeZone(secondsFromGMT: 0)
            lastCheckupDate = formatter.date(from: lastCheckupDateString)
        } else {
            lastCheckupDate = try container.decodeIfPresent(Date.self, forKey: .lastCheckupDate)
        }

        // Handle created_at and updated_at timestamps
        if let createdAtString = try container.decodeIfPresent(String.self, forKey: .createdAt) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSSSSSXXXXX"
            formatter.locale = Locale(identifier: "en_US_POSIX")
            createdAt = formatter.date(from: createdAtString) ?? Date()
        } else {
            createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt) ?? Date()
        }

        if let updatedAtString = try container.decodeIfPresent(String.self, forKey: .updatedAt) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSSSSSXXXXX"
            formatter.locale = Locale(identifier: "en_US_POSIX")
            updatedAt = formatter.date(from: updatedAtString) ?? Date()
        } else {
            updatedAt = try container.decodeIfPresent(Date.self, forKey: .updatedAt) ?? Date()
        }
        
        // Decode comprehensive new fields
        gender = try container.decodeIfPresent(String.self, forKey: .gender)
        isSpayedNeutered = try container.decodeIfPresent(Bool.self, forKey: .isSpayedNeutered)
        microchipId = try container.decodeIfPresent(String.self, forKey: .microchipId)
        currentFood = try container.decodeIfPresent(String.self, forKey: .currentFood)
        feedingSchedule = try container.decodeIfPresent(String.self, forKey: .feedingSchedule)
        waterIntakeML = try container.decodeIfPresent(Int.self, forKey: .waterIntakeML)
        dietaryRestrictions = try container.decodeIfPresent([String].self, forKey: .dietaryRestrictions)
        exerciseMinutesDaily = try container.decodeIfPresent(Int.self, forKey: .exerciseMinutesDaily)
        walkingFrequency = try container.decodeIfPresent(String.self, forKey: .walkingFrequency)
        favoriteActivities = try container.decodeIfPresent([String].self, forKey: .favoriteActivities)
        trainingLevel = try container.decodeIfPresent(String.self, forKey: .trainingLevel)
        knownCommands = try container.decodeIfPresent([String].self, forKey: .knownCommands)
        behaviorIssues = try container.decodeIfPresent([String].self, forKey: .behaviorIssues)
        socialBehavior = try container.decodeIfPresent(String.self, forKey: .socialBehavior)
        veterinarianInfo = try container.decodeIfPresent(String.self, forKey: .veterinarianInfo)
        insuranceInfo = try container.decodeIfPresent(String.self, forKey: .insuranceInfo)
        // Handle emergency_contacts - can be either String (JSON) or Array from database
        // First try to decode as String, then handle errors if it's an array
        do {
            emergencyContacts = try container.decodeIfPresent(String.self, forKey: .emergencyContacts)
        } catch {
            // If String decoding fails, it might be an array - set to empty for now
            // This will be handled gracefully by the app
            print("⚠️ Emergency contacts field is not a string, skipping...")
            emergencyContacts = nil
        }
    }
}

// MARK: - Database Memory Model

struct DatabaseMemory: Codable {
    let id: UUID
    let petId: UUID
    let userId: UUID
    let title: String
    let content: String?
    let memoryType: String
    let mediaUrl: String?
    let thumbnailUrl: String?
    let duration: Double?
    let aiTags: [String]?
    let aiSentiment: String?
    let aiMilestone: String?
    let aiConfidence: Double?
    let isPublic: Bool?
    let isFavorite: Bool?
    let isFeatured: Bool?
    let likeCount: Int?
    let viewCount: Int?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case petId = "pet_id"
        case userId = "user_id"
        case title
        case content
        case memoryType = "memory_type"
        case mediaUrl = "media_url"
        case thumbnailUrl = "thumbnail_url"
        case duration
        case aiTags = "ai_tags"
        case aiSentiment = "ai_sentiment"
        case aiMilestone = "ai_milestone"
        case aiConfidence = "ai_confidence"
        case isPublic = "is_public"
        case isFavorite = "is_favorite"
        case isFeatured = "is_featured"
        case likeCount = "like_count"
        case viewCount = "view_count"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    init(id: UUID, petId: UUID, userId: UUID, title: String, content: String?, memoryType: String, mediaUrl: String?, thumbnailUrl: String?, duration: Double?, aiTags: [String]?, aiSentiment: String?, aiMilestone: String?, isPublic: Bool?, isFavorite: Bool? = false) {
        self.id = id
        self.petId = petId
        self.userId = userId
        self.title = title
        self.content = content
        self.memoryType = memoryType
        self.mediaUrl = mediaUrl
        self.thumbnailUrl = thumbnailUrl
        self.duration = duration
        self.aiTags = aiTags
        self.aiSentiment = aiSentiment
        self.aiMilestone = aiMilestone
        self.aiConfidence = nil
        self.isPublic = isPublic
        self.isFavorite = isFavorite
        self.isFeatured = false
        self.likeCount = 0
        self.viewCount = 0
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Database Memorial Garden Model

struct DatabaseMemorialGarden: Codable {
    let id: UUID
    let petId: UUID
    let userId: UUID
    let petName: String
    let petImageUrl: String?
    let dateOfPassing: Date?
    let memorialMessage: String?
    let theme: String?
    let isPublic: Bool?
    let tributeCount: Int?
    let visitCount: Int?
    let flowerCount: Int?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case petId = "pet_id"
        case userId = "user_id"
        case petName = "pet_name"
        case petImageUrl = "pet_image_url"
        case dateOfPassing = "date_of_passing"
        case memorialMessage = "memorial_message"
        case theme
        case isPublic = "is_public"
        case tributeCount = "tribute_count"
        case visitCount = "visit_count"
        case flowerCount = "flower_count"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Database Memorial Tribute Model

struct DatabaseMemorialTribute: Codable {
    let id: UUID
    let memorialId: UUID
    let authorId: UUID
    let message: String
    let isAnonymous: Bool?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case memorialId = "memorial_id"
        case authorId = "author_id"
        case message
        case isAnonymous = "is_anonymous"
        case createdAt = "created_at"
    }
}

// MARK: - Database Virtual Flower Model

struct DatabaseVirtualFlower: Codable {
    let id: UUID
    let memorialId: UUID
    let tributeId: UUID?
    let flowerType: String
    let flowerColor: String
    let message: String?
    let leftBy: UUID
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case memorialId = "memorial_id"
        case tributeId = "tribute_id"
        case flowerType = "flower_type"
        case flowerColor = "flower_color"
        case message
        case leftBy = "left_by"
        case createdAt = "created_at"
    }
}

// MARK: - Database Subscription Model

struct DatabaseSubscription: Codable {
    let id: UUID
    let userId: UUID
    let productId: String
    let transactionId: String?
    let originalTransactionId: String?
    let subscriptionTier: String
    let status: String
    let startsAt: Date
    let expiresAt: Date
    let autoRenew: Bool?
    let price: Double?
    let currency: String?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case productId = "product_id"
        case transactionId = "transaction_id"
        case originalTransactionId = "original_transaction_id"
        case subscriptionTier = "subscription_tier"
        case status
        case startsAt = "starts_at"
        case expiresAt = "expires_at"
        case autoRenew = "auto_renew"
        case price
        case currency
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Database Analytics Models

struct DatabaseUserAnalytics: Codable {
    let id: UUID
    let userId: UUID
    let eventType: String
    let eventData: String? // Changed to String to store JSON
    let sessionId: UUID?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case eventType = "event_type"
        case eventData = "event_data"
        case sessionId = "session_id"
        case createdAt = "created_at"
    }
}

struct DatabaseRevenueAnalytics: Codable {
    let id: UUID
    let userId: UUID
    let transactionId: String
    let productId: String
    let revenue: Double
    let currency: String?
    let subscriptionTier: String?
    let eventType: String?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case transactionId = "transaction_id"
        case productId = "product_id"
        case revenue
        case currency
        case subscriptionTier = "subscription_tier"
        case eventType = "event_type"
        case createdAt = "created_at"
    }
}

// MARK: - Database Chat Models

struct DatabaseChatChannel: Codable {
    let id: UUID
    let name: String
    let description: String?
    let channelType: String?
    let createdBy: UUID?
    let isActive: Bool?
    let memberCount: Int?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case description
        case channelType = "channel_type"
        case createdBy = "created_by"
        case isActive = "is_active"
        case memberCount = "member_count"
        case createdAt = "created_at"
    }
}

struct DatabaseChatMessage: Codable {
    let id: UUID
    let channelId: UUID
    let senderId: UUID
    let content: String
    let messageType: String?
    let replyTo: UUID?
    let isEdited: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case channelId = "channel_id"
        case senderId = "sender_id"
        case content
        case messageType = "message_type"
        case replyTo = "reply_to"
        case isEdited = "is_edited"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - AI Conversation Database Models

struct DatabaseAIAgent: Codable {
    let id: UUID
    let name: String
    let description: String?
    let specialization: String?
    let avatarUrl: String?
    let systemPrompt: String?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case description
        case specialization
        case avatarUrl = "avatar_url"
        case systemPrompt = "system_prompt"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct DatabaseAIConversation: Codable {
    let id: UUID
    let userId: UUID?
    let agentId: UUID
    let petId: UUID?
    let title: String?
    let lastMessageAt: Date?
    let messageCount: Int?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?
    let agent: DatabaseAIAgent?

    enum CodingKeys: String, CodingKey {
        case id
        case userId = "user_id"
        case agentId = "agent_id"
        case petId = "pet_id"
        case title
        case lastMessageAt = "last_message_at"
        case messageCount = "message_count"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case agent = "ai_agents"
    }
}

struct DatabaseAIConversationBasic: Codable {
    let id: UUID
    let agentId: UUID
    let petId: UUID?
    let title: String?
    let lastMessageAt: Date?
    let messageCount: Int?
    let isActive: Bool?
    let createdAt: Date?
    let updatedAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case agentId = "agent_id"
        case petId = "pet_id"
        case title
        case lastMessageAt = "last_message_at"
        case messageCount = "message_count"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct DatabaseAIConversationInsert: Codable {
    let userId: UUID
    let agentId: UUID
    let petId: UUID?
    let title: String

    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case agentId = "agent_id"
        case petId = "pet_id"
        case title
    }
}

struct DatabaseAIMessage: Codable {
    let id: UUID
    let conversationId: UUID
    let content: String
    let isFromUser: Bool
    let messageType: String?
    let metadata: [String: Any]?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id
        case conversationId = "conversation_id"
        case content
        case isFromUser = "is_from_user"
        case messageType = "message_type"
        case metadata
        case createdAt = "created_at"
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        conversationId = try container.decode(UUID.self, forKey: .conversationId)
        content = try container.decode(String.self, forKey: .content)
        isFromUser = try container.decode(Bool.self, forKey: .isFromUser)
        messageType = try container.decodeIfPresent(String.self, forKey: .messageType)
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt)

        // Handle JSONB metadata
        if let metadataData = try container.decodeIfPresent(Data.self, forKey: .metadata) {
            metadata = try JSONSerialization.jsonObject(with: metadataData) as? [String: Any]
        } else {
            metadata = nil
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(conversationId, forKey: .conversationId)
        try container.encode(content, forKey: .content)
        try container.encode(isFromUser, forKey: .isFromUser)
        try container.encodeIfPresent(messageType, forKey: .messageType)
        try container.encodeIfPresent(createdAt, forKey: .createdAt)

        if let metadata = metadata {
            let metadataData = try JSONSerialization.data(withJSONObject: metadata)
            try container.encode(metadataData, forKey: .metadata)
        }
    }
}

struct DatabaseAIMessageInsert: Codable {
    let conversationId: UUID
    let content: String
    let isFromUser: Bool
    let messageType: String
    let metadata: String?

    enum CodingKeys: String, CodingKey {
        case conversationId = "conversation_id"
        case content
        case isFromUser = "is_from_user"
        case messageType = "message_type"
        case metadata
    }

    init(conversationId: UUID, content: String, isFromUser: Bool, messageType: String = "text", metadata: [String: Any]? = nil) {
        self.conversationId = conversationId
        self.content = content
        self.isFromUser = isFromUser
        self.messageType = messageType

        if let metadata = metadata {
            if let jsonData = try? JSONSerialization.data(withJSONObject: metadata),
               let jsonString = String(data: jsonData, encoding: .utf8) {
                self.metadata = jsonString
            } else {
                self.metadata = nil
            }
        } else {
            self.metadata = nil
        }
    }
}
