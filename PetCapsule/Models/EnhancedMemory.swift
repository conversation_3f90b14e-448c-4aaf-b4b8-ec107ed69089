import Foundation
import SwiftUI

// MARK: - Enhanced Memory Model
@available(iOS 17.0, *)
struct EnhancedMemory: Identifiable, Codable {
    let id: String
    var title: String
    var content: String
    var mediaItems: [MemoryMediaItem]
    var thumbnailURL: String?
    var createdAt: Date
    var updatedAt: Date
    var petIds: [String]
    var location: MemoryLocation?
    var weather: MemoryWeather?
    var mood: MemoryMood
    var eventType: MemoryEventType
    var isSecureVault: Bool
    var isMemorial: Bool
    var tags: [String]
    var measurements: [PetMeasurement]?
    var voiceNoteURL: String?
    var isProtected: Bool
    var milestoneData: MilestoneData?
    var reminderDate: Date?
    var shareSettings: MemoryShareSettings
    
    init(
        id: String = UUID().uuidString,
        title: String,
        content: String,
        mediaItems: [MemoryMediaItem] = [],
        thumbnailURL: String? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        petIds: [String] = [],
        location: MemoryLocation? = nil,
        weather: MemoryWeather? = nil,
        mood: MemoryMood = .happy,
        eventType: MemoryEventType = .general,
        isSecureVault: Bool = false,
        isMemorial: Bool = false,
        tags: [String] = [],
        measurements: [PetMeasurement]? = nil,
        voiceNoteURL: String? = nil,
        isProtected: Bool = false,
        milestoneData: MilestoneData? = nil,
        reminderDate: Date? = nil,
        shareSettings: MemoryShareSettings = MemoryShareSettings()
    ) {
        self.id = id
        self.title = title
        self.content = content
        self.mediaItems = mediaItems
        self.thumbnailURL = thumbnailURL
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.petIds = petIds
        self.location = location
        self.weather = weather
        self.mood = mood
        self.eventType = eventType
        self.isSecureVault = isSecureVault
        self.isMemorial = isMemorial
        self.tags = tags
        self.measurements = measurements
        self.voiceNoteURL = voiceNoteURL
        self.isProtected = isProtected
        self.milestoneData = milestoneData
        self.reminderDate = reminderDate
        self.shareSettings = shareSettings
    }
    
    var primaryMediaItem: MemoryMediaItem? {
        mediaItems.first
    }
    
    var hasVideo: Bool {
        mediaItems.contains { $0.type == .video }
    }
    
    var hasMultipleMedia: Bool {
        mediaItems.count > 1
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: createdAt)
    }
    
    var ageCategory: MemoryAgeCategory {
        let daysSince = Calendar.current.dateComponents([.day], from: createdAt, to: Date()).day ?? 0
        
        if daysSince <= 7 { return .thisWeek }
        if daysSince <= 30 { return .thisMonth }
        if daysSince <= 90 { return .recent }
        if daysSince <= 365 { return .thisYear }
        return .older
    }
}

// MARK: - Memory Media Item
struct MemoryMediaItem: Identifiable, Codable {
    let id: String
    let url: String
    let thumbnailURL: String?
    let type: MediaType
    let duration: TimeInterval?
    let fileSize: Int64?
    let dimensions: MediaDimensions?
    let uploadedAt: Date
    let metadata: MediaMetadata?
    
    enum MediaType: String, Codable, CaseIterable {
        case photo = "photo"
        case video = "video"
        case audio = "audio"
        
        var systemImage: String {
            switch self {
            case .photo: return "photo"
            case .video: return "video"
            case .audio: return "waveform"
            }
        }
    }
    
    init(
        id: String = UUID().uuidString,
        url: String,
        thumbnailURL: String? = nil,
        type: MediaType,
        duration: TimeInterval? = nil,
        fileSize: Int64? = nil,
        dimensions: MediaDimensions? = nil,
        uploadedAt: Date = Date(),
        metadata: MediaMetadata? = nil
    ) {
        self.id = id
        self.url = url
        self.thumbnailURL = thumbnailURL
        self.type = type
        self.duration = duration
        self.fileSize = fileSize
        self.dimensions = dimensions
        self.uploadedAt = uploadedAt
        self.metadata = metadata
    }
}

// MARK: - Supporting Models
struct MediaDimensions: Codable {
    let width: Int
    let height: Int
    
    var aspectRatio: Double {
        Double(width) / Double(height)
    }
}

struct MediaMetadata: Codable {
    let cameraModel: String?
    let lensModel: String?
    let iso: Int?
    let aperture: Double?
    let shutterSpeed: String?
    let flashUsed: Bool?
    let gpsData: GPSData?
}

struct GPSData: Codable {
    let latitude: Double
    let longitude: Double
    let altitude: Double?
}

struct MemoryLocation: Codable {
    let name: String
    let address: String?
    let coordinates: Coordinates?
    let placeType: PlaceType
    
    enum PlaceType: String, Codable, CaseIterable {
        case home = "home"
        case park = "park"
        case beach = "beach"
        case vet = "vet"
        case groomer = "groomer"
        case training = "training"
        case other = "other"
        
        var icon: String {
            switch self {
            case .home: return "house.fill"
            case .park: return "tree.fill"
            case .beach: return "beach.umbrella.fill"
            case .vet: return "cross.case.fill"
            case .groomer: return "scissors"
            case .training: return "graduationcap.fill"
            case .other: return "location.fill"
            }
        }
    }
}

struct Coordinates: Codable {
    let latitude: Double
    let longitude: Double
}

struct MemoryWeather: Codable {
    let condition: WeatherCondition
    let temperature: Double
    let humidity: Int
    let description: String
    
    enum WeatherCondition: String, Codable, CaseIterable {
        case sunny = "sunny"
        case cloudy = "cloudy"
        case rainy = "rainy"
        case snowy = "snowy"
        case stormy = "stormy"
        case windy = "windy"
        
        var icon: String {
            switch self {
            case .sunny: return "sun.max.fill"
            case .cloudy: return "cloud.fill"
            case .rainy: return "cloud.rain.fill"
            case .snowy: return "cloud.snow.fill"
            case .stormy: return "cloud.bolt.fill"
            case .windy: return "wind"
            }
        }
    }
}

enum MemoryMood: String, Codable, CaseIterable {
    case ecstatic = "ecstatic"
    case happy = "happy"
    case content = "content"
    case calm = "calm"
    case excited = "excited"
    case playful = "playful"
    case sleepy = "sleepy"
    case curious = "curious"
    case proud = "proud"
    case nostalgic = "nostalgic"
    
    var emoji: String {
        switch self {
        case .ecstatic: return "🤩"
        case .happy: return "😊"
        case .content: return "😌"
        case .calm: return "😇"
        case .excited: return "🤗"
        case .playful: return "😄"
        case .sleepy: return "😴"
        case .curious: return "🤔"
        case .proud: return "🥰"
        case .nostalgic: return "🥲"
        }
    }
    
    var color: Color {
        switch self {
        case .ecstatic: return .purple
        case .happy: return .yellow
        case .content: return .green
        case .calm: return .blue
        case .excited: return .orange
        case .playful: return .pink
        case .sleepy: return .indigo
        case .curious: return .teal
        case .proud: return .red
        case .nostalgic: return .brown
        }
    }
}

enum MemoryEventType: String, Codable, CaseIterable {
    // General
    case general = "general"
    case daily = "daily"
    case special = "special"
    
    // Milestones
    case adoption = "adoption"
    case birthday = "birthday"
    case gotchaDay = "gotcha_day"
    case firstDay = "first_day"
    
    // Health & Care
    case veterinary = "veterinary"
    case vaccination = "vaccination"
    case grooming = "grooming"
    case dental = "dental"
    case medication = "medication"
    
    // Training & Development
    case training = "training"
    case achievement = "achievement"
    case socialization = "socialization"
    case behavior = "behavior"
    
    // Activities
    case exercise = "exercise"
    case play = "play"
    case adventure = "adventure"
    case travel = "travel"
    case walk = "walk"
    
    // Social
    case family = "family"
    case friends = "friends"
    case other_pets = "other_pets"
    case visitors = "visitors"
    
    // Memorial
    case memorial = "memorial"
    case remembrance = "remembrance"
    case final_moments = "final_moments"
    
    var displayName: String {
        switch self {
        case .general: return "General"
        case .daily: return "Daily Life"
        case .special: return "Special Moment"
        case .adoption: return "Adoption Day"
        case .birthday: return "Birthday"
        case .gotchaDay: return "Gotcha Day"
        case .firstDay: return "First Day"
        case .veterinary: return "Vet Visit"
        case .vaccination: return "Vaccination"
        case .grooming: return "Grooming"
        case .dental: return "Dental Care"
        case .medication: return "Medication"
        case .training: return "Training"
        case .achievement: return "Achievement"
        case .socialization: return "Socialization"
        case .behavior: return "Behavior"
        case .exercise: return "Exercise"
        case .play: return "Playtime"
        case .adventure: return "Adventure"
        case .travel: return "Travel"
        case .walk: return "Walk"
        case .family: return "Family Time"
        case .friends: return "With Friends"
        case .other_pets: return "With Other Pets"
        case .visitors: return "Visitors"
        case .memorial: return "Memorial"
        case .remembrance: return "Remembrance"
        case .final_moments: return "Final Moments"
        }
    }
    
    var icon: String {
        switch self {
        case .general: return "star"
        case .daily: return "calendar"
        case .special: return "sparkles"
        case .adoption: return "heart.fill"
        case .birthday: return "birthday.cake"
        case .gotchaDay: return "gift"
        case .firstDay: return "house.and.flag"
        case .veterinary: return "cross.case"
        case .vaccination: return "syringe"
        case .grooming: return "scissors"
        case .dental: return "mouth"
        case .medication: return "pills"
        case .training: return "graduationcap"
        case .achievement: return "trophy"
        case .socialization: return "person.3"
        case .behavior: return "brain.head.profile"
        case .exercise: return "figure.run"
        case .play: return "tennisball"
        case .adventure: return "mountain.2"
        case .travel: return "car"
        case .walk: return "figure.walk"
        case .family: return "house.fill"
        case .friends: return "person.2"
        case .other_pets: return "pawprint.circle"
        case .visitors: return "door.left.hand.open"
        case .memorial: return "heart.circle"
        case .remembrance: return "star.circle"
        case .final_moments: return "moon.stars"
        }
    }
    
    var color: Color {
        switch self {
        case .general: return .gray
        case .daily: return .blue
        case .special: return .purple
        case .adoption, .gotchaDay: return .red
        case .birthday: return .pink
        case .firstDay: return .green
        case .veterinary, .vaccination, .dental, .medication: return .red
        case .grooming: return .cyan
        case .training, .achievement: return .yellow
        case .socialization, .behavior: return .orange
        case .exercise, .play, .walk: return .green
        case .adventure, .travel: return .blue
        case .family, .friends, .other_pets, .visitors: return .purple
        case .memorial, .remembrance, .final_moments: return .indigo
        }
    }
    
    var isMemorialType: Bool {
        return [.memorial, .remembrance, .final_moments].contains(self)
    }
}

struct PetMeasurement: Codable {
    let type: MeasurementType
    let value: Double
    let unit: String
    let date: Date
    let notes: String?
    
    enum MeasurementType: String, Codable, CaseIterable {
        case weight = "weight"
        case height = "height"
        case length = "length"
        case temperature = "temperature"
        
        var displayName: String {
            switch self {
            case .weight: return "Weight"
            case .height: return "Height"
            case .length: return "Length"
            case .temperature: return "Temperature"
            }
        }
        
        var defaultUnit: String {
            switch self {
            case .weight: return "lbs"
            case .height: return "inches"
            case .length: return "inches"
            case .temperature: return "°F"
            }
        }
    }
}

struct MilestoneData: Codable {
    let type: MilestoneType
    let achievementDescription: String
    let significance: String?
    let nextGoal: String?
    let witnessedBy: [String]?
    
    enum MilestoneType: String, Codable, CaseIterable {
        case first = "first"
        case achievement = "achievement"
        case behavioral = "behavioral"
        case health = "health"
        case social = "social"
        
        var displayName: String {
            switch self {
            case .first: return "First Time"
            case .achievement: return "Achievement"
            case .behavioral: return "Behavioral"
            case .health: return "Health"
            case .social: return "Social"
            }
        }
    }
}

struct MemoryShareSettings: Codable {
    var isPublic: Bool = false
    var sharedWith: [String] = []
    var allowComments: Bool = true
    var allowDownload: Bool = false
    var expiresAt: Date?
    
    init(
        isPublic: Bool = false,
        sharedWith: [String] = [],
        allowComments: Bool = true,
        allowDownload: Bool = false,
        expiresAt: Date? = nil
    ) {
        self.isPublic = isPublic
        self.sharedWith = sharedWith
        self.allowComments = allowComments
        self.allowDownload = allowDownload
        self.expiresAt = expiresAt
    }
}

enum MemoryAgeCategory: String, CaseIterable {
    case thisWeek = "this_week"
    case thisMonth = "this_month"
    case recent = "recent"
    case thisYear = "this_year"
    case older = "older"
    
    var displayName: String {
        switch self {
        case .thisWeek: return "This Week"
        case .thisMonth: return "This Month"
        case .recent: return "Recent"
        case .thisYear: return "This Year"
        case .older: return "Older"
        }
    }
}

// MARK: - Memory Collections
enum MemoryCollection: String, CaseIterable {
    case all = "all"
    case recent = "recent"
    case favorites = "favorites"
    case videos = "videos"
    case milestones = "milestones"
    case vault = "vault"
    case memorial = "memorial"
    case thisMonth = "this_month"
    case thisYear = "this_year"
    
    var displayName: String {
        switch self {
        case .all: return "All Memories"
        case .recent: return "Recent"
        case .favorites: return "Favorites"
        case .videos: return "Videos"
        case .milestones: return "Milestones"
        case .vault: return "Secure Vault"
        case .memorial: return "Memorial"
        case .thisMonth: return "This Month"
        case .thisYear: return "This Year"
        }
    }
    
    var icon: String {
        switch self {
        case .all: return "photo.stack"
        case .recent: return "clock"
        case .favorites: return "heart.fill"
        case .videos: return "video"
        case .milestones: return "star.circle"
        case .vault: return "lock.shield"
        case .memorial: return "heart.circle"
        case .thisMonth: return "calendar"
        case .thisYear: return "calendar.badge.clock"
        }
    }
    
    var color: Color {
        switch self {
        case .all: return .blue
        case .recent: return .green
        case .favorites: return .red
        case .videos: return .purple
        case .milestones: return .yellow
        case .vault: return .orange
        case .memorial: return .indigo
        case .thisMonth: return .cyan
        case .thisYear: return .teal
        }
    }
    
    var description: String {
        switch self {
        case .all: return "All your pet memories in one place"
        case .recent: return "Recent memories from the past week"
        case .favorites: return "Your favorite memories"
        case .videos: return "Video memories only"
        case .milestones: return "Important milestones and achievements"
        case .vault: return "Protected memories with biometric security"
        case .memorial: return "Tribute memories for beloved pets"
        case .thisMonth: return "Memories from this month"
        case .thisYear: return "Memories from this year"
        }
    }
} 