//
//  MemoryGem.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftData

@Model
final class MemoryGem {
    var id: UUID = UUID() // Default value for CloudKit
    var userID: String = "" // Default value for CloudKit
    var action: GemAction = GemAction.uploadMemory // Fully qualified default value for CloudKit
    var amount: Int = 0 // Default value for CloudKit
    var gemDescription: String = "" // Default value for CloudKit
    var earnedAt: Date = Date() // Default value for CloudKit

    init(
        userID: String,
        action: GemAction,
        amount: Int,
        gemDescription: String
    ) {
        self.id = UUID()
        self.userID = userID
        self.action = action
        self.amount = amount
        self.gemDescription = gemDescription
        self.earnedAt = Date()
    }
}

enum GemAction: String, CaseIterable, Codable {
    case uploadMemory = "upload_memory"
    case createVault = "create_vault"
    case joinNetwork = "join_network"
    case shareMontage = "share_montage"
    case createGarden = "create_garden"
    case dailyLogin = "daily_login"
    case completeMilestone = "complete_milestone"
    case inviteFriend = "invite_friend"

    var displayName: String {
        switch self {
        case .uploadMemory:
            return "Upload Memory"
        case .createVault:
            return "Create Vault"
        case .joinNetwork:
            return "Join Network"
        case .shareMontage:
            return "Share Montage"
        case .createGarden:
            return "Create Garden"
        case .dailyLogin:
            return "Daily Login"
        case .completeMilestone:
            return "Complete Milestone"
        case .inviteFriend:
            return "Invite Friend"
        }
    }

    var gemReward: Int {
        switch self {
        case .uploadMemory:
            return 1
        case .createVault:
            return 5
        case .joinNetwork:
            return 3
        case .shareMontage:
            return 2
        case .createGarden:
            return 3
        case .dailyLogin:
            return 1
        case .completeMilestone:
            return 5
        case .inviteFriend:
            return 10
        }
    }

    var systemImage: String {
        switch self {
        case .uploadMemory:
            return "photo.badge.plus"
        case .createVault:
            return "lock.shield"
        case .joinNetwork:
            return "person.2"
        case .shareMontage:
            return "square.and.arrow.up"
        case .createGarden:
            return "leaf"
        case .dailyLogin:
            return "calendar"
        case .completeMilestone:
            return "star.circle"
        case .inviteFriend:
            return "person.badge.plus"
        }
    }
}

// MARK: - Gem Rewards System
struct GemRewardSystem {
    static func earnGems(for action: GemAction, user: User) -> MemoryGem {
        let amount = action.gemReward
        let gemDescription = "Earned \(amount) gem\(amount == 1 ? "" : "s") for \(action.displayName.lowercased())"

        user.earnGems(amount)

        return MemoryGem(
            userID: user.id,
            action: action,
            amount: amount,
            gemDescription: gemDescription
        )
    }

    static func canEarnDailyLogin(user: User, gems: [MemoryGem]) -> Bool {
        let today = Calendar.current.startOfDay(for: Date())
        let todayLogins = gems.filter { gem in
            gem.action == .dailyLogin &&
            Calendar.current.startOfDay(for: gem.earnedAt) == today
        }
        return todayLogins.isEmpty
    }
}
