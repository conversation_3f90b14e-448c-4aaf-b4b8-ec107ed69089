//
//  MissingTypes.swift
//  PetCapsule
//
//  Created by the AI assistant to resolve build errors.
//  This file contains stub implementations for types that are not found.
//

import Foundation
import SwiftUI
import CoreLocation

// MARK: - Missing Service Types

// EmergencyCall type for EmergencyCallService
struct EmergencyCall: Identifiable, Codable {
    let id: String
    let contactId: String
    let contactName: String
    let contactPhone: String
    let emergencyType: EmergencyType
    let petId: String?
    let startTime: Date
    var endTime: Date?
    var status: EmergencyCallStatus
    let location: CLLocationCoordinate2D?
    let notes: String?
}

enum EmergencyCallStatus: String, CaseIterable, Codable {
    case initiated = "initiated"
    case connected = "connected"
    case ended = "ended"
    case failed = "failed"
}

// MARK: - Environmental Alert Types

struct ActiveEnvironmentalAlert: Identifiable, Codable {
    let id: String
    let alertType: EnvironmentalAlertType
    let title: String
    let message: String
    let severity: AlertSeverity
    let currentValue: Double?
    let threshold: Double?
    let triggeredAt: Date
    var isActive: Bool
    let petIds: [String]
}

struct AlertHistoryItem: Identifiable, Codable {
    let id: String
    let alertType: EnvironmentalAlertType
    let title: String
    let message: String
    let severity: AlertSeverity
    let triggeredAt: Date
    var acknowledged: Bool
}

// MARK: - Missing Network Types

struct SharedCommunityPost: Identifiable, Codable {
    let id: String
    let authorId: String
    let authorName: String
    let content: String
    let createdAt: Date
    var likesCount: Int
    var commentsCount: Int
}

typealias SharedSharedPostVisibility = PostVisibility

struct SharedPlaydate: Identifiable, Codable {
    let id: String
    let organizerId: String
    let title: String
    let description: String
    let scheduledDate: Date
    let location: String
    var status: PlaydateStatus
}

// MARK: - Missing Alert Service Types

struct SharedEnvironmentalAlert: Identifiable, Codable {
    let id: String
    let alertType: EnvironmentalAlertType
    let severity: AlertSeverity
    let title: String
    let message: String
    let triggeredAt: Date
    var isActive: Bool
}

// MARK: - Health Analytics Types
// Note: HealthDataPoint, ActivityDataPoint, MedicationDataPoint are defined in PetHealthAnalyticsService.swift

// MARK: - Missing Planner Types

typealias SharedAlertSeverity = AlertSeverity

struct SharedHealthTask: Identifiable, Codable {
    let id: String
    let petId: String
    let title: String
    let description: String
    let taskType: String
    let priority: RecommendationPriority
    let dueDate: Date
    var isCompleted: Bool
    var completedDate: Date?
    let reminderTime: Date?
    let notes: String?
    let createdAt: Date
}

struct SharedVaccinationReminder: Identifiable, Codable {
    let id: String
    let petId: String
    let vaccineName: String
    let dueDate: Date
    let vetClinic: String?
    let notes: String?
    var isCompleted: Bool
    var completedDate: Date?
    let reminderDays: [Int]
    let createdAt: Date
}

struct HealthTask: Identifiable, Codable {
    let id: String
    let petId: String
    let title: String
    let description: String
    let taskType: String
    let priority: RecommendationPriority
    let dueDate: Date
    var isCompleted: Bool
    var completedDate: Date?
    let reminderTime: Date?
    let notes: String?
    let createdAt: Date
}

struct VaccinationReminder: Identifiable, Codable {
    let id: String
    let petId: String
    let vaccineName: String
    let dueDate: Date
    let vetClinic: String?
    let notes: String?
    var isCompleted: Bool
    var completedDate: Date?
    let reminderDays: [Int]
    let createdAt: Date
}

// typealias SharedRecommendationPriority = RecommendationPriority // Already defined in SharedTypes.swift

// MARK: - Social Types

struct SharedSharedSharedSocialConnection: Identifiable, Codable {
    let id: String
    let userId: String
    let connectedUserId: String
    let connectedUserName: String
    let connectionType: String
    var status: ConnectionStatus
    let createdAt: Date
    var updatedAt: Date
    let mutualConnections: Int
    let sharedInterests: [String]
}

enum SharedPlaydateAction: String, CaseIterable, Codable {
    case join = "join"
    case leave = "leave"
    case cancel = "cancel"
    case complete = "complete"

    var displayName: String {
        rawValue.capitalized
    }
}

// MARK: - Emergency Planning Types

struct EmergencyPlan: Identifiable, Codable {
    let id: String
    let petId: String
    var evacuationPlan: EvacuationPlan
    var emergencyKit: EmergencyKit
    var emergencyContacts: [SharedEmergencyContact]
    var notes: String
}

struct EvacuationPlan: Codable {
    var meetingPoint: String
    var evacuationRoute: String
    var shelterInPlaceInstructions: String
}

struct EmergencyKit: Identifiable, Codable {
    let id: String
    var items: [EmergencyKitItem]
}

struct EmergencyKitItem: Identifiable, Codable {
    let id: String
    var name: String
    var quantity: Int
    var expirationDate: Date?
}

struct EmergencyLocation: Identifiable, Codable {
    let id: String
    var name: String
    var address: String
    var type: String
}

// MARK: - Environmental Scoring Types

struct EnvironmentalScore: Identifiable, Codable {
    var id = UUID()
    var score: Double
    var overallScore: Double
    var components: EnvironmentalComponents
    var rawData: EnvironmentalRawData
    var scoreColor: Color {
        if overallScore >= 0.8 { return .green }
        else if overallScore >= 0.6 { return .blue }
        else if overallScore >= 0.4 { return .orange }
        else { return .red }
    }
    var scoreCategory: ScoreCategory {
        if overallScore >= 0.8 { return .excellent }
        else if overallScore >= 0.6 { return .good }
        else if overallScore >= 0.4 { return .fair }
        else { return .poor }
    }
    
    init(score: Double, components: EnvironmentalComponents, rawData: EnvironmentalRawData? = nil) {
        self.score = score
        self.overallScore = score
        self.components = components
        self.rawData = rawData ?? EnvironmentalRawData(
            weather: EnvironmentalWeatherData(),
            airQuality: EnvironmentalAirQualityData(),
            pollen: 50,
            timestamp: Date(),
            location: CLLocationCoordinate2D(latitude: 0, longitude: 0)
        )
    }
}

struct ComponentScore: Codable {
    let score: Double
    let message: String
}

struct EnvironmentalComponents: Codable {
    var temperature: ComponentScore
    var humidity: ComponentScore
    var airQuality: ComponentScore
    var pollen: ComponentScore
    var weatherCondition: ComponentScore
    var wind: ComponentScore
    var timeOfDay: ComponentScore
}

enum PetLocationCategory: CaseIterable {
    case park
    case vet
    case store
    case grooming
    case other
}

// MARK: - Health Analytics Additional Types

typealias SharedHealthMetric = HealthMetric
enum SharedTimeRange {
    case day
    case week
    case month
    case year
}

// Use HealthTrend from SharedTypes.swift instead

// MARK: - AI Service Types

struct SharedSharedAIBehaviorInsights: Codable {
    var insights: [String]
}

// MARK: - AI Service Stub Types

// Use ComfortSituation and AIServiceError from AIService.swift instead

// MARK: - Type Aliases for missing complex types
typealias NutritionalInfo = String
typealias PetActivityLevel = Int
// Use types from PlannerModels.swift instead

// MARK: - Missing Social Interaction Types

struct Playdate: Identifiable, Codable {
    let id: String
    let organizerId: String
    let title: String
    let description: String
    let scheduledDate: Date
    let location: String
    var status: PlaydateStatus
}

struct CommunityPost: Identifiable, Codable {
    let id: String
    let authorId: String
    let authorName: String
    let content: String
    let createdAt: Date
    var likesCount: Int
    var commentsCount: Int
}

// typealias SharedRecommendationType = RecommendationType // Already defined in SharedTypes.swift

// MARK: - Environmental Scoring Additional Types

struct PersonalizedWalkRecommendation: Identifiable, Codable {
    let id: String
    let petId: String
    let recommendedTime: Date
    let duration: TimeInterval
    let route: String?
    let score: Double
    let reasoning: String
}

struct ScoringWeights {
    let temperature: Double = 0.3
    let humidity: Double = 0.2
    let airQuality: Double = 0.25
    let pollen: Double = 0.15
    let weatherCondition: Double = 0.1
    let wind: Double = 0.05
    let timeOfDay: Double = 0.05
    
    mutating func normalize() {
        // Already normalized weights
    }
}

enum EnvironmentalFactor: String, CaseIterable {
    case temperature
    case humidity
    case airQuality
    case pollen
    case weather
}

// MARK: - Missing Planner Service Types

struct SharedWalkRecommendation: Identifiable, Codable {
    let id: String
    let petId: String
    let recommendedTime: Date
    let duration: TimeInterval
    let route: String?
    let score: Double
    let reasoning: String
}

// MARK: - Environmental Scoring Additional Types

struct EnvironmentalRawData: Codable {
    let weather: EnvironmentalWeatherData
    let airQuality: EnvironmentalAirQualityData
    let pollen: Int
    let timestamp: Date
    let location: CLLocationCoordinate2D
}

struct EnvironmentalWeatherData: Codable {
    let temperature: Int
    let humidity: Int
    let windSpeed: Double
    let uvIndex: Int
    
    init(temperature: Int = 70, humidity: Int = 50, windSpeed: Double = 5, uvIndex: Int = 5) {
        self.temperature = temperature
        self.humidity = humidity
        self.windSpeed = windSpeed
        self.uvIndex = uvIndex
    }
}

struct EnvironmentalAirQualityData: Codable {
    let index: Int
    let category: String
    
    init(index: Int = 50) {
        self.index = index
        self.category = index < 50 ? "Good" : index < 100 ? "Moderate" : "Poor"
    }
}

enum ScoreCategory: String, CaseIterable {
    case excellent = "excellent"
    case good = "good" 
    case fair = "fair"
    case poor = "poor"
    
    var displayName: String {
        rawValue.capitalized
    }
    
    var icon: String {
        switch self {
        case .excellent: return "star.fill"
        case .good: return "checkmark.circle.fill"
        case .fair: return "exclamationmark.triangle.fill"
        case .poor: return "xmark.circle.fill"
        }
    }
}

enum ScoreImpact: String, CaseIterable, Codable {
    case positive = "positive"
    case neutral = "neutral"
    case negative = "negative"
    case severe = "severe"
    
    var displayName: String {
        rawValue.capitalized
    }
    
    var color: Color {
        switch self {
        case .positive: return .green
        case .neutral: return .blue
        case .negative: return .orange
        case .severe: return .red
        }
    }
}

// MARK: - Pollen Data Types

enum PollenRisk: String, CaseIterable, Codable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case veryHigh = "very_high"
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .moderate: return "Moderate"
        case .high: return "High"
        case .veryHigh: return "Very High"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .green
        case .moderate: return .yellow
        case .high: return .orange
        case .veryHigh: return .red
        }
    }
}

struct PollenData: Identifiable, Codable {
    let id: String
    let treeIndex: Int
    let grassIndex: Int
    let weedIndex: Int
    let overallRisk: PollenRisk
    let lastUpdated: Date
    
    init(id: String = UUID().uuidString, treeIndex: Int, grassIndex: Int, weedIndex: Int, overallRisk: PollenRisk, lastUpdated: Date) {
        self.id = id
        self.treeIndex = treeIndex
        self.grassIndex = grassIndex
        self.weedIndex = weedIndex
        self.overallRisk = overallRisk
        self.lastUpdated = lastUpdated
    }
    
    static let sample = PollenData(
        treeIndex: 3,
        grassIndex: 2,
        weedIndex: 1,
        overallRisk: .moderate,
        lastUpdated: Date()
    )
}
