//
//  NutritionModels.swift
//  PetCapsule
//
//  Data models for nutrition planning and tracking
//

import Foundation
import SwiftUI

// MARK: - Nutrition Plan

struct NutritionPlan: Identifiable, Codable {
    let id: String
    let petId: String
    let petName: String
    let dailyCalories: Int
    let meals: [MealRecommendation]
    let goals: [NutritionGoal]
    let createdAt: Date
    let lastUpdated: Date
    
    var totalProtein: Int {
        meals.flatMap { $0.foodItems }.reduce(0) { $0 + $1.macros.protein }
    }
    
    var totalFat: Int {
        meals.flatMap { $0.foodItems }.reduce(0) { $0 + $1.macros.fat }
    }
    
    var totalCarbs: Int {
        meals.flatMap { $0.foodItems }.reduce(0) { $0 + $1.macros.carbs }
    }
}

// MARK: - Meal Recommendation

struct MealRecommendation: Identifiable, Codable {
    let id: String
    let name: String
    let time: String
    let calories: Int
    let foodItems: [FoodRecommendation]
    let notes: String
    
    var totalCost: Double {
        foodItems.reduce(0) { $0 + ($1.estimatedCost ?? 0) }
    }
}

struct FoodRecommendation: Identifiable, Codable {
    let id: UUID
    let foodId: String
    let name: String
    let amount: String
    let calories: Int
    let macros: Macronutrients
    let estimatedCost: Double?

    init(foodId: String, name: String, amount: String, calories: Int, macros: Macronutrients, estimatedCost: Double? = nil) {
        self.id = UUID()
        self.foodId = foodId
        self.name = name
        self.amount = amount
        self.calories = calories
        self.macros = macros
        self.estimatedCost = estimatedCost
    }
}

// MARK: - Nutrition Goals

struct NutritionGoal: Identifiable, Codable {
    let id: String
    let type: GoalType
    let target: String
    let currentValue: Int
    let targetValue: Int
    let unit: String
    let priority: NutritionPriority
    
    var progress: Double {
        guard targetValue > 0 else { return 0 }
        return min(Double(currentValue) / Double(targetValue), 1.0)
    }
    
    var isCompleted: Bool {
        currentValue >= targetValue
    }
}

enum GoalType: String, CaseIterable, Codable {
    case weight = "weight"
    case hydration = "hydration"
    case activity = "activity"
    case supplements = "supplements"
    case treats = "treats"
    
    var displayName: String {
        switch self {
        case .weight: return "Weight Management"
        case .hydration: return "Hydration"
        case .activity: return "Activity Level"
        case .supplements: return "Supplements"
        case .treats: return "Treat Moderation"
        }
    }
    
    var icon: String {
        switch self {
        case .weight: return "scalemass"
        case .hydration: return "drop.fill"
        case .activity: return "figure.walk"
        case .supplements: return "pills.fill"
        case .treats: return "heart.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .weight: return .blue
        case .hydration: return .cyan
        case .activity: return .green
        case .supplements: return .orange
        case .treats: return .pink
        }
    }
}

enum NutritionPriority: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var color: Color {
        switch self {
        case .low: return .gray
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

// MARK: - Meal Scheduling

struct MealSchedule: Identifiable, Codable {
    let id: String
    let petId: String
    let planId: String
    var meals: [ScheduledMeal]
    let startDate: Date
    let isActive: Bool
    
    var todaysMeals: [ScheduledMeal] {
        let calendar = Calendar.current
        return meals.filter { calendar.isDateInToday($0.scheduledTime) }
    }
    
    var completionRate: Double {
        let totalMeals = todaysMeals.count
        let completedMeals = todaysMeals.filter { $0.isCompleted }.count
        return totalMeals > 0 ? Double(completedMeals) / Double(totalMeals) : 0
    }
}

struct ScheduledMeal: Identifiable, Codable {
    let id: String
    let mealId: String
    let scheduledTime: Date
    var isCompleted: Bool
    var actualTime: Date?
    var notes: String?
    
    var isOverdue: Bool {
        !isCompleted && Date() > scheduledTime.addingTimeInterval(3600) // 1 hour grace period
    }
    
    var timeStatus: String {
        if isCompleted {
            return "Completed"
        } else if isOverdue {
            return "Overdue"
        } else if Date() > scheduledTime {
            return "Due Now"
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "h:mm a"
            return "Due at \(formatter.string(from: scheduledTime))"
        }
    }
}

// MARK: - Food Database

struct FoodItem: Identifiable, Codable {
    let id: String
    let name: String
    let brand: String
    let category: FoodCategory
    let species: PetSpecies
    let ageGroup: AgeGroup
    let caloriesPerCup: Int
    let macros: Macronutrients
    let ingredients: [String]
    let allergens: [String]
    let price: Double
    let rating: Double
    
    var pricePerCalorie: Double {
        price / Double(caloriesPerCup)
    }
}

enum FoodCategory: String, CaseIterable, Codable {
    case dryFood = "dry_food"
    case wetFood = "wet_food"
    case rawFood = "raw_food"
    case treats = "treats"
    case supplements = "supplements"
    
    var displayName: String {
        switch self {
        case .dryFood: return "Dry Food"
        case .wetFood: return "Wet Food"
        case .rawFood: return "Raw Food"
        case .treats: return "Treats"
        case .supplements: return "Supplements"
        }
    }
    
    var icon: String {
        switch self {
        case .dryFood: return "circle.fill"
        case .wetFood: return "drop.fill"
        case .rawFood: return "leaf.fill"
        case .treats: return "heart.fill"
        case .supplements: return "pills.fill"
        }
    }
}

enum PetSpecies: String, CaseIterable, Codable {
    case dog = "dog"
    case cat = "cat"
    case bird = "bird"
    case rabbit = "rabbit"
    case other = "other"
    
    var displayName: String {
        rawValue.capitalized
    }
}

enum AgeGroup: String, CaseIterable, Codable {
    case puppy = "puppy"
    case adult = "adult"
    case senior = "senior"
    case allAges = "all_ages"
    
    var displayName: String {
        switch self {
        case .puppy: return "Puppy/Kitten"
        case .adult: return "Adult"
        case .senior: return "Senior"
        case .allAges: return "All Ages"
        }
    }
}

// MARK: - Macronutrients

struct Macronutrients: Codable {
    let protein: Int // grams
    let fat: Int // grams
    let carbs: Int // grams
    let fiber: Int // grams
    
    var totalMacros: Int {
        protein + fat + carbs
    }
    
    var proteinPercentage: Double {
        Double(protein) / Double(totalMacros) * 100
    }
    
    var fatPercentage: Double {
        Double(fat) / Double(totalMacros) * 100
    }
    
    var carbPercentage: Double {
        Double(carbs) / Double(totalMacros) * 100
    }
}

// MARK: - Progress Tracking

struct NutritionProgress: Codable {
    let completionRate: Double
    let totalMeals: Int
    let completedMeals: Int
    let currentStreak: Int
    let weeklyGoalProgress: Double
    
    var streakText: String {
        if currentStreak == 0 {
            return "Start your streak!"
        } else if currentStreak == 1 {
            return "1 day streak"
        } else {
            return "\(currentStreak) day streak"
        }
    }
    
    var progressText: String {
        "\(completedMeals)/\(totalMeals) meals completed"
    }
}

// MARK: - Nutrition Analytics

struct NutritionAnalytics: Codable {
    let weeklyCalories: [Int]
    let macroTrends: MacroTrends
    let costAnalysis: CostAnalysis
    let healthMetrics: SharedHealthMetrics
}

struct MacroTrends: Codable {
    let proteinTrend: [Double]
    let fatTrend: [Double]
    let carbTrend: [Double]
    let fiberTrend: [Double]
}

struct CostAnalysis: Codable {
    let weeklySpend: Double
    let monthlyProjection: Double
    let costPerCalorie: Double
    let budgetStatus: BudgetStatus
}

enum BudgetStatus: String, Codable {
    case underBudget = "under_budget"
    case onTrack = "on_track"
    case overBudget = "over_budget"
    
    var color: Color {
        switch self {
        case .underBudget: return .green
        case .onTrack: return .blue
        case .overBudget: return .red
        }
    }
    
    var displayName: String {
        switch self {
        case .underBudget: return "Under Budget"
        case .onTrack: return "On Track"
        case .overBudget: return "Over Budget"
        }
    }
}

struct HealthMetrics: Codable {
    let weightTrend: [Double]
    let energyLevel: Int // 1-10 scale
    let digestiveHealth: Int // 1-10 scale
    let coatCondition: Int // 1-10 scale
    let overallHealth: Int // 1-10 scale
}
