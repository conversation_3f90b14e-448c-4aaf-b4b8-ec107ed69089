//
//  Pet.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftData

// Use String array for emergency contacts to avoid type conflicts
// typealias PetEmergencyContact = String
import SwiftUI
import AppIntents

@Model
final class Pet: Identifiable {
    var id: String = UUID().uuidString
    var name: String = ""
    var species: String = ""
    var breed: String?
    var age: Int = 0
    var weight: Double?
    var profileImageURL: String?
    var dateOfBirth: Date?
    var adoptionDate: Date?
    var microchipId: String?
    var isSpayedNeutered: Bool = false
    var sex: String = "unknown"
    
    // CRITICAL EMERGENCY INFO
    var emergencyContacts: [EmergencyContact] = []
    var primaryVet: VeterinarianInfo?
    var emergencyVet: VeterinarianInfo?
    var allergies: [String] = []
    var medications: [Medication] = [] // Added default value for CloudKit
    var medicalConditions: [MedicalCondition] = [] // Added default value for CloudKit
    var bloodType: String?
    var insuranceInfo: InsuranceInfo?
    
    // HEALTH TRACKING
    var healthAlerts: [HealthAlert] = [] // Added default value for CloudKit
    var vaccinationRecords: [VaccinationRecord] = [] // Added default value for CloudKit
    var weightHistory: [WeightRecord] = [] // Added default value for CloudKit
    var symptomsLog: [SymptomLog] = [] // Added default value for CloudKit
    
    // DAILY CARE
    var feedingSchedule: [FeedingTime] = [] // Added default value for CloudKit
    var exerciseNeeds: ExerciseNeeds = ExerciseNeeds() // Added default value for CloudKit
    var behaviorNotes: String?
    var specialInstructions: String?
    
    // LOCATION & SAFETY
    var lastKnownLocation: LocationRecord?
    var isLost: Bool = false // Added default value for CloudKit
    var collarInfo: CollarInfo?
    
    // ADDITIONAL PROPERTIES FOR COMPATIBILITY
    var activityLevel: String = "moderate" // Added default value for CloudKit
    var personalityTraits: [String] = [] // Added default value for CloudKit
    var vaccinations: [String] = [] // Added default value for CloudKit
    var aiRecommendations: [String] = [] // Added default value for CloudKit
    var lastCheckupDate: Date?
    var chronicConditions: [String] = [] // Added default value for CloudKit
    
    // MISSING PROPERTIES THAT ARE REFERENCED IN CODE
    var foodAllergies: [String] = [] // Added default value for CloudKit
    var healthScore: Double = 0.0 // Added default value for CloudKit
    var dailyCalories: Int = 0 // Added default value for CloudKit
    var lastAIAnalysis: Date?
    var storedMemoryCount: Int = 0 // Added default value for CloudKit
    var subscriptionTier: String = "paw_starter" // Added default value for CloudKit
    
    // Additional missing properties found in code
    var gender: String?
    var vetName: String?
    var vetContact: String?
    var currentFood: String?
    var foodBrand: String?
    var createdAt: Date = Date() // Added default value for CloudKit
    var updatedAt: Date = Date() // Added default value for CloudKit
    
    // Social and profile properties
    var friendsCount: Int = 0 // Added default value for CloudKit
    var bio: String = "" // Added default value for CloudKit
    var achievementBadges: [String] = [] // Added default value for CloudKit
    
    // Award-winning multi-pet management features
    var isFavorite: Bool = false // Added default value for CloudKit
    var bondedWith: String? // Pet ID of bonded companion
    var isIndoor: Bool = true // Added default value for CloudKit
    var careGroup: String? // For grouping pets with similar care needs
    
    // Comprehensive data collection fields for enhanced pet management
    var waterIntakeML: Int?
    var dietaryRestrictions: [String] = [] // Added default value for CloudKit
    var exerciseMinutesDaily: Int?
    var walkingFrequency: String?
    var favoriteActivities: [String] = [] // Added default value for CloudKit
    var trainingLevel: String?
    var knownCommands: [String] = [] // Added default value for CloudKit
    var behaviorIssues: [String] = [] // Added default value for CloudKit
    var socialBehavior: String?
    var veterinarianInfo: VeterinarianInfo?

    // CloudKit-compatible relationships using UUID references (avoids circular references)
    var ownerID: String? // Reference to User.id
    var memoryIDs: [String] = [] // References to Memory.id
    var vaultIDs: [String] = [] // References to Vault.id

    init(
        id: String = UUID().uuidString,
        name: String,
        species: String,
        breed: String? = nil,
        age: Int,
        weight: Double? = nil,
        profileImageURL: String? = nil,
        dateOfBirth: Date? = nil,
        adoptionDate: Date? = nil,
        microchipId: String? = nil,
        isSpayedNeutered: Bool = false,
        sex: String = "unknown",
        emergencyContacts: [EmergencyContact] = [],
        primaryVet: VeterinarianInfo? = nil,
        emergencyVet: VeterinarianInfo? = nil,
        allergies: [String] = [],
        medications: [Medication] = [],
        medicalConditions: [MedicalCondition] = [],
        bloodType: String? = nil,
        insuranceInfo: InsuranceInfo? = nil,
        healthAlerts: [HealthAlert] = [],
        vaccinationRecords: [VaccinationRecord] = [],
        weightHistory: [WeightRecord] = [],
        symptomsLog: [SymptomLog] = [],
        feedingSchedule: [FeedingTime] = [],
        exerciseNeeds: ExerciseNeeds = ExerciseNeeds(),
        behaviorNotes: String? = nil,
        specialInstructions: String? = nil,
        lastKnownLocation: LocationRecord? = nil,
        isLost: Bool = false,
        collarInfo: CollarInfo? = nil,
        activityLevel: String = "moderate",
        personalityTraits: [String] = [],
        vaccinations: [String] = [],
        aiRecommendations: [String] = [],
        lastCheckupDate: Date? = nil,
        chronicConditions: [String] = [],
        foodAllergies: [String] = [],
        healthScore: Double = 0.85,
        dailyCalories: Int = 1200,
        lastAIAnalysis: Date? = nil,
        storedMemoryCount: Int = 0,
        subscriptionTier: String = "FREE",
        memories: [Memory] = [],
        gender: String? = nil,
        vetName: String? = nil,
        vetContact: String? = nil,
        currentFood: String? = nil,
        foodBrand: String? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        friendsCount: Int = 0,
        bio: String = "",
        achievementBadges: [String] = [],
        isFavorite: Bool = false,
        bondedWith: String? = nil,
        isIndoor: Bool = true,
        careGroup: String? = nil,
        waterIntakeML: Int? = nil,
        dietaryRestrictions: [String] = [],
        exerciseMinutesDaily: Int? = nil,
        walkingFrequency: String? = nil,
        favoriteActivities: [String] = [],
        trainingLevel: String? = nil,
        knownCommands: [String] = [],
        behaviorIssues: [String] = [],
        socialBehavior: String? = nil,
        veterinarianInfo: VeterinarianInfo? = nil
    ) {
        self.id = id
        self.name = name
        self.species = species
        self.breed = breed
        self.age = age
        self.weight = weight
        self.profileImageURL = profileImageURL
        self.dateOfBirth = dateOfBirth
        self.adoptionDate = adoptionDate
        self.microchipId = microchipId
        self.isSpayedNeutered = isSpayedNeutered
        self.sex = sex
        self.emergencyContacts = emergencyContacts
        self.primaryVet = primaryVet
        self.emergencyVet = emergencyVet
        self.allergies = allergies
        self.medications = medications
        self.medicalConditions = medicalConditions
        self.bloodType = bloodType
        self.insuranceInfo = insuranceInfo
        self.healthAlerts = healthAlerts
        self.vaccinationRecords = vaccinationRecords
        self.weightHistory = weightHistory
        self.symptomsLog = symptomsLog
        self.feedingSchedule = feedingSchedule
        self.exerciseNeeds = exerciseNeeds
        self.behaviorNotes = behaviorNotes
        self.specialInstructions = specialInstructions
        self.lastKnownLocation = lastKnownLocation
        self.isLost = isLost
        self.collarInfo = collarInfo
        self.activityLevel = activityLevel
        self.personalityTraits = personalityTraits
        self.vaccinations = vaccinations
        self.aiRecommendations = aiRecommendations
        self.lastCheckupDate = lastCheckupDate
        self.chronicConditions = chronicConditions
        self.foodAllergies = foodAllergies
        self.healthScore = healthScore
        self.dailyCalories = dailyCalories
        self.lastAIAnalysis = lastAIAnalysis
        self.storedMemoryCount = storedMemoryCount
        self.subscriptionTier = subscriptionTier
        self.memoryIDs = memories.map { $0.id.uuidString }
        self.gender = gender
        self.vetName = vetName
        self.vetContact = vetContact
        self.currentFood = currentFood
        self.foodBrand = foodBrand
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.friendsCount = friendsCount
        self.bio = bio
        self.achievementBadges = achievementBadges
        self.isFavorite = isFavorite
        self.bondedWith = bondedWith
        self.isIndoor = isIndoor
        self.careGroup = careGroup
        self.waterIntakeML = waterIntakeML
        self.dietaryRestrictions = dietaryRestrictions
        self.exerciseMinutesDaily = exerciseMinutesDaily
        self.walkingFrequency = walkingFrequency
        self.favoriteActivities = favoriteActivities
        self.trainingLevel = trainingLevel
        self.knownCommands = knownCommands
        self.behaviorIssues = behaviorIssues
        self.socialBehavior = socialBehavior
        self.veterinarianInfo = veterinarianInfo
    }

    // MARK: - Convenience Initializer for Database Integration
    convenience init(
        name: String,
        species: String,
        breed: String,
        birthDate: Date,
        adoptionDate: Date,
        weight: Double? = nil,
        activityLevel: String = "moderate",
        personalityTraits: [String] = [],
        healthConditions: [String] = [],
        medications: [Medication] = [],
        vaccinations: [String] = [],
        healthAlerts: [HealthAlert] = [],
        aiRecommendations: [String] = [],
        profileImageURL: String? = nil,
        lastCheckupDate: Date? = nil
    ) {
        // Calculate age in months from birth date
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.month], from: birthDate, to: Date())
        let calculatedAge = ageComponents.month ?? 0
        
        self.init(
            name: name,
            species: species,
            breed: breed,
            age: calculatedAge,
            weight: weight,
            profileImageURL: profileImageURL,
            dateOfBirth: birthDate,
            adoptionDate: adoptionDate
        )
        
        // Set additional properties
        self.activityLevel = activityLevel
        self.personalityTraits = personalityTraits
        self.vaccinations = vaccinations
        self.aiRecommendations = aiRecommendations
        self.lastCheckupDate = lastCheckupDate
        self.chronicConditions = healthConditions
        self.foodAllergies = []
        self.healthScore = 0.85
        self.dailyCalories = 1200
        self.lastAIAnalysis = nil
        self.storedMemoryCount = 0
        self.subscriptionTier = "FREE"
        self.memoryIDs = []
        self.gender = nil
        self.vetName = nil
        self.vetContact = nil
        self.currentFood = nil
        self.foodBrand = nil
        self.createdAt = Date()
        self.updatedAt = Date()
        self.friendsCount = 0
        self.bio = ""
        self.achievementBadges = []
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, name, species, breed, age, weight, profileImageURL, dateOfBirth, adoptionDate, microchipId, isSpayedNeutered, sex
        case emergencyContacts, primaryVet, emergencyVet, allergies, medications, medicalConditions, bloodType, insuranceInfo
        case healthAlerts, vaccinationRecords, weightHistory, symptomsLog, feedingSchedule, exerciseNeeds, behaviorNotes, specialInstructions
        case lastKnownLocation, isLost, collarInfo
        case activityLevel, personalityTraits, vaccinations, aiRecommendations, lastCheckupDate, chronicConditions
        case foodAllergies, healthScore, dailyCalories, lastAIAnalysis, storedMemoryCount, subscriptionTier, memoryIDs
        case gender, vetName, vetContact, currentFood, foodBrand, createdAt, updatedAt
        case friendsCount, bio, achievementBadges
        case isFavorite, bondedWith, isIndoor, careGroup
        case waterIntakeML, dietaryRestrictions, exerciseMinutesDaily, walkingFrequency, favoriteActivities
        case trainingLevel, knownCommands, behaviorIssues, socialBehavior, veterinarianInfo
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        self.id = try container.decode(String.self, forKey: .id)
        self.name = try container.decode(String.self, forKey: .name)
        self.species = try container.decode(String.self, forKey: .species)
        self.breed = try container.decodeIfPresent(String.self, forKey: .breed)
        self.age = try container.decode(Int.self, forKey: .age)
        self.weight = try container.decodeIfPresent(Double.self, forKey: .weight)
        self.profileImageURL = try container.decodeIfPresent(String.self, forKey: .profileImageURL)
        self.dateOfBirth = try container.decodeIfPresent(Date.self, forKey: .dateOfBirth)
        self.adoptionDate = try container.decodeIfPresent(Date.self, forKey: .adoptionDate)
        self.microchipId = try container.decodeIfPresent(String.self, forKey: .microchipId)
        self.isSpayedNeutered = try container.decode(Bool.self, forKey: .isSpayedNeutered)
        self.sex = try container.decode(String.self, forKey: .sex)
        self.emergencyContacts = try container.decode([EmergencyContact].self, forKey: .emergencyContacts)
        self.primaryVet = try container.decodeIfPresent(VeterinarianInfo.self, forKey: .primaryVet)
        self.emergencyVet = try container.decodeIfPresent(VeterinarianInfo.self, forKey: .emergencyVet)
        self.allergies = try container.decode([String].self, forKey: .allergies)
        self.medications = try container.decode([Medication].self, forKey: .medications)
        self.medicalConditions = try container.decode([MedicalCondition].self, forKey: .medicalConditions)
        self.bloodType = try container.decodeIfPresent(String.self, forKey: .bloodType)
        self.insuranceInfo = try container.decodeIfPresent(InsuranceInfo.self, forKey: .insuranceInfo)
        self.healthAlerts = try container.decode([HealthAlert].self, forKey: .healthAlerts)
        self.vaccinationRecords = try container.decode([VaccinationRecord].self, forKey: .vaccinationRecords)
        self.weightHistory = try container.decode([WeightRecord].self, forKey: .weightHistory)
        self.symptomsLog = try container.decode([SymptomLog].self, forKey: .symptomsLog)
        self.feedingSchedule = try container.decode([FeedingTime].self, forKey: .feedingSchedule)
        self.exerciseNeeds = try container.decode(ExerciseNeeds.self, forKey: .exerciseNeeds)
        self.behaviorNotes = try container.decodeIfPresent(String.self, forKey: .behaviorNotes)
        self.specialInstructions = try container.decodeIfPresent(String.self, forKey: .specialInstructions)
        self.lastKnownLocation = try container.decodeIfPresent(LocationRecord.self, forKey: .lastKnownLocation)
        self.isLost = try container.decode(Bool.self, forKey: .isLost)
        self.collarInfo = try container.decodeIfPresent(CollarInfo.self, forKey: .collarInfo)
        self.activityLevel = try container.decodeIfPresent(String.self, forKey: .activityLevel) ?? "moderate"
        self.personalityTraits = try container.decodeIfPresent([String].self, forKey: .personalityTraits) ?? []
        self.vaccinations = try container.decodeIfPresent([String].self, forKey: .vaccinations) ?? []
        self.aiRecommendations = try container.decodeIfPresent([String].self, forKey: .aiRecommendations) ?? []
        self.lastCheckupDate = try container.decodeIfPresent(Date.self, forKey: .lastCheckupDate)
        self.chronicConditions = try container.decodeIfPresent([String].self, forKey: .chronicConditions) ?? []
        self.foodAllergies = try container.decodeIfPresent([String].self, forKey: .foodAllergies) ?? []
        self.healthScore = try container.decodeIfPresent(Double.self, forKey: .healthScore) ?? 85.0
        self.dailyCalories = try container.decodeIfPresent(Int.self, forKey: .dailyCalories) ?? 1200
        self.lastAIAnalysis = try container.decodeIfPresent(Date.self, forKey: .lastAIAnalysis)
        self.storedMemoryCount = try container.decodeIfPresent(Int.self, forKey: .storedMemoryCount) ?? 0
        self.subscriptionTier = try container.decodeIfPresent(String.self, forKey: .subscriptionTier) ?? "FREE"
        self.memoryIDs = []
        self.gender = try container.decodeIfPresent(String.self, forKey: .gender)
        self.vetName = try container.decodeIfPresent(String.self, forKey: .vetName)
        self.vetContact = try container.decodeIfPresent(String.self, forKey: .vetContact)
        self.currentFood = try container.decodeIfPresent(String.self, forKey: .currentFood)
        self.foodBrand = try container.decodeIfPresent(String.self, forKey: .foodBrand)
        self.createdAt = try container.decode(Date.self, forKey: .createdAt)
        self.updatedAt = try container.decode(Date.self, forKey: .updatedAt)
        self.friendsCount = try container.decode(Int.self, forKey: .friendsCount)
        self.bio = try container.decode(String.self, forKey: .bio)
        self.achievementBadges = try container.decode([String].self, forKey: .achievementBadges)
        self.isFavorite = try container.decodeIfPresent(Bool.self, forKey: .isFavorite) ?? false
        self.bondedWith = try container.decodeIfPresent(String.self, forKey: .bondedWith)
        self.isIndoor = try container.decodeIfPresent(Bool.self, forKey: .isIndoor) ?? true
        self.careGroup = try container.decodeIfPresent(String.self, forKey: .careGroup)
        self.waterIntakeML = try container.decodeIfPresent(Int.self, forKey: .waterIntakeML)
        self.dietaryRestrictions = try container.decodeIfPresent([String].self, forKey: .dietaryRestrictions) ?? []
        self.exerciseMinutesDaily = try container.decodeIfPresent(Int.self, forKey: .exerciseMinutesDaily)
        self.walkingFrequency = try container.decodeIfPresent(String.self, forKey: .walkingFrequency)
        self.favoriteActivities = try container.decodeIfPresent([String].self, forKey: .favoriteActivities) ?? []
        self.trainingLevel = try container.decodeIfPresent(String.self, forKey: .trainingLevel)
        self.knownCommands = try container.decodeIfPresent([String].self, forKey: .knownCommands) ?? []
        self.behaviorIssues = try container.decodeIfPresent([String].self, forKey: .behaviorIssues) ?? []
        self.socialBehavior = try container.decodeIfPresent(String.self, forKey: .socialBehavior)
        self.veterinarianInfo = try container.decodeIfPresent(VeterinarianInfo.self, forKey: .veterinarianInfo)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(species, forKey: .species)
        try container.encode(breed, forKey: .breed)
        try container.encode(age, forKey: .age)
        try container.encode(weight, forKey: .weight)
        try container.encode(profileImageURL, forKey: .profileImageURL)
        try container.encode(dateOfBirth, forKey: .dateOfBirth)
        try container.encode(adoptionDate, forKey: .adoptionDate)
        try container.encode(microchipId, forKey: .microchipId)
        try container.encode(isSpayedNeutered, forKey: .isSpayedNeutered)
        try container.encode(sex, forKey: .sex)
        try container.encode(emergencyContacts, forKey: .emergencyContacts)
        try container.encode(primaryVet, forKey: .primaryVet)
        try container.encode(emergencyVet, forKey: .emergencyVet)
        try container.encode(allergies, forKey: .allergies)
        try container.encode(medications, forKey: .medications)
        try container.encode(medicalConditions, forKey: .medicalConditions)
        try container.encode(bloodType, forKey: .bloodType)
        try container.encode(insuranceInfo, forKey: .insuranceInfo)
        try container.encode(healthAlerts, forKey: .healthAlerts)
        try container.encode(vaccinationRecords, forKey: .vaccinationRecords)
        try container.encode(weightHistory, forKey: .weightHistory)
        try container.encode(symptomsLog, forKey: .symptomsLog)
        try container.encode(feedingSchedule, forKey: .feedingSchedule)
        try container.encode(exerciseNeeds, forKey: .exerciseNeeds)
        try container.encode(behaviorNotes, forKey: .behaviorNotes)
        try container.encode(specialInstructions, forKey: .specialInstructions)
        try container.encode(lastKnownLocation, forKey: .lastKnownLocation)
        try container.encode(isLost, forKey: .isLost)
        try container.encode(collarInfo, forKey: .collarInfo)
        try container.encode(activityLevel, forKey: .activityLevel)
        try container.encode(personalityTraits, forKey: .personalityTraits)
        try container.encode(vaccinations, forKey: .vaccinations)
        try container.encode(aiRecommendations, forKey: .aiRecommendations)
        try container.encode(lastCheckupDate, forKey: .lastCheckupDate)
        try container.encode(chronicConditions, forKey: .chronicConditions)
        try container.encode(foodAllergies, forKey: .foodAllergies)
        try container.encode(healthScore, forKey: .healthScore)
        try container.encode(dailyCalories, forKey: .dailyCalories)
        try container.encode(lastAIAnalysis, forKey: .lastAIAnalysis)
        try container.encode(storedMemoryCount, forKey: .storedMemoryCount)
        try container.encode(subscriptionTier, forKey: .subscriptionTier)
        try container.encode(memoryIDs, forKey: .memoryIDs)
        try container.encode(gender, forKey: .gender)
        try container.encode(vetName, forKey: .vetName)
        try container.encode(vetContact, forKey: .vetContact)
        try container.encode(currentFood, forKey: .currentFood)
        try container.encode(foodBrand, forKey: .foodBrand)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
        try container.encode(friendsCount, forKey: .friendsCount)
        try container.encode(bio, forKey: .bio)
        try container.encode(achievementBadges, forKey: .achievementBadges)
        try container.encode(isFavorite, forKey: .isFavorite)
        try container.encode(bondedWith, forKey: .bondedWith)
        try container.encode(isIndoor, forKey: .isIndoor)
        try container.encode(careGroup, forKey: .careGroup)
        try container.encode(waterIntakeML, forKey: .waterIntakeML)
        try container.encode(dietaryRestrictions, forKey: .dietaryRestrictions)
        try container.encode(exerciseMinutesDaily, forKey: .exerciseMinutesDaily)
        try container.encode(walkingFrequency, forKey: .walkingFrequency)
        try container.encode(favoriteActivities, forKey: .favoriteActivities)
        try container.encode(trainingLevel, forKey: .trainingLevel)
        try container.encode(knownCommands, forKey: .knownCommands)
        try container.encode(behaviorIssues, forKey: .behaviorIssues)
        try container.encode(socialBehavior, forKey: .socialBehavior)
        try container.encode(veterinarianInfo, forKey: .veterinarianInfo)
    }
}

// MARK: - Emergency & Veterinary Info

// EmergencyContact is defined in SharedTypes.swift

struct VeterinarianInfo: Codable, Hashable {
    var clinicName: String?
    var veterinarianName: String?
    var phoneNumber: String?
    var emergencyPhoneNumber: String?
    var address: String?
    var email: String?
    var website: String?
    var is24Hour: Bool?
    var specialties: [String]?
    var notes: String?
    
    // Provide default values for required usage
    var displayName: String {
        return clinicName ?? veterinarianName ?? "Unknown Clinic"
    }
    
    var displayPhone: String {
        return phoneNumber ?? emergencyPhoneNumber ?? "No phone number"
    }
}

struct InsuranceInfo: Codable, Hashable {
    var provider: String?
    var policyNumber: String?
    var groupNumber: String?
    var memberID: String?
    var phoneNumber: String?
    var website: String?
    var coverageDetails: String?
    var deductible: Double?
    var copayAmount: Double?
    
    // Provide default values for required usage
    var displayProvider: String {
        return provider ?? "Unknown Provider"
    }
    
    var displayPolicyNumber: String {
        return policyNumber ?? "No Policy Number"
    }
}

// MARK: - Health & Medical

struct Medication: Identifiable, Codable, Hashable {
    let id: String
    var name: String
    var dosage: String
    var frequency: String // "twice daily", "as needed", etc.
    var prescribedBy: String
    var startDate: Date
    var endDate: Date?
    var purpose: String
    var sideEffects: [String]
    var instructions: String?
    var isActive: Bool
    var reminderTimes: [Date] // Daily reminder times
    
    init(
        id: String = UUID().uuidString,
        name: String,
        dosage: String,
        frequency: String,
        prescribedBy: String,
        startDate: Date,
        endDate: Date? = nil,
        purpose: String,
        sideEffects: [String] = [],
        instructions: String? = nil,
        isActive: Bool = true,
        reminderTimes: [Date] = []
    ) {
        self.id = id
        self.name = name
        self.dosage = dosage
        self.frequency = frequency
        self.prescribedBy = prescribedBy
        self.startDate = startDate
        self.endDate = endDate
        self.purpose = purpose
        self.sideEffects = sideEffects
        self.instructions = instructions
        self.isActive = isActive
        self.reminderTimes = reminderTimes
    }
}

struct MedicalCondition: Identifiable, Codable, Hashable {
    let id: String
    var name: String
    var diagnosedDate: Date
    var severity: String // "mild", "moderate", "severe"
    var status: String // "active", "managed", "resolved"
    var treatmentPlan: String?
    var notes: String?
    var relatedMedications: [String] // Medication IDs
    
    init(
        id: String = UUID().uuidString,
        name: String,
        diagnosedDate: Date,
        severity: String = "mild",
        status: String = "active",
        treatmentPlan: String? = nil,
        notes: String? = nil,
        relatedMedications: [String] = []
    ) {
        self.id = id
        self.name = name
        self.diagnosedDate = diagnosedDate
        self.severity = severity
        self.status = status
        self.treatmentPlan = treatmentPlan
        self.notes = notes
        self.relatedMedications = relatedMedications
    }
}

// HealthAlert is defined in SharedTypes.swift

struct VaccinationRecord: Identifiable, Codable, Hashable {
    let id: String
    var vaccineName: String
    var dateAdministered: Date
    var nextDueDate: Date?
    var veterinarian: String
    var clinic: String
    var lotNumber: String?
    var manufacturer: String?
    var notes: String?
    var isCore: Bool // Core vs non-core vaccines
    
    init(
        id: String = UUID().uuidString,
        vaccineName: String,
        dateAdministered: Date,
        nextDueDate: Date? = nil,
        veterinarian: String,
        clinic: String,
        lotNumber: String? = nil,
        manufacturer: String? = nil,
        notes: String? = nil,
        isCore: Bool = true
    ) {
        self.id = id
        self.vaccineName = vaccineName
        self.dateAdministered = dateAdministered
        self.nextDueDate = nextDueDate
        self.veterinarian = veterinarian
        self.clinic = clinic
        self.lotNumber = lotNumber
        self.manufacturer = manufacturer
        self.notes = notes
        self.isCore = isCore
    }
}

struct WeightRecord: Identifiable, Codable, Hashable {
    let id: String
    var weight: Double // in pounds
    var date: Date
    var notes: String?
    var measuredBy: String? // "owner", "vet", "groomer"
    
    init(
        id: String = UUID().uuidString,
        weight: Double,
        date: Date,
        notes: String? = nil,
        measuredBy: String? = nil
    ) {
        self.id = id
        self.weight = weight
        self.date = date
        self.notes = notes
        self.measuredBy = measuredBy
    }
}

struct SymptomLog: Identifiable, Codable, Hashable {
    let id: String
    var symptom: String
    var severity: Int // 1-10 scale
    var dateObserved: Date
    var duration: String? // "30 minutes", "all day", etc.
    var triggers: [String]
    var treatments: [String]
    var photos: [String] // URLs to photos
    var notes: String?
    var followUpRequired: Bool
    
    init(
        id: String = UUID().uuidString,
        symptom: String,
        severity: Int,
        dateObserved: Date,
        duration: String? = nil,
        triggers: [String] = [],
        treatments: [String] = [],
        photos: [String] = [],
        notes: String? = nil,
        followUpRequired: Bool = false
    ) {
        self.id = id
        self.symptom = symptom
        self.severity = severity
        self.dateObserved = dateObserved
        self.duration = duration
        self.triggers = triggers
        self.treatments = treatments
        self.photos = photos
        self.notes = notes
        self.followUpRequired = followUpRequired
    }
}

// MARK: - Daily Care & Routine

struct FeedingTime: Identifiable, Codable, Hashable {
    let id: String
    var time: Date // Time of day
    var foodType: String
    var amount: String
    var isCompleted: Bool
    var completedAt: Date?
    var notes: String?
    
    init(
        id: String = UUID().uuidString,
        time: Date,
        foodType: String,
        amount: String,
        isCompleted: Bool = false,
        completedAt: Date? = nil,
        notes: String? = nil
    ) {
        self.id = id
        self.time = time
        self.foodType = foodType
        self.amount = amount
        self.isCompleted = isCompleted
        self.completedAt = completedAt
        self.notes = notes
    }
}

struct ExerciseNeeds: Codable, Hashable {
    var dailyMinutes: Int
    var exerciseType: [String] // "walk", "play", "training"
    var intensity: String // "low", "moderate", "high"
    var restrictions: [String] // Physical limitations
    var preferredTimes: [Date] // Preferred exercise times
    
    init(
        dailyMinutes: Int = 30,
        exerciseType: [String] = ["walk"],
        intensity: String = "moderate",
        restrictions: [String] = [],
        preferredTimes: [Date] = []
    ) {
        self.dailyMinutes = dailyMinutes
        self.exerciseType = exerciseType
        self.intensity = intensity
        self.restrictions = restrictions
        self.preferredTimes = preferredTimes
    }
}

// MARK: - Location & Safety

struct LocationRecord: Codable, Hashable {
    var latitude: Double
    var longitude: Double
    var timestamp: Date
    var accuracy: Double
    var address: String?
    
    init(
        latitude: Double,
        longitude: Double,
        timestamp: Date = Date(),
        accuracy: Double,
        address: String? = nil
    ) {
        self.latitude = latitude
        self.longitude = longitude
        self.timestamp = timestamp
        self.accuracy = accuracy
        self.address = address
    }
}

struct CollarInfo: Codable, Hashable {
    var type: String // "regular", "GPS", "smart"
    var color: String
    var hasNameTag: Bool
    var hasGPS: Bool
    var batteryLevel: Double? // For smart collars
    var lastSync: Date?
    
    init(
        type: String = "regular",
        color: String,
        hasNameTag: Bool = true,
        hasGPS: Bool = false,
        batteryLevel: Double? = nil,
        lastSync: Date? = nil
    ) {
        self.type = type
        self.color = color
        self.hasNameTag = hasNameTag
        self.hasGPS = hasGPS
        self.batteryLevel = batteryLevel
        self.lastSync = lastSync
    }
}

// MARK: - Pet Extensions for Practical Features

extension Pet {
    var ageInYears: Double {
        return Double(age) / 12.0
    }
    
    var isOverdue: Bool {
        // Check if any medications or vaccinations are overdue
        let overdueMeds = medications.filter { medication in
            medication.isActive && medication.reminderTimes.contains { reminderTime in
                Calendar.current.isDate(reminderTime, inSameDayAs: Date()) && reminderTime < Date()
            }
        }
        
        let overdueVaccinations = vaccinationRecords.filter { vaccination in
            if let nextDue = vaccination.nextDueDate {
                return nextDue < Date()
            }
            return false
        }
        
        return !overdueMeds.isEmpty || !overdueVaccinations.isEmpty
    }
    
    var criticalHealthAlerts: [HealthAlert] {
        return healthAlerts.filter { $0.severity == .critical && $0.isActive && !$0.isAcknowledged }
    }
    
    var upcomingMedications: [Medication] {
        let today = Date()
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today) ?? today
        
        return medications.filter { medication in
            medication.isActive && medication.reminderTimes.contains { reminderTime in
                reminderTime >= today && reminderTime <= tomorrow
            }
        }
    }
    
    var emergencyContactInfo: String {
        let primaryContact = emergencyContacts.first { $0.type == .emergency } ?? 
                           emergencyContacts.first { $0.type == .veterinarian } ?? 
                           emergencyContacts.first
        guard let contact = primaryContact else { return "No emergency contact" }
        return "\(contact.name): \(contact.phoneNumber)"
    }
    
    var quickMedicalSummary: String {
        var summary = "\(name) - \(species.capitalized)"
        if let breed = breed { summary += " (\(breed))" }
        let ageYears = ageInYears
        summary += ", \(String(format: "%.1f", ageYears)) years old"
        if let weight = weight { summary += ", \(String(format: "%.1f", weight)) lbs" }
        
        if !allergies.isEmpty {
            summary += "\nAllergies: \(allergies.joined(separator: ", "))"
        }
        
        if !medications.isEmpty {
            let activeMeds = medications.filter { $0.isActive }
            if !activeMeds.isEmpty {
                summary += "\nMedications: \(activeMeds.map { $0.name }.joined(separator: ", "))"
            }
        }
        
        return summary
    }
}

// MARK: - Pet Extensions
extension Pet {
    var displayAge: String {
        if let dateOfBirth = dateOfBirth {
            let calendar = Calendar.current
            let endDate = isLost ? Date() : Date()
            let components = calendar.dateComponents([.year, .month], from: dateOfBirth, to: endDate)

            if let years = components.year, years > 0 {
                return "\(years) year\(years == 1 ? "" : "s") old"
            } else if let months = components.month, months > 0 {
                return "\(months) month\(months == 1 ? "" : "s") old"
            } else {
                return "Less than a month old"
            }
        } else {
            return "\(age) month\(age == 1 ? "" : "s") old"
        }
    }

    var memoryCount: Int {
        // TODO: Implement proper memory count when memory system is ready
        return 0
    }

    var vaultCount: Int {
        // TODO: Implement proper vault count when vault system is ready
        return 0
    }

    // MARK: - Sample Data
    static let samplePet: Pet = {
        let pet = Pet(
            name: "Buddy",
            species: "Dog",
            breed: "Golden Retriever",
            age: 36 // 3 years in months
        )
        pet.weight = 65.0
        pet.dateOfBirth = Calendar.current.date(byAdding: .year, value: -3, to: Date())
        pet.adoptionDate = Date()
        pet.isSpayedNeutered = false
        pet.sex = "male"
        pet.activityLevel = "moderate"
        pet.personalityTraits = ["friendly", "energetic"]
        return pet
    }()
}

// MARK: - Sendable PetEntity for App Intents (Swift 6 Fix)
@available(iOS 18.0, *)
struct PetEntity: AppEntity, Sendable {
    static var typeDisplayRepresentation: TypeDisplayRepresentation = TypeDisplayRepresentation(name: "Pet")
    static var defaultQuery = PetEntityQuery()
    
    var id: String
    var name: String
    var species: String
    var breed: String?
    var age: Int
    var weight: Double?
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)", subtitle: "\(species)")
    }
    
    // Convert from Pet to PetEntity
    init(from pet: Pet) {
        self.id = "\(pet.persistentModelID)"
        self.name = pet.name
        self.species = pet.species
        self.breed = pet.breed
        self.age = pet.age
        self.weight = pet.weight
    }
    
    // Direct initialization
    init(id: String, name: String, species: String, breed: String?, age: Int, weight: Double?) {
        self.id = id
        self.name = name
        self.species = species
        self.breed = breed
        self.age = age
        self.weight = weight
    }
}

// MARK: - PetEntityQuery for AppIntents
@available(iOS 18.0, *)
struct PetEntityQuery: EntityQuery {
    func entities(for identifiers: [PetEntity.ID]) async throws -> [PetEntity] {
        let petService = await RealDataService()
        let pets = await MainActor.run { petService.pets }
        
        // Extract data first to avoid Sendable conformance issues
        let petData = pets.map { (id: "\($0.persistentModelID)", name: $0.name, species: $0.species, breed: $0.breed, age: $0.age, weight: $0.weight) }
        
        return petData.compactMap { petInfo in
            guard identifiers.contains(petInfo.id) else { return nil }
            return PetEntity(
                id: petInfo.id,
                name: petInfo.name,
                species: petInfo.species,
                breed: petInfo.breed,
                age: petInfo.age,
                weight: petInfo.weight
            )
        }
    }
    
    func suggestedEntities() async throws -> [PetEntity] {
        let petService = await RealDataService()
        let pets = await MainActor.run { petService.pets }
        
        // Extract data first to avoid Sendable conformance issues  
        let petData = pets.map { (id: "\($0.persistentModelID)", name: $0.name, species: $0.species, breed: $0.breed, age: $0.age, weight: $0.weight) }
        
        return petData.map { petInfo in
            PetEntity(
                id: petInfo.id,
                name: petInfo.name,
                species: petInfo.species,
                breed: petInfo.breed,
                age: petInfo.age,
                weight: petInfo.weight
            )
        }
    }
}
