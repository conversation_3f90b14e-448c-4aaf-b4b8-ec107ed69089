//
//  PetBreedData.swift
//  PetCapsule
//
//  Species-specific breed data and selection logic
//

import Foundation

struct PetBreedData {
    static func getBreeds(for species: String) -> [String] {
        switch species.lowercased() {
        case "dog":
            return dogBreeds
        case "cat":
            return catBreeds
        case "bird":
            return birdBreeds
        case "rabbit":
            return rabbitBreeds
        case "hamster":
            return hamsterBreeds
        case "fish":
            return fishBreeds
        case "reptile":
            return reptileBreeds
        default:
            return ["Not Applicable", "Mixed/Unknown", "Other"]
        }
    }
    
    static let dogBreeds = [
        "Golden Retriever", "Labrador Retriever", "German Shepherd", "Bulldog",
        "Poodle", "Beagle", "Rottweiler", "Yorkshire Terrier", "Dachshund",
        "Siberian Husky", "Border Collie", "French Bulldog", "Australian Shepherd",
        "Shih Tzu", "Boston Terrier", "Pomeranian", "Great Dane", "Chihuahua",
        "Boxer", "Cocker Spaniel", "Mixed Breed", "Other"
    ]
    
    static let catBreeds = [
        "Domestic Shorthair", "Domestic Longhair", "Maine Coon", "Persian",
        "Siamese", "British Shorthair", "Russian Blue", "Ragdoll", "Bengal",
        "Abyssinian", "Scottish Fold", "Sphynx", "Norwegian Forest Cat",
        "Birman", "Oriental Shorthair", "American Shorthair", "Exotic Shorthair",
        "Turkish Angora", "Manx", "Himalayan", "Mixed Breed", "Other"
    ]
    
    static let birdBreeds = [
        "Budgerigar", "Cockatiel", "Canary", "Lovebird", "Conure",
        "African Grey Parrot", "Macaw", "Cockatoo", "Amazon Parrot",
        "Finch", "Parakeet", "Caique", "Eclectus", "Lorikeet",
        "Quaker Parrot", "Senegal Parrot", "Sun Conure", "Green Cheek Conure",
        "Mixed/Unknown", "Other"
    ]
    
    static let rabbitBreeds = [
        "Holland Lop", "Netherland Dwarf", "Mini Rex", "Lionhead",
        "Dutch", "Flemish Giant", "New Zealand White", "Californian",
        "Rex", "English Angora", "French Lop", "Mini Lop",
        "Himalayan", "Polish", "Jersey Wooly", "Havana",
        "Mixed Breed", "Other"
    ]
    
    static let hamsterBreeds = [
        "Syrian (Golden)", "Dwarf Campbell Russian", "Dwarf Winter White Russian",
        "Roborovski Dwarf", "Chinese", "European", "Mixed/Unknown", "Other"
    ]
    
    static let fishBreeds = [
        "Goldfish", "Betta", "Guppy", "Neon Tetra", "Angelfish",
        "Molly", "Platy", "Swordtail", "Corydoras", "Zebra Danio",
        "Cardinal Tetra", "Discus", "Cichlid", "Barb", "Rasbora",
        "Mixed/Unknown", "Other"
    ]
    
    static let reptileBreeds = [
        "Bearded Dragon", "Ball Python", "Leopard Gecko", "Corn Snake",
        "Blue-tongued Skink", "Crested Gecko", "Red-eared Slider",
        "Russian Tortoise", "Iguana", "Monitor Lizard", "King Snake",
        "Chameleon", "Uromastyx", "Tegu", "Mixed/Unknown", "Other"
    ]
    
    static func getSpeciesIcon(for species: String) -> String {
        switch species.lowercased() {
        case "dog": return "🐕"
        case "cat": return "🐱"
        case "bird": return "🦜"
        case "rabbit": return "🐰"
        case "hamster": return "🐹"
        case "fish": return "🐠"
        case "reptile": return "🦎"
        case "other": return "🐾"
        default: return "🐾"
        }
    }
    
    static func getWeightRange(for species: String, breed: String? = nil) -> ClosedRange<Double> {
        switch species.lowercased() {
        case "dog":
            guard let breed = breed?.lowercased() else { return 2.0...80.0 }
            switch breed {
            case "chihuahua": return 1.5...3.0
            case "yorkshire terrier": return 2.0...3.5
            case "pomeranian": return 1.5...3.5
            case "french bulldog": return 9.0...13.0
            case "beagle": return 9.0...11.0
            case "border collie": return 14.0...20.0
            case "golden retriever": return 25.0...32.0
            case "labrador retriever": return 25.0...36.0
            case "german shepherd": return 22.0...40.0
            case "great dane": return 45.0...90.0
            default: return 2.0...80.0
            }
        case "cat":
            return 2.0...10.0
        case "bird":
            return 0.01...2.0
        case "rabbit":
            return 0.5...8.0
        case "hamster":
            return 0.02...0.2
        case "fish":
            return 0.001...5.0
        case "reptile":
            return 0.01...50.0
        default:
            return 0.1...100.0
        }
    }
    
    static func getLifeExpectancy(for species: String, breed: String? = nil) -> ClosedRange<Int> {
        switch species.lowercased() {
        case "dog":
            guard let breed = breed?.lowercased() else { return 10...16 }
            switch breed {
            case "chihuahua", "yorkshire terrier", "pomeranian": return 12...18
            case "french bulldog", "bulldog": return 8...12
            case "great dane": return 6...10
            case "border collie", "australian shepherd": return 12...15
            case "golden retriever", "labrador retriever": return 10...14
            default: return 10...16
            }
        case "cat":
            return 12...20
        case "bird":
            return 5...80 // Varies greatly by species
        case "rabbit":
            return 8...12
        case "hamster":
            return 2...4
        case "fish":
            return 1...20
        case "reptile":
            return 10...50
        default:
            return 5...15
        }
    }
} 