//
//  PlannerModels.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//
import SwiftUI
import Foundation
import CoreLocation
// Type alias to resolve ambiguity with multiple AlertSeverity definitions
typealias PlannerAlertSeverity = AlertSeverity
// MARK: - Location Category for Map Integration
enum LocationCategory: String, CaseIterable {
    case park = "park"
    case petStore = "pet_store"
    case restaurant = "restaurant"
    case veterinary = "veterinary"
    case grooming = "grooming"
    case beach = "beach"
    case trail = "trail"
    case other = "other"
    var icon: String {
        switch self {
        case .park: return "tree.fill"
        case .petStore: return "bag.fill"
        case .restaurant: return "fork.knife"
        case .veterinary: return "cross.fill"
        case .grooming: return "scissors"
        case .beach: return "beach.umbrella.fill"
        case .trail: return "figure.hiking"
        case .other: return "mappin"
        }
    }
}
// MARK: - Weather Data
struct WeatherData {
    let temperature: Int
    let humidity: Int
    let windSpeed: Double
    let condition: String
    let icon: String
    static let sample = WeatherData(
        temperature: 72,
        humidity: 45,
        windSpeed: 5.2,
        condition: "Sunny",
        icon: "sun.max.fill"
    )
}
// MARK: - Air Quality Data
struct AirQualityData {
    let index: Int
    let description: String
    let color: Color
    static let sample = AirQualityData(
        index: 42,
        description: "Good",
        color: .green
    )
}
// MARK: - Hourly Forecast
struct HourlyForecast {
    let hour: String
    let temperature: Int
    let icon: String
    let color: Color
    let walkQuality: WalkQuality
    static let sampleData = [
        HourlyForecast(hour: "9 AM", temperature: 68, icon: "sun.max", color: .orange, walkQuality: .excellent),
        HourlyForecast(hour: "10 AM", temperature: 72, icon: "sun.max", color: .orange, walkQuality: .excellent),
        HourlyForecast(hour: "11 AM", temperature: 75, icon: "sun.max", color: .orange, walkQuality: .good),
        HourlyForecast(hour: "12 PM", temperature: 78, icon: "sun.max", color: .orange, walkQuality: .good),
        HourlyForecast(hour: "1 PM", temperature: 82, icon: "sun.max", color: .red, walkQuality: .fair),
        HourlyForecast(hour: "2 PM", temperature: 85, icon: "sun.max", color: .red, walkQuality: .poor),
        HourlyForecast(hour: "3 PM", temperature: 83, icon: "cloud.sun", color: .blue, walkQuality: .fair),
        HourlyForecast(hour: "4 PM", temperature: 80, icon: "cloud.sun", color: .blue, walkQuality: .good)
    ]
}
// MARK: - Weekly Forecast
struct WeeklyForecast {
    let day: String
    let highTemp: Int
    let lowTemp: Int
    let icon: String
    let color: Color
    let walkQuality: WalkQuality
    static let sampleData = [
        WeeklyForecast(day: "Today", highTemp: 78, lowTemp: 62, icon: "sun.max", color: .orange, walkQuality: .excellent),
        WeeklyForecast(day: "Tomorrow", highTemp: 75, lowTemp: 58, icon: "cloud.sun", color: .blue, walkQuality: .good),
        WeeklyForecast(day: "Wednesday", highTemp: 72, lowTemp: 55, icon: "cloud", color: .gray, walkQuality: .good),
        WeeklyForecast(day: "Thursday", highTemp: 69, lowTemp: 52, icon: "cloud.rain", color: .blue, walkQuality: .poor),
        WeeklyForecast(day: "Friday", highTemp: 74, lowTemp: 56, icon: "cloud.sun", color: .blue, walkQuality: .good),
        WeeklyForecast(day: "Saturday", highTemp: 79, lowTemp: 61, icon: "sun.max", color: .orange, walkQuality: .excellent),
        WeeklyForecast(day: "Sunday", highTemp: 82, lowTemp: 64, icon: "sun.max", color: .orange, walkQuality: .good)
    ]
}
// MARK: - Walk Quality
enum WalkQuality: String, CaseIterable {
    case excellent = "Excellent"
    case good = "Good"
    case fair = "Fair"
    case poor = "Poor"
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .orange
        case .poor: return .red
        }
    }
    var description: String {
        return self.rawValue
    }
}
// MARK: - Pet Friendly Location
struct PetFriendlyLocation {
    let id = UUID()
    let name: String
    let type: LocationType
    let rating: Double
    let distance: Double
    let address: String
    let imageURL: String
    let amenities: [String]
    let isOpen: Bool
    let coordinate: CLLocationCoordinate2D
    let phoneNumber: String?
    let website: String?
    let hours: String?
    // Computed property for compatibility with map views
    var category: LocationCategory {
        switch type {
        case .park: return .park
        case .store: return .petStore
        case .restaurant: return .restaurant
        case .veterinary: return .veterinary
        case .trail: return .trail
        case .beach: return .beach
        case .hotel: return .other
        }
    }
    static let sampleData = [
        PetFriendlyLocation(
            name: "Central Park Dog Run",
            type: .park,
            rating: 4.8,
            distance: 0.3,
            address: "123 Park Ave, New York, NY",
            imageURL: "https://images.unsplash.com/photo-1544737151-6e4b9e0e4d5a?w=400&h=300&fit=crop",
            amenities: ["Off-leash area", "Water fountains", "Waste bags"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 40.7829, longitude: -73.9654),
            phoneNumber: "(*************",
            website: "https://www.centralparknyc.org",
            hours: "6:00 AM - 1:00 AM"
        ),
        PetFriendlyLocation(
            name: "Pawsome Pet Store",
            type: .store,
            rating: 4.5,
            distance: 0.7,
            address: "456 Main St, New York, NY",
            imageURL: "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop",
            amenities: ["Pet supplies", "Grooming", "Training classes"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 40.7580, longitude: -73.9855),
            phoneNumber: "(*************",
            website: "https://pawsomepetstore.com",
            hours: "9:00 AM - 8:00 PM"
        ),
        PetFriendlyLocation(
            name: "The Barking Lot Cafe",
            type: .restaurant,
            rating: 4.3,
            distance: 1.2,
            address: "789 Coffee St, New York, NY",
            imageURL: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop",
            amenities: ["Pet-friendly patio", "Dog treats", "Water bowls"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 40.7614, longitude: -73.9776),
            phoneNumber: "(*************",
            website: "https://barkinglotcafe.com",
            hours: "7:00 AM - 9:00 PM"
        ),
        PetFriendlyLocation(
            name: "Happy Tails Veterinary",
            type: .veterinary,
            rating: 4.9,
            distance: 2.1,
            address: "321 Health Ave, New York, NY",
            imageURL: "https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=400&h=300&fit=crop",
            amenities: ["Emergency care", "Grooming", "Boarding"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 40.7505, longitude: -73.9934),
            phoneNumber: "(*************",
            website: "https://happytailsvet.com",
            hours: "8:00 AM - 6:00 PM"
        ),
        PetFriendlyLocation(
            name: "Riverside Walking Trail",
            type: .trail,
            rating: 4.6,
            distance: 1.8,
            address: "Riverside Dr, New York, NY",
            imageURL: "https://images.unsplash.com/photo-1506784365867-9513bfef44c3?w=400&h=300&fit=crop",
            amenities: ["Scenic views", "Multiple trails", "Pet waste stations"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 40.7851, longitude: -73.9683),
            phoneNumber: nil,
            website: "https://nycparks.org",
            hours: "6:00 AM - 10:00 PM"
        )
    ]
}
// MARK: - Location Type
enum LocationType: String, CaseIterable {
    case park = "Park"
    case store = "Pet Store"
    case restaurant = "Restaurant"
    case veterinary = "Veterinary"
    case trail = "Trail"
    case beach = "Beach"
    case hotel = "Pet Hotel"
    var displayName: String {
        switch self {
        case .park: return "Parks"
        case .store: return "Pet Stores"
        case .restaurant: return "Restaurants"
        case .veterinary: return "Veterinary"
        case .trail: return "Trails"
        case .beach: return "Beaches"
        case .hotel: return "Pet Hotels"
        }
    }
    var color: Color {
        switch self {
        case .park: return .green
        case .store: return .blue
        case .restaurant: return .orange
        case .veterinary: return .red
        case .trail: return .brown
        case .beach: return .cyan
        case .hotel: return .purple
        }
    }
    var icon: String {
        switch self {
        case .park: return "tree.fill"
        case .store: return "bag.fill"
        case .restaurant: return "fork.knife"
        case .veterinary: return "cross.fill"
        case .trail: return "figure.hiking"
        case .beach: return "beach.umbrella.fill"
        case .hotel: return "bed.double.fill"
        }
    }
}
// MARK: - Environmental Alert UI Model (for display purposes)
struct EnvironmentalAlertUI: Identifiable {
    let id = UUID()
    let title: String
    let message: String
    let severity: PlannerAlertSeverity
    let icon: String
    let timeAgo: String
}
// AlertSeverity is now defined in SharedTypes.swift as SharedAlertSeverity
// MARK: - Walk Memory (moved to PlannerAppleNativeDataService.swift)
// MARK: - Community Event (moved to PlannerAppleNativeDataService.swift)
// MARK: - Event Category (keeping for UI compatibility)
enum EventCategory: String, CaseIterable {
    case all = "all"
    case walks = "walks"
    case training = "training"
    case social = "social"
    case adoption = "adoption"
    case emergency = "emergency"
    var displayName: String {
        switch self {
        case .all: return "All"
        case .walks: return "Walks"
        case .training: return "Training"
        case .social: return "Social"
        case .adoption: return "Adoption"
        case .emergency: return "Emergency"
        }
    }
    var color: Color {
        switch self {
        case .all: return .blue
        case .walks: return .green
        case .training: return .blue
        case .social: return .orange
        case .adoption: return .purple
        case .emergency: return .red
        }
    }
    var icon: String {
        switch self {
        case .all: return "calendar"
        case .walks: return "figure.walk"
        case .training: return "graduationcap.fill"
        case .social: return "person.3.fill"
        case .adoption: return "heart.fill"
        case .emergency: return "cross.case.fill"
        }
    }
}
// MARK: - Missing Model Types (Stubs)
struct WalkMemory: Identifiable, Codable {
    let id: String = UUID().uuidString
    let title: String
    let description: String?
    let date: Date
    let duration: TimeInterval?
    let distance: Double?
    let distanceMeters: Double?
    let locationName: String?
    let latitude: Double
    let longitude: Double
    let isFavorite: Bool
    let createdAt: Date
    let durationMinutes: Int?
    let mediaUrls: [String]
    let temperature: Double?
    let humidity: Double?
    let tags: [String]
    init(title: String, description: String? = nil, date: Date = Date(), duration: TimeInterval? = nil, distance: Double? = nil, distanceMeters: Double? = nil, locationName: String? = nil, latitude: Double = 0.0, longitude: Double = 0.0, isFavorite: Bool = false, durationMinutes: Int? = nil, mediaUrls: [String] = [], temperature: Double? = nil, humidity: Double? = nil, tags: [String] = []) {
        self.title = title
        self.description = description
        self.date = date
        self.duration = duration
        self.distance = distance
        self.distanceMeters = distanceMeters
        self.locationName = locationName
        self.latitude = latitude
        self.longitude = longitude
        self.isFavorite = isFavorite
        self.createdAt = Date()
        self.durationMinutes = durationMinutes
        self.mediaUrls = mediaUrls
        self.temperature = temperature
        self.humidity = humidity
        self.tags = tags
    }
}
struct CommunityEvent: Identifiable, Codable {
    let id = UUID()
    var title: String = ""
    var description: String? = nil
    var date: Date = Date()
    var location: String = ""
    var organizerID: String = ""
    var attendeeIDs: [String] = []
    var latitude: Double = 0.0
    var longitude: Double = 0.0
    var currentParticipants: Int = 0
    var maxAttendees: Int? = nil
    var minAirQualityScore: Int = 100
    var status: String = "active"
}
struct WalkPlan: Identifiable, Codable {
    let id = UUID()
    var title: String = ""
    var description: String = ""
    var scheduledDate: Date = Date()
    var duration: TimeInterval = 3600
    var petID: String = ""
    var routePoints: [WalkLocation] = []
}
struct WalkLocation: Identifiable, Codable {
    let id = UUID()
    var name: String = ""
    var latitude: Double = 0.0
    var longitude: Double = 0.0
    var description: String = ""
}
enum RSVPStatus: String, Codable, CaseIterable {
    case attending = "attending"
    case maybe = "maybe"
    case notAttending = "not_attending"
    var icon: String {
        switch self {
        case .attending: return "checkmark.circle.fill"
        case .maybe: return "questionmark.circle.fill"
        case .notAttending: return "xmark.circle.fill"
        }
    }
    var color: Color {
        switch self {
        case .attending: return .green
        case .maybe: return .orange
        case .notAttending: return .red
        }
    }
    var displayName: String {
        switch self {
        case .attending: return "Attending"
        case .maybe: return "Maybe"
        case .notAttending: return "Not Attending"
        }
    }
}
struct RevenueData: Identifiable, Codable {
    let id = UUID()
    var month: String = ""
    var amount: Double = 0.0
    var date: Date = Date()
}
struct UserGrowthData: Identifiable, Codable {
    let id = UUID()
    var month: String = ""
    var newUsers: Int = 0
    var totalUsers: Int = 0
    var date: Date = Date()
}
struct EngagementData: Identifiable, Codable {
    let id = UUID()
    var metric: String = ""
    var value: Double = 0.0
    var date: Date = Date()
}
struct ConversionData: Identifiable, Codable {
    let id = UUID()
    var source: String = ""
    var conversions: Int = 0
    var rate: Double = 0.0
    var date: Date = Date()
}
struct CodableMemory: Codable {
    let id: UUID
    let title: String
    let content: String
    let type: MemoryType
    let createdAt: Date
    let updatedAt: Date
}
enum EmergencyContactType: String, CaseIterable, Codable {
    case emergency = "emergency"
    case veterinarian = "veterinarian"
    case custom = "custom"
    case family = "family"
    case friend = "friend"
    case poisonControl = "poison_control"
    case animalHospital = "animal_hospital"
    var displayName: String {
        switch self {
        case .emergency: return "Emergency Service"
        case .veterinarian: return "Veterinarian"
        case .custom: return "Custom Contact"
        case .family: return "Family"
        case .friend: return "Friend"
        case .poisonControl: return "Poison Control"
        case .animalHospital: return "Animal Hospital"
        }
    }
    var icon: String {
        switch self {
        case .emergency: return "phone.badge.plus"
        case .veterinarian: return "stethoscope"
        case .custom: return "person.crop.circle.badge.plus"
        case .family: return "house"
        case .friend: return "person.2"
        case .poisonControl: return "exclamationmark.triangle.fill"
        case .animalHospital: return "cross.case.fill"
        }
    }
    var color: Color {
        switch self {
        case .emergency: return .red
        case .veterinarian: return .blue
        case .custom: return .purple
        case .family: return .green
        case .friend: return .orange
        case .poisonControl: return .orange
        case .animalHospital: return .green
        }
    }
}
enum SecureVault {
    case walkMemory(WalkMemory)
    case vault(Vault)
    case memory(Memory)
    var id: String {
        switch self {
        case .walkMemory(let walkMemory):
            return walkMemory.id
        case .vault(let vault):
            return vault.id.uuidString
        case .memory(let memory):
            return memory.id.uuidString
        }
    }
    var createdAt: Date {
        switch self {
        case .walkMemory(let walkMemory):
            return walkMemory.createdAt
        case .vault(let vault):
            return vault.createdAt
        case .memory(let memory):
            return memory.createdAt
        }
    }
    var name: String {
        switch self {
        case .walkMemory(let walkMemory):
            return walkMemory.title
        case .vault(let vault):
            return vault.name
        case .memory(let memory):
            return memory.title
        }
    }
    var description: String {
        switch self {
        case .walkMemory(let walkMemory):
            return walkMemory.description ?? ""
        case .vault(let vault):
            return vault.vaultDescription
        case .memory(let memory):
            return memory.content
        }
    }
}
