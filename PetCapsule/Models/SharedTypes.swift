//
//  SharedTypes.swift
//  PetCapsule
//
//  Centralized type definitions to resolve ambiguities
//
import Foundation
import SwiftUI
import CoreLocation
import MapKit
// MARK: - Codable Extensions
extension CLLocationCoordinate2D: Codable {
    public enum CodingKeys: String, CodingKey {
        case latitude
        case longitude
    }
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(latitude, forKey: .latitude)
        try container.encode(longitude, forKey: .longitude)
    }
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let latitude = try container.decode(CLLocationDegrees.self, forKey: .latitude)
        let longitude = try container.decode(CLLocationDegrees.self, forKey: .longitude)
        self.init(latitude: latitude, longitude: longitude)
    }
}
// MARK: - Alert Types
enum AlertSeverity: String, CaseIterable, Codable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case critical = "critical"
    case warning = "warning"
    var displayName: String {
        rawValue.capitalized
    }
    var color: Color {
        switch self {
        case .low: return .blue
        case .moderate: return .orange
        case .high: return .red
        case .critical: return .purple
        case .warning: return .yellow
        }
    }
    var icon: String {
        switch self {
        case .low: return "info.circle"
        case .moderate: return "exclamationmark.triangle"
        case .high: return "exclamationmark.octagon"
        case .critical: return "exclamationmark.octagon.fill"
        case .warning: return "exclamationmark.triangle.fill"
        }
    }
}
enum EnvironmentalAlertType: String, CaseIterable, Codable {
    case temperature = "temperature"
    case airQuality = "air_quality"
    case pollen = "pollen"
    case weatherCondition = "weather_condition"
    case humidity = "humidity"
    case windSpeed = "wind_speed"
    var displayName: String {
        switch self {
        case .temperature: return "Temperature"
        case .airQuality: return "Air Quality"
        case .pollen: return "Pollen"
        case .weatherCondition: return "Weather Condition"
        case .humidity: return "Humidity"
        case .windSpeed: return "Wind Speed"
        }
    }
    var icon: String {
        switch self {
        case .temperature: return "thermometer"
        case .airQuality: return "aqi.medium"
        case .pollen: return "leaf.fill"
        case .weatherCondition: return "cloud.fill"
        case .humidity: return "humidity.fill"
        case .windSpeed: return "wind"
        }
    }
}
// MARK: - Activity Types
enum ActivityType: String, CaseIterable, Codable {
    case walk = "walk"
    case run = "run"
    case play = "play"
    case training = "training"
    case rest = "rest"
    case feeding = "feeding"
    case grooming = "grooming"
    case vet = "vet"
    var displayName: String {
        rawValue.capitalized
    }
    var icon: String {
        switch self {
        case .walk: return "figure.walk"
        case .run: return "figure.run"
        case .play: return "gamecontroller.fill"
        case .training: return "brain.head.profile"
        case .rest: return "bed.double.fill"
        case .feeding: return "fork.knife"
        case .grooming: return "scissors"
        case .vet: return "cross.fill"
        }
    }
    var color: Color {
        switch self {
        case .walk: return .blue
        case .run: return .green
        case .play: return .orange
        case .training: return .purple
        case .rest: return .indigo
        case .feeding: return .yellow
        case .grooming: return .pink
        case .vet: return .red
        }
    }
}
// MARK: - Health Types
enum HealthMetric: String, CaseIterable, Codable {
    case weight = "weight"
    case activity = "activity"
    case heartRate = "heart_rate"
    case temperature = "temperature"
    case appetite = "appetite"
    case energy = "energy"
    case mood = "mood"
    case sleep = "sleep"
    var displayName: String {
        switch self {
        case .weight: return "Weight"
        case .activity: return "Activity"
        case .heartRate: return "Heart Rate"
        case .temperature: return "Temperature"
        case .appetite: return "Appetite"
        case .energy: return "Energy"
        case .mood: return "Mood"
        case .sleep: return "Sleep"
        }
    }
    var unit: String {
        switch self {
        case .weight: return "lbs"
        case .activity: return "minutes"
        case .heartRate: return "bpm"
        case .temperature: return "°F"
        case .appetite: return "score"
        case .energy: return "score"
        case .mood: return "score"
        case .sleep: return "hours"
        }
    }
    var icon: String {
        switch self {
        case .weight: return "scalemass"
        case .activity: return "figure.walk"
        case .heartRate: return "heart.fill"
        case .temperature: return "thermometer"
        case .appetite: return "fork.knife"
        case .energy: return "bolt.fill"
        case .mood: return "face.smiling"
        case .sleep: return "bed.double.fill"
        }
    }
    var color: Color {
        switch self {
        case .weight: return .blue
        case .activity: return .green
        case .heartRate: return .red
        case .temperature: return .orange
        case .appetite: return .yellow
        case .energy: return .purple
        case .mood: return .pink
        case .sleep: return .indigo
        }
    }
}
enum TimeRange: String, CaseIterable, Codable {
    case day = "day"
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    case year = "year"
    var displayName: String {
        switch self {
        case .day: return "Daily"
        case .week: return "Weekly"
        case .month: return "Monthly"
        case .quarter: return "Quarterly"
        case .year: return "Yearly"
        }
    }
    var timeInterval: TimeInterval {
        switch self {
        case .day: return 24 * 60 * 60
        case .week: return 7 * 24 * 60 * 60
        case .month: return 30 * 24 * 60 * 60
        case .quarter: return 90 * 24 * 60 * 60
        case .year: return 365 * 24 * 60 * 60
        }
    }
}
// MARK: - Social Types
enum ConnectionStatus: String, CaseIterable, Codable {
    case pending = "pending"
    case connecting = "connecting"
    case connected = "connected"
    case blocked = "blocked"
    case declined = "declined"
    var displayName: String {
        rawValue.capitalized
    }
    var color: Color {
        switch self {
        case .pending: return .orange
        case .connecting: return .yellow
        case .connected: return .green
        case .blocked: return .red
        case .declined: return .gray
        }
    }
    var icon: String {
        switch self {
        case .pending: return "clock"
        case .connecting: return "antenna.radiowaves.left.and.right"
        case .connected: return "checkmark.circle"
        case .blocked: return "xmark.circle"
        case .declined: return "xmark"
        }
    }
}
enum PostVisibility: String, CaseIterable, Codable {
    case `public` = "public"
    case friends = "friends"
    case `private` = "private"
    var displayName: String {
        rawValue.capitalized
    }
    var icon: String {
        switch self {
        case .`public`: return "globe"
        case .friends: return "person.2"
        case .`private`: return "lock"
        }
    }
}
enum PlaydateStatus: String, CaseIterable, Codable {
    case pending = "pending"
    case confirmed = "confirmed"
    case cancelled = "cancelled"
    case completed = "completed"
    var displayName: String {
        rawValue.capitalized
    }
    var color: Color {
        switch self {
        case .pending: return .orange
        case .confirmed: return .green
        case .cancelled: return .red
        case .completed: return .blue
        }
    }
}
// MARK: - Emergency Types
enum EmergencyType: String, CaseIterable, Codable {
    case veterinary = "veterinary"
    case poison = "poison"
    case injury = "injury"
    case lost = "lost"
    case natural_disaster = "natural_disaster"
    case other = "other"
    var displayName: String {
        switch self {
        case .veterinary: return "Veterinary Emergency"
        case .poison: return "Poisoning"
        case .injury: return "Injury"
        case .lost: return "Lost Pet"
        case .natural_disaster: return "Natural Disaster"
        case .other: return "Other Emergency"
        }
    }
    var icon: String {
        switch self {
        case .veterinary: return "cross.fill"
        case .poison: return "exclamationmark.triangle.fill"
        case .injury: return "bandage.fill"
        case .lost: return "location.slash.fill"
        case .natural_disaster: return "tornado"
        case .other: return "exclamationmark.circle.fill"
        }
    }
    var color: Color {
        switch self {
        case .veterinary: return .red
        case .poison: return .purple
        case .injury: return .orange
        case .lost: return .blue
        case .natural_disaster: return .gray
        case .other: return .yellow
        }
    }
}
// MARK: - Recommendation Types
enum RecommendationType: String, CaseIterable, Codable {
    case activity = "activity"
    case health = "health"
    case social = "social"
    case environmental = "environmental"
    case nutrition = "nutrition"
    case training = "training"
    case playdate = "playdate"
    case communityEvent = "communityEvent"
    case socialConnection = "socialConnection"
    case petMatch = "petMatch"
    case groupActivity = "groupActivity"
    var displayName: String {
        switch self {
        case .playdate: return "Playdate"
        case .communityEvent: return "Community Event"
        case .socialConnection: return "Social Connection"
        case .petMatch: return "Pet Match"
        case .groupActivity: return "Group Activity"
        default: return rawValue.capitalized
        }
    }
    var icon: String {
        switch self {
        case .activity: return "figure.walk"
        case .health: return "heart.fill"
        case .social: return "person.2.fill"
        case .environmental: return "leaf.fill"
        case .nutrition: return "fork.knife"
        case .training: return "brain.head.profile"
        case .playdate: return "gamecontroller.fill"
        case .communityEvent: return "calendar.badge.plus"
        case .socialConnection: return "person.badge.plus"
        case .petMatch: return "heart.circle"
        case .groupActivity: return "person.3.fill"
        }
    }
    var color: Color {
        switch self {
        case .activity: return .blue
        case .health: return .red
        case .social: return .orange
        case .environmental: return .green
        case .nutrition: return .yellow
        case .training: return .purple
        case .playdate: return .pink
        case .communityEvent: return .cyan
        case .socialConnection: return .indigo
        case .petMatch: return .red
        case .groupActivity: return .mint
        }
    }
}
enum RecommendationPriority: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case urgent = "urgent"
    var displayName: String {
        rawValue.capitalized
    }
    var color: Color {
        switch self {
        case .low: return .blue
        case .medium: return .orange
        case .high: return .red
        case .urgent: return .purple
        }
    }
    var icon: String {
        switch self {
        case .low: return "info.circle"
        case .medium: return "exclamationmark.triangle"
        case .high: return "exclamationmark.octagon"
        case .urgent: return "exclamationmark.octagon.fill"
        }
    }
}
// MARK: - Social Shared Types
struct PostComment: Identifiable, Codable {
    let id: String
    let postId: String
    let authorId: String
    let authorName: String
    let content: String
    let createdAt: Date
    var likesCount: Int
}
struct SharedSocialConnection: Identifiable, Codable {
    let id: String
    let userId: String
    let connectedUserId: String
    let connectedUserName: String
    let connectionType: String
    var status: ConnectionStatus
    let createdAt: Date
    var updatedAt: Date
    let mutualConnections: Int
    let sharedInterests: [String]
}
// Additional social types
enum PlaydateActivityType: String, CaseIterable, Codable {
    case walk = "walk"
    case play = "play"
    case training = "training"
    case socialization = "socialization"
    case exercise = "exercise"
    var displayName: String {
        rawValue.capitalized
    }
}
struct SharedPlaydatePetInfo: Identifiable, Codable {
    let id: String
    let name: String
    let breed: String
    let age: Int
    let size: String
    let temperament: String
    let isVaccinated: Bool
    let ownerName: String
    let profileImageUrl: String?
}
// Health trend type
struct HealthTrend: Identifiable, Codable {
    var id = UUID()
    let trend: HealthTrendType
    let metric: HealthMetric
    var changePercentage: Double = 0.0
    var timeframe: String = "week"
    enum HealthTrendType: String, CaseIterable, Codable {
        case improving = "improving"
        case stable = "stable"
        case declining = "declining"
        case critical = "critical"
        var displayName: String {
            rawValue.capitalized
        }
        var color: Color {
            switch self {
            case .improving: return .green
            case .stable: return .blue
            case .declining: return .orange
            case .critical: return .red
            }
        }
        var icon: String {
            switch self {
            case .improving: return "arrow.up.circle.fill"
            case .stable: return "minus.circle.fill"
            case .declining: return "arrow.down.circle.fill"
            case .critical: return "exclamationmark.triangle.fill"
            }
        }
    }
    var displayName: String { trend.displayName }
    var description: String { trend.displayName }
    var color: Color { trend.color }
    var icon: String { trend.icon }
    init(trend: HealthTrendType, metric: HealthMetric) {
        self.trend = trend
        self.metric = metric
    }
}
// Environmental alert setting type
struct SharedEnvironmentalAlertSetting: Identifiable, Codable {
    let id: String
    let alertType: EnvironmentalAlertType
    var isEnabled: Bool
    var threshold: Double
    var severity: AlertSeverity
    var notificationEnabled: Bool
    let createdAt: Date
    var updatedAt: Date
    // Additional properties for compatibility
    var latitude: Double?
    var longitude: Double?
    var radiusMeters: Int?
    var minThreshold: Double?
    var maxThreshold: Double?
    var conditionValues: [String]? = []
    var petIds: [String] = []
}
struct EnvironmentalAlertSettingResponse: Codable {
    let id: String
    let alertType: String
    let isEnabled: Bool
    let threshold: Double
    let severity: String
    let notificationEnabled: Bool
    let createdAt: String
    let updatedAt: String
    let latitude: Double?
    let longitude: Double?
    let radiusMeters: Int?
    let minThreshold: Double?
    let maxThreshold: Double?
    func toEnvironmentalAlertSetting() -> SharedEnvironmentalAlertSetting {
        return SharedEnvironmentalAlertSetting(
            id: id,
            alertType: EnvironmentalAlertType(rawValue: alertType) ?? .temperature,
            isEnabled: isEnabled,
            threshold: threshold,
            severity: AlertSeverity(rawValue: severity) ?? .moderate,
            notificationEnabled: notificationEnabled,
            createdAt: ISO8601DateFormatter().date(from: createdAt) ?? Date(),
            updatedAt: ISO8601DateFormatter().date(from: updatedAt) ?? Date(),
            latitude: latitude,
            longitude: longitude,
            radiusMeters: radiusMeters,
            minThreshold: minThreshold,
            maxThreshold: maxThreshold
        )
    }
    func toAlertSetting() -> SharedEnvironmentalAlertSetting {
        return toEnvironmentalAlertSetting()
    }
}
// Additional missing types
typealias IntentsEmergencyContact = SharedEmergencyContact
typealias CallServiceEmergencyContact = SharedEmergencyContact
struct SharedAIBehaviorInsights: Identifiable, Codable {
    let id: String
    let petId: String
    let insights: [String]
    let recommendations: [String]
    let confidence: Double
    let generatedAt: Date
}
// Additional missing types for social features
// Note: Removed circular references - using direct struct definitions
enum PlaydateAction: String, CaseIterable, Codable {
    case join = "join"
    case leave = "leave"
    case cancel = "cancel"
    case complete = "complete"
    var displayName: String {
        rawValue.capitalized
    }
}
// Emergency and planner types
typealias SharedEmergencyContactEntry = SharedEmergencyContact
typealias SharedHealthMetrics = HealthMetric
// Add today member to TimeRange
extension TimeRange {
    static var today: TimeRange {
        return .day
    }
}
// Training skill level enum
enum SkillLevel: String, CaseIterable, Codable {
    case needsWork = "needs_work"
    case developing = "developing"
    case proficient = "proficient"
    case excellent = "excellent"
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case allLevels = "all_levels"
    var displayName: String {
        switch self {
        case .needsWork: return "Needs Work"
        case .developing: return "Developing"
        case .proficient: return "Proficient"
        case .excellent: return "Excellent"
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        case .allLevels: return "All levels"
        }
    }
    var color: Color {
        switch self {
        case .needsWork: return .red
        case .developing: return .orange
        case .proficient: return .blue
        case .excellent: return .green
        case .beginner: return .green
        case .intermediate: return .orange
        case .advanced: return .red
        case .allLevels: return .blue
        }
    }
}
// MARK: - Activity Shared Types
struct ActivityLog: Identifiable, Codable {
    let id: String
    let petId: String
    let activityType: ActivityType
    let startTime: Date
    let endTime: Date?
    let duration: TimeInterval
    let distance: Double?
    let caloriesBurned: Double?
    let averageHeartRate: Int?
    let maxHeartRate: Int?
    let steps: Int?
    let location: String?
    let latitude: Double?
    let longitude: Double?
    let notes: String?
    let weatherConditions: String?
    let temperature: Int?
    let humidity: Int?
    let createdAt: Date
}
struct ExerciseLog: Identifiable, Codable {
    let id: String
    let petId: String
    let activityType: ActivityType
    let exerciseType: String
    let intensity: String
    let duration: TimeInterval
    let caloriesBurned: Double
    let heartRateData: [Int]
    let gpsRoute: [String]
    let elevationGain: Double?
    let averageSpeed: Double?
    let maxSpeed: Double?
    let notes: String?
    let createdAt: Date
    init(from activityLog: ActivityLog) {
        self.id = activityLog.id
        self.petId = activityLog.petId
        self.activityType = activityLog.activityType
        self.exerciseType = activityLog.activityType.displayName
        self.intensity = "medium" // Default intensity
        self.duration = activityLog.duration
        self.caloriesBurned = activityLog.caloriesBurned ?? 0
        self.heartRateData = []
        self.gpsRoute = []
        self.elevationGain = nil
        self.averageSpeed = activityLog.distance.map { $0 / activityLog.duration }
        self.maxSpeed = nil
        self.notes = activityLog.notes
        self.createdAt = activityLog.createdAt
    }
}
struct ActiveSession: Identifiable, Codable {
    let id: String
    let petId: String
    let activityType: ActivityType
    let startTime: Date
    var currentDuration: TimeInterval
    var currentDistance: Double
    var currentCalories: Double
    var currentHeartRate: Int?
    var isActive: Bool
    var isPaused: Bool
    let targetDuration: TimeInterval?
    let targetDistance: Double?
    let targetCalories: Double?
}
// MARK: - Health Shared Types
// MARK: - Walk Recommendation
struct WalkRecommendation: Identifiable, Codable {
    let id: String
    let petId: String
    let recommendedTime: Date
    let duration: TimeInterval
    let route: String?
    let weatherScore: Double
    let airQualityScore: Double
    let pollenScore: Double
    let overallScore: Double
    let reasoning: String
    let alternatives: [String]
    let createdAt: Date
}
// MARK: - Environmental Alert
struct EnvironmentalAlert: Identifiable, Codable {
    let id: String
    let alertType: EnvironmentalAlertType
    let severity: AlertSeverity
    let title: String
    let message: String
    let location: String?
    let latitude: Double?
    let longitude: Double?
    let triggeredAt: Date
    let expiresAt: Date?
    var isAcknowledged: Bool
    let petIds: [String]
    let actionRequired: Bool
    let recommendations: [String]
    // Additional properties for compatibility
    var isActive: Bool { !isAcknowledged }
    var notificationEnabled: Bool { true }
    var notificationTimeStart: String { "00:00" }
    var notificationTimeEnd: String { "23:59" }
    var comparisonOperator: String { ">" }
    var thresholdValue: Double { 50.0 }
}
// MARK: - Additional Shared Types
// AI Models - already defined earlier in this file
// Health Alert
struct HealthAlert: Identifiable, Codable, Hashable {
    let id: String
    let petId: String
    let type: String
    let severity: AlertSeverity
    let title: String
    let message: String
    let triggeredAt: Date
    var isActive: Bool
    var isAcknowledged: Bool
    // Custom description for string conversion
    var description: String {
        return message
    }
}
// Emergency Contact types moved to EmergencyContactsService.swift to avoid duplication
struct SharedEmergencyContact: Identifiable, Codable {
    let id: String
    let name: String
    let phoneNumber: String
    let relationship: String
}
// Main EmergencyContact model used by views
struct EmergencyContact: Identifiable, Codable {
    let id: String
    var name: String
    var phoneNumber: String
    var type: EmergencyContactType
    var country: String
    let isEditable: Bool
    var description: String?
    var isPrimary: Bool
    init(
        id: String = UUID().uuidString,
        name: String,
        phoneNumber: String,
        type: EmergencyContactType,
        country: String = "US",
        isEditable: Bool = true,
        description: String? = nil,
        isPrimary: Bool = false
    ) {
        self.id = id
        self.name = name
        self.phoneNumber = phoneNumber
        self.type = type
        self.country = country
        self.isEditable = isEditable
        self.description = description
        self.isPrimary = isPrimary
    }
    static let fallbackContact = EmergencyContact(
        name: "Emergency Services",
        phoneNumber: "911",
        type: .emergency,
        country: "US",
        isEditable: false,
        description: "Emergency services fallback contact",
        isPrimary: true
    )
}
// MARK: - Location and Environmental Context
struct EnvironmentalContext: Codable {
    let temperature: Double?
    let humidity: Double?
    let airQuality: Int?
    let pollenCount: Int?
    let weatherCondition: String?
    let windSpeed: Double?
    let uvIndex: Int?
    let timestamp: Date
    let location: String?
    let coordinates: CLLocationCoordinate2D?
    enum CodingKeys: String, CodingKey {
        case temperature, humidity, airQuality, pollenCount
        case weatherCondition, windSpeed, uvIndex, timestamp, location
        case latitude, longitude
    }
    init(temperature: Double? = nil, humidity: Double? = nil, airQuality: Int? = nil,
         pollenCount: Int? = nil, weatherCondition: String? = nil, windSpeed: Double? = nil,
         uvIndex: Int? = nil, timestamp: Date = Date(), location: String? = nil,
         coordinates: CLLocationCoordinate2D? = nil) {
        self.temperature = temperature
        self.humidity = humidity
        self.airQuality = airQuality
        self.pollenCount = pollenCount
        self.weatherCondition = weatherCondition
        self.windSpeed = windSpeed
        self.uvIndex = uvIndex
        self.timestamp = timestamp
        self.location = location
        self.coordinates = coordinates
    }
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        temperature = try container.decodeIfPresent(Double.self, forKey: .temperature)
        humidity = try container.decodeIfPresent(Double.self, forKey: .humidity)
        airQuality = try container.decodeIfPresent(Int.self, forKey: .airQuality)
        pollenCount = try container.decodeIfPresent(Int.self, forKey: .pollenCount)
        weatherCondition = try container.decodeIfPresent(String.self, forKey: .weatherCondition)
        windSpeed = try container.decodeIfPresent(Double.self, forKey: .windSpeed)
        uvIndex = try container.decodeIfPresent(Int.self, forKey: .uvIndex)
        timestamp = try container.decode(Date.self, forKey: .timestamp)
        location = try container.decodeIfPresent(String.self, forKey: .location)
        if let lat = try container.decodeIfPresent(Double.self, forKey: .latitude),
           let lon = try container.decodeIfPresent(Double.self, forKey: .longitude) {
            coordinates = CLLocationCoordinate2D(latitude: lat, longitude: lon)
        } else {
            coordinates = nil
        }
    }
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encodeIfPresent(temperature, forKey: .temperature)
        try container.encodeIfPresent(humidity, forKey: .humidity)
        try container.encodeIfPresent(airQuality, forKey: .airQuality)
        try container.encodeIfPresent(pollenCount, forKey: .pollenCount)
        try container.encodeIfPresent(weatherCondition, forKey: .weatherCondition)
        try container.encodeIfPresent(windSpeed, forKey: .windSpeed)
        try container.encodeIfPresent(uvIndex, forKey: .uvIndex)
        try container.encode(timestamp, forKey: .timestamp)
        try container.encodeIfPresent(location, forKey: .location)
        try container.encodeIfPresent(coordinates?.latitude, forKey: .latitude)
        try container.encodeIfPresent(coordinates?.longitude, forKey: .longitude)
    }
}
// TODO: Add other shared types here
struct SafetyScore: Identifiable, Codable {
    let id: String
    let safetyScore: Double
    let petFriendlinessScore: Double
    let timestamp: Date
}
struct DetailedLocationInfo {
    let coordinate: CLLocationCoordinate2D
    let name: String?
    let thoroughfare: String?
    let subThoroughfare: String?
    let locality: String?
    let subLocality: String?
    let administrativeArea: String?
    let subAdministrativeArea: String?
    let postalCode: String?
    let country: String?
    let isoCountryCode: String?
    let formattedAddress: String
    let timezone: TimeZone?
    let region: CLCircularRegion?
}
struct PointOfInterest: Identifiable, Codable {
    let id: UUID
    let name: String
    let category: String
    let coordinate: CLLocationCoordinate2D
    let distance: Double
}
struct WalkMemoryLocationTag {
    let coordinate: CLLocationCoordinate2D
    let locationInfo: DetailedLocationInfo
    let nearbyPointsOfInterest: [PointOfInterest]
    let tags: [String]
    let walkabilityScore: Double
}
enum LocationTaggingError: Error {
    case locationNotAvailable
}
enum MapServiceError: Error {
    case geocodingFailed
    case routeCalculationFailed
    case locationSearchFailed
}
enum WalkDifficulty: String, Codable {
    case easy
    case moderate
    case challenging
    case difficult
    var color: Color {
        switch self {
        case .easy: return .green
        case .moderate: return .yellow
        case .challenging: return .orange
        case .difficult: return .red
        }
    }
    var displayName: String {
        rawValue.capitalized
    }
}
struct WalkRoute: Codable {
    let coordinates: [CLLocationCoordinate2D]
    let distance: Double
    let estimatedTime: TimeInterval
    let difficulty: WalkDifficulty
    let scenicScore: Double
    let safetyScore: Double
    let polyline: Data
    enum CodingKeys: String, CodingKey {
        case coordinates
        case distance
        case estimatedTime
        case difficulty
        case scenicScore
        case safetyScore
        case polyline
    }
    init(coordinates: [CLLocationCoordinate2D], distance: Double, estimatedTime: TimeInterval, difficulty: WalkDifficulty, scenicScore: Double, safetyScore: Double, polyline: MKPolyline) {
        self.coordinates = coordinates
        self.distance = distance
        self.estimatedTime = estimatedTime
        self.difficulty = difficulty
        self.scenicScore = scenicScore
        self.safetyScore = safetyScore
        self.polyline = try! NSKeyedArchiver.archivedData(withRootObject: polyline, requiringSecureCoding: false)
    }
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        coordinates = try container.decode([CLLocationCoordinate2D].self, forKey: .coordinates)
        distance = try container.decode(Double.self, forKey: .distance)
        estimatedTime = try container.decode(TimeInterval.self, forKey: .estimatedTime)
        difficulty = try container.decode(WalkDifficulty.self, forKey: .difficulty)
        scenicScore = try container.decode(Double.self, forKey: .scenicScore)
        safetyScore = try container.decode(Double.self, forKey: .safetyScore)
        polyline = try container.decode(Data.self, forKey: .polyline)
    }
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(coordinates, forKey: .coordinates)
        try container.encode(distance, forKey: .distance)
        try container.encode(estimatedTime, forKey: .estimatedTime)
        try container.encode(difficulty, forKey: .difficulty)
        try container.encode(scenicScore, forKey: .scenicScore)
        try container.encode(safetyScore, forKey: .safetyScore)
        try container.encode(polyline, forKey: .polyline)
    }
    func getMKPolyline() -> MKPolyline? {
        // Create a new polyline from the coordinates since MKPolyline doesn't conform to NSCoding
        let polyline = MKPolyline(coordinates: self.coordinates, count: self.coordinates.count)
        return polyline
    }
}
enum WalkingPreference {
    case fastest
    case scenic
    case safest
}
