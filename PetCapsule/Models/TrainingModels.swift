//
//  TrainingModels.swift
//  PetCapsule
//
//  Data models for training planning and tracking
//

import Foundation
import SwiftUI

// Type alias to resolve ambiguity - using the SkillLevel defined in this file
typealias TrainingSkillLevel = SkillLevel

// MARK: - Training Plan

struct TrainingPlan: Identifiable, Codable {
    let id: String
    let petId: String
    let petName: String
    let goals: [TrainingGoal]
    let experienceLevel: ExperienceLevel
    let modules: [TrainingModule]
    let schedule: TrainingSchedule
    let milestones: [TrainingMilestone]
    let estimatedDuration: Int // weeks
    let createdAt: Date
    let lastUpdated: Date
    
    var completionRate: Double {
        let completedModules = modules.filter { $0.isCompleted }.count
        return modules.count > 0 ? Double(completedModules) / Double(modules.count) : 0
    }
    
    var currentModule: TrainingModule? {
        modules.first { !$0.isCompleted }
    }
    
    var nextMilestone: TrainingMilestone? {
        milestones.first { !$0.isCompleted }
    }
}

// MARK: - Training Goals

enum TrainingGoal: String, CaseIterable, Codable {
    case basicObedience = "basic_obedience"
    case houseTraining = "house_training"
    case leashTraining = "leash_training"
    case socialSkills = "social_skills"
    case advancedTricks = "advanced_tricks"
    case behaviorCorrection = "behavior_correction"
    case agility = "agility"
    case therapy = "therapy"
    
    var displayName: String {
        switch self {
        case .basicObedience: return "Basic Obedience"
        case .houseTraining: return "House Training"
        case .leashTraining: return "Leash Training"
        case .socialSkills: return "Social Skills"
        case .advancedTricks: return "Advanced Tricks"
        case .behaviorCorrection: return "Behavior Correction"
        case .agility: return "Agility Training"
        case .therapy: return "Therapy Training"
        }
    }
    
    var description: String {
        switch self {
        case .basicObedience: return "Sit, stay, come, down commands"
        case .houseTraining: return "Proper bathroom habits and crate training"
        case .leashTraining: return "Walk politely without pulling"
        case .socialSkills: return "Interact well with people and other pets"
        case .advancedTricks: return "Fun tricks like roll over, play dead, etc."
        case .behaviorCorrection: return "Address problematic behaviors"
        case .agility: return "Navigate obstacle courses"
        case .therapy: return "Prepare for therapy or service work"
        }
    }
    
    var icon: String {
        switch self {
        case .basicObedience: return "hand.raised.fill"
        case .houseTraining: return "house.fill"
        case .leashTraining: return "figure.walk"
        case .socialSkills: return "person.2.fill"
        case .advancedTricks: return "star.fill"
        case .behaviorCorrection: return "exclamationmark.triangle.fill"
        case .agility: return "figure.run"
        case .therapy: return "heart.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .basicObedience: return .blue
        case .houseTraining: return .green
        case .leashTraining: return .orange
        case .socialSkills: return .purple
        case .advancedTricks: return .pink
        case .behaviorCorrection: return .red
        case .agility: return .cyan
        case .therapy: return .indigo
        }
    }
    
    var estimatedWeeks: Int {
        switch self {
        case .basicObedience: return 4
        case .houseTraining: return 8
        case .leashTraining: return 6
        case .socialSkills: return 12
        case .advancedTricks: return 8
        case .behaviorCorrection: return 10
        case .agility: return 16
        case .therapy: return 24
        }
    }
}

// MARK: - Experience Level

enum ExperienceLevel: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"
    
    var displayName: String {
        rawValue.capitalized
    }
    
    var description: String {
        switch self {
        case .beginner: return "New to training, needs basic guidance"
        case .intermediate: return "Some training experience, ready for more"
        case .advanced: return "Experienced trainer, wants challenging goals"
        case .expert: return "Professional level training expertise"
        }
    }
    
    var sessionsPerWeek: Int {
        switch self {
        case .beginner: return 3
        case .intermediate: return 4
        case .advanced: return 5
        case .expert: return 6
        }
    }
    
    var sessionDuration: Int {
        switch self {
        case .beginner: return 15
        case .intermediate: return 20
        case .advanced: return 30
        case .expert: return 45
        }
    }
}

// MARK: - Training Module

struct TrainingModule: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let category: TrainingCategory
    let difficulty: Difficulty
    let estimatedWeeks: Double
    let exercises: [TrainingExercise]
    let prerequisites: [String]
    let learningObjectives: [String]
    var isCompleted: Bool = false
    var completedDate: Date?
    
    var progress: Double {
        let completedExercises = exercises.filter { $0.isCompleted }.count
        return exercises.count > 0 ? Double(completedExercises) / Double(exercises.count) : 0
    }
    
    var nextExercise: TrainingExercise? {
        exercises.first { !$0.isCompleted }
    }
}

enum TrainingCategory: String, CaseIterable, Codable {
    case obedience = "obedience"
    case houseTraining = "house_training"
    case leashTraining = "leash_training"
    case socialization = "socialization"
    case tricks = "tricks"
    case behaviorModification = "behavior_modification"
    case agility = "agility"
    case therapy = "therapy"
    
    var displayName: String {
        switch self {
        case .obedience: return "Obedience"
        case .houseTraining: return "House Training"
        case .leashTraining: return "Leash Training"
        case .socialization: return "Socialization"
        case .tricks: return "Tricks"
        case .behaviorModification: return "Behavior Modification"
        case .agility: return "Agility"
        case .therapy: return "Therapy"
        }
    }
    
    var icon: String {
        switch self {
        case .obedience: return "hand.raised.fill"
        case .houseTraining: return "house.fill"
        case .leashTraining: return "figure.walk"
        case .socialization: return "person.2.fill"
        case .tricks: return "star.fill"
        case .behaviorModification: return "exclamationmark.triangle.fill"
        case .agility: return "figure.run"
        case .therapy: return "heart.fill"
        }
    }
}

enum Difficulty: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"
    
    var displayName: String {
        rawValue.capitalized
    }
    
    var color: Color {
        switch self {
        case .beginner: return .green
        case .intermediate: return .yellow
        case .advanced: return .orange
        case .expert: return .red
        }
    }
    
    var stars: Int {
        switch self {
        case .beginner: return 1
        case .intermediate: return 2
        case .advanced: return 3
        case .expert: return 4
        }
    }
}

// MARK: - Training Exercise

struct TrainingExercise: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let category: TrainingCategory
    let difficulty: Difficulty
    let steps: [String]
    let videoURL: URL?
    let requiredEquipment: [String]
    let tips: [String]
    var isCompleted: Bool = false
    var averagePerformance: Double = 0.0
    var lastPracticed: Date?
}

// MARK: - Training Schedule

struct TrainingSchedule: Codable {
    let sessionsPerWeek: Int
    let sessionDuration: TimeInterval
    let recommendedDays: [Weekday]
    let preferredTimeOfDay: TimeOfDay
}

enum Weekday: String, CaseIterable, Codable {
    case sunday, monday, tuesday, wednesday, thursday, friday, saturday
    
    var displayName: String {
        rawValue.capitalized
    }
}

enum TimeOfDay: String, CaseIterable, Codable {
    case morning, afternoon, evening
    
    var displayName: String {
        rawValue.capitalized
    }
}

// MARK: - Training Milestone

struct TrainingMilestone: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let requiredSkills: [String]
    let badgeURL: URL?
    var isCompleted: Bool = false
    var completedDate: Date?
}

// MARK: - Training Session

struct TrainingSession: Identifiable, Codable {
    let id: String
    let petId: String
    let exerciseId: String
    let date: Date
    let duration: TimeInterval // in seconds
    let performance: PerformanceRating
    let notes: String?
    let mediaAttachments: [URL]
    let trainerFeedback: String?
}

enum PerformanceRating: String, CaseIterable, Codable {
    case needsWork = "needs_work"
    case good = "good"
    case excellent = "excellent"
    case perfect = "perfect"
    
    var displayName: String {
        rawValue.capitalized.replacingOccurrences(of: "_", with: " ")
    }
    
    var color: Color {
        switch self {
        case .needsWork: return .red
        case .good: return .orange
        case .excellent: return .yellow
        case .perfect: return .green
        }
    }
    
    var value: Int {
        switch self {
        case .needsWork: return 1
        case .good: return 2
        case .excellent: return 3
        case .perfect: return 4
        }
    }
}

// MARK: - Training Progress

struct TrainingProgress: Codable {
    let petId: String
    let overallCompletion: Double
    let skillsMastered: [String]
    let timeSpent: TimeInterval
    let successRate: Double
    let lastTrained: Date
    let nextRecommendation: String?
    let weeklySummary: WeeklyTrainingSummary
}

struct WeeklyTrainingSummary: Codable {
    let weekOf: Date
    let sessionsCompleted: Int
    let totalDuration: TimeInterval
    let topPerformingSkill: String?
    let skillToImprove: String?
}

// MARK: - Training Program

struct TrainingProgram: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let category: TrainingCategory
    let difficulty: Difficulty
    let estimatedDuration: Int // weeks
    let modules: [String] // Module IDs
    let prerequisites: [String]
    let targetAudience: [String]
    let price: Double?
    let rating: Double
    let reviews: Int
    let author: String
    let isPopular: Bool
    let isFree: Bool
    
    var formattedPrice: String {
        if isFree {
            return "Free"
        } else if let price = price {
            return String(format: "$%.2f", price)
        } else {
            return "Premium"
        }
    }
}

// MARK: - Skill Assessment

struct SkillAssessment: Identifiable, Codable {
    let id: String
    let petId: String
    let assessmentDate: Date
    let skills: [SkillEvaluation]
    let overallScore: Int // 1-100
    let recommendations: [String]
    let nextAssessmentDate: Date
    
    var skillsNeedingWork: [SkillEvaluation] {
        skills.filter { $0.score < 70 }
    }
    
    var strongSkills: [SkillEvaluation] {
        skills.filter { $0.score >= 85 }
    }
}

struct SkillEvaluation: Identifiable, Codable {
    var id = UUID()
    let skillName: String
    let category: TrainingCategory
    let score: Int // 1-100
    let notes: String
    let improvementAreas: [String]
    
    var level: TrainingSkillLevel {
        switch score {
        case 0..<50: return .needsWork
        case 50..<70: return .developing
        case 70..<85: return .proficient
        case 85...100: return .excellent
        default: return .needsWork
        }
    }
}

// TrainingSkillLevel is already defined earlier in this file
