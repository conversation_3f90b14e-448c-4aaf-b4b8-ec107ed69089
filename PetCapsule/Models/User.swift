//
//  User.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftData

@Model
final class User {
    var id: String = UUID().uuidString
    var email: String = ""
    var displayName: String = ""
    var fullName: String = ""
    var profileImageURL: String?
    var subscriptionTier: SubscriptionTier = SubscriptionTier.pawStarter // Fully qualified
    var memoryGems: Int = 0
    var totalUploads: Int = 0
    var totalVaults: Int = 0
    var joinedNetworkAt: Date?
    var createdAt: Date = Date()
    var updatedAt: Date = Date()
    var lastActiveAt: Date = Date()

    // CloudKit-compatible relationships using UUID references (avoids circular references)
    var petIDs: [String] = [] // References to Pet.id

    init(
        id: String,
        email: String,
        displayName: String,
        fullName: String? = nil,
        profileImageURL: String? = nil,
        subscriptionTier: SubscriptionTier = .pawStarter
    ) {
        self.id = id
        self.email = email
        self.displayName = displayName
        self.fullName = fullName ?? displayName
        self.profileImageURL = profileImageURL
        self.subscriptionTier = subscriptionTier
        self.memoryGems = 0
        self.totalUploads = 0
        self.totalVaults = 0
        self.createdAt = Date()
        self.updatedAt = Date()
        self.lastActiveAt = Date()
    }
}

enum SubscriptionTier: String, CaseIterable, Codable {
    case pawStarter = "paw_starter"
    case growingBond = "growing_bond"
    case familyCircle = "family_circle"
    case premiumPro = "premium_pro"

    var displayName: String {
        switch self {
        case .pawStarter:
            return "Paw Starter"
        case .growingBond:
            return "Growing Bond"
        case .familyCircle:
            return "Family Circle"
        case .premiumPro:
            return "Premium Pro"
        }
    }

    var maxPets: Int {
        switch self {
        case .pawStarter:
            return 1
        case .growingBond:
            return 2
        case .familyCircle:
            return 5
        case .premiumPro:
            return 10
        }
    }

    var maxUploads: Int {
        // Make all features unlimited for now
        return Int.max

        // Original limits (commented out)
        // switch self {
        // case .pawStarter:
        //     return 5
        // case .growingBond, .familyCircle, .alphaPack:
        //     return Int.max
        // }
    }

    var maxVaults: Int {
        // Make all features unlimited for now
        return Int.max

        // Original limits (commented out)
        // switch self {
        // case .pawStarter:
        //     return 1
        // case .growingBond:
        //     return 5
        // case .familyCircle, .alphaPack:
        //     return Int.max
        // }
    }

    var hasUnlimitedAI: Bool {
        // Make all features unlimited for now
        return true

        // Original limits (commented out)
        // switch self {
        // case .pawStarter:
        //     return false
        // case .growingBond, .familyCircle, .alphaPack:
        //     return true
        // }
    }
    
    var hasAIAgents: Bool {
        switch self {
        case .pawStarter:
            return false
        case .growingBond, .familyCircle, .premiumPro:
            return true
        }
    }
    
    var aiAgentCount: Int {
        switch self {
        case .pawStarter:
            return 0
        case .growingBond:
            return 4 // Pet Master, Health Guardian, Dr. Nutrition, Trainer Pro
        case .familyCircle:
            return 8 // All 8 specialized AI agents
        case .premiumPro:
            return 8 // All 8 specialized AI agents
        }
    }
    
    var storageLimit: String {
        switch self {
        case .pawStarter:
            return "100MB"
        case .growingBond:
            return "2GB"
        case .familyCircle:
            return "10GB"
        case .premiumPro:
            return "50GB"
        }
    }

    var monthlyPrice: String {
        switch self {
        case .pawStarter:
            return "Free"
        case .growingBond:
            return "$9.99"
        case .familyCircle:
            return "$14.99"
        case .premiumPro:
            return "$19.99"
        }
    }
    
    var yearlyPrice: Double {
        switch self {
        case .pawStarter:
            return 0.0
        case .growingBond:
            return 99.99 // ~$8.33/month
        case .familyCircle:
            return 149.99 // ~$12.50/month
        case .premiumPro:
            return 199.99 // ~$16.67/month
        }
    }
    
    // Legacy support for old enum values
    static func from(oldValue: String) -> SubscriptionTier {
        switch oldValue {
        case "free":
            return .pawStarter
        case "premium":
            return .growingBond
        case "family":
            return .familyCircle
        default:
            return .pawStarter
        }
    }
}

// MARK: - User Extensions
extension User {
    var canUploadMore: Bool {
        totalUploads < subscriptionTier.maxUploads
    }

    var canCreateMoreVaults: Bool {
        totalVaults < subscriptionTier.maxVaults
    }

    var uploadsRemaining: Int {
        max(0, subscriptionTier.maxUploads - totalUploads)
    }

    var vaultsRemaining: Int {
        max(0, subscriptionTier.maxVaults - totalVaults)
    }

    func earnGems(_ amount: Int) {
        memoryGems += amount
        updatedAt = Date()
    }

    func spendGems(_ amount: Int) -> Bool {
        guard memoryGems >= amount else { return false }
        memoryGems -= amount
        updatedAt = Date()
        return true
    }

    func updateActivity() {
        lastActiveAt = Date()
        updatedAt = Date()
    }
}
