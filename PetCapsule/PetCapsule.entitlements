<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- Sign in with Apple -->
	<key>com.apple.developer.applesignin</key>
	<array>
		<string>Default</string>
	</array>

	<!-- CloudKit for cross-device sync -->
	<key>com.apple.developer.icloud-services</key>
	<array>
		<string>CloudKit</string>
	</array>
	<key>com.apple.developer.icloud-container-identifiers</key>
	<array>
		<string>iCloud.com.petcapsule.app</string>
	</array>

	<!-- Push Notifications -->
	<key>aps-environment</key>
	<string>development</string>
	
	<!-- App Groups for sharing data with extensions -->
	<key>com.apple.security.application-groups</key>
	<array>
		<string>group.com.petcapsule.shared</string>
	</array>
	
	<!-- HealthKit (if used for pet health monitoring) -->
	<key>com.apple.developer.healthkit</key>
	<true/>
	
	<!-- Keychain Sharing -->
	<key>keychain-access-groups</key>
	<array>
		<string>$(AppIdentifierPrefix)com.petcapsule.shared</string>
	</array>
	
	<!-- Associated Domains for Universal Links -->
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:petcapsule.app</string>
	</array>
	
	<!-- WeatherKit for weather data -->
	<key>com.apple.developer.weatherkit</key>
	<true/>
	<key>com.apple.developer.weatherkit-access</key>
	<true/>
</dict>
</plist>
