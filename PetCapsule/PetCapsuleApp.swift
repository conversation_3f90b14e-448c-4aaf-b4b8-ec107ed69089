//
//  PetCapsuleApp.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//  Enhanced with iOS 18 Features for Apple Award Consideration
//

import SwiftUI
import SwiftData
import AppIntents
#if canImport(ActivityKit)
import ActivityKit
#endif

@main
struct PetCapsuleApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    // Use AppleNativeDataService for all data management
    // Removed old sharedModelContainer that was causing CloudKit schema conflicts
    
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}
