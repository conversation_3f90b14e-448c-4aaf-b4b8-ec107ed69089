//
//  AIAgentToolService.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import Foundation
import AppIntents

/// Service that provides tool calling capabilities for AI agents to access pet data
@available(iOS 18.0, *)
@MainActor
class AIAgentToolService: ObservableObject {
    static let shared = AIAgentToolService()
    
    private let dataService = AppleIntelligenceDataService.shared
    private var toolRegistry: [String: AITool] = [:]
    
    private init() {
        registerTools()
    }
    
    // MARK: - Tool Registration
    
    private func registerTools() {
        // Health Tools
        registerTool(PetHealthSummaryTool())
        registerTool(VaccinationHistoryTool())
        registerTool(MedicationScheduleTool())
        registerTool(WeightTrendsTool())
        
        // Care Management Tools
        registerTool(UpcomingAppointmentsTool())
        registerTool(FeedingScheduleTool())
        registerTool(ExerciseLogTool())
        registerTool(BehaviorNotesTool())
        
        // Memory and Milestone Tools
        registerTool(RecentMemoriesTool())
        registerTool(MilestonesTool())
        registerTool(EmergencyContactsTool())
        
        // Action Tools (to be implemented)
        // registerTool(CreateAppointmentTool())
        // registerTool(AddReminderTool())
        // registerTool(LogHealthDataTool())
    }
    
    private func registerTool(_ tool: AITool) {
        toolRegistry[tool.name] = tool
    }
    
    // MARK: - Tool Execution
    
    /// Execute a tool function and return the result
    func executeTool(name: String, parameters: [String: Any]) async throws -> AIToolResult {
        guard let tool = toolRegistry[name] else {
            throw AIToolError.toolNotFound(name)
        }
        
        do {
            let result = try await tool.execute(parameters: parameters)
            return result
        } catch {
            throw AIToolError.executionFailed(name, error.localizedDescription)
        }
    }
    
    /// Get available tools for a specific context
    func getAvailableTools(for context: AIAgentContext) -> [AIToolDescription] {
        return toolRegistry.values
            .filter { tool in
                tool.isAvailable(for: context)
            }
            .map { tool in
                AIToolDescription(
                    name: tool.name,
                    description: tool.description,
                    parameters: tool.parameters,
                    category: tool.category
                )
            }
    }
    
    /// Get tool suggestions based on user query
    func suggestTools(for query: String, context: AIAgentContext) -> [String] {
        let lowercaseQuery = query.lowercased()
        var suggestions: [String] = []
        
        // Health-related keywords
        if lowercaseQuery.contains("health") || lowercaseQuery.contains("sick") || lowercaseQuery.contains("vet") {
            suggestions.append("getPetHealthSummary")
            suggestions.append("getUpcomingAppointments")
        }
        
        // Weight-related keywords
        if lowercaseQuery.contains("weight") || lowercaseQuery.contains("heavy") || lowercaseQuery.contains("thin") {
            suggestions.append("getWeightTrends")
        }
        
        // Vaccination keywords
        if lowercaseQuery.contains("vaccination") || lowercaseQuery.contains("vaccine") || lowercaseQuery.contains("shot") {
            suggestions.append("getVaccinationHistory")
        }
        
        // Medication keywords
        if lowercaseQuery.contains("medication") || lowercaseQuery.contains("medicine") || lowercaseQuery.contains("pill") {
            suggestions.append("getMedicationSchedule")
        }
        
        // Appointment keywords
        if lowercaseQuery.contains("appointment") || lowercaseQuery.contains("schedule") || lowercaseQuery.contains("visit") {
            suggestions.append("getUpcomingAppointments")
            suggestions.append("createAppointment")
        }
        
        // Feeding keywords
        if lowercaseQuery.contains("food") || lowercaseQuery.contains("feeding") || lowercaseQuery.contains("meal") {
            suggestions.append("getFeedingSchedule")
        }
        
        // Exercise keywords
        if lowercaseQuery.contains("exercise") || lowercaseQuery.contains("walk") || lowercaseQuery.contains("activity") {
            suggestions.append("getExerciseLog")
        }
        
        // Behavior keywords
        if lowercaseQuery.contains("behavior") || lowercaseQuery.contains("training") || lowercaseQuery.contains("problem") {
            suggestions.append("getBehaviorNotes")
        }
        
        // Memory keywords
        if lowercaseQuery.contains("memory") || lowercaseQuery.contains("photo") || lowercaseQuery.contains("milestone") {
            suggestions.append("getRecentMemories")
            suggestions.append("getMilestones")
        }
        
        // Emergency keywords
        if lowercaseQuery.contains("emergency") || lowercaseQuery.contains("urgent") || lowercaseQuery.contains("contact") {
            suggestions.append("getEmergencyContacts")
        }
        
        return suggestions
    }
}

// MARK: - Tool Protocol and Base Classes

/// Protocol that all AI tools must implement
protocol AITool {
    var name: String { get }
    var description: String { get }
    var parameters: [AIToolParameter] { get }
    var category: AIToolCategory { get }
    
    func execute(parameters: [String: Any]) async throws -> AIToolResult
    func isAvailable(for context: AIAgentContext) -> Bool
}

/// Tool parameter definition
struct AIToolParameter {
    let name: String
    let type: AIToolParameterType
    let description: String
    let required: Bool
    let defaultValue: Any?

    init(name: String, type: AIToolParameterType, description: String, required: Bool = true, defaultValue: Any? = nil) {
        self.name = name
        self.type = type
        self.description = description
        self.required = required
        self.defaultValue = defaultValue
    }
}

enum AIToolParameterType: String, Codable {
    case string = "string"
    case integer = "integer"
    case double = "double"
    case boolean = "boolean"
    case date = "date"
    case uuid = "uuid"
    case array = "array"
    case object = "object"
}

/// Tool execution result
struct AIToolResult {
    let success: Bool
    let data: [String: Any]?
    let error: String?
    let metadata: [String: Any]?

    init(success: Bool, data: [String: Any]? = nil, error: String? = nil, metadata: [String: Any]? = nil) {
        self.success = success
        self.data = data
        self.error = error
        self.metadata = metadata
    }

    static func success(data: [String: Any], metadata: [String: Any]? = nil) -> AIToolResult {
        return AIToolResult(success: true, data: data, metadata: metadata)
    }

    static func failure(error: String) -> AIToolResult {
        return AIToolResult(success: false, error: error)
    }
}

/// Tool description for AI agents
struct AIToolDescription {
    let name: String
    let description: String
    let parameters: [AIToolParameter]
    let category: AIToolCategory
}

/// Tool categories for organization
enum AIToolCategory: String, CaseIterable, Codable {
    case health = "health"
    case care = "care"
    case appointments = "appointments"
    case memories = "memories"
    case behavior = "behavior"
    case actions = "actions"
    case emergency = "emergency"
}

/// Context for AI agent interactions
struct AIAgentContext {
    let agentType: AIAgentType
    let selectedPetId: UUID?
    let conversationHistory: [String]
    let userPermissions: Set<String>
    
    init(agentType: AIAgentType, selectedPetId: UUID? = nil, conversationHistory: [String] = [], userPermissions: Set<String> = []) {
        self.agentType = agentType
        self.selectedPetId = selectedPetId
        self.conversationHistory = conversationHistory
        self.userPermissions = userPermissions
    }
}

/// Errors that can occur during tool execution
enum AIToolError: Error, LocalizedError {
    case toolNotFound(String)
    case invalidParameters(String)
    case executionFailed(String, String)
    case permissionDenied(String)
    case petNotFound(UUID)
    
    var errorDescription: String? {
        switch self {
        case .toolNotFound(let name):
            return "Tool '\(name)' not found"
        case .invalidParameters(let details):
            return "Invalid parameters: \(details)"
        case .executionFailed(let tool, let reason):
            return "Tool '\(tool)' execution failed: \(reason)"
        case .permissionDenied(let tool):
            return "Permission denied for tool '\(tool)'"
        case .petNotFound(let id):
            return "Pet with ID '\(id)' not found"
        }
    }
}

// MARK: - Base Tool Implementation

/// Base class for AI tools with common functionality
class BaseAITool: AITool {
    let name: String
    let description: String
    let parameters: [AIToolParameter]
    let category: AIToolCategory
    
    init(name: String, description: String, parameters: [AIToolParameter], category: AIToolCategory) {
        self.name = name
        self.description = description
        self.parameters = parameters
        self.category = category
    }
    
    func execute(parameters: [String: Any]) async throws -> AIToolResult {
        // Validate parameters
        try validateParameters(parameters)
        
        // Execute the tool-specific logic
        return try await performExecution(parameters: parameters)
    }
    
    func isAvailable(for context: AIAgentContext) -> Bool {
        // Default implementation - can be overridden by specific tools
        return true
    }
    
    // MARK: - Abstract Methods
    
    func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        fatalError("Subclasses must implement performExecution")
    }
    
    // MARK: - Helper Methods
    
    private func validateParameters(_ parameters: [String: Any]) throws {
        for parameter in self.parameters where parameter.required {
            guard parameters[parameter.name] != nil else {
                throw AIToolError.invalidParameters("Missing required parameter: \(parameter.name)")
            }
        }
    }
    
    func extractPetId(from parameters: [String: Any]) throws -> UUID {
        guard let petIdString = parameters["petId"] as? String,
              let petId = UUID(uuidString: petIdString) else {
            throw AIToolError.invalidParameters("Invalid or missing petId parameter")
        }
        return petId
    }
    
    func extractOptionalString(from parameters: [String: Any], key: String) -> String? {
        return parameters[key] as? String
    }
    
    func extractOptionalInt(from parameters: [String: Any], key: String) -> Int? {
        return parameters[key] as? Int
    }
    
    func extractOptionalDate(from parameters: [String: Any], key: String) -> Date? {
        if let timestamp = parameters[key] as? TimeInterval {
            return Date(timeIntervalSince1970: timestamp)
        }
        return nil
    }
}
