//
//  AIConversationService.swift
//  PetCapsule
//
//  AI Conversation History Service with Apple Native Integration
//  🤖 Manages AI agent conversation persistence and retrieval
//
import Foundation
import Combine
class AIConversationService: ObservableObject {
    static let shared = AIConversationService()
    @Published var conversations: [AIConversation] = []
    @Published var currentConversation: AIConversation?
    @Published var messages: [AIMessage] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    private var cancellables = Set<AnyCancellable>()
    private init() {
        // Listen for auth changes to load user conversations
        Task {
            await loadUserConversations()
        }
    }
    // MARK: - Conversation Management
    func loadUserConversations() async {
        isLoading = true
        defer { isLoading = false }
        let response: [DatabaseAIConversation] = []
        conversations = response.compactMap { AIConversation(from: $0) }
    }
    func createConversation(agentId: UUID, petId: UUID? = nil, title: String? = nil) async -> AIConversation? {
        // Create conversation locally
        let conversationTitle = title ?? "New Conversation"
        let newConversation = AIConversation(
            id: UUID(),
            agentId: agentId,
            agentName: "AI Agent", // Default agent name
            petId: petId,
            title: conversationTitle,
            lastMessageAt: Date(),
            messageCount: 0,
            isActive: true,
            createdAt: Date(),
            updatedAt: Date()
        )
        conversations.insert(newConversation, at: 0)
        currentConversation = newConversation
        messages = []
        return newConversation
    }
    func updateConversationTitle(_ conversationId: UUID, title: String) async -> Bool {
        // Update conversation title locally
        if let index = conversations.firstIndex(where: { $0.id == conversationId }) {
            conversations[index].title = title
            return true
        }
        return false
    }
    func deleteConversation(_ conversationId: UUID) async -> Bool {
        // Mark conversation as inactive locally
        conversations.removeAll { $0.id == conversationId }
        if currentConversation?.id == conversationId {
            currentConversation = nil
            messages = []
        }
        return true
    }
    // MARK: - Message Management
    func loadMessages(for conversationId: UUID) async {
        isLoading = true
        defer { isLoading = false }
        let response: [DatabaseAIMessage] = []
        messages = response.map { AIMessage(from: $0) }
    }
    func saveMessage(
        conversationId: UUID,
        content: String,
        isFromUser: Bool,
        messageType: String = "text",
        metadata: [String: Any]? = nil
    ) async -> AIMessage? {
        // Create message directly using AIMessage constructor
        let message = AIMessage(
            conversationId: conversationId,
            content: content,
            isFromUser: isFromUser,
            messageType: messageType,
            metadata: metadata
        )
        messages.append(message)
        // Update conversation's last message time and count
        await updateConversationActivity(conversationId)
        return message
    }
    private func updateConversationActivity(_ conversationId: UUID) async {
        // Update conversation activity locally
        // Update local conversation object
        if let index = conversations.firstIndex(where: { $0.id == conversationId }) {
            conversations[index].lastMessageAt = Date()
            conversations[index].messageCount = messages.count
            // Move to top of list
            let conversation = conversations.remove(at: index)
            conversations.insert(conversation, at: 0)
        }
    }
    // MARK: - Helper Methods
    func getOrCreateConversation(agentId: UUID, petId: UUID? = nil) async -> AIConversation? {
        // Check if there's an existing active conversation with this agent and pet
        if let existing = conversations.first(where: {
            $0.agentId == agentId && $0.petId == petId
        }) {
            currentConversation = existing
            await loadMessages(for: existing.id)
            return existing
        }
        // Create new conversation
        return await createConversation(agentId: agentId, petId: petId)
    }
    func clearLocalData() {
        conversations = []
        currentConversation = nil
        messages = []
        errorMessage = nil
    }
    func setCurrentConversation(_ conversation: AIConversation) async {
        currentConversation = conversation
        await loadMessages(for: conversation.id)
    }
    func getConversations(for userId: UUID?) async -> [AIConversation] {
        // Load conversations if not already loaded
        if conversations.isEmpty {
            await loadUserConversations()
        }
        return conversations
    }
}