//
//  CareManagementTools.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import Foundation

// MARK: - Upcoming Appointments Tool

@available(iOS 18.0, *)
class UpcomingAppointmentsTool: BaseAITool {
    init() {
        super.init(
            name: "getUpcomingAppointments",
            description: "Get upcoming appointments for a pet including vet visits, grooming, and other scheduled activities",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get appointments for"),
                AIToolParameter(name: "limit", type: .integer, description: "Maximum number of appointments to return", required: false, defaultValue: 10),
                AIToolParameter(name: "timeframe", type: .string, description: "Timeframe to look ahead (week, month, year)", required: false, defaultValue: "month")
            ],
            category: .appointments
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let limit = extractOptionalInt(from: parameters, key: "limit") ?? 10
        let timeframe = extractOptionalString(from: parameters, key: "timeframe") ?? "month"
        let dataService = await AppleIntelligenceDataService.shared
        
        let appointments = (await dataService.getUpcomingAppointments(for: petId)).prefix(limit)
        let filteredAppointments = filterAppointmentsByTimeframe(Array(appointments), timeframe: timeframe)
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "totalAppointments": filteredAppointments.count,
            "timeframe": timeframe,
            "appointments": filteredAppointments.map { appointment in
                [
                    "id": appointment.id.uuidString,
                    "title": appointment.title,
                    "description": appointment.description ?? "",
                    "appointmentType": appointment.appointmentType,
                    "scheduledDate": appointment.scheduledDate.timeIntervalSince1970,
                    "duration": appointment.duration,
                    "location": [
                        "name": appointment.location.name,
                        "address": appointment.location.address ?? "",
                        "phoneNumber": appointment.location.phoneNumber ?? ""
                    ],
                    "status": appointment.status,
                    "notes": appointment.notes ?? "",
                    "daysUntil": Calendar.current.dateComponents([.day], from: Date(), to: appointment.scheduledDate).day ?? 0
                ]
            }
        ]
        
        let metadata: [String: Any] = [
            "summary": generateAppointmentsSummary(from: filteredAppointments),
            "nextAppointment": getNextAppointmentInfo(from: filteredAppointments),
            "urgentAppointments": getUrgentAppointments(from: filteredAppointments)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func filterAppointmentsByTimeframe(_ appointments: [PetAppointment], timeframe: String) -> [PetAppointment] {
        let calendar = Calendar.current
        let now = Date()
        
        let endDate: Date
        switch timeframe.lowercased() {
        case "week":
            endDate = calendar.date(byAdding: .weekOfYear, value: 1, to: now) ?? now
        case "month":
            endDate = calendar.date(byAdding: .month, value: 1, to: now) ?? now
        case "year":
            endDate = calendar.date(byAdding: .year, value: 1, to: now) ?? now
        default:
            endDate = calendar.date(byAdding: .month, value: 1, to: now) ?? now
        }
        
        return appointments.filter { $0.scheduledDate <= endDate }
    }
    
    private func generateAppointmentsSummary(from appointments: [PetAppointment]) -> String {
        if appointments.isEmpty {
            return "No upcoming appointments scheduled."
        }
        
        let appointmentTypes = Dictionary(grouping: appointments) { $0.appointmentType }
        var summary = "\(appointments.count) upcoming appointment(s): "
        
        let typeCounts = appointmentTypes.map { type, apps in
            "\(apps.count) \(type.lowercased())"
        }.joined(separator: ", ")
        
        summary += typeCounts + ". "
        
        if let nextAppointment = appointments.first {
            let daysUntil = Calendar.current.dateComponents([.day], from: Date(), to: nextAppointment.scheduledDate).day ?? 0
            summary += "Next appointment is \(nextAppointment.title) in \(daysUntil) day(s)."
        }
        
        return summary
    }
    
    private func getNextAppointmentInfo(from appointments: [PetAppointment]) -> [String: Any]? {
        guard let nextAppointment = appointments.first else { return nil }
        
        let daysUntil = Calendar.current.dateComponents([.day], from: Date(), to: nextAppointment.scheduledDate).day ?? 0
        
        return [
            "title": nextAppointment.title,
            "type": nextAppointment.appointmentType,
            "date": nextAppointment.scheduledDate.timeIntervalSince1970,
            "daysUntil": daysUntil,
            "location": nextAppointment.location.name
        ]
    }
    
    private func getUrgentAppointments(from appointments: [PetAppointment]) -> [[String: Any]] {
        let urgentAppointments = appointments.filter { appointment in
            let daysUntil = Calendar.current.dateComponents([.day], from: Date(), to: appointment.scheduledDate).day ?? 0
            return daysUntil <= 3 && daysUntil >= 0
        }
        
        return urgentAppointments.map { appointment in
            [
                "title": appointment.title,
                "type": appointment.appointmentType,
                "daysUntil": Calendar.current.dateComponents([.day], from: Date(), to: appointment.scheduledDate).day ?? 0
            ]
        }
    }
}

// MARK: - Feeding Schedule Tool

@available(iOS 18.0, *)
class FeedingScheduleTool: BaseAITool {
    init() {
        super.init(
            name: "getFeedingSchedule",
            description: "Get feeding schedule and meal information for a pet including times, portions, and compliance",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get feeding schedule for"),
                AIToolParameter(name: "includeHistory", type: .boolean, description: "Whether to include recent feeding history", required: false, defaultValue: true)
            ],
            category: .care
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let includeHistory = parameters["includeHistory"] as? Bool ?? true
        let dataService = await AppleIntelligenceDataService.shared
        
        let feedingSchedules = await dataService.getFeedingSchedule(for: petId)
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "totalSchedules": feedingSchedules.count,
            "activeSchedules": feedingSchedules.filter { $0.isActive }.count,
            "feedingSchedules": feedingSchedules.map { schedule in
                [
                    "id": schedule.id.uuidString,
                    "title": schedule.title,
                    "description": schedule.description ?? "",
                    "frequency": schedule.frequency.displayName,
                    "isActive": schedule.isActive,
                    "lastCompleted": schedule.lastCompleted?.timeIntervalSince1970,
                    "nextDue": schedule.nextDue?.timeIntervalSince1970,
                    "tags": schedule.tags,
                    "scheduledTimes": schedule.scheduledTimes.map { $0.timeIntervalSince1970 }
                ]
            }
        ]
        
        let metadata: [String: Any] = [
            "summary": generateFeedingSummary(from: feedingSchedules),
            "nextMealTime": getNextMealTime(from: feedingSchedules),
            "missedMeals": getMissedMeals(from: feedingSchedules),
            "dailyMealCount": getDailyMealCount(from: feedingSchedules)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func generateFeedingSummary(from schedules: [PetCareSchedule]) -> String {
        let activeSchedules = schedules.filter { $0.isActive }
        
        if activeSchedules.isEmpty {
            return "No active feeding schedules configured."
        }
        
        let dailyMeals = getDailyMealCount(from: activeSchedules)
        var summary = "\(dailyMeals) meal(s) per day scheduled. "
        
        let missedMeals = getMissedMeals(from: activeSchedules)
        if !missedMeals.isEmpty {
            summary += "\(missedMeals.count) missed meal(s) detected. "
        }
        
        if let nextMeal = getNextMealTime(from: activeSchedules) {
            let hoursUntil = (nextMeal.timeIntervalSince(Date())) / 3600
            if hoursUntil > 0 {
                summary += "Next meal in \(String(format: "%.1f", hoursUntil)) hours."
            } else {
                summary += "Next meal is overdue."
            }
        }
        
        return summary
    }
    
    private func getNextMealTime(from schedules: [PetCareSchedule]) -> Date? {
        let activeSchedules = schedules.filter { $0.isActive }
        
        return activeSchedules.compactMap { $0.nextDue }
            .filter { $0 > Date() }
            .min()
    }
    
    private func getMissedMeals(from schedules: [PetCareSchedule]) -> [PetCareSchedule] {
        let activeSchedules = schedules.filter { $0.isActive }
        
        return activeSchedules.filter { schedule in
            guard let nextDue = schedule.nextDue else { return false }
            return nextDue < Date()
        }
    }
    
    private func getDailyMealCount(from schedules: [PetCareSchedule]) -> Int {
        let activeSchedules = schedules.filter { $0.isActive }
        
        return activeSchedules.reduce(0) { total, schedule in
            switch schedule.frequency {
            case .daily(let times):
                return total + times
            case .weekly(_):
                return total + 1 // Simplified - would need more complex calculation
            case .monthly(_):
                return total
            case .custom(_):
                return total + 1 // Simplified
            }
        }
    }
}

// MARK: - Exercise Log Tool

@available(iOS 18.0, *)
class ExerciseLogTool: BaseAITool {
    init() {
        super.init(
            name: "getExerciseLog",
            description: "Get exercise history and activity patterns for a pet including duration, intensity, and recommendations",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get exercise log for"),
                AIToolParameter(name: "timeframe", type: .string, description: "Timeframe for analysis (week, month)", required: false, defaultValue: "week")
            ],
            category: .care
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let timeframe = extractOptionalString(from: parameters, key: "timeframe") ?? "week"
        let dataService = await AppleIntelligenceDataService.shared
        
        let exerciseAnalysis = await dataService.getExerciseLog(for: petId)
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "timeframe": timeframe,
            "weeklyExerciseMinutes": exerciseAnalysis.weeklyExerciseMinutes,
            "exercisePatterns": exerciseAnalysis.exercisePatterns,
            "recommendations": exerciseAnalysis.recommendations,
            "lastUpdated": exerciseAnalysis.lastUpdated.timeIntervalSince1970
        ]
        
        let metadata: [String: Any] = [
            "summary": generateExerciseSummary(from: exerciseAnalysis),
            "activityLevel": determineActivityLevel(from: exerciseAnalysis),
            "improvementSuggestions": generateImprovementSuggestions(from: exerciseAnalysis)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func generateExerciseSummary(from analysis: ExerciseAnalysis) -> String {
        let weeklyMinutes = analysis.weeklyExerciseMinutes
        let dailyAverage = weeklyMinutes / 7
        
        var summary = "Weekly exercise: \(weeklyMinutes) minutes (\(dailyAverage) minutes/day average). "
        
        let activityLevel = determineActivityLevel(from: analysis)
        summary += "Activity level: \(activityLevel). "
        
        if !analysis.exercisePatterns.isEmpty {
            summary += "Patterns: \(analysis.exercisePatterns.joined(separator: ", ")). "
        }
        
        return summary
    }
    
    private func determineActivityLevel(from analysis: ExerciseAnalysis) -> String {
        let weeklyMinutes = analysis.weeklyExerciseMinutes
        
        switch weeklyMinutes {
        case 0..<60:
            return "Low"
        case 60..<180:
            return "Moderate"
        case 180..<300:
            return "High"
        default:
            return "Very High"
        }
    }
    
    private func generateImprovementSuggestions(from analysis: ExerciseAnalysis) -> [String] {
        var suggestions: [String] = []
        
        let weeklyMinutes = analysis.weeklyExerciseMinutes
        
        if weeklyMinutes < 60 {
            suggestions.append("Consider increasing daily exercise to at least 15 minutes")
        }
        
        if analysis.exercisePatterns.contains("No regular exercise schedule") {
            suggestions.append("Establish a consistent daily exercise routine")
        }
        
        if weeklyMinutes > 300 {
            suggestions.append("Monitor for signs of overexertion")
        }
        
        return suggestions
    }
}

// MARK: - Behavior Notes Tool

@available(iOS 18.0, *)
class BehaviorNotesTool: BaseAITool {
    init() {
        super.init(
            name: "getBehaviorNotes",
            description: "Get behavior observations and training progress for a pet including patterns and recommendations",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get behavior notes for"),
                AIToolParameter(name: "limit", type: .integer, description: "Maximum number of behavior records to return", required: false, defaultValue: 20),
                AIToolParameter(name: "behaviorType", type: .string, description: "Filter by behavior type (positive, concerning, training, etc.)", required: false)
            ],
            category: .behavior
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let limit = extractOptionalInt(from: parameters, key: "limit") ?? 20
        let behaviorTypeFilter = extractOptionalString(from: parameters, key: "behaviorType")
        let dataService = await AppleIntelligenceDataService.shared
        
        var behaviorRecords = await dataService.getBehaviorNotes(for: petId)
        
        // Filter by behavior type if specified
        if let behaviorType = behaviorTypeFilter {
            behaviorRecords = behaviorRecords.filter { $0.behaviorType == behaviorType }
        }
        
        let limitedRecords = Array(behaviorRecords.prefix(limit))
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "totalRecords": limitedRecords.count,
            "behaviorType": behaviorTypeFilter ?? "all",
            "behaviorRecords": limitedRecords.map { record in
                [
                    "id": record.id.uuidString,
                    "behaviorType": record.behaviorType,
                    "title": record.title,
                    "description": record.description,
                    "date": record.date.timeIntervalSince1970,
                    "severity": record.severity.rawValue,
                    "context": [
                        "location": record.context.location,
                        "timeOfDay": record.context.timeOfDay,
                        "peoplePresent": record.context.peoplePresent,
                        "otherPetsPresent": record.context.otherPetsPresent,
                        "environmentalFactors": record.context.environmentalFactors
                    ],
                    "triggers": record.triggers,
                    "interventions": record.interventions,
                    "outcome": record.outcome?.rawValue,
                    "tags": record.tags
                ]
            }
        ]
        
        let metadata: [String: Any] = [
            "summary": generateBehaviorSummary(from: limitedRecords),
            "behaviorPatterns": analyzeBehaviorPatterns(from: limitedRecords),
            "trainingProgress": analyzeTrainingProgress(from: limitedRecords),
            "recommendations": generateBehaviorRecommendations(from: limitedRecords)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func generateBehaviorSummary(from records: [PetBehaviorRecord]) -> String {
        if records.isEmpty {
            return "No behavior records found."
        }
        
        let behaviorTypes = Dictionary(grouping: records) { $0.behaviorType }
        let recentRecords = records.filter { 
            Calendar.current.dateInterval(of: .weekOfYear, for: Date())?.contains($0.date) ?? false 
        }
        
        var summary = "\(records.count) behavior record(s) total, \(recentRecords.count) this week. "
        
        let typeCounts = behaviorTypes.map { type, records in
            "\(records.count) \(type.lowercased())"
        }.joined(separator: ", ")
        
        summary += "Types: \(typeCounts). "
        
        let concerningRecords = records.filter { $0.behaviorType == "concerning" || $0.behaviorType == "aggressive" }
        if !concerningRecords.isEmpty {
            summary += "\(concerningRecords.count) concerning behavior(s) noted."
        }
        
        return summary
    }
    
    private func analyzeBehaviorPatterns(from records: [PetBehaviorRecord]) -> [String] {
        var patterns: [String] = []
        
        // Analyze time patterns
        let timeGroups = Dictionary(grouping: records) { $0.context.timeOfDay }
        if let mostCommonTime = timeGroups.max(by: { $0.value.count < $1.value.count })?.key {
            patterns.append("Most behaviors occur during \(mostCommonTime)")
        }
        
        // Analyze location patterns
        let locationGroups = Dictionary(grouping: records) { $0.context.location }
        if let mostCommonLocation = locationGroups.max(by: { $0.value.count < $1.value.count })?.key {
            patterns.append("Most behaviors occur at \(mostCommonLocation)")
        }
        
        // Analyze trigger patterns
        let allTriggers = records.flatMap { $0.triggers }
        let triggerCounts = Dictionary(grouping: allTriggers) { $0 }
        if let commonTrigger = triggerCounts.max(by: { $0.value.count < $1.value.count })?.key {
            patterns.append("Common trigger: \(commonTrigger)")
        }
        
        return patterns
    }
    
    private func analyzeTrainingProgress(from records: [PetBehaviorRecord]) -> [String: Any] {
        let trainingRecords = records.filter { $0.behaviorType == "training" }
        let positiveRecords = records.filter { $0.behaviorType == "positive" }
        
        let improvedOutcomes = records.filter { $0.outcome == .improved || $0.outcome == .resolved }
        let progressRate = trainingRecords.isEmpty ? 0.0 : Double(improvedOutcomes.count) / Double(trainingRecords.count)
        
        return [
            "trainingSessionsCount": trainingRecords.count,
            "positiveRecordsCount": positiveRecords.count,
            "progressRate": progressRate,
            "improvementTrend": progressRate > 0.7 ? "Excellent" : progressRate > 0.5 ? "Good" : "Needs Attention"
        ]
    }
    
    private func generateBehaviorRecommendations(from records: [PetBehaviorRecord]) -> [String] {
        var recommendations: [String] = []
        
        let concerningRecords = records.filter {
            $0.behaviorType == "concerning" || $0.behaviorType == "aggressive" || $0.severity.rawValue == "severe"
        }
        
        if !concerningRecords.isEmpty {
            recommendations.append("Consider consulting a professional trainer for concerning behaviors")
        }
        
        let trainingRecords = records.filter { $0.behaviorType == "training" }
        if trainingRecords.count < 5 {
            recommendations.append("Increase training session frequency for better progress")
        }
        
        let recentRecords = records.filter { 
            Calendar.current.dateInterval(of: .weekOfYear, for: Date())?.contains($0.date) ?? false 
        }
        if recentRecords.isEmpty {
            recommendations.append("Regular behavior logging helps track progress")
        }
        
        return recommendations
    }
}
