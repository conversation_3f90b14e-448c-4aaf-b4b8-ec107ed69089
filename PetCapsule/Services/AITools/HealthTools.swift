//
//  HealthTools.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import Foundation

// MARK: - Pet Health Summary Tool

@available(iOS 18.0, *)
class PetHealthSummaryTool: BaseAITool {
    init() {
        super.init(
            name: "getPetHealthSummary",
            description: "Get a comprehensive health summary for a pet including health score, recent records, vaccination status, and alerts",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get health summary for")
            ],
            category: .health
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let dataService = await AppleIntelligenceDataService.shared

        let healthSummary = await dataService.getPetHealthSummary(for: petId)
        
        let data: [String: Any] = [
            "petId": healthSummary.petId.uuidString,
            "overallHealthScore": healthSummary.overallHealthScore,
            "healthScorePercentage": Int(healthSummary.overallHealthScore * 100),
            "recentRecordsCount": healthSummary.recentRecords.count,
            "vaccinationUpToDate": healthSummary.vaccinationStatus.isUpToDate,
            "lastVaccinationDate": healthSummary.vaccinationStatus.lastVaccination?.date.timeIntervalSince1970,
            "activeMedicationsCount": healthSummary.activeMedications.count,
            "upcomingAppointmentsCount": healthSummary.upcomingAppointments.count,
            "healthAlertsCount": healthSummary.healthAlerts.count,
            "healthAlerts": healthSummary.healthAlerts.map { alert in
                [
                    "id": alert.id.uuidString,
                    "type": alert.type.rawValue,
                    "title": alert.title,
                    "message": alert.message,
                    "severity": alert.severity.rawValue
                ]
            },
            "lastUpdated": healthSummary.lastUpdated.timeIntervalSince1970
        ]
        
        let metadata: [String: Any] = [
            "summary": generateHealthSummaryText(from: healthSummary),
            "recommendations": generateHealthRecommendations(from: healthSummary)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func generateHealthSummaryText(from summary: PetHealthSummary) -> String {
        var text = "Health score is \(Int(summary.overallHealthScore * 100))%. "
        
        if summary.vaccinationStatus.isUpToDate {
            text += "Vaccinations are up to date. "
        } else {
            text += "Vaccinations may be overdue. "
        }
        
        if !summary.activeMedications.isEmpty {
            text += "Currently on \(summary.activeMedications.count) medication(s). "
        }
        
        if !summary.healthAlerts.isEmpty {
            text += "There are \(summary.healthAlerts.count) health alert(s) requiring attention. "
        }
        
        if !summary.upcomingAppointments.isEmpty {
            text += "\(summary.upcomingAppointments.count) upcoming appointment(s) scheduled."
        }
        
        return text
    }
    
    private func generateHealthRecommendations(from summary: PetHealthSummary) -> [String] {
        var recommendations: [String] = []
        
        if summary.overallHealthScore < 0.7 {
            recommendations.append("Consider scheduling a veterinary checkup due to lower health score")
        }
        
        if !summary.vaccinationStatus.isUpToDate {
            recommendations.append("Schedule vaccination appointment - may be overdue")
        }
        
        if summary.healthAlerts.contains(where: { $0.severity == .serious || $0.severity == .critical }) {
            recommendations.append("Address critical health alerts immediately")
        }
        
        if summary.recentRecords.isEmpty {
            recommendations.append("Consider logging recent health observations")
        }
        
        return recommendations
    }
}

// MARK: - Vaccination History Tool

@available(iOS 18.0, *)
class VaccinationHistoryTool: BaseAITool {
    init() {
        super.init(
            name: "getVaccinationHistory",
            description: "Get complete vaccination history for a pet including dates, types, and next due dates",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get vaccination history for"),
                AIToolParameter(name: "limit", type: .integer, description: "Maximum number of vaccination records to return", required: false, defaultValue: 10)
            ],
            category: .health
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let limit = extractOptionalInt(from: parameters, key: "limit") ?? 10
        let dataService = await AppleIntelligenceDataService.shared
        
        let vaccinations = await dataService.getVaccinationHistory(for: petId).prefix(limit)
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "totalVaccinations": vaccinations.count,
            "vaccinations": vaccinations.map { vaccination in
                [
                    "id": vaccination.id.uuidString,
                    "title": vaccination.title,
                    "description": vaccination.description,
                    "date": vaccination.date.timeIntervalSince1970,
                    "vetName": vaccination.vetName ?? "",
                    "severity": vaccination.severity.rawValue,
                    "tags": vaccination.tags
                ]
            },
            "lastVaccination": vaccinations.first?.date.timeIntervalSince1970,
            "isUpToDate": isVaccinationUpToDate(vaccinations: Array(vaccinations))
        ]
        
        let metadata: [String: Any] = [
            "summary": generateVaccinationSummary(from: Array(vaccinations)),
            "nextDueDate": calculateNextVaccinationDate(from: Array(vaccinations))?.timeIntervalSince1970
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func isVaccinationUpToDate(vaccinations: [PetHealthRecord]) -> Bool {
        guard let lastVaccination = vaccinations.first else { return false }
        let daysSinceVaccination = Calendar.current.dateComponents([.day], from: lastVaccination.date, to: Date()).day ?? 0
        return daysSinceVaccination <= 365
    }
    
    private func generateVaccinationSummary(from vaccinations: [PetHealthRecord]) -> String {
        if vaccinations.isEmpty {
            return "No vaccination records found."
        }
        
        let isUpToDate = isVaccinationUpToDate(vaccinations: vaccinations)
        let lastDate = vaccinations.first?.date
        
        var summary = "Total of \(vaccinations.count) vaccination record(s). "
        
        if let lastDate = lastDate {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            summary += "Last vaccination was on \(formatter.string(from: lastDate)). "
        }
        
        summary += isUpToDate ? "Vaccinations are up to date." : "Vaccinations may be overdue."
        
        return summary
    }
    
    private func calculateNextVaccinationDate(from vaccinations: [PetHealthRecord]) -> Date? {
        guard let lastVaccination = vaccinations.first else { return nil }
        return Calendar.current.date(byAdding: .year, value: 1, to: lastVaccination.date)
    }
}

// MARK: - Medication Schedule Tool

@available(iOS 18.0, *)
class MedicationScheduleTool: BaseAITool {
    init() {
        super.init(
            name: "getMedicationSchedule",
            description: "Get current medication schedule and compliance information for a pet",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get medication schedule for"),
                AIToolParameter(name: "activeOnly", type: .boolean, description: "Whether to return only active medications", required: false, defaultValue: true)
            ],
            category: .health
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let activeOnly = parameters["activeOnly"] as? Bool ?? true
        let dataService = await AppleIntelligenceDataService.shared
        
        var medications = await dataService.getMedicationSchedule(for: petId)
        
        if activeOnly {
            medications = medications.filter { $0.isActive }
        }
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "totalMedications": medications.count,
            "activeMedications": medications.filter { $0.isActive }.count,
            "medications": medications.map { medication in
                [
                    "id": medication.id.uuidString,
                    "title": medication.title,
                    "description": medication.description ?? "",
                    "frequency": medication.frequency.displayName,
                    "isActive": medication.isActive,
                    "lastCompleted": medication.lastCompleted?.timeIntervalSince1970,
                    "nextDue": medication.nextDue?.timeIntervalSince1970,
                    "tags": medication.tags
                ]
            }
        ]
        
        let metadata: [String: Any] = [
            "summary": generateMedicationSummary(from: medications),
            "upcomingDoses": getUpcomingDoses(from: medications)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func generateMedicationSummary(from medications: [PetCareSchedule]) -> String {
        let activeMedications = medications.filter { $0.isActive }
        
        if activeMedications.isEmpty {
            return "No active medications currently scheduled."
        }
        
        var summary = "Currently on \(activeMedications.count) active medication(s). "
        
        let overdueMedications = activeMedications.filter { medication in
            guard let nextDue = medication.nextDue else { return false }
            return nextDue < Date()
        }
        
        if !overdueMedications.isEmpty {
            summary += "\(overdueMedications.count) dose(s) are overdue. "
        }
        
        return summary
    }
    
    private func getUpcomingDoses(from medications: [PetCareSchedule]) -> [[String: Any]] {
        let activeMedications = medications.filter { $0.isActive }
        
        return activeMedications.compactMap { medication in
            guard let nextDue = medication.nextDue, nextDue > Date() else { return nil }
            
            return [
                "medicationTitle": medication.title,
                "nextDue": nextDue.timeIntervalSince1970,
                "hoursUntilDue": nextDue.timeIntervalSince(Date()) / 3600
            ]
        }.sorted { (first, second) in
            let firstDue = first["nextDue"] as? TimeInterval ?? 0
            let secondDue = second["nextDue"] as? TimeInterval ?? 0
            return firstDue < secondDue
        }
    }
}

// MARK: - Weight Trends Tool

@available(iOS 18.0, *)
class WeightTrendsTool: BaseAITool {
    init() {
        super.init(
            name: "getWeightTrends",
            description: "Get weight history and trend analysis for a pet including recommendations",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get weight trends for"),
                AIToolParameter(name: "timeframe", type: .string, description: "Timeframe for analysis (week, month, year)", required: false, defaultValue: "month")
            ],
            category: .health
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        _ = extractOptionalString(from: parameters, key: "timeframe") ?? "month"
        let dataService = await AppleIntelligenceDataService.shared
        
        let weightAnalysis = await dataService.getWeightTrends(for: petId)
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "currentWeight": weightAnalysis.currentWeight ?? 0,
            "weightHistory": weightAnalysis.weightHistory.map { entry in
                [
                    "date": entry.date.timeIntervalSince1970,
                    "weight": entry.weight
                ]
            },
            "trend": weightAnalysis.trend.rawValue,
            "recommendations": weightAnalysis.recommendations,
            "lastUpdated": weightAnalysis.lastUpdated.timeIntervalSince1970
        ]
        
        let metadata: [String: Any] = [
            "summary": generateWeightSummary(from: weightAnalysis),
            "trendDescription": getTrendDescription(weightAnalysis.trend),
            "dataPoints": weightAnalysis.weightHistory.count
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func generateWeightSummary(from analysis: WeightTrendAnalysis) -> String {
        guard let currentWeight = analysis.currentWeight else {
            return "No weight data available."
        }
        
        var summary = "Current weight is \(String(format: "%.1f", currentWeight)) kg. "
        
        switch analysis.trend {
        case .increasing:
            summary += "Weight has been increasing recently. "
        case .decreasing:
            summary += "Weight has been decreasing recently. "
        case .stable:
            summary += "Weight has been stable. "
        }
        
        if !analysis.recommendations.isEmpty {
            summary += "Recommendations: \(analysis.recommendations.joined(separator: ", "))"
        }
        
        return summary
    }
    
    private func getTrendDescription(_ trend: WeightTrend) -> String {
        switch trend {
        case .increasing:
            return "Weight is trending upward - monitor diet and exercise"
        case .decreasing:
            return "Weight is trending downward - ensure adequate nutrition"
        case .stable:
            return "Weight is stable - maintain current routine"
        }
    }
}
