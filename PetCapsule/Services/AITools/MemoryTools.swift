//
//  MemoryTools.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import Foundation

// MARK: - Recent Memories Tool

@available(iOS 18.0, *)
class RecentMemoriesTool: BaseAITool {
    init() {
        super.init(
            name: "getRecentMemories",
            description: "Get recent memories, photos, and moments for a pet including emotional context and sharing status",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get memories for"),
                AIToolParameter(name: "limit", type: .integer, description: "Maximum number of memories to return", required: false, defaultValue: 10),
                AIToolParameter(name: "timeframe", type: .string, description: "Timeframe for memories (week, month, year)", required: false, defaultValue: "month"),
                AIToolParameter(name: "memoryType", type: .string, description: "Filter by memory type (photo, video, achievement, etc.)", required: false)
            ],
            category: .memories
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let limit = extractOptionalInt(from: parameters, key: "limit") ?? 10
        let timeframe = extractOptionalString(from: parameters, key: "timeframe") ?? "month"
        let memoryTypeFilter = extractOptionalString(from: parameters, key: "memoryType")
        let dataService = await AppleIntelligenceDataService.shared
        
        var memories = await dataService.getRecentMemories(for: petId, limit: limit * 2) // Get more to filter
        
        // Filter by timeframe
        memories = filterMemoriesByTimeframe(memories, timeframe: timeframe)
        
        // Filter by memory type if specified
        if let memoryType = memoryTypeFilter {
            memories = memories.filter { $0.memoryType.rawValue == memoryType }
        }
        
        // Limit results
        let limitedMemories = Array(memories.prefix(limit))
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "totalMemories": limitedMemories.count,
            "timeframe": timeframe,
            "memoryType": memoryTypeFilter ?? "all",
            "memories": limitedMemories.map { memory in
                [
                    "id": memory.id.uuidString,
                    "memoryType": memory.memoryType.rawValue,
                    "title": memory.title,
                    "description": memory.description ?? "",
                    "date": memory.date.timeIntervalSince1970,
                    "location": memory.location ?? "",
                    "mediaURLs": memory.mediaURLs,
                    "tags": memory.tags,
                    "milestone": memory.milestone,
                    "shareStatus": memory.shareStatus.rawValue,
                    "emotionalTone": memory.emotionalTone.rawValue,
                    "daysAgo": Calendar.current.dateComponents([.day], from: memory.date, to: Date()).day ?? 0
                ]
            }
        ]
        
        let metadata: [String: Any] = [
            "summary": generateMemoriesSummary(from: limitedMemories),
            "emotionalAnalysis": analyzeEmotionalTones(from: limitedMemories),
            "memoryTypes": analyzeMemoryTypes(from: limitedMemories),
            "recentHighlights": getRecentHighlights(from: limitedMemories)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func filterMemoriesByTimeframe(_ memories: [PetMemory], timeframe: String) -> [PetMemory] {
        let calendar = Calendar.current
        let now = Date()
        
        let startDate: Date
        switch timeframe.lowercased() {
        case "week":
            startDate = calendar.date(byAdding: .weekOfYear, value: -1, to: now) ?? now
        case "month":
            startDate = calendar.date(byAdding: .month, value: -1, to: now) ?? now
        case "year":
            startDate = calendar.date(byAdding: .year, value: -1, to: now) ?? now
        default:
            startDate = calendar.date(byAdding: .month, value: -1, to: now) ?? now
        }
        
        return memories.filter { $0.date >= startDate }
    }
    
    private func generateMemoriesSummary(from memories: [PetMemory]) -> String {
        if memories.isEmpty {
            return "No recent memories found."
        }
        
        let milestones = memories.filter { $0.milestone }
        let mediaMemories = memories.filter { !$0.mediaURLs.isEmpty }
        
        var summary = "\(memories.count) recent memor(ies) captured. "
        
        if !milestones.isEmpty {
            summary += "\(milestones.count) milestone(s) achieved. "
        }
        
        if !mediaMemories.isEmpty {
            summary += "\(mediaMemories.count) with photos/videos. "
        }
        
        let emotionalTones = Dictionary(grouping: memories) { $0.emotionalTone }
        if let dominantTone = emotionalTones.max(by: { $0.value.count < $1.value.count })?.key {
            summary += "Overall mood: \(dominantTone.displayName.lowercased())."
        }
        
        return summary
    }
    
    private func analyzeEmotionalTones(from memories: [PetMemory]) -> [String: Any] {
        let emotionalGroups = Dictionary(grouping: memories) { $0.emotionalTone }
        let emotionalCounts = emotionalGroups.mapValues { $0.count }
        
        let totalMemories = memories.count
        let emotionalPercentages = emotionalCounts.mapValues { count in
            totalMemories > 0 ? Double(count) / Double(totalMemories) * 100 : 0.0
        }
        
        return [
            "emotionalCounts": emotionalCounts.mapKeys { $0.rawValue },
            "emotionalPercentages": emotionalPercentages.mapKeys { $0.rawValue },
            "dominantEmotion": emotionalCounts.max(by: { $0.value < $1.value })?.key.rawValue ?? "unknown"
        ]
    }
    
    private func analyzeMemoryTypes(from memories: [PetMemory]) -> [String: Int] {
        let typeGroups = Dictionary(grouping: memories) { $0.memoryType }
        return typeGroups.mapValues { $0.count }.mapKeys { $0.rawValue }
    }
    
    private func getRecentHighlights(from memories: [PetMemory]) -> [[String: Any]] {
        let highlights = memories.filter { memory in
            memory.milestone || 
            memory.emotionalTone == .excited || 
            memory.emotionalTone == .proud ||
            memory.memoryType == .achievement ||
            memory.memoryType == .firstTime
        }.prefix(3)
        
        return highlights.map { memory in
            [
                "title": memory.title,
                "type": memory.memoryType.displayName,
                "emotionalTone": memory.emotionalTone.displayName,
                "daysAgo": Calendar.current.dateComponents([.day], from: memory.date, to: Date()).day ?? 0,
                "milestone": memory.milestone
            ]
        }
    }
}

// MARK: - Milestones Tool

@available(iOS 18.0, *)
class MilestonesTool: BaseAITool {
    init() {
        super.init(
            name: "getMilestones",
            description: "Get important milestones and achievements for a pet including first times, training successes, and health milestones",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get milestones for"),
                AIToolParameter(name: "limit", type: .integer, description: "Maximum number of milestones to return", required: false, defaultValue: 20)
            ],
            category: .memories
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = try extractPetId(from: parameters)
        let limit = extractOptionalInt(from: parameters, key: "limit") ?? 20
        let dataService = await AppleIntelligenceDataService.shared
        
        let milestones = Array((await dataService.getMilestones(for: petId)).prefix(limit))
        
        let data: [String: Any] = [
            "petId": petId.uuidString,
            "totalMilestones": milestones.count,
            "milestones": milestones.map { milestone in
                [
                    "id": milestone.id.uuidString,
                    "memoryType": milestone.memoryType.rawValue,
                    "title": milestone.title,
                    "description": milestone.description ?? "",
                    "date": milestone.date.timeIntervalSince1970,
                    "location": milestone.location ?? "",
                    "mediaURLs": milestone.mediaURLs,
                    "tags": milestone.tags,
                    "emotionalTone": milestone.emotionalTone.rawValue,
                    "ageAtMilestone": calculateAgeAtMilestone(petId: petId, milestoneDate: milestone.date)
                ]
            }
        ]
        
        let metadata: [String: Any] = [
            "summary": generateMilestonesSummary(from: milestones),
            "milestoneCategories": categorizeMilestones(from: milestones),
            "recentMilestones": getRecentMilestones(from: milestones),
            "upcomingMilestones": suggestUpcomingMilestones(for: petId)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func calculateAgeAtMilestone(petId: UUID, milestoneDate: Date) -> [String: Any] {
        // This would need access to pet's birth date from the data service
        // For now, return a placeholder
        return [
            "months": 0,
            "description": "Age calculation requires birth date"
        ]
    }
    
    private func generateMilestonesSummary(from milestones: [PetMemory]) -> String {
        if milestones.isEmpty {
            return "No milestones recorded yet."
        }
        
        let categories = categorizeMilestones(from: milestones)
        let recentMilestones = milestones.filter { 
            Calendar.current.dateInterval(of: .month, for: Date())?.contains($0.date) ?? false 
        }
        
        var summary = "\(milestones.count) milestone(s) achieved. "
        
        if !recentMilestones.isEmpty {
            summary += "\(recentMilestones.count) this month. "
        }
        
        let categoryNames = categories.keys.sorted()
        if !categoryNames.isEmpty {
            summary += "Categories: \(categoryNames.joined(separator: ", "))."
        }
        
        return summary
    }
    
    private func categorizeMilestones(from milestones: [PetMemory]) -> [String: Int] {
        let categories = Dictionary(grouping: milestones) { milestone in
            switch milestone.memoryType {
            case .achievement, .training:
                return "Training & Achievements"
            case .firstTime:
                return "First Times"
            case .health:
                return "Health Milestones"
            case .social:
                return "Social Development"
            default:
                return "General Milestones"
            }
        }
        
        return categories.mapValues { $0.count }
    }
    
    private func getRecentMilestones(from milestones: [PetMemory]) -> [[String: Any]] {
        let recentMilestones = milestones.filter {
            let threeMonthsAgo = Calendar.current.date(byAdding: .month, value: -3, to: Date()) ?? Date()
            return $0.date >= threeMonthsAgo
        }.prefix(5)
        
        return recentMilestones.map { milestone in
            [
                "title": milestone.title,
                "type": milestone.memoryType.displayName,
                "date": milestone.date.timeIntervalSince1970,
                "daysAgo": Calendar.current.dateComponents([.day], from: milestone.date, to: Date()).day ?? 0
            ]
        }
    }
    
    private func suggestUpcomingMilestones(for petId: UUID) -> [String] {
        // This would analyze the pet's age and existing milestones to suggest upcoming ones
        return [
            "First birthday celebration",
            "Advanced training commands",
            "Socialization with new pets",
            "Outdoor adventure milestones"
        ]
    }
}

// MARK: - Emergency Contacts Tool

@available(iOS 18.0, *)
class EmergencyContactsTool: BaseAITool {
    init() {
        super.init(
            name: "getEmergencyContacts",
            description: "Get emergency contact information including veterinarians, pet sitters, and family contacts",
            parameters: [
                AIToolParameter(name: "petId", type: .uuid, description: "The ID of the pet to get emergency contacts for (optional for general contacts)", required: false),
                AIToolParameter(name: "contactType", type: .string, description: "Filter by contact type (veterinarian, emergency_vet, pet_sitter, etc.)", required: false),
                AIToolParameter(name: "availableNow", type: .boolean, description: "Filter for contacts available right now", required: false, defaultValue: false)
            ],
            category: .emergency
        )
    }
    
    override func performExecution(parameters: [String: Any]) async throws -> AIToolResult {
        let petId = parameters["petId"] as? String != nil ? try extractPetId(from: parameters) : nil
        let contactTypeFilter = extractOptionalString(from: parameters, key: "contactType")
        let availableNow = parameters["availableNow"] as? Bool ?? false
        let dataService = await AppleIntelligenceDataService.shared
        
        var contacts = await dataService.getEmergencyContacts(for: petId)
        
        // Filter by contact type if specified
        if let contactType = contactTypeFilter {
            contacts = contacts.filter { $0.type.rawValue == contactType }
        }
        
        // Filter by availability if requested
        if availableNow {
            contacts = contacts.filter { isContactAvailableNow($0) }
        }
        
        let data: [String: Any] = [
            "petId": petId?.uuidString ?? "all",
            "totalContacts": contacts.count,
            "contactType": contactTypeFilter ?? "all",
            "availableNow": availableNow,
            "contacts": contacts.map { contact in
                [
                    "id": contact.id,
                    "contactType": contact.type.rawValue,
                    "name": contact.name,
                    "phoneNumber": contact.phoneNumber,
                    "country": contact.country,
                    "description": contact.description ?? "",
                    "cost": "Contact for pricing", // EmergencyContact doesn't have cost property
                    "availability": "Call to check availability", // EmergencyContact doesn't have availability property
                    "isEditable": contact.isEditable,
                    "isAvailableNow": isContactAvailableNow(contact)
                ]
            }
        ]
        
        let metadata: [String: Any] = [
            "summary": generateContactsSummary(from: contacts),
            "primaryContacts": getPrimaryContacts(from: contacts),
            "emergencyContacts": getEmergencyContacts(from: contacts),
            "contactsByType": groupContactsByType(from: contacts)
        ]
        
        return AIToolResult.success(data: data, metadata: metadata)
    }
    
    private func isContactAvailableNow(_ contact: EmergencyContact) -> Bool {
        // For EmergencyContact type, availability is a String?, so we'll use simple logic
        // Emergency contacts are generally considered always available
        if contact.type == .emergency {
            return true
        }

        // For other types, check if availability string indicates they're available
        // EmergencyContact doesn't have availability property, so we'll provide default info
        if contact.type == .emergency {
            // Emergency contacts are assumed to be available 24/7
            return true
        }

        // Default to available during business hours (9 AM - 5 PM)
        let now = Date()
        let calendar = Calendar.current
        let currentHour = calendar.component(.hour, from: now)

        return currentHour >= 9 && currentHour <= 17
    }
    
    private func generateContactsSummary(from contacts: [EmergencyContact]) -> String {
        if contacts.isEmpty {
            return "No emergency contacts configured."
        }
        
        let contactTypes = Dictionary(grouping: contacts) { $0.type }
        let availableNow = contacts.filter { isContactAvailableNow($0) }
        let primaryContacts = contacts.filter { $0.type == .emergency }
        
        var summary = "\(contacts.count) emergency contact(s) available. "
        
        if !primaryContacts.isEmpty {
            summary += "\(primaryContacts.count) primary contact(s). "
        }
        
        if !availableNow.isEmpty {
            summary += "\(availableNow.count) available right now. "
        }
        
        let typeNames = contactTypes.keys.map { $0.displayName }.sorted()
        if !typeNames.isEmpty {
            summary += "Types: \(typeNames.joined(separator: ", "))."
        }
        
        return summary
    }
    
    private func getPrimaryContacts(from contacts: [EmergencyContact]) -> [[String: Any]] {
        let primaryContacts = contacts.filter { $0.type == .emergency }

        return primaryContacts.map { contact in
            [
                "name": contact.name,
                "type": contact.type.displayName,
                "phoneNumber": contact.phoneNumber,
                "isAvailableNow": isContactAvailableNow(contact)
            ]
        }
    }
    
    private func getEmergencyContacts(from contacts: [EmergencyContact]) -> [[String: Any]] {
        let emergencyContacts = contacts.filter { 
            $0.type == .emergency || $0.type == .veterinarian
        }
        
        return emergencyContacts.map { contact in
            [
                "name": contact.name,
                "type": contact.type.displayName,
                "phoneNumber": contact.phoneNumber,
                "isAvailableNow": isContactAvailableNow(contact),
                "availability": "Call to check availability" // EmergencyContact doesn't have availability property
            ]
        }
    }
    
    private func groupContactsByType(from contacts: [EmergencyContact]) -> [String: Int] {
        let contactGroups = Dictionary(grouping: contacts) { $0.type }
        var result: [String: Int] = [:]
        for (key, value) in contactGroups {
            result[key.rawValue] = value.count
        }
        return result
    }
}

// MARK: - Helper Extensions

extension Dictionary {
    func mapKeys<T>(_ transform: (Key) -> T) -> [T: Value] {
        return Dictionary<T, Value>(uniqueKeysWithValues: self.map { (transform($0.key), $0.value) })
    }
}
