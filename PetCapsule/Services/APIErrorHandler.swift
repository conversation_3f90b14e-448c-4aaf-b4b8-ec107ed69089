//
//  APIErrorHandler.swift
//  PetCapsule
//
//  Handles API errors gracefully and provides fallback functionality

import Foundation

class APIErrorHandler {
    static let shared = APIErrorHandler()
    
    private init() {}
    
    // Track API failures to avoid repeated requests
    private var failedAPIs: Set<APIService> = []
    private var lastFailureCheck: Date = Date()
    
    // Handle API authentication errors
    func handleAPIError(for service: APIService, error: Error) {
        let errorDescription = error.localizedDescription
        
        // Check if it's an authentication error
        if isAuthenticationError(error) {
            print("🔑 Authentication failed for \(service): \(errorDescription)")
            markAPIAsFailed(service)
            logAPISetupInstructions(for: service)
        } else if isRateLimitError(error) {
            print("⏰ Rate limit exceeded for \(service): \(errorDescription)")
            // Implement exponential backoff
        } else {
            print("❌ API error for \(service): \(errorDescription)")
        }
    }
    
    // Check if API is currently failing
    func isAPIFailing(_ service: APIService) -> Bool {
        // Reset failure status after 5 minutes
        if Date().timeIntervalSince(lastFailureCheck) > 300 {
            failedAPIs.removeAll()
            lastFailureCheck = Date()
        }
        
        return failedAPIs.contains(service) || !APIKeys.isConfigured(for: service)
    }
    
    // Get fallback data when API is unavailable
    func getFallbackData(for service: APIService) -> Any? {
        switch service {
        case .appleMapKit:
            return getFallbackLocationData()
        case .appleWeatherKit:
            return getFallbackWeatherData()
        case .appleCoreLocation:
            return getFallbackLocationData()
        case .openWeather:
            return getFallbackWeatherData()
        }
    }
    
    // MARK: - Private Methods
    
    private func isAuthenticationError(_ error: Error) -> Bool {
        let errorString = error.localizedDescription.lowercased()
        return errorString.contains("403") || 
               errorString.contains("forbidden") ||
               errorString.contains("unauthorized") ||
               errorString.contains("authentication") ||
               errorString.contains("api key")
    }
    
    private func isRateLimitError(_ error: Error) -> Bool {
        let errorString = error.localizedDescription.lowercased()
        return errorString.contains("429") || 
               errorString.contains("rate limit") ||
               errorString.contains("quota exceeded")
    }
    
    private func markAPIAsFailed(_ service: APIService) {
        failedAPIs// Removed Supabase insert callservice)
        lastFailureCheck = Date()
    }
    
    private func logAPISetupInstructions(for service: APIService) {
        print("\n📋 API Setup Instructions for \(service):")
        
        switch service {
        case .appleMapKit:
            print("""
            🗺️  APPLE MAPKIT (NATIVE)
            ✅ No setup required - MapKit is built into iOS
            💡 Ensure location permissions are configured in Info.plist
            """)
            
        case .appleWeatherKit:
            print("""
            🌤️  APPLE WEATHERKIT (NATIVE)
            ✅ No setup required - WeatherKit is built into iOS
            💡 Available on iOS 16+ with automatic integration
            """)
            
        case .appleCoreLocation:
            print("""
            📍 APPLE CORE LOCATION (NATIVE)
            ✅ No setup required - Core Location is built into iOS
            💡 Ensure location permissions are configured in Info.plist
            """)
            
        case .openWeather:
            print("""
            🌤️  OPENWEATHER API SETUP (OPTIONAL BACKUP)
            1. Go to OpenWeatherMap: https://openweathermap.org/api
            2. Sign up for a free account
            3. Get your API key from the dashboard
            4. Set environment variable: OPENWEATHER_API_KEY="your-api-key"
            """)
        }
        
        print("💡 Most features use native Apple services - no external API keys required!\n")
    }
    
    // MARK: - Fallback Data Methods
    
    private func getFallbackLocationData() -> [String: Any] {
        return [
            "location": "Location services unavailable",
            "coordinates": ["lat": 0.0, "lng": 0.0],
            "note": "Configure Google Maps API for location services"
        ]
    }
    
    private func getFallbackPlacesData() -> [[String: Any]] {
        return [
            [
                "name": "Local Pet Store",
                "type": "pet_store", 
                "rating": 4.0,
                "note": "Configure Google Places API for nearby pet-friendly places"
            ],
            [
                "name": "Neighborhood Park",
                "type": "park",
                "rating": 4.5,
                "note": "Configure Google Places API for location details"
            ]
        ]
    }
    
    private func getFallbackPollenData() -> [String: Any] {
        return [
            "tree_pollen": 2,
            "grass_pollen": 1,
            "weed_pollen": 1,
            "overall_risk": "low",
            "note": "Configure Google Pollen API for accurate pollen data"
        ]
    }
    
    private func getFallbackWeatherData() -> [String: Any] {
        return [
            "temperature": 72,
            "humidity": 50,
            "air_quality": 85,
            "condition": "clear",
            "walkability_score": 0.8,
            "note": "Configure OpenWeather API for accurate weather data"
        ]
    }
}

// MARK: - API Request Wrapper

extension APIErrorHandler {
    
    // Wrapper for safe API requests
    func safeAPIRequest<T>(
        for service: APIService,
        request: @escaping () async throws -> T,
        fallback: @escaping () -> T
    ) async -> T {
        
        // Check if API is already known to be failing
        if isAPIFailing(service) {
            print("⚠️ \(service) API unavailable, using fallback data")
            return fallback()
        }
        
        do {
            let result = try await request()
            // Reset failure status on successful request
            failedAPIs.remove(service)
            return result
        } catch {
            handleAPIError(for: service, error: error)
            return fallback()
        }
    }
    
    // Log overall API health status
    func logAPIHealthStatus() {
        print("\n🔍 API Health Check:")
        APIKeys.logConfigurationStatus()
        
        if !failedAPIs.isEmpty {
            print("❌ Currently failing APIs: \(failedAPIs)")
        } else {
            print("✅ All configured APIs are responding")
        }
        print()
    }
} 