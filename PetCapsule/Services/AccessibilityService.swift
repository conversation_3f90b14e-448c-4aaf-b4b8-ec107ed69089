//
//  AccessibilityService.swift
//  PetCapsule
//
//  Comprehensive accessibility support for WCAG compliance
//

import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

@MainActor
class AccessibilityService: ObservableObject {
    static let shared = AccessibilityService()
    
    // MARK: - Accessibility State
    @Published var isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
    @Published var isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
    @Published var isDynamicTypeEnabled = true
    @Published var preferredContentSizeCategory = UIApplication.shared.preferredContentSizeCategory
    @Published var isHighContrastEnabled = UIAccessibility.isDarkerSystemColorsEnabled
    
    // MARK: - Color Contrast
    private let minimumContrastRatio: Double = 4.5 // WCAG AA standard
    private let enhancedContrastRatio: Double = 7.0 // WCAG AAA standard
    
    private init() {
        setupAccessibilityObservers()
    }
    
    // MARK: - Accessibility Observers
    
    private func setupAccessibilityObservers() {
        NotificationCenter.default.addObserver(
            forName: UIAccessibility.voiceOverStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
            }
        }

        NotificationCenter.default.addObserver(
            forName: UIAccessibility.reduceMotionStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
            }
        }

        NotificationCenter.default.addObserver(
            forName: UIContentSizeCategory.didChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.preferredContentSizeCategory = UIApplication.shared.preferredContentSizeCategory
            }
        }

        NotificationCenter.default.addObserver(
            forName: UIAccessibility.darkerSystemColorsStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isHighContrastEnabled = UIAccessibility.isDarkerSystemColorsEnabled
            }
        }
    }
    
    // MARK: - Color Contrast Validation
    
    func validateColorContrast(foreground: Color, background: Color) -> ContrastValidation {
        let foregroundUIColor = UIColor(foreground)
        let backgroundUIColor = UIColor(background)
        
        let ratio = calculateContrastRatio(foreground: foregroundUIColor, background: backgroundUIColor)
        
        return ContrastValidation(
            ratio: ratio,
            passesAA: ratio >= minimumContrastRatio,
            passesAAA: ratio >= enhancedContrastRatio,
            recommendation: getContrastRecommendation(ratio: ratio)
        )
    }
    
    private func calculateContrastRatio(foreground: UIColor, background: UIColor) -> Double {
        let foregroundLuminance = calculateLuminance(color: foreground)
        let backgroundLuminance = calculateLuminance(color: background)
        
        let lighter = max(foregroundLuminance, backgroundLuminance)
        let darker = min(foregroundLuminance, backgroundLuminance)
        
        return (lighter + 0.05) / (darker + 0.05)
    }
    
    private func calculateLuminance(color: UIColor) -> Double {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        let sRGB = [red, green, blue].map { component in
            let c = Double(component)
            return c <= 0.03928 ? c / 12.92 : pow((c + 0.055) / 1.055, 2.4)
        }
        
        return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2]
    }
    
    private func getContrastRecommendation(ratio: Double) -> String {
        if ratio >= enhancedContrastRatio {
            return "Excellent contrast (AAA)"
        } else if ratio >= minimumContrastRatio {
            return "Good contrast (AA)"
        } else {
            return "Insufficient contrast - needs improvement"
        }
    }
    
    // MARK: - Dynamic Type Support
    
    func scaledFont(size: CGFloat, weight: Font.Weight = .regular, design: Font.Design = .default) -> Font {
        return Font.system(size: size, weight: weight, design: design)
    }
    
    func accessibleFont(style: Font.TextStyle, weight: Font.Weight = .regular) -> Font {
        return Font.system(style, design: .default, weight: weight)
    }
    
    // MARK: - VoiceOver Support
    
    func createAccessibilityLabel(for element: AccessibleElement) -> String {
        var components: [String] = []
        
        if let title = element.title {
            components.append(title)
        }
        
        if let value = element.value {
            components.append("Value: \(value)")
        }
        
        if let status = element.status {
            components.append("Status: \(status)")
        }
        
        return components.joined(separator: ", ")
    }
    
    func createAccessibilityHint(for action: AccessibilityAction) -> String {
        switch action {
        case .tap:
            return "Double tap to activate"
        case .swipe:
            return "Swipe to navigate"
        case .longPress:
            return "Double tap and hold to activate"
        case .custom(let hint):
            return hint
        }
    }
    
    // MARK: - Motion Reduction
    
    func shouldReduceMotion() -> Bool {
        return isReduceMotionEnabled
    }
    
    func accessibleAnimation<V: Equatable>(_ animation: Animation, value: V) -> Animation? {
        return shouldReduceMotion() ? nil : animation
    }
    
    // MARK: - High Contrast Support
    
    func adjustColorForHighContrast(_ color: Color) -> Color {
        guard isHighContrastEnabled else { return color }
        
        // Increase contrast for high contrast mode
        let uiColor = UIColor(color)
        var hue: CGFloat = 0
        var saturation: CGFloat = 0
        var brightness: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha)
        
        // Increase saturation and adjust brightness for better contrast
        let adjustedSaturation = min(saturation * 1.2, 1.0)
        let adjustedBrightness = brightness < 0.5 ? brightness * 0.8 : min(brightness * 1.2, 1.0)
        
        return Color(UIColor(hue: hue, saturation: adjustedSaturation, brightness: adjustedBrightness, alpha: alpha))
    }
    
    // MARK: - Accessibility Validation
    
    func validateAccessibility(for view: AccessibleView) -> AccessibilityValidation {
        var issues: [AccessibilityIssue] = []
        
        // Check color contrast
        if let foreground = view.foregroundColor, let background = view.backgroundColor {
            let contrast = validateColorContrast(foreground: foreground, background: background)
            if !contrast.passesAA {
                issues.append(.insufficientContrast(contrast.ratio))
            }
        }
        
        // Check accessibility labels
        if view.accessibilityLabel?.isEmpty ?? true {
            issues.append(.missingAccessibilityLabel)
        }
        
        // Check touch target size
        if let size = view.touchTargetSize, size.width < 44 || size.height < 44 {
            issues.append(.touchTargetTooSmall(size))
        }
        
        return AccessibilityValidation(
            isValid: issues.isEmpty,
            issues: issues,
            score: calculateAccessibilityScore(issues: issues)
        )
    }
    
    private func calculateAccessibilityScore(issues: [AccessibilityIssue]) -> Double {
        let maxScore = 100.0
        let deductionPerIssue = 20.0
        
        return max(0, maxScore - (Double(issues.count) * deductionPerIssue))
    }
}

// MARK: - Supporting Types

struct ContrastValidation {
    let ratio: Double
    let passesAA: Bool
    let passesAAA: Bool
    let recommendation: String
}

struct AccessibleElement {
    let title: String?
    let value: String?
    let status: String?
}

enum AccessibilityAction {
    case tap
    case swipe
    case longPress
    case custom(String)
}

struct AccessibleView {
    let accessibilityLabel: String?
    let foregroundColor: Color?
    let backgroundColor: Color?
    let touchTargetSize: CGSize?
}

enum AccessibilityIssue {
    case insufficientContrast(Double)
    case missingAccessibilityLabel
    case touchTargetTooSmall(CGSize)
    case missingAccessibilityHint
    case improperHeadingStructure
}

struct AccessibilityValidation {
    let isValid: Bool
    let issues: [AccessibilityIssue]
    let score: Double
}

// MARK: - SwiftUI Extensions

extension View {
    func accessibleTouchTarget(minSize: CGSize = CGSize(width: 44, height: 44)) -> some View {
        self.frame(minWidth: minSize.width, minHeight: minSize.height)
    }
    
    func accessibleContrast() -> some View {
        self.modifier(AccessibleContrastModifier())
    }
    
    func accessibleMotion() -> some View {
        self.modifier(AccessibleMotionModifier())
    }
    
    func accessibleFont(_ style: Font.TextStyle, weight: Font.Weight = .regular) -> some View {
        self.font(AccessibilityService.shared.accessibleFont(style: style, weight: weight))
    }
}

struct AccessibleContrastModifier: ViewModifier {
    @StateObject private var accessibilityService = AccessibilityService.shared
    
    func body(content: Content) -> some View {
        content
            .foregroundColor(accessibilityService.adjustColorForHighContrast(.primary))
    }
}

struct AccessibleMotionModifier: ViewModifier {
    @StateObject private var accessibilityService = AccessibilityService.shared
    
    func body(content: Content) -> some View {
        content
            .animation(accessibilityService.shouldReduceMotion() ? nil : .default, value: accessibilityService.isReduceMotionEnabled)
    }
}
