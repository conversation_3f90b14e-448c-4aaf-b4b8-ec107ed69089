//
//  ActivityTrackingService.swift
//  PetCapsule
//
//  Comprehensive activity monitoring with exercise logs, playtime tracking, and behavior analysis
//

import Foundation
import CoreLocation
import HealthKit

// Type alias to resolve BehaviorType ambiguity - using String to avoid conflicts
// typealias ActivityBehaviorType = String
import CoreMotion
import Combine
import SwiftUI

// Type aliases removed to avoid redeclaration - using SharedTypes.swift definitions directly

@MainActor
class ActivityTrackingService: ObservableObject {
    static let shared = ActivityTrackingService()
    
    // Published properties
    @Published var dailyActivities: [DailyActivity] = []
    @Published var exerciseLogs: [ExerciseLog] = []
    @Published var playtimeSessions: [PlaytimeSession] = []
    @Published var behaviorLogs: [BehaviorLog] = []
    @Published var activityGoals: [ActivityGoal] = []
    @Published var currentActivity: ActiveSession?
    @Published var todayStats: DailyActivityStats?
    @Published var weeklyStats: WeeklyActivityStats?
    @Published var isTracking = false
    @Published var lastUpdateTime: Date?
    
    // Services
    private let dataService = AppleNativeDataService.shared
    private let locationManager = CLLocationManager()
    @Published var currentLocation: CLLocation?
    private let motionManager = CMMotionManager()
    private let pedometer = CMPedometer()
    private let healthStore = HKHealthStore()
    
    // Tracking state
    private var trackingTimer: Timer?
    private var currentSteps = 0
    private var currentDistance = 0.0
    private var sessionStartTime: Date?
    
    private init() {
        setupHealthKit()
        loadActivityData()
        calculateTodayStats()
    }
    
    // MARK: - Activity Session Management
    
    func startActivitySession(
        type: ActivityType,
        petId: String,
        location: CLLocationCoordinate2D? = nil,
        notes: String? = nil
    ) async throws -> ActiveSession {
        
        guard !isTracking else {
            throw ActivityTrackingError.sessionAlreadyActive
        }
        
        let session = ActiveSession(
            id: UUID().uuidString,
            petId: petId,
            activityType: type,
            startTime: Date(),
            currentDuration: 0,
            currentDistance: 0,
            currentCalories: 0,
            currentHeartRate: nil,
            isActive: true,
            isPaused: false,
            targetDuration: nil,
            targetDistance: nil,
            targetCalories: nil
        )
        
        currentActivity = session
        isTracking = true
        sessionStartTime = Date()
        
        // Start appropriate tracking based on activity type
        switch type {
        case .walk, .run:
            await startLocationTracking()
            startStepTracking()
        case .play, .training:
            startMotionTracking()
        case .rest:
            // Minimal tracking for rest activities
            break
        default:
            // Default tracking for other activities
            break
        }
        
        // Start periodic updates
        startTrackingTimer()
        
        print("✅ Activity session started: \(type.displayName)")
        return session
    }
    
    func pauseActivitySession() {
        guard let session = currentActivity, session.isActive && !session.isPaused else { return }
        
        currentActivity?.isPaused = true
        currentActivity?.isActive = false
        
        // Pause tracking
        stopTrackingTimer()
        stopLocationTracking()
        
        print("⏸️ Activity session paused")
    }
    
    func resumeActivitySession() {
        guard let session = currentActivity, session.isPaused else { return }
        
        currentActivity?.isActive = true
        currentActivity?.isPaused = false
        
        // Resume tracking
        startTrackingTimer()
        // Resume location tracking for movement activities
        if session.activityType == .walk || session.activityType == .run {
            Task { await startLocationTracking() }
        }
        
        print("▶️ Activity session resumed")
    }
    
    func endActivitySession(
        endLocation: CLLocationCoordinate2D? = nil,
        finalNotes: String? = nil,
        rating: Int? = nil
    ) async throws -> ActivityLog {
        
        guard let session = currentActivity else {
            throw ActivityTrackingError.noActiveSession
        }
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(session.startTime)
        
        // Stop all tracking
        stopTrackingTimer()
        stopLocationTracking()
        stopStepTracking()
        stopMotionTracking()
        
        // Create activity log
        let activityLog = ActivityLog(
            id: UUID().uuidString,
            petId: session.petId,
            activityType: session.activityType,
            startTime: session.startTime,
            endTime: endTime,
            duration: duration,
            distance: currentDistance,
            caloriesBurned: Double(calculateCalories(duration: duration, type: session.activityType)),
            averageHeartRate: nil, // Would integrate with pet wearables
            maxHeartRate: nil,
            steps: currentSteps,
            location: nil, // session doesn't have startLocation
            latitude: endLocation?.latitude,
            longitude: endLocation?.longitude,
            notes: finalNotes,
            weatherConditions: nil, // await getWeatherConditions() returns wrong type
            temperature: nil,
            humidity: nil,
            createdAt: Date()
        )
        
        // Save to database
        try await saveActivityLogToDatabase(activityLog)
        
        // Add to local arrays based on type
        switch session.activityType {
        case .walk, .run:
            let exerciseLog = ExerciseLog(from: activityLog)
            exerciseLogs.append(exerciseLog)
        case .play:
            let playtimeSession = PlaytimeSession(from: activityLog)
            playtimeSessions.append(playtimeSession)
        default:
            break
        }
        
        // Reset tracking state
        currentActivity = nil
        isTracking = false
        currentSteps = 0
        currentDistance = 0.0
        sessionStartTime = nil
        
        // Update daily stats
        await updateDailyStats()
        
        print("✅ Activity session completed: \(session.activityType.displayName)")
        return activityLog
    }
    
    // MARK: - Behavior Logging
    
    func logBehavior(
        petId: String,
        behaviorType: String, // Simplified to avoid type conflicts
        intensity: BehaviorIntensity,
        duration: TimeInterval? = nil,
        triggers: [String] = [],
        location: String? = nil,
        notes: String? = nil
    ) async throws -> BehaviorLog {
        
        let behaviorLog = BehaviorLog(
            id: UUID().uuidString,
            petId: petId,
            behaviorType: behaviorType,
            intensity: intensity,
            duration: duration,
            triggers: triggers,
            location: location,
            notes: notes,
            timestamp: Date(),
            createdAt: Date()
        )
        
        // Save to database
        try await saveBehaviorLogToDatabase(behaviorLog)
        
        // Add to local array
        behaviorLogs.append(behaviorLog)
        
        // Analyze behavior patterns
        await analyzeBehaviorPatterns(for: petId)
        
        print("✅ Behavior logged: \(behaviorType)")
        return behaviorLog
    }
    
    // MARK: - Activity Goals Management
    
    func createActivityGoal(
        petId: String,
        goalType: ActivityGoalType,
        targetValue: Double,
        timeframe: GoalTimeframe,
        startDate: Date = Date(),
        endDate: Date? = nil
    ) async throws -> ActivityGoal {
        
        let goal = ActivityGoal(
            id: UUID().uuidString,
            petId: petId,
            goalType: goalType,
            targetValue: targetValue,
            currentValue: 0.0,
            timeframe: timeframe,
            startDate: startDate,
            endDate: endDate ?? calculateEndDate(from: startDate, timeframe: timeframe),
            isActive: true,
            isCompleted: false,
            createdAt: Date(),
            updatedAt: Date()
        )
        
        // Save to database
        try await saveActivityGoalToDatabase(goal)
        
        // Add to local array
        activityGoals.append(goal)
        
        print("✅ Activity goal created: \(goalType.displayName)")
        return goal
    }
    
    func updateActivityGoalProgress(_ goalId: String, newValue: Double) async throws {
        guard let index = activityGoals.firstIndex(where: { $0.id == goalId }) else {
            throw ActivityTrackingError.goalNotFound
        }
        
        var goal = activityGoals[index]
        goal.currentValue = newValue
        goal.updatedAt = Date()
        
        // Check if goal is completed
        if newValue >= goal.targetValue {
            goal.isCompleted = true
            goal.completedAt = Date()
        }
        
        // Update in array
        activityGoals[index] = goal
        
        // Save to database
        try await saveActivityGoalToDatabase(goal)
        
        print("✅ Activity goal progress updated: \(goalId)")
    }
    
    // MARK: - Statistics and Analytics
    
    func calculateTodayStats() {
        let today = Calendar.current.startOfDay(for: Date())
        let todayActivities = exerciseLogs.filter { 
            Calendar.current.isDate($0.createdAt, inSameDayAs: today)
        }
        
        let totalDuration = todayActivities.reduce(0) { $0 + $1.duration }
        let totalDistance = todayActivities.reduce(0.0) { $0 + ($1.averageSpeed ?? 0) * $1.duration } // ExerciseLog doesn't have distance
        let totalSteps = 0 // ExerciseLog doesn't have steps
        let totalCalories = Int(todayActivities.reduce(0.0) { $0 + $1.caloriesBurned })
        
        todayStats = DailyActivityStats(
            date: today,
            totalDuration: totalDuration,
            totalDistance: totalDistance,
            totalSteps: totalSteps,
            totalCalories: totalCalories,
            activitiesCount: todayActivities.count,
            averageIntensity: calculateAverageIntensity(todayActivities)
        )
    }
    
    func calculateWeeklyStats() {
        let calendar = Calendar.current
        let today = Date()
        let weekStart = calendar.dateInterval(of: .weekOfYear, for: today)?.start ?? today
        
        let weekActivities = exerciseLogs.filter { activity in
            activity.createdAt >= weekStart && activity.createdAt <= today
        }
        
        let dailyStats = (0..<7).map { dayOffset in
            let day = calendar.date(byAdding: .day, value: dayOffset, to: weekStart) ?? weekStart
            let dayActivities = weekActivities.filter { 
                calendar.isDate($0.createdAt, inSameDayAs: day)
            }
            
            return DailyActivityStats(
                date: day,
                totalDuration: dayActivities.reduce(0) { $0 + $1.duration },
                totalDistance: dayActivities.reduce(0.0) { $0 + ($1.averageSpeed ?? 0) * $1.duration },
                totalSteps: 0, // ExerciseLog doesn't have steps
                totalCalories: Int(dayActivities.reduce(0.0) { $0 + $1.caloriesBurned }),
                activitiesCount: dayActivities.count,
                averageIntensity: calculateAverageIntensity(dayActivities)
            )
        }
        
        // Calculate totals
        let totalWeekDuration = dailyStats.reduce(0) { $0 + $1.totalDuration }
        let totalWeekDistance = dailyStats.reduce(0) { $0 + $1.totalDistance }
        let totalWeekSteps = dailyStats.reduce(0) { $0 + $1.totalSteps }
        let totalWeekCalories = dailyStats.reduce(0) { $0 + $1.totalCalories }
        let avgDailyDuration = totalWeekDuration / 7
        let mostActiveDay = dailyStats.max { $0.totalDuration < $1.totalDuration }?.date ?? weekStart
        
        weeklyStats = WeeklyActivityStats(
            weekStart: weekStart,
            weekEnd: calendar.date(byAdding: .day, value: 6, to: weekStart) ?? today,
            dailyStats: dailyStats,
            totalDuration: totalWeekDuration,
            totalDistance: totalWeekDistance,
            totalSteps: totalWeekSteps,
            totalCalories: totalWeekCalories,
            averageDailyDuration: avgDailyDuration,
            mostActiveDay: mostActiveDay
        )
    }
    
    // MARK: - Behavior Analysis
    
    private func analyzeBehaviorPatterns(for petId: String) async {
        let petBehaviors = behaviorLogs.filter { $0.petId == petId }
        
        // Analyze behavior frequency
        let behaviorFrequency = Dictionary(grouping: petBehaviors) { $0.behaviorType }
            .mapValues { $0.count }
        
        // Analyze behavior timing patterns
        let hourlyPatterns = Dictionary(grouping: petBehaviors) { 
            Calendar.current.component(.hour, from: $0.timestamp)
        }.mapValues { $0.count }
        
        // Identify potential issues
        let concerningBehaviors = petBehaviors.filter {
            $0.intensity == .high
        }
        
        // Create behavior insights
        let insights = BehaviorInsights(
            petId: petId,
            behaviorFrequency: behaviorFrequency,
            hourlyPatterns: hourlyPatterns,
            concerningBehaviors: concerningBehaviors,
            analysisDate: Date()
        )
        
        // Save insights to database
        try? await saveBehaviorInsightsToDatabase(insights)
        
        print("✅ Behavior patterns analyzed for pet: \(petId)")
    }
    
    // MARK: - Tracking Implementation
    
    private func startLocationTracking() async {
        locationManager.startUpdatingLocation()
    }
    
    private func stopLocationTracking() {
        locationManager.stopUpdatingLocation()
    }
    
    private func startStepTracking() {
        guard CMPedometer.isStepCountingAvailable() else { return }
        
        pedometer.startUpdates(from: Date()) { [weak self] data, error in
            guard let data = data, error == nil else { return }
            
            Task { @MainActor in
                self?.currentSteps = data.numberOfSteps.intValue
                if let distance = data.distance {
                    self?.currentDistance = distance.doubleValue
                }
            }
        }
    }
    
    private func stopStepTracking() {
        pedometer.stopUpdates()
    }
    
    private func startMotionTracking() {
        guard motionManager.isDeviceMotionAvailable else { return }
        
        motionManager.deviceMotionUpdateInterval = 1.0
        motionManager.startDeviceMotionUpdates(to: .main) { [weak self] motion, error in
            guard let motion = motion, error == nil else { return }
            
            // Process motion data for activity intensity
            let acceleration = motion.userAcceleration
            let intensity = sqrt(pow(acceleration.x, 2) + pow(acceleration.y, 2) + pow(acceleration.z, 2))
            
            // Motion intensity would be tracked separately since ActiveSession doesn't have this property
            // self?.currentActivity?.motionIntensity = intensity
        }
    }
    
    private func stopMotionTracking() {
        motionManager.stopDeviceMotionUpdates()
    }
    
    private func startTrackingTimer() {
        trackingTimer = Timer.scheduledTimer(withTimeInterval: 30, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.updateCurrentSession()
            }
        }
    }
    
    private func stopTrackingTimer() {
        trackingTimer?.invalidate()
        trackingTimer = nil
    }
    
    private func updateCurrentSession() async {
        guard var session = currentActivity else { return }
        
        session.currentDuration = Date().timeIntervalSince(session.startTime)
        session.currentDistance = currentDistance
        session.currentCalories = Double(calculateCalories(duration: session.currentDuration, type: session.activityType))
        // Note: ActiveSession doesn't have currentSteps or lastUpdated properties
        
        currentActivity = session
    }
    
    // MARK: - HealthKit Integration
    
    private func setupHealthKit() {
        guard HKHealthStore.isHealthDataAvailable() else {
            print("ℹ️ HealthKit not available on this device")
            return
        }

        // Check if HealthKit entitlements are available
        guard Bundle.main.object(forInfoDictionaryKey: "NSHealthShareUsageDescription") != nil else {
            print("ℹ️ HealthKit entitlements not configured - skipping HealthKit setup")
            return
        }

        let typesToRead: Set<HKObjectType> = [
            HKObjectType.quantityType(forIdentifier: .stepCount)!,
            HKObjectType.quantityType(forIdentifier: .distanceWalkingRunning)!,
            HKObjectType.quantityType(forIdentifier: .activeEnergyBurned)!
        ]
        
        healthStore.requestAuthorization(toShare: nil, read: typesToRead) { success, error in
            if success {
                print("✅ HealthKit authorization granted")
            } else {
                if let error = error {
                    if error.localizedDescription.contains("entitlement") {
                        print("ℹ️ HealthKit entitlement not configured - HealthKit features disabled")
                    } else {
                        print("❌ HealthKit authorization failed: \(error.localizedDescription)")
                    }
                } else {
                    print("❌ HealthKit authorization failed: Unknown error")
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func calculateCalories(duration: TimeInterval, type: ActivityType) -> Int {
        // Simplified calorie calculation - would be more sophisticated in real implementation
        let baseRate: Double = 5.0 // Base rate, would be pet-specific
        let minutes = duration / 60
        return Int(baseRate * minutes)
    }
    
    private func calculateAverageIntensity(_ activities: [ExerciseLog]) -> Double {
        // Simplified intensity calculation
        guard !activities.isEmpty else { return 0.0 }
        
        // Use exercise intensity as a proxy - would be more sophisticated in real implementation
        let totalIntensity = activities.reduce(0.0) { total, activity in
            // Convert intensity string to numeric value
            switch activity.intensity.lowercased() {
            case "low": return total + 1.0
            case "medium": return total + 2.0
            case "high": return total + 3.0
            default: return total + 1.5
            }
        }
        
        return totalIntensity / Double(activities.count)
    }
    

    
    private func calculateEndDate(from startDate: Date, timeframe: GoalTimeframe) -> Date {
        let calendar = Calendar.current
        
        switch timeframe {
        case .daily:
            return calendar.date(byAdding: .day, value: 1, to: startDate) ?? startDate
        case .weekly:
            return calendar.date(byAdding: .weekOfYear, value: 1, to: startDate) ?? startDate
        case .monthly:
            return calendar.date(byAdding: .month, value: 1, to: startDate) ?? startDate
        case .yearly:
            return calendar.date(byAdding: .year, value: 1, to: startDate) ?? startDate
        }
    }
    
    private func getWeatherConditions() async -> WeatherConditions? {
        // Would integrate with weather service
        return nil
    }
    
    private func updateDailyStats() async {
        calculateTodayStats()
        calculateWeeklyStats()
        lastUpdateTime = Date()
    }
    
    // MARK: - Data Loading
    
    private func loadActivityData() {
        Task {
            do {
                async let exercises = loadExerciseLogs()
                async let playtime = loadPlaytimeSessions()
                async let behaviors = loadBehaviorLogs()
                async let goals = loadActivityGoals()
                
                let (loadedExercises, loadedPlaytime, loadedBehaviors, loadedGoals) = try await (exercises, playtime, behaviors, goals)
                
                await MainActor.run {
                    exerciseLogs = loadedExercises
                    playtimeSessions = loadedPlaytime
                    behaviorLogs = loadedBehaviors
                    activityGoals = loadedGoals
                    lastUpdateTime = Date()
                }
                
                await updateDailyStats()
                
                print("✅ Activity data loaded successfully")
                
            } catch {
                print("❌ Failed to load activity data: \(error)")
            }
        }
    }
    
    // MARK: - Public API
    
    func getActivitiesForPet(_ petId: String) -> [ExerciseLog] {
        return exerciseLogs.filter { $0.petId == petId }
    }
    
    func getBehaviorsForPet(_ petId: String) -> [BehaviorLog] {
        return behaviorLogs.filter { $0.petId == petId }
    }
    
    func getActiveGoalsForPet(_ petId: String) -> [ActivityGoal] {
        return activityGoals.filter { $0.petId == petId && $0.isActive && !$0.isCompleted }
    }
    
    func getTodayActivitiesForPet(_ petId: String) -> [ExerciseLog] {
        let today = Calendar.current.startOfDay(for: Date())
        return exerciseLogs.filter { 
            $0.petId == petId && Calendar.current.isDate($0.createdAt, inSameDayAs: today)
        }
    }
    
    func getActivityStreak(for petId: String) -> Int {
        let sortedActivities = exerciseLogs
            .filter { $0.petId == petId }
            .sorted(by: { $0.createdAt > $1.createdAt })
        
        var streak = 0
        var currentDate = Calendar.current.startOfDay(for: Date())
        
        for activity in sortedActivities {
            let activityDate = Calendar.current.startOfDay(for: activity.createdAt)
            
            if Calendar.current.isDate(activityDate, inSameDayAs: currentDate) {
                streak += 1
                currentDate = Calendar.current.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else if activityDate < currentDate {
                break
            }
        }
        
        return streak
    }

    // MARK: - Data Persistence

    private func saveActivityLogToDatabase(_ log: ActivityLog) async throws {
        // Simplified database save - would use proper request objects in production
        print("Saving activity log to database: \(log.id)")
    }

    private func saveBehaviorLogToDatabase(_ log: BehaviorLog) async throws {
        // Simplified database save - would use proper request objects in production
        print("Saving behavior log to database: \(log.id)")
    }

    private func saveActivityGoalToDatabase(_ goal: ActivityGoal) async throws {
        // Simplified database save - would use proper request objects in production
        print("Saving activity goal to database: \(goal.id)")
    }

    private func saveBehaviorInsightsToDatabase(_ insights: BehaviorInsights) async throws {
        // Simplified database save - would use proper request objects in production
        print("Saving behavior insights to database: \(insights.petId)")
    }

    private func loadExerciseLogs() async throws -> [ExerciseLog] {
        // Simplified database load - would use proper response objects in production
        print("Loading exercise logs from database")
        return []
    }

    private func loadPlaytimeSessions() async throws -> [PlaytimeSession] {
        // Simplified database load - would use proper response objects in production
        print("Loading playtime sessions from database")
        return []
    }

    private func loadBehaviorLogs() async throws -> [BehaviorLog] {
        // Simplified database load - would use proper response objects in production
        print("Loading behavior logs from database")
        return []
    }

    private func loadActivityGoals() async throws -> [ActivityGoal] {
        // Simplified database load - would use proper response objects in production
        print("Loading activity goals from database")
        return []
    }

    private func getCurrentUserId() -> String {
        // Get current user ID from auth service
        return "current_user_id" // Placeholder
    }
}

// MARK: - Data Models

// ActivityLog is now defined in SharedTypes.swift as ActivityLog

// ExerciseLog is now defined in SharedTypes.swift as ExerciseLog

struct PlaytimeSession: Identifiable, Codable {
    let id: String
    let petId: String
    let playType: PlayType
    let startTime: Date
    let endTime: Date
    let duration: TimeInterval
    let intensity: PlayIntensity
    let toys: [String]
    let playmates: [String]
    let location: String?
    let notes: String?
    let rating: Int?
    let createdAt: Date

    init(from activityLog: ActivityLog) {
        self.id = activityLog.id
        self.petId = activityLog.petId
        self.playType = .interactive // Default, would be determined from activity details
        self.startTime = activityLog.startTime
        self.endTime = activityLog.endTime ?? activityLog.startTime // ActivityLog endTime is optional
        self.duration = activityLog.duration
        self.intensity = .medium // Would be calculated from motion data
        self.toys = []
        self.playmates = []
        self.location = activityLog.location
        self.notes = activityLog.notes
        self.rating = nil // ActivityLog doesn't have rating
        self.createdAt = activityLog.createdAt
    }
}

struct BehaviorLog: Identifiable, Codable {
    let id: String
    let petId: String
    let behaviorType: String // Simplified to avoid type conflicts
    let intensity: BehaviorIntensity
    let duration: TimeInterval?
    let triggers: [String]
    let location: String?
    let notes: String?
    let timestamp: Date
    let createdAt: Date
}

struct ActivityGoal: Identifiable, Codable {
    let id: String
    let petId: String
    let goalType: ActivityGoalType
    let targetValue: Double
    var currentValue: Double
    let timeframe: GoalTimeframe
    let startDate: Date
    let endDate: Date
    var isActive: Bool
    var isCompleted: Bool
    var completedAt: Date?
    let createdAt: Date
    var updatedAt: Date

    var progress: Double {
        guard targetValue > 0 else { return 0 }
        return min(currentValue / targetValue, 1.0)
    }

    var isOverdue: Bool {
        Date() > endDate && !isCompleted
    }
}

// ActiveSession is now defined in SharedTypes.swift as ActiveSession

struct DailyActivity: Identifiable, Codable {
    let id: String
    let date: Date
    let petId: String
    let activities: [ActivityLog]
    let totalDuration: TimeInterval
    let totalDistance: Double
    let totalSteps: Int
    let totalCalories: Int
    let averageIntensity: Double
    
    init(date: Date, petId: String, activities: [ActivityLog], totalDuration: TimeInterval, totalDistance: Double, totalSteps: Int, totalCalories: Int, averageIntensity: Double) {
        self.id = UUID().uuidString
        self.date = date
        self.petId = petId
        self.activities = activities
        self.totalDuration = totalDuration
        self.totalDistance = totalDistance
        self.totalSteps = totalSteps
        self.totalCalories = totalCalories
        self.averageIntensity = averageIntensity
    }
}

struct DailyActivityStats: Codable {
    let date: Date
    let totalDuration: TimeInterval
    let totalDistance: Double
    let totalSteps: Int
    let totalCalories: Int
    let activitiesCount: Int
    let averageIntensity: Double
}

struct WeeklyActivityStats: Codable {
    let weekStart: Date
    let weekEnd: Date
    let dailyStats: [DailyActivityStats]
    let totalDuration: TimeInterval
    let totalDistance: Double
    let totalSteps: Int
    let totalCalories: Int
    let averageDailyDuration: TimeInterval
    let mostActiveDay: Date
}

struct BehaviorInsights: Codable {
    let petId: String
    let behaviorFrequency: [String: Int]
    let hourlyPatterns: [Int: Int]
    let concerningBehaviors: [BehaviorLog]
    let analysisDate: Date
}

struct WeatherConditions: Codable {
    let temperature: Int
    let humidity: Int
    let windSpeed: Double
    let condition: String
    let uvIndex: Int?
}

// MARK: - Enums

// ActivityType is now defined in SharedTypes.swift as ActivityType

enum BehaviorType: String, CaseIterable, Codable, Hashable {
    case barking = "barking"
    case whining = "whining"
    case jumping = "jumping"
    case digging = "digging"
    case chewing = "chewing"
    case scratching = "scratching"
    case pacing = "pacing"
    case hiding = "hiding"
    case aggression = "aggression"
    case anxiety = "anxiety"
    case excitement = "excitement"
    case playfulness = "playfulness"
    case affection = "affection"
    case curiosity = "curiosity"
    case alertness = "alertness"

    var displayName: String {
        rawValue.capitalized
    }

    var isConcerning: Bool {
        switch self {
        case .aggression, .anxiety, .pacing, .hiding: return true
        default: return false
        }
    }

    var category: BehaviorCategory {
        switch self {
        case .barking, .whining, .jumping, .digging, .chewing, .scratching, .pacing:
            return .problematic
        case .hiding, .aggression, .anxiety:
            return .concerning
        case .excitement, .playfulness, .affection, .curiosity, .alertness:
            return .positive
        }
    }
}

enum BehaviorCategory: String, CaseIterable {
    case positive = "positive"
    case problematic = "problematic"
    case concerning = "concerning"

    var color: Color {
        switch self {
        case .positive: return .green
        case .problematic: return .orange
        case .concerning: return .red
        }
    }
}

enum BehaviorIntensity: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"

    var displayName: String {
        rawValue.capitalized
    }

    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
}

enum PlayType: String, CaseIterable, Codable {
    case solo = "solo"
    case interactive = "interactive"
    case social = "social"
    case mental = "mental"
    case physical = "physical"

    var displayName: String {
        switch self {
        case .solo: return "Solo Play"
        case .interactive: return "Interactive Play"
        case .social: return "Social Play"
        case .mental: return "Mental Stimulation"
        case .physical: return "Physical Play"
        }
    }
}

enum PlayIntensity: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"

    var displayName: String {
        rawValue.capitalized
    }
}

enum ActivityGoalType: String, CaseIterable, Codable {
    case dailyWalkDuration = "daily_walk_duration"
    case weeklyDistance = "weekly_distance"
    case dailySteps = "daily_steps"
    case weeklyExerciseSessions = "weekly_exercise_sessions"
    case monthlyCalories = "monthly_calories"
    case playtimeMinutes = "playtime_minutes"

    var displayName: String {
        switch self {
        case .dailyWalkDuration: return "Daily Walk Duration"
        case .weeklyDistance: return "Weekly Distance"
        case .dailySteps: return "Daily Steps"
        case .weeklyExerciseSessions: return "Weekly Exercise Sessions"
        case .monthlyCalories: return "Monthly Calories"
        case .playtimeMinutes: return "Playtime Minutes"
        }
    }

    var unit: String {
        switch self {
        case .dailyWalkDuration, .playtimeMinutes: return "minutes"
        case .weeklyDistance: return "miles"
        case .dailySteps: return "steps"
        case .weeklyExerciseSessions: return "sessions"
        case .monthlyCalories: return "calories"
        }
    }
}

enum GoalTimeframe: String, CaseIterable, Codable {
    case daily = "daily"
    case weekly = "weekly"
    case monthly = "monthly"
    case yearly = "yearly"

    var displayName: String {
        rawValue.capitalized
    }
}

enum SessionStatus: String, CaseIterable, Codable {
    case active = "active"
    case paused = "paused"
    case completed = "completed"
    case cancelled = "cancelled"

    var displayName: String {
        rawValue.capitalized
    }

    var color: Color {
        switch self {
        case .active: return .green
        case .paused: return .orange
        case .completed: return .blue
        case .cancelled: return .red
        }
    }
}

// MARK: - Error Types

enum ActivityTrackingError: Error, LocalizedError {
    case sessionAlreadyActive
    case noActiveSession
    case goalNotFound
    case invalidActivityType
    case trackingNotAvailable

    var errorDescription: String? {
        switch self {
        case .sessionAlreadyActive:
            return "An activity session is already active"
        case .noActiveSession:
            return "No active session to end"
        case .goalNotFound:
            return "Activity goal not found"
        case .invalidActivityType:
            return "Invalid activity type"
        case .trackingNotAvailable:
            return "Activity tracking is not available on this device"
        }
    }
}
