//
//  AppDelegate.swift
//  PetCapsule
//
//  App Delegate for Apple Push Notifications integration
//

import UIKit
import UserNotifications

class AppDelegate: NSObject, UIApplicationDelegate {
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Register for remote notifications
        UNUserNotificationCenter.current().delegate = self
        
        // Request authorization
        Task { @MainActor in
            await requestNotificationPermissions()
        }
        
        return true
    }
    
    // MARK: - APNs Registration
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        // Pass device token to notification service
        Task { @MainActor in
            EnvironmentalNotificationService.shared.setDeviceToken(deviceToken)
        }
    }
    
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("❌ Failed to register for remote notifications: \(error)")
    }
    
    // MARK: - Background App Refresh
    
    func application(_ application: UIApplication, performFetchWithCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        // Handle background notification processing
        Task {
            await handleBackgroundFetch()
            completionHandler(.newData)
        }
    }
    
    // MARK: - Private Methods
    
    @MainActor
    private func requestNotificationPermissions() async {
        let center = UNUserNotificationCenter.current()
        
        do {
            let granted = try await center.requestAuthorization(options: [
                .alert,
                .sound,
                .badge,
                .criticalAlert,
                .providesAppNotificationSettings
            ])
            
            if granted {
                await UIApplication.shared.registerForRemoteNotifications()
                print("✅ Notification permissions granted")
            } else {
                print("❌ Notification permissions denied")
            }
        } catch {
            print("❌ Error requesting notification permissions: \(error)")
        }
    }
    
    private func handleBackgroundFetch() async {
        // Update environmental monitoring in background
        await RealTimeWeatherService.shared.updateWeatherData()
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension AppDelegate: UNUserNotificationCenterDelegate {
    
    // Handle notifications when app is in foreground
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification banner even when app is active
        completionHandler([.banner, .sound, .badge])
    }
    
    // Handle notification taps
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        handleNotificationResponse(response, userInfo: userInfo)
        completionHandler()
    }
    
    // Handle notification settings button tap
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        openSettingsFor notification: UNNotification?
    ) {
        // Navigate to app's notification settings
        NotificationCenter.default.post(
            name: .navigateToNotificationSettings,
            object: nil
        )
    }
    
    // MARK: - Notification Response Handling
    
    private func handleNotificationResponse(_ response: UNNotificationResponse, userInfo: [AnyHashable: Any]) {
        switch response.actionIdentifier {
        case "VIEW_DETAILS":
            handleViewDetailsAction(userInfo: userInfo)
        case "DISMISS":
            handleDismissAction(userInfo: userInfo)
        case "SNOOZE":
            handleSnoozeAction(userInfo: userInfo)
        case "CALL_VET":
            handleCallVetAction(userInfo: userInfo)
        case "VIEW_FORECAST":
            handleViewForecastAction(userInfo: userInfo)
        case UNNotificationDefaultActionIdentifier:
            // User tapped the notification body
            handleDefaultAction(userInfo: userInfo)
        default:
            break
        }
    }
    
    private func handleViewDetailsAction(userInfo: [AnyHashable: Any]) {
        if let alertId = userInfo["alert_id"] as? String {
            NotificationCenter.default.post(name: .navigateToEnvironmentalAlerts, object: alertId)
        } else if let petId = userInfo["pet_id"] as? String {
            NotificationCenter.default.post(name: .navigateToPetHealth, object: petId)
        }
    }
    
    private func handleDismissAction(userInfo: [AnyHashable: Any]) {
        if let alertId = userInfo["alert_id"] as? String {
            Task {
                await EnvironmentalNotificationService.shared.markNotificationDismissed(alertId: alertId)
            }
        }
    }
    
    private func handleSnoozeAction(userInfo: [AnyHashable: Any]) {
        if let alertId = userInfo["alert_id"] as? String {
            Task {
                await EnvironmentalNotificationService.shared.scheduleSnoozeReminder(alertId: alertId)
            }
        }
    }
    
    private func handleCallVetAction(userInfo: [AnyHashable: Any]) {
        if let petId = userInfo["pet_id"] as? String {
            NotificationCenter.default.post(name: .initiateVetCall, object: petId)
        }
    }
    
    private func handleViewForecastAction(userInfo: [AnyHashable: Any]) {
        NotificationCenter.default.post(name: .navigateToWeatherForecast, object: nil)
    }
    
    private func handleDefaultAction(userInfo: [AnyHashable: Any]) {
        // Route to appropriate view based on notification type
        if let type = userInfo["type"] as? String {
            switch type {
            case "environmental_alert":
                NotificationCenter.default.post(name: .navigateToEnvironmentalAlerts, object: userInfo["alert_id"])
            case "health_reminder":
                NotificationCenter.default.post(name: .navigateToPetHealth, object: userInfo["pet_id"])
            case "walk_notification":
                NotificationCenter.default.post(name: .navigateToWalkPlanner, object: nil)
            case "community_event":
                NotificationCenter.default.post(name: .navigateToCommunityEvents, object: userInfo["event_id"])
            default:
                NotificationCenter.default.post(name: .navigateToNotifications, object: nil)
            }
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let navigateToNotificationSettings = Notification.Name("navigateToNotificationSettings")
    static let navigateToPetHealth = Notification.Name("navigateToPetHealth")
    static let navigateToWeatherForecast = Notification.Name("navigateToWeatherForecast")
    static let navigateToWalkPlanner = Notification.Name("navigateToWalkPlanner")
    static let navigateToCommunityEvents = Notification.Name("navigateToCommunityEvents")
    static let navigateToNotifications = Notification.Name("navigateToNotifications")
    static let initiateVetCall = Notification.Name("initiateVetCall")
} 