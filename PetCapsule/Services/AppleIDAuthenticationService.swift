import Foundation
import AuthenticationServices
import SwiftUI

// MARK: - Apple ID Authentication Service
@MainActor
class AppleIDAuthenticationService: NSObject, ObservableObject {
    static let shared = AppleIDAuthenticationService()
    
    // MARK: - Published Properties
    @Published var isAuthenticated: Bool = false
    @Published var currentUser: AppleUser?
    @Published var authenticationState: AuthenticationState = .unknown
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let appleDataService = AppleNativeDataService.shared
    
    // MARK: - Types
    enum AuthenticationState {
        case unknown
        case authenticated
        case unauthenticated
        case authenticating
        case error(String)
    }
    
    struct AppleUser {
        let userID: String
        let email: String?
        let fullName: PersonNameComponents?
        let authorizationCode: Data?
        let identityToken: Data?
        
        init(from authorization: ASAuthorization) {
            if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
                self.userID = appleIDCredential.user
                self.email = appleIDCredential.email
                self.fullName = appleIDCredential.fullName
                self.authorizationCode = appleIDCredential.authorizationCode
                self.identityToken = appleIDCredential.identityToken
            } else {
                self.userID = UUID().uuidString
                self.email = nil
                self.fullName = nil
                self.authorizationCode = nil
                self.identityToken = nil
            }
        }
        
        // Initializer for reconstructing from stored data
        init(userID: String, email: String?, fullName: PersonNameComponents?, authorizationCode: Data?, identityToken: Data?) {
            self.userID = userID
            self.email = email
            self.fullName = fullName
            self.authorizationCode = authorizationCode
            self.identityToken = identityToken
        }
    }
    
    // MARK: - Initialization
    private override init() {
        super.init()
        checkAuthenticationStatus()
    }
    
    // MARK: - Authentication Status
    func checkAuthenticationStatus() {
        guard let userID = getUserID() else {
            authenticationState = .unauthenticated
            isAuthenticated = false
            return
        }
        
        let provider = ASAuthorizationAppleIDProvider()
        provider.getCredentialState(forUserID: userID) { [weak self] credentialState, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                if let error = error {
                    self.authenticationState = .error(error.localizedDescription)
                    self.isAuthenticated = false
                    return
                }
                
                switch credentialState {
                case .authorized:
                    // User is authorized
                    self.authenticationState = .authenticated
                    self.isAuthenticated = true
                    self.loadStoredUser()
                    
                case .revoked:
                    // User's authorization has been revoked
                    self.handleSignOut()
                    
                case .notFound:
                    // No credential was found
                    self.authenticationState = .unauthenticated
                    self.isAuthenticated = false
                    
                @unknown default:
                    self.authenticationState = .unknown
                    self.isAuthenticated = false
                }
            }
        }
    }
    
    // MARK: - Sign In
    func signInWithApple() {
        authenticationState = .authenticating
        
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }
    
    // MARK: - Sign Out
    func signOut() async {
        // Clear local storage
        clearStoredUser()
        
        // Sign out from Apple data service
        await appleDataService.signOut()
        
        // Update state
        currentUser = nil
        isAuthenticated = false
        authenticationState = .unauthenticated
        errorMessage = nil
    }
    
    // MARK: - User Storage
    private func getUserID() -> String? {
        return UserDefaults.standard.string(forKey: "AppleUserID")
    }
    
    private func storeUser(_ user: AppleUser) {
        UserDefaults.standard.set(user.userID, forKey: "AppleUserID")
        
        if let email = user.email {
            UserDefaults.standard.set(email, forKey: "AppleUserEmail")
        }
        
        if let fullName = user.fullName {
            let formatter = PersonNameComponentsFormatter()
            let fullNameString = formatter.string(from: fullName)
            UserDefaults.standard.set(fullNameString, forKey: "AppleUserFullName")
        }
    }
    
    private func loadStoredUser() {
        guard let userID = getUserID() else { return }
        
        let email = UserDefaults.standard.string(forKey: "AppleUserEmail")
        let fullNameString = UserDefaults.standard.string(forKey: "AppleUserFullName")
        
        var fullName: PersonNameComponents?
        if let fullNameString = fullNameString {
            let formatter = PersonNameComponentsFormatter()
            fullName = formatter.personNameComponents(from: fullNameString)
        }
        
        // Create a mock authorization to reconstruct the user
        let reconstructedUser = AppleUser(
            userID: userID,
            email: email,
            fullName: fullName,
            authorizationCode: nil,
            identityToken: nil
        )
        
        currentUser = reconstructedUser
    }
    
    private func clearStoredUser() {
        UserDefaults.standard.removeObject(forKey: "AppleUserID")
        UserDefaults.standard.removeObject(forKey: "AppleUserEmail")
        UserDefaults.standard.removeObject(forKey: "AppleUserFullName")
    }
    
    // MARK: - Handle Authentication Success
    private func handleAuthenticationSuccess(_ user: AppleUser) async {
        // Store user locally
        storeUser(user)
        currentUser = user
        
        // Create or update user in Apple data service
        do {
            try await appleDataService.signInWithApple()
            
            // Update authentication state
            authenticationState = .authenticated
            isAuthenticated = true
            errorMessage = nil
            
        } catch {
            authenticationState = .error("Failed to sync with data service: \(error.localizedDescription)")
            errorMessage = error.localizedDescription
            isAuthenticated = false
        }
    }
    
    // MARK: - Handle Sign Out
    private func handleSignOut() {
        Task {
            await signOut()
        }
    }
}

// MARK: - ASAuthorizationControllerDelegate
extension AppleIDAuthenticationService: ASAuthorizationControllerDelegate {
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        
        let user = AppleUser(from: authorization)
        
        Task {
            await handleAuthenticationSuccess(user)
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        let errorMessage: String
        
        if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
                errorMessage = "Sign in was canceled"
            case .failed:
                errorMessage = "Sign in failed"
            case .invalidResponse:
                errorMessage = "Invalid response from Apple"
            case .notHandled:
                errorMessage = "Sign in not handled"
            case .unknown:
                errorMessage = "Unknown error occurred"
            @unknown default:
                errorMessage = "Unexpected error occurred"
            }
        } else {
            errorMessage = error.localizedDescription
        }
        
        DispatchQueue.main.async {
            self.authenticationState = .error(errorMessage)
            self.errorMessage = errorMessage
            self.isAuthenticated = false
        }
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding
extension AppleIDAuthenticationService: ASAuthorizationControllerPresentationContextProviding {
    
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return ASPresentationAnchor()
        }
        return window
    }
}

// MARK: - Convenience Extensions for UI
extension AppleIDAuthenticationService {
    
    var isSignedIn: Bool {
        return isAuthenticated && currentUser != nil
    }
    
    var userDisplayName: String {
        guard let currentUser = currentUser else { return "Unknown User" }
        
        if let fullName = currentUser.fullName {
            let formatter = PersonNameComponentsFormatter()
            formatter.style = .default
            return formatter.string(from: fullName)
        }
        
        if let email = currentUser.email {
            return email
        }
        
        return "Apple User"
    }
    
    var userEmail: String? {
        return currentUser?.email
    }
    
    var userID: String? {
        return currentUser?.userID
    }
}

// MARK: - Sign In with Apple Button View
struct SignInWithAppleButton: UIViewRepresentable {
    @EnvironmentObject private var authService: AppleIDAuthenticationService
    
    let type: ASAuthorizationAppleIDButton.ButtonType
    let style: ASAuthorizationAppleIDButton.Style
    
    init(type: ASAuthorizationAppleIDButton.ButtonType = .signIn, 
         style: ASAuthorizationAppleIDButton.Style = .black) {
        self.type = type
        self.style = style
    }
    
    func makeUIView(context: Context) -> ASAuthorizationAppleIDButton {
        let button = ASAuthorizationAppleIDButton(type: type, style: style)
        button.addTarget(context.coordinator, action: #selector(Coordinator.signInWithApple), for: .touchUpInside)
        return button
    }
    
    func updateUIView(_ uiView: ASAuthorizationAppleIDButton, context: Context) {
        // No updates needed
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(authService: authService)
    }
    
    class Coordinator: NSObject {
        let authService: AppleIDAuthenticationService
        
        init(authService: AppleIDAuthenticationService) {
            self.authService = authService
        }
        
        @objc func signInWithApple() {
            Task { @MainActor in
                authService.signInWithApple()
            }
        }
    }
}

// MARK: - SwiftUI Views
struct AppleIDAuthenticationView: View {
    @StateObject private var authService = AppleIDAuthenticationService.shared
    @State private var showingError = false
    
    var body: some View {
        VStack(spacing: 30) {
            
            // App Logo and Title
            VStack(spacing: 20) {
                Image(systemName: "pawprint.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)
                
                Text("PetCapsule")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Your pet's digital companion")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Authentication Content
            VStack(spacing: 20) {
                switch authService.authenticationState {
                case .unknown:
                    ProgressView("Checking authentication...")
                    
                case .unauthenticated:
                    authenticationButtons
                    
                case .authenticating:
                    VStack {
                        ProgressView("Signing in...")
                        Text("Please complete the Apple ID sign-in")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                case .authenticated:
                    VStack {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.green)
                        
                        Text("Welcome, \(authService.userDisplayName)!")
                            .font(.headline)
                        
                        Button("Continue") {
                            // Navigate to main app
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    
                case .error(let error):
                    VStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.red)
                        
                        Text("Authentication Error")
                            .font(.headline)
                        
                        Text(error)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        Button("Try Again") {
                            authService.checkAuthenticationStatus()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
            }
            
            Spacer()
            
            // Privacy and Terms
            VStack(spacing: 10) {
                Text("By signing in, you agree to our")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack {
                    Button("Privacy Policy") {
                        // Show privacy policy
                    }
                    .font(.caption)
                    
                    Text("and")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Button("Terms of Service") {
                        // Show terms of service
                    }
                    .font(.caption)
                }
            }
        }
        .padding()
        .alert("Authentication Error", isPresented: $showingError) {
            Button("OK") {
                showingError = false
            }
        } message: {
            Text(authService.errorMessage ?? "An unknown error occurred")
        }
        .onChange(of: authService.errorMessage) { _ in
            if authService.errorMessage != nil {
                showingError = true
            }
        }
    }
    
    private var authenticationButtons: some View {
        VStack(spacing: 15) {
            // Sign in with Apple - Primary option
            SignInWithAppleButton(type: .signIn, style: .black)
                .frame(height: 50)
                .environmentObject(authService)
            
            Text("Secure authentication with your Apple ID")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
}

// MARK: - Preview
struct AppleIDAuthenticationView_Previews: PreviewProvider {
    static var previews: some View {
        AppleIDAuthenticationView()
    }
}

// MARK: - Extension for backwards compatibility
extension AppleIDAuthenticationService {
    
    // Maintain compatibility with existing code that expects these methods
    func signIn(email: String, password: String) async throws {
        // For Apple ID authentication, we don't use email/password
        // Instead, trigger Apple ID sign-in
        signInWithApple()
    }
    
    func signUp(email: String, password: String, fullName: String?) async throws {
        // For Apple ID authentication, we don't use email/password
        // Instead, trigger Apple ID sign-in which handles both sign-in and sign-up
        signInWithApple()
    }
    
    func getCurrentUser() async throws -> User? {
        return try await appleDataService.fetchCurrentUser()
    }
    
    func refreshSession() async throws {
        checkAuthenticationStatus()
    }
    
    var session: Any? {
        // Return current user as session equivalent
        return currentUser
    }
    
    var user: Any? {
        // Return current user
        return currentUser
    }
} 