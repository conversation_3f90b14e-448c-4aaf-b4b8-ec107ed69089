//
//  AppleIntelligenceDataService.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import Foundation
import AppIntents
import Combine

/// Service for providing structured data access to Apple Intelligence and AI agents
@available(iOS 18.0, *)
@MainActor
class AppleIntelligenceDataService: ObservableObject {
    static let shared = AppleIntelligenceDataService()
    
    private let dataManager = PetDataManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Cached Data for Performance
    @Published private var cachedHealthRecords: [UUID: [PetHealthRecord]] = [:]
    @Published private var cachedAppointments: [UUID: [PetAppointment]] = [:]
    @Published private var cachedCareSchedules: [UUID: [PetCareSchedule]] = [:]
    @Published private var cachedBehaviorRecords: [UUID: [PetBehaviorRecord]] = [:]
    @Published private var cachedMemories: [UUID: [PetMemory]] = [:]
    @Published private var cachedEmergencyContacts: [UUID: [EmergencyContact]] = [:]
    
    private init() {
        setupDataBinding()
        loadAllData()
    }
    
    // MARK: - Pet Health Data Access
    
    /// Get comprehensive health summary for a pet
    @available(iOS 18.0, *)
    func getPetHealthSummary(for petId: UUID) async -> PetHealthSummary {
        let healthRecords = getHealthRecords(for: petId)
        let recentRecords = healthRecords.filter { 
            Calendar.current.dateInterval(of: .month, for: Date())?.contains($0.date) ?? false 
        }
        
        let vaccinations = healthRecords.filter { $0.recordType == .vaccination }
        let checkups = healthRecords.filter { $0.recordType == .checkup }
        let medications = healthRecords.filter { $0.recordType == .medication }
        
        let upcomingAppointments = getUpcomingAppointments(for: petId)
        let healthScore = calculateHealthScore(for: petId)
        
        return PetHealthSummary(
            petId: petId,
            overallHealthScore: healthScore,
            recentRecords: recentRecords,
            vaccinationStatus: getVaccinationStatus(from: vaccinations),
            lastCheckup: checkups.max(by: { $0.date < $1.date }),
            activeMedications: medications.filter { isActiveMedication($0) },
            upcomingAppointments: upcomingAppointments,
            healthAlerts: generateHealthAlerts(for: petId),
            lastUpdated: Date()
        )
    }
    
    /// Get vaccination history and status
    func getVaccinationHistory(for petId: UUID) -> [PetHealthRecord] {
        return getHealthRecords(for: petId).filter { $0.recordType == .vaccination }
            .sorted { $0.date > $1.date }
    }
    
    /// Get current medication schedule
    func getMedicationSchedule(for petId: UUID) -> [PetCareSchedule] {
        return getCareSchedules(for: petId).filter { 
            $0.scheduleType == .medication && $0.isActive 
        }
    }
    
    /// Get weight trends and analysis
    func getWeightTrends(for petId: UUID) -> WeightTrendAnalysis {
        let weightRecords = getHealthRecords(for: petId)
            .filter { $0.recordType == .weightCheck }
            .sorted { $0.date < $1.date }
        
        let weights = weightRecords.compactMap { record -> WeightEntry? in
            // Extract weight from description or tags
            if let weightString = record.tags.first(where: { $0.contains("kg") || $0.contains("lbs") }),
               let weight = extractWeight(from: weightString) {
                return WeightEntry(date: record.date, weight: weight)
            }
            return nil
        }

        return WeightTrendAnalysis(
            petId: petId,
            weightHistory: weights,
            currentWeight: weights.last?.weight,
            trend: calculateWeightTrend(from: weights),
            recommendations: generateWeightRecommendations(from: weights),
            lastUpdated: Date()
        )
    }
    
    // MARK: - Care Management Data Access
    
    /// Get upcoming appointments
    func getUpcomingAppointments(for petId: UUID) -> [PetAppointment] {
        return getAppointments(for: petId).filter { 
            $0.scheduledDate > Date() && $0.status != "cancelled"
        }.sorted { $0.scheduledDate < $1.scheduledDate }
    }
    
    /// Get feeding schedule
    func getFeedingSchedule(for petId: UUID) -> [PetCareSchedule] {
        return getCareSchedules(for: petId).filter { 
            $0.scheduleType == .feeding && $0.isActive 
        }
    }
    
    /// Get exercise log and patterns
    func getExerciseLog(for petId: UUID) -> ExerciseAnalysis {
        let exerciseSchedules = getCareSchedules(for: petId).filter { 
            $0.scheduleType == .exercise 
        }
        
        let recentExercise = exerciseSchedules.filter {
            guard let lastCompleted = $0.lastCompleted else { return false }
            return Calendar.current.dateInterval(of: .weekOfYear, for: Date())?.contains(lastCompleted) ?? false
        }
        
        return ExerciseAnalysis(
            petId: petId,
            weeklyExerciseMinutes: calculateWeeklyExercise(from: recentExercise),
            exercisePatterns: analyzeExercisePatterns(from: exerciseSchedules),
            recommendations: generateExerciseRecommendations(for: petId),
            lastUpdated: Date()
        )
    }
    
    /// Get behavior notes and patterns
    func getBehaviorNotes(for petId: UUID) -> [PetBehaviorRecord] {
        return getBehaviorRecords(for: petId).sorted { $0.date > $1.date }
    }
    
    // MARK: - Memory and Milestone Data Access
    
    /// Get recent memories
    func getRecentMemories(for petId: UUID, limit: Int = 10) -> [PetMemory] {
        return getMemories(for: petId)
            .sorted { $0.date > $1.date }
            .prefix(limit)
            .map { $0 }
    }
    
    /// Get milestones
    func getMilestones(for petId: UUID) -> [PetMemory] {
        return getMemories(for: petId).filter { $0.milestone }
            .sorted { $0.date > $1.date }
    }
    
    /// Get emergency contacts
    func getEmergencyContacts(for petId: UUID? = nil) -> [EmergencyContact] {
        if let petId = petId {
            return getEmergencyContactsForPet(petId)
        } else {
            return getAllEmergencyContacts()
        }
    }
    
    // MARK: - Private Helper Methods
    
    private func setupDataBinding() {
        // Bind to PetDataManager updates
        dataManager.$pets
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.loadAllData()
            }
            .store(in: &cancellables)
    }
    
    private func loadAllData() {
        Task {
            await refreshCachedData()
        }
    }
    
    private func refreshCachedData() async {
        // Load and cache data for all pets
        for pet in dataManager.pets {
            await loadDataForPet(UUID(uuidString: pet.id) ?? UUID())
        }
    }
    
    private func loadDataForPet(_ petId: UUID) async {
        // Convert existing Pet model data to Apple Intelligence models
        guard let pet = dataManager.pets.first(where: { $0.id == petId.uuidString }) else { return }
        
        // Convert health records
        let healthRecords = pet.vaccinationRecords.map { record in
            PetHealthRecord(
                petId: petId,
                recordType: .vaccination,
                title: record.vaccineName,
                description: record.notes ?? "Vaccination record",
                date: record.dateAdministered,
                vetName: record.veterinarian,
                severity: .normal,
                tags: [record.vaccineName, "vaccination"],
                attachments: []
            )
        }
        cachedHealthRecords[petId] = healthRecords
        
        // Convert appointments
        // TODO: Get appointments from appointment service
        let appointments: [PetAppointment] = []
        cachedAppointments[petId] = appointments
        
        // Convert memories
        // Get memories for this pet
        if #available(iOS 18.0, *) {
            let petMemories = ProductionMemoryService.shared.memories.filter { $0.petID == pet.id }
            var memories: [PetMemory] = []
            
            for memory in petMemories {
                let petMemory = PetMemory(
                    petId: petId,
                    memoryType: mapMemoryType(memory.type.rawValue),
                    title: memory.title,
                    description: memory.content,
                    date: memory.createdAt,
                    location: nil, // Memory model doesn't have location
                    mediaURLs: memory.mediaURL != nil ? [memory.mediaURL!] : [],
                    tags: memory.tags,
                    milestone: memory.milestone != nil,
                    emotionalTone: mapEmotionalTone(memory.sentiment ?? "neutral")
                )
                memories.append(petMemory)
            }
            
            cachedMemories[petId] = memories
        } else {
            cachedMemories[petId] = []
        }
    }
    
    // MARK: - Data Access Methods
    
    private func getHealthRecords(for petId: UUID) -> [PetHealthRecord] {
        return cachedHealthRecords[petId] ?? []
    }
    
    private func getAppointments(for petId: UUID) -> [PetAppointment] {
        return cachedAppointments[petId] ?? []
    }
    
    private func getCareSchedules(for petId: UUID) -> [PetCareSchedule] {
        return cachedCareSchedules[petId] ?? []
    }
    
    private func getBehaviorRecords(for petId: UUID) -> [PetBehaviorRecord] {
        return cachedBehaviorRecords[petId] ?? []
    }
    
    private func getMemories(for petId: UUID) -> [PetMemory] {
        return cachedMemories[petId] ?? []
    }
    
    private func getEmergencyContactsForPet(_ petId: UUID) -> [EmergencyContact] {
        return cachedEmergencyContacts[petId] ?? []
    }
    
    private func getAllEmergencyContacts() -> [EmergencyContact] {
        return cachedEmergencyContacts.values.flatMap { $0 }
    }

    // MARK: - Helper Methods for Data Analysis

    private func calculateHealthScore(for petId: UUID) -> Double {
        let healthRecords = getHealthRecords(for: petId)
        let recentRecords = healthRecords.filter {
            Calendar.current.dateInterval(of: .month, for: Date())?.contains($0.date) ?? false
        }

        // Base score
        var score = 0.8

        // Adjust based on recent health issues
        let concerningRecords = recentRecords.filter {
            $0.severity == .serious || $0.severity == .critical
        }
        score -= Double(concerningRecords.count) * 0.1

        // Adjust based on vaccination status
        let vaccinations = healthRecords.filter { $0.recordType == .vaccination }
        if let lastVaccination = vaccinations.max(by: { $0.date < $1.date }) {
            let daysSinceVaccination = Calendar.current.dateComponents([.day], from: lastVaccination.date, to: Date()).day ?? 0
            if daysSinceVaccination > 365 {
                score -= 0.1
            }
        }

        return max(0.0, min(1.0, score))
    }

    @available(iOS 18.0, *)
    private func getVaccinationStatus(from vaccinations: [PetHealthRecord]) -> PetVaccinationStatus {
        guard let lastVaccination = vaccinations.max(by: { $0.date < $1.date }) else {
            return PetVaccinationStatus(isUpToDate: false, lastVaccination: nil, nextDue: nil, overdue: [])
        }

        let daysSinceVaccination = Calendar.current.dateComponents([.day], from: lastVaccination.date, to: Date()).day ?? 0
        let isUpToDate = daysSinceVaccination <= 365

        return PetVaccinationStatus(
            isUpToDate: isUpToDate,
            lastVaccination: lastVaccination,
            nextDue: Calendar.current.date(byAdding: .year, value: 1, to: lastVaccination.date),
            overdue: isUpToDate ? [] : ["Annual vaccination"]
        )
    }

    private func isActiveMedication(_ record: PetHealthRecord) -> Bool {
        // Check if medication is still active based on tags or description
        return record.tags.contains("active") || record.tags.contains("ongoing")
    }

    private func generateHealthAlerts(for petId: UUID) -> [AIHealthAlert] {
        var alerts: [AIHealthAlert] = []

        // Check vaccination status
        let vaccinations = getVaccinationHistory(for: petId)
        if let lastVaccination = vaccinations.first {
            let daysSinceVaccination = Calendar.current.dateComponents([.day], from: lastVaccination.date, to: Date()).day ?? 0
            if daysSinceVaccination > 365 {
                alerts.append(AIHealthAlert(
                    id: UUID(),
                    petId: petId,
                    type: .vaccination,
                    title: "Vaccination Overdue",
                    message: "Annual vaccination is overdue by \(daysSinceVaccination - 365) days",
                    severity: .moderate,
                    date: Date()
                ))
            }
        }

        // Check for upcoming appointments
        let upcomingAppointments = getUpcomingAppointments(for: petId)
        for appointment in upcomingAppointments.prefix(3) {
            let daysUntil = Calendar.current.dateComponents([.day], from: Date(), to: appointment.scheduledDate).day ?? 0
            if daysUntil <= 7 {
                alerts.append(AIHealthAlert(
                    id: UUID(),
                    petId: petId,
                    type: .appointment,
                    title: "Upcoming Appointment",
                    message: "\(appointment.title) in \(daysUntil) days",
                    severity: .normal,
                    date: Date()
                ))
            }
        }

        return alerts
    }

    private func extractWeight(from string: String) -> Double? {
        let regex = try? NSRegularExpression(pattern: #"(\d+\.?\d*)\s*(kg|lbs)"#, options: .caseInsensitive)
        let range = NSRange(location: 0, length: string.utf16.count)

        if let match = regex?.firstMatch(in: string, options: [], range: range),
           let weightRange = Range(match.range(at: 1), in: string) {
            return Double(String(string[weightRange]))
        }

        return nil
    }

    private func calculateWeightTrend(from weights: [WeightEntry]) -> WeightTrend {
        guard weights.count >= 2 else { return .stable }

        let recent = weights.suffix(3)
        let older = weights.dropLast(3).suffix(3)

        let recentAverage = recent.map { $0.weight }.reduce(0, +) / Double(recent.count)
        let olderAverage = older.map { $0.weight }.reduce(0, +) / Double(older.count)

        let difference = recentAverage - olderAverage

        if difference > 0.5 {
            return .increasing
        } else if difference < -0.5 {
            return .decreasing
        } else {
            return .stable
        }
    }

    private func generateWeightRecommendations(from weights: [WeightEntry]) -> [String] {
        guard let currentWeight = weights.last?.weight else { return [] }

        var recommendations: [String] = []

        let trend = calculateWeightTrend(from: weights)
        switch trend {
        case .increasing:
            recommendations.append("Consider adjusting portion sizes or increasing exercise")
        case .decreasing:
            recommendations.append("Monitor food intake and consult vet if weight loss continues")
        case .stable:
            recommendations.append("Maintain current diet and exercise routine")
        }

        return recommendations
    }

    private func calculateWeeklyExercise(from schedules: [PetCareSchedule]) -> Int {
        // Calculate total exercise minutes for the current week
        return schedules.reduce(0) { total, schedule in
            // Simplified calculation - would need more complex logic for real implementation
            return total + 30 // Assume 30 minutes per exercise session
        }
    }

    private func analyzeExercisePatterns(from schedules: [PetCareSchedule]) -> [String] {
        var patterns: [String] = []

        let activeSchedules = schedules.filter { $0.isActive }
        if activeSchedules.isEmpty {
            patterns.append("No regular exercise schedule")
        } else {
            patterns.append("Regular exercise \(activeSchedules.count) times per week")
        }

        return patterns
    }

    private func generateExerciseRecommendations(for petId: UUID) -> [String] {
        // Generate exercise recommendations based on pet data
        return [
            "Aim for at least 30 minutes of exercise daily",
            "Mix different types of activities for variety",
            "Monitor energy levels and adjust intensity accordingly"
        ]
    }

    // MARK: - Data Mapping Methods

    private func mapHealthRecordType(_ type: String) -> HealthRecordType {
        return HealthRecordType(rawValue: type) ?? .checkup
    }

    private func mapHealthSeverity(_ severity: String) -> HealthSeverity {
        return HealthSeverity(rawValue: severity) ?? .normal
    }

    private func mapAppointmentType(_ type: String) -> AppointmentType {
        return AppointmentType(rawValue: type) ?? .checkup
    }

    private func mapAppointmentStatus(_ status: String) -> AppointmentStatus {
        return AppointmentStatus(rawValue: status) ?? .scheduled
    }

    private func mapMemoryType(_ type: String) -> AIMemoryType {
        return AIMemoryType(rawValue: type) ?? .daily
    }

    private func mapEmotionalTone(_ mood: String) -> EmotionalTone {
        return EmotionalTone(rawValue: mood) ?? .happy
    }
}

// MARK: - Supporting Data Structures

/// Comprehensive health summary for AI agents
@available(iOS 18.0, *)
struct PetHealthSummary: Codable {
    let petId: UUID
    let overallHealthScore: Double
    let recentRecords: [PetHealthRecord]
    let vaccinationStatus: PetVaccinationStatus
    let lastCheckup: PetHealthRecord?
    let activeMedications: [PetHealthRecord]
    let upcomingAppointments: [PetAppointment]
    let healthAlerts: [AIHealthAlert]
    let lastUpdated: Date
}

/// Vaccination status information
@available(iOS 18.0, *)
struct PetVaccinationStatus: Codable {
    let isUpToDate: Bool
    let lastVaccination: PetHealthRecord?
    let nextDue: Date?
    let overdue: [String]
}

/// Health alert for AI agents
struct AIHealthAlert: Codable, Identifiable {
    let id: UUID
    let petId: UUID
    let type: AIHealthAlertType
    let title: String
    let message: String
    let severity: HealthSeverity
    let date: Date
}

enum AIHealthAlertType: String, CaseIterable, Codable {
    case vaccination = "vaccination"
    case appointment = "appointment"
    case medication = "medication"
    case weight = "weight"
    case behavior = "behavior"
    case emergency = "emergency"
}

/// Weight entry for Codable compliance
struct WeightEntry: Codable {
    let date: Date
    let weight: Double
}

/// Weight trend analysis
struct WeightTrendAnalysis: Codable {
    let petId: UUID
    let weightHistory: [WeightEntry]
    let currentWeight: Double?
    let trend: WeightTrend
    let recommendations: [String]
    let lastUpdated: Date
}

enum WeightTrend: String, CaseIterable, Codable {
    case increasing = "increasing"
    case decreasing = "decreasing"
    case stable = "stable"
}

/// Exercise analysis for AI agents
struct ExerciseAnalysis: Codable {
    let petId: UUID
    let weeklyExerciseMinutes: Int
    let exercisePatterns: [String]
    let recommendations: [String]
    let lastUpdated: Date
}
