//
//  AppleMapService.swift
//  PetCapsule
//
//  Native Apple MapKit implementation replacing Google Places API
//  No API keys required, better iOS integration
//

import Foundation
import MapKit
import CoreLocation

@MainActor
class AppleMapService: NSObject, ObservableObject {
    static let shared = AppleMapService()
    
    private let locationManager = CLLocationManager()
    
    // Published property for InteractiveMapView
    @Published var nearbyPetFriendlyLocations: [PetFriendlyLocation] = []
    
    // Published property for CreateEventView
    @Published var isLoadingLocations: Bool = false
    
    override init() {
        super.init()
        setupLocationManager()
    }
    
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.requestWhenInUseAuthorization()
    }
    
    // MARK: - Pet-Friendly Places Search
    
    func searchPetFriendlyPlaces(near location: CLLocationCoordinate2D, radius: CLLocationDistance = 5000) async throws -> [PetFriendlyLocation] {
        isLoadingLocations = true
        defer { isLoadingLocations = false }
        
        var allResults: [PetFriendlyLocation] = []
        
        // Search for different types of pet-friendly places
        let searchQueries = [
            "veterinarian",
            "pet store", 
            "dog park",
            "pet grooming",
            "animal hospital",
            "pet hotel",
            "pet friendly restaurant"
        ]
        
        for query in searchQueries {
            let places = try await searchPlaces(query: query, near: location, radius: radius)
            allResults.append(contentsOf: places)
        }
        
        // Remove duplicates and sort by distance
        let uniquePlaces = removeDuplicates(from: allResults)
        let sortedPlaces = uniquePlaces.sorted { $0.distance < $1.distance }
        
        // Update the published property for UI
        await MainActor.run {
            self.nearbyPetFriendlyLocations = sortedPlaces
        }
        
        return sortedPlaces
    }
    
    private func searchPlaces(query: String, near location: CLLocationCoordinate2D, radius: CLLocationDistance) async throws -> [PetFriendlyLocation] {
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = query
        request.region = MKCoordinateRegion(
            center: location,
            latitudinalMeters: radius * 2,
            longitudinalMeters: radius * 2
        )
        
        let search = MKLocalSearch(request: request)
        let response = try await search.start()
        
        return response.mapItems.compactMap { mapItem in
            convertToLocationModel(mapItem, userLocation: location)
        }
    }
    
    // MARK: - Location Details
    
    func getPlaceDetails(for mapItem: MKMapItem) async -> PetFriendlyLocation? {
        guard mapItem.placemark.location?.coordinate != nil else { return nil }
        
        let userLocation = await getCurrentLocation()
        return convertToLocationModel(mapItem, userLocation: userLocation)
    }
    
    // MARK: - Current Location
    
    func getCurrentLocation() async -> CLLocationCoordinate2D {
        guard let location = locationManager.location?.coordinate else {
            // Fallback to San Francisco if location unavailable
            return CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)
        }
        return location
    }
    
    func reverseGeocode(coordinate: CLLocationCoordinate2D) async throws -> String {
        let geocoder = CLGeocoder()
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        
        let placemarks = try await geocoder.reverseGeocodeLocation(location)
        guard let placemark = placemarks.first else {
            return "Unknown Location"
        }
        
        return formatAddress(from: placemark)
    }
    
    // MARK: - Missing Methods for LocationTaggingService
    
    func getDetailedLocationInfo(coordinate: CLLocationCoordinate2D) async throws -> DetailedLocationInfo {
        let geocoder = CLGeocoder()
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        
        let placemarks = try await geocoder.reverseGeocodeLocation(location)
        guard let placemark = placemarks.first else {
            throw NSError(domain: "AppleMapService", code: 1, userInfo: [NSLocalizedDescriptionKey: "No placemark found"])
        }
        
        return DetailedLocationInfo(
            coordinate: coordinate,
            name: placemark.name ?? "Unknown Location",
            thoroughfare: placemark.thoroughfare,
            subThoroughfare: placemark.subThoroughfare,
            locality: placemark.locality,
            subLocality: placemark.subLocality,
            administrativeArea: placemark.administrativeArea,
            subAdministrativeArea: placemark.subAdministrativeArea,
            postalCode: placemark.postalCode,
            country: placemark.country,
            isoCountryCode: placemark.isoCountryCode,
            formattedAddress: formatAddress(from: placemark),
            timezone: placemark.timeZone,
            region: placemark.region as? CLCircularRegion
        )
    }
    
    func searchNearbyPointsOfInterest(coordinate: CLLocationCoordinate2D, radius: CLLocationDistance = 1000) async throws -> [PointOfInterest] {
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = "point of interest"
        request.region = MKCoordinateRegion(
            center: coordinate,
            latitudinalMeters: radius * 2,
            longitudinalMeters: radius * 2
        )
        
        let search = MKLocalSearch(request: request)
        let response = try await search.start()
        
        return response.mapItems.compactMap { mapItem in
            guard let itemCoordinate = mapItem.placemark.location?.coordinate else { return nil }
            
            let location = CLLocation(latitude: itemCoordinate.latitude, longitude: itemCoordinate.longitude)
            let userLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
            let distance = userLocation.distance(from: location)
            
            return PointOfInterest(
                id: UUID(),
                name: mapItem.name ?? "Unknown POI",
                category: mapItem.pointOfInterestCategory?.rawValue ?? "Unknown",
                coordinate: itemCoordinate,
                distance: distance
            )
        }
    }
    
    // MARK: - Address Geocoding for CreateEventView
    
    func geocodeAddress(_ address: String) async throws -> CLLocationCoordinate2D {
        let geocoder = CLGeocoder()
        let placemarks = try await geocoder.geocodeAddressString(address)
        
        guard let placemark = placemarks.first,
              let location = placemark.location else {
            throw NSError(domain: "AppleMapService", code: 2, userInfo: [NSLocalizedDescriptionKey: "Could not geocode address"])
        }
        
        return location.coordinate
    }
    
    // MARK: - Route Planning for RouteOptionsSheet
    
    func planWalkRoute(from start: CLLocationCoordinate2D, to end: CLLocationCoordinate2D, preference: WalkingPreference = .fastest) async throws -> WalkRoute {
        let request = MKDirections.Request()
        
        // Configure source and destination
        let sourcePlacemark = MKPlacemark(coordinate: start)
        let destinationPlacemark = MKPlacemark(coordinate: end)
        
        request.source = MKMapItem(placemark: sourcePlacemark)
        request.destination = MKMapItem(placemark: destinationPlacemark)
        request.transportType = .walking
        
        // Set route options based on preference
        switch preference {
        case .fastest:
            request.requestsAlternateRoutes = false
        case .scenic:
            request.requestsAlternateRoutes = true
        case .safest:
            request.requestsAlternateRoutes = true
        }
        
        let directions = MKDirections(request: request)
        let response = try await directions.calculate()
        
        guard let route = response.routes.first else {
            throw NSError(domain: "AppleMapService", code: 3, userInfo: [NSLocalizedDescriptionKey: "No route found"])
        }
        
        // Extract coordinates from the route
        let coordinates = extractCoordinates(from: route.polyline)
        
        // Calculate additional metrics
        let difficulty = calculateDifficulty(distance: route.distance)
        let scenicScore = calculateScenicScore(route: route, preference: preference)
        let safetyScore = calculateSafetyScore(route: route, preference: preference)
        
        return WalkRoute(
            coordinates: coordinates,
            distance: route.distance,
            estimatedTime: route.expectedTravelTime,
            difficulty: difficulty,
            scenicScore: scenicScore,
            safetyScore: safetyScore,
            polyline: route.polyline
        )
    }
    
    private func extractCoordinates(from polyline: MKPolyline) -> [CLLocationCoordinate2D] {
        let pointCount = polyline.pointCount
        let points = polyline.points()
        
        var coordinates: [CLLocationCoordinate2D] = []
        for i in 0..<pointCount {
            let mapPoint = points[i]
            let coordinate = mapPoint.coordinate
            coordinates.append(coordinate)
        }
        
        return coordinates
    }
    
    private func calculateDifficulty(distance: CLLocationDistance) -> WalkDifficulty {
        let distanceInKm = distance / 1000.0
        
        switch distanceInKm {
        case 0...2:
            return .easy
        case 2...5:
            return .moderate
        case 5...10:
            return .challenging
        default:
            return .difficult
        }
    }
    
    private func calculateScenicScore(route: MKRoute, preference: WalkingPreference) -> Double {
        // Base score calculation (simplified)
        var score = 0.5
        
        // Adjust based on preference
        switch preference {
        case .scenic:
            score += 0.3
        case .fastest:
            score += 0.1
        case .safest:
            score += 0.2
        }
        
        // Add some randomness for demo purposes
        score += Double.random(in: -0.2...0.2)
        
        return max(0.0, min(1.0, score))
    }
    
    private func calculateSafetyScore(route: MKRoute, preference: WalkingPreference) -> Double {
        // Base safety score calculation (simplified)
        var score = 0.7
        
        // Adjust based on preference
        switch preference {
        case .safest:
            score += 0.2
        case .scenic:
            score += 0.1
        case .fastest:
            score -= 0.1
        }
        
        // Add some randomness for demo purposes
        score += Double.random(in: -0.1...0.1)
        
        return max(0.0, min(1.0, score))
    }
    
    // MARK: - Helper Methods
    
    private func convertToLocationModel(_ mapItem: MKMapItem, userLocation: CLLocationCoordinate2D) -> PetFriendlyLocation? {
        guard let coordinate = mapItem.placemark.location?.coordinate else { return nil }
        
        let location = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        let userCLLocation = CLLocation(latitude: userLocation.latitude, longitude: userLocation.longitude)
        let distance = userCLLocation.distance(from: location) / 1000.0 // Convert to kilometers
        
        let locationType = determineLocationType(from: mapItem)
        let amenities = extractAmenities(for: locationType, from: mapItem)
        
        return PetFriendlyLocation(
            name: mapItem.name ?? "Unknown Place",
            type: locationType,
            rating: Double.random(in: 3.5...4.8), // MapKit doesn't provide ratings, use estimated
            distance: distance,
            address: formatAddress(from: mapItem.placemark),
            imageURL: getPlaceholderImageURL(for: locationType),
            amenities: amenities,
            isOpen: isCurrentlyOpen(mapItem),
            coordinate: coordinate,
            phoneNumber: mapItem.phoneNumber,
            website: mapItem.url?.absoluteString,
            hours: getBusinessHours(for: mapItem)
        )
    }
    
    private func determineLocationType(from mapItem: MKMapItem) -> LocationType {
        let name = mapItem.name?.lowercased() ?? ""
        let category = mapItem.pointOfInterestCategory
        
        if category == .hospital || category == .pharmacy || name.contains("vet") || name.contains("animal hospital") {
            return .veterinary
        } else if name.contains("pet") && name.contains("store") {
            return .store
        } else if name.contains("park") || name.contains("dog park") || category == .park {
            return .park
        } else if category == .restaurant || category == .cafe || name.contains("restaurant") || name.contains("cafe") {
            return .restaurant
        } else if name.contains("trail") || name.contains("hiking") || category == .nationalPark {
            return .trail
        } else if name.contains("beach") || category == .beach {
            return .beach
        } else if name.contains("hotel") || name.contains("pet hotel") || category == .hotel {
            return .hotel
        } else {
            return .store // Default fallback
        }
    }
    
    private func extractAmenities(for type: LocationType, from mapItem: MKMapItem) -> [String] {
        switch type {
        case .park:
            return ["Off-leash area", "Water fountains", "Waste bags", "Walking trails", "Benches"]
        case .veterinary:
            return ["Emergency care", "Vaccinations", "Surgery", "Dental care", "Grooming"]
        case .store:
            return ["Pet supplies", "Pet food", "Toys", "Accessories", "Grooming supplies"]
        case .restaurant:
            return ["Pet-friendly patio", "Water bowls", "Dog treats", "Outdoor seating"]
        case .trail:
            return ["Hiking trails", "Scenic views", "Pet waste stations", "Nature paths"]
        case .beach:
            return ["Off-leash area", "Water access", "Sand play area", "Beach cleanup bags"]
        case .hotel:
            return ["Pet-friendly rooms", "Pet sitting", "Dog walking", "Pet amenities"]
        }
    }
    
    private func formatAddress(from placemark: CLPlacemark) -> String {
        var addressComponents: [String] = []
        
        if let streetNumber = placemark.subThoroughfare {
            addressComponents.append(streetNumber)
        }
        if let streetName = placemark.thoroughfare {
            addressComponents.append(streetName)
        }
        if let city = placemark.locality {
            addressComponents.append(city)
        }
        if let state = placemark.administrativeArea {
            addressComponents.append(state)
        }
        if let zipCode = placemark.postalCode {
            addressComponents.append(zipCode)
        }
        
        return addressComponents.joined(separator: " ")
    }
    
    private func getPlaceholderImageURL(for type: LocationType) -> String {
        switch type {
        case .park:
            return "https://images.unsplash.com/photo-1544737151-6e4b9e0e4d5a?w=400&h=300&fit=crop"
        case .veterinary:
            return "https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=400&h=300&fit=crop"
        case .store:
            return "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop"
        case .restaurant:
            return "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop"
        case .trail:
            return "https://images.unsplash.com/photo-1506784365867-9513bfef44c3?w=400&h=300&fit=crop" // Added for trail
        case .beach:
            return "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=400&h=300&fit=crop" // Added for beach
        case .hotel:
            return "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop" // Reusing restaurant image for hotel
        }
    }
    
    private func isCurrentlyOpen(_ mapItem: MKMapItem) -> Bool {
        // MapKit doesn't provide opening hours info, so we'll estimate based on current time
        let hour = Calendar.current.component(.hour, from: Date())
        return hour >= 8 && hour <= 20 // Assume most places open 8 AM - 8 PM
    }
    
    private func removeDuplicates(from places: [PetFriendlyLocation]) -> [PetFriendlyLocation] {
        var uniquePlaces: [PetFriendlyLocation] = []
        var seenNames: Set<String> = []
        
        for place in places {
            let key = "\(place.name.lowercased())_\(place.address.prefix(20))"
            if !seenNames.contains(key) {
                seenNames// Removed Supabase insert callkey)
                uniquePlaces.append(place)
            }
        }
        
        return uniquePlaces
    }
}

// MARK: - CLLocationManagerDelegate

extension AppleMapService: CLLocationManagerDelegate {
    nonisolated func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        // Handle location updates if needed
    }
    
    nonisolated func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("⚠️ Location error: \(error.localizedDescription)")
    }
    
    nonisolated func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            print("✅ Location access granted")
        case .denied, .restricted:
            print("❌ Location access denied")
        case .notDetermined:
            manager.requestWhenInUseAuthorization()
        @unknown default:
            break
        }
    }
    
    private func getBusinessHours(for mapItem: MKMapItem) -> String? {
        // In a real app, you would extract this from the mapItem's business hours
        // For now, return typical hours based on location type
        let locationType = determineLocationType(from: mapItem)
        switch locationType {
        case .park, .trail, .beach:
            return "6:00 AM - 10:00 PM"
        case .veterinary:
            return "8:00 AM - 6:00 PM"
        case .store:
            return "9:00 AM - 8:00 PM"
        case .restaurant:
            return "11:00 AM - 10:00 PM"
        case .hotel:
            return "24 Hours"
        }
    }
}

// MARK: - Supporting Models

extension PetFriendlyLocation {
    static let sampleLocations: [PetFriendlyLocation] = [
        PetFriendlyLocation(
            name: "Golden Gate Park Dog Area",
            type: .park,
            rating: 4.6,
            distance: 1.2,
            address: "Golden Gate Park, San Francisco, CA",
            imageURL: "https://images.unsplash.com/photo-1544737151-6e4b9e0e4d5a?w=400&h=300&fit=crop",
            amenities: ["Off-leash area", "Water fountains", "Waste bags"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 37.7694, longitude: -122.4862),
            phoneNumber: "(*************",
            website: "https://sfrecpark.org",
            hours: "6:00 AM - 10:00 PM"
        ),
        PetFriendlyLocation(
            name: "Mission Animal Hospital",
            type: .veterinary,
            rating: 4.8,
            distance: 0.8,
            address: "123 Mission St, San Francisco, CA",
            imageURL: "https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=400&h=300&fit=crop",
            amenities: ["Emergency care", "Vaccinations", "Surgery"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 37.7599, longitude: -122.4148),
            phoneNumber: "(*************",
            website: "https://missionanimalhospital.com",
            hours: "8:00 AM - 6:00 PM"
        )
    ]
}
