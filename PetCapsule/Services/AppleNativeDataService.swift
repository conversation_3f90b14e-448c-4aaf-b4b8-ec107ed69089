//
//  AppleNativeDataService.swift
//  PetCapsule
//
//  🍎 100% Apple Ecosystem Data Service - Zero External Dependencies
//  🔒 Complete Privacy with SwiftData + CloudKit + Local Storage
//
import SwiftData
import CloudKit
import Foundation
import AuthenticationServices
// MARK: - Apple Native Data Service
@MainActor
class AppleNativeDataService: ObservableObject {
    // MARK: - Singleton
    static let shared = AppleNativeDataService()
    
    // MARK: - Properties
    private let modelContainer: ModelContainer
    private let modelContext: ModelContext
    private let cloudKitContainer: CKContainer? // Optional - disabled due to schema migration conflict
    @Published var isAuthenticated: Bool = false
    @Published var currentUser: User?
    @Published var syncStatus: SyncStatus = .idle
    enum SyncStatus {
        case idle
        case syncing
        case completed
        case error(String)
    }
    // MARK: - Initialization
    private init() {
        // CloudKit-enabled configuration for cross-device sync
        let schema = Schema([
            User.self, Pet.self, Memory.self, MemoryGem.self,
            HealthRecord.self, Vault.self
        ])
        // CloudKit-enabled configuration for cross-device sync
        let modelConfiguration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false, // Use persistent storage
            cloudKitDatabase: .private("iCloud.com.petcapsule.app") // Enable CloudKit sync
        )
        do {
            self.modelContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
            self.modelContext = modelContainer.mainContext
            self.cloudKitContainer = CKContainer(identifier: "iCloud.com.petcapsule.app") // Enable CloudKit
            print("✅ SwiftData ModelContainer created successfully (CloudKit Sync Enabled)")
        } catch {
            print("❌ Failed to create ModelContainer with CloudKit: \(error)")
            // Fallback to local-only storage if CloudKit fails
            print("🔄 Falling back to local-only storage...")
            let localConfig = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
            do {
                self.modelContainer = try ModelContainer(for: schema, configurations: [localConfig])
                self.modelContext = modelContainer.mainContext
                self.cloudKitContainer = nil
                print("✅ Fallback ModelContainer created successfully (Local Storage Only)")
            } catch {
                fatalError("Even local storage failed: \(error)")
            }
        }
        // Start authentication check
        Task {
            await checkAuthenticationStatus()
        }
    }
    // MARK: - Authentication
    func signInWithApple() async throws {
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]
        // In a real implementation, this would present the Apple ID sign-in UI
        // For now, we'll simulate success
        let userIdentifier = "apple_user_\(UUID().uuidString)"
        await createOrUpdateUser(appleUserID: userIdentifier, email: "<EMAIL>", fullName: "Apple User")
    }
    func signOut() async {
        currentUser = nil
        isAuthenticated = false
        // Clear local data if needed
        try? modelContext.delete(model: User.self)
        try? modelContext.save()
    }
    private func checkAuthenticationStatus() async {
        // Check if user exists in local storage
        let descriptor = FetchDescriptor<User>()
        do {
            let users = try modelContext.fetch(descriptor)
            if let user = users.first {
                currentUser = user
                isAuthenticated = true
            }
        } catch {
            print("Error checking authentication: \(error)")
        }
    }
    private func createOrUpdateUser(appleUserID: String, email: String, fullName: String?) async {
        do {
            // Check if user already exists
            let descriptor = FetchDescriptor<User>(predicate: #Predicate { $0.email == email })
            let existingUsers = try modelContext.fetch(descriptor)
            let user: User
            if let existingUser = existingUsers.first {
                user = existingUser
                if let fullName = fullName {
                    user.fullName = fullName
                }
                user.lastActiveAt = Date()
                user.updatedAt = Date()
            } else {
                // Create new user with required parameters
                let userId = UUID().uuidString
                user = User(
                    id: userId,
                    email: email,
                    displayName: fullName ?? email,
                    fullName: fullName
                )
                modelContext
            }
            try modelContext.save()
            currentUser = user
            isAuthenticated = true
            // Trigger CloudKit sync
            await syncWithCloudKit()
        } catch {
            print("Error creating/updating user: \(error)")
        }
    }
    // MARK: - CloudKit Sync
    private func syncWithCloudKit() async {
        syncStatus = .syncing
        if cloudKitContainer != nil {
            // CloudKit is enabled - data syncs across user's devices automatically
            print("☁️ CloudKit sync enabled - data syncing across user's devices")
            // SwiftData + CloudKit handles sync automatically
            // No manual sync needed - just update status
            try? await Task.sleep(for: .seconds(1)) // Brief delay for sync status
            syncStatus = .completed
        } else {
            // Fallback: Local storage only
            print("📱 Local storage only - CloudKit unavailable")
            syncStatus = .completed
        }
    }
    // MARK: - User Management
    func fetchCurrentUser() async throws -> User? {
        guard isAuthenticated else { return nil }
        return currentUser
    }
    func updateUser(_ user: User) async throws {
        user.updatedAt = Date()
        try modelContext.save()
        await syncWithCloudKit()
    }
    // MARK: - Pet Management
    func fetchPets(for user: User) async throws -> [Pet] {
        // Since Pet model doesn't have owner relationship, fetch all pets
        // In a real implementation, you'd need to add ownerID to Pet model
        let descriptor = FetchDescriptor<Pet>(
            sortBy: [SortDescriptor(\.name)]
        )
        return try modelContext.fetch(descriptor)
    }
    func createPet(name: String, species: String, breed: String?, for user: User) async throws -> Pet {
        let pet = Pet(
            name: name,
            species: species,
            breed: breed,
            age: 0 // Default age, should be provided by user
        )
        modelContext
        // Basic CloudKit approach - just update the UI array for now
        // CloudKit relationships will be added incrementally to avoid circular reference issues
        user.petIDs.append(pet.id)
        user.updatedAt = Date()
        try modelContext.save()
        await syncWithCloudKit()
        return pet
    }
    func updatePet(_ pet: Pet) async throws {
        pet.updatedAt = Date()
        try modelContext.save()
        await syncWithCloudKit()
    }
    func deletePet(_ pet: Pet) async throws {
        modelContext.delete(pet)
        try modelContext.save()
        await syncWithCloudKit()
    }
    // MARK: - Memory Management
    func fetchMemories(for pet: Pet) async throws -> [Memory] {
        // Temporarily fetch all memories due to predicate issues
        // TODO: Fix predicate when Pet-Memory relationship is properly configured
        let descriptor = FetchDescriptor<Memory>(
            sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
        )
        let allMemories = try modelContext.fetch(descriptor)
        // Filter manually for now
        return allMemories.filter { $0.petID == pet.id }
    }
    func createMemory(title: String, content: String?, memoryType: String, for pet: Pet, user: User) async throws -> Memory {
        // Convert string memoryType to MemoryType enum
        let type = MemoryType(rawValue: memoryType) ?? .text
        let memory = Memory(
            title: title,
            content: content ?? "",
            type: type
        )
        memory.petID = pet.id
        modelContext
        // Update user's updated timestamp
        user.updatedAt = Date()
        try modelContext.save()
        await syncWithCloudKit()
        return memory
    }
    func updateMemory(_ memory: Memory) async throws {
        memory.updatedAt = Date()
        try modelContext.save()
        await syncWithCloudKit()
    }
    func deleteMemory(_ memory: Memory) async throws {
        modelContext.delete(memory)
        try modelContext.save()
        await syncWithCloudKit()
    }
    // MARK: - Vault Management
    func getAllVaults() async -> [Vault] {
        // Stub implementation - would fetch from SwiftData in production
        return []
    }
    func createVault(_ vault: Vault) async throws {
        // Stub implementation - would save to SwiftData in production
        print("Creating vault: \(vault.name)")
    }
    func deleteVault(_ vault: Vault) async throws {
        // Stub implementation - would delete from SwiftData in production
        print("Deleting vault: \(vault.name)")
    }
}
// MARK: - Migration Service
class AppleNativeMigrationService {
    private let appleDataService: AppleNativeDataService
    private let migrationService: AppleNativeDataService
    init(appleDataService: AppleNativeDataService, migrationService: AppleNativeDataService) {
        self.appleDataService = appleDataService
        self.migrationService = migrationService
    }
    func performMigration() async throws {
        // This would be the actual migration logic
        // For now, we'll focus on the new Apple native system
        print("Migration service ready - implement as needed")
    }
}
// MARK: - Compatibility Extensions
extension AppleNativeDataService {
    func fetchUser() async throws -> User? {
        return try await fetchCurrentUser()
    }
    func createUser(email: String, password: String, fullName: String?) async throws -> User {
        // For Apple ID, we don't use password - create user with Apple ID instead
        let userId = UUID().uuidString
        let user = User(
            id: userId,
            email: email,
            displayName: fullName ?? email,
            fullName: fullName
        )
        modelContainer.mainContext
        try modelContainer.mainContext.save()
        return user
    }
    func updateUserProfile(_ user: User, fullName: String?, bio: String?, location: String?) async throws {
        // Only update properties that exist in the User model
        if let fullName = fullName {
            user.fullName = fullName
        }
        // Note: bio and location properties don't exist in current User model
        // TODO: Add these properties to User model if needed
        try await updateUser(user)
    }
}
extension AppleNativeDataService {
    class MigrationService {
        private let appleDataService: AppleNativeDataService
        init(appleDataService: AppleNativeDataService) {
            self.appleDataService = appleDataService
        }
        func migratePets() async throws {
            print("Migrating pets to Apple native storage")
            // Migration logic would go here
        }
        func migrateMemories() async throws {
            print("Migrating memories to Apple native storage")
            // Migration logic would go here
        }
        func migrateUsers() async throws {
            print("Migrating users to Apple native storage")
            // Migration logic would go here
        }
    }
}
