//
//  AppleNativeStorageService.swift
//  PetCapsule
//
//  Apple-native storage service using local file system and CloudKit
//  Uses privacy-first local storage
//

import Foundation
import UIKit
import SwiftUI
import PhotosUI

@MainActor
class AppleNativeStorageService: ObservableObject {
    static let shared = AppleNativeStorageService()
    
    // MARK: - Storage Directories
    private let documentsDirectory: URL
    private let memoryImagesDirectory: URL
    private let thumbnailsDirectory: URL
    private let petPhotosDirectory: URL
    
    @Published var isUploading = false
    @Published var uploadProgress: Double = 0.0
    
    private init() {
        // Setup local storage directories
        self.documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        self.memoryImagesDirectory = documentsDirectory.appendingPathComponent("MemoryImages")
        self.thumbnailsDirectory = documentsDirectory.appendingPathComponent("Thumbnails")
        self.petPhotosDirectory = documentsDirectory.appendingPathComponent("PetPhotos")
        
        createDirectoriesIfNeeded()
    }
    
    // MARK: - Directory Setup
    private func createDirectoriesIfNeeded() {
        let directories = [memoryImagesDirectory, thumbnailsDirectory, petPhotosDirectory]
        
        for directory in directories {
            if !FileManager.default.fileExists(atPath: directory.path) {
                try? FileManager.default.createDirectory(at: directory, withIntermediateDirectories: true)
            }
        }
    }
    
    // MARK: - Memory Image Storage
    func saveMemoryImage(_ imageData: Data, memoryId: String) async throws -> String? {
        isUploading = true
        uploadProgress = 0.0
        
        defer {
            isUploading = false
            uploadProgress = 0.0
        }
        
        let fileName = "\(memoryId)_\(UUID().uuidString).jpg"
        let fileURL = memoryImagesDirectory.appendingPathComponent(fileName)
        
        do {
            // Simulate upload progress
            for i in 0...100 {
                uploadProgress = Double(i) / 100.0
                try await Task.sleep(nanoseconds: 10_000_000) // 0.01 seconds
            }
            
            try imageData.write(to: fileURL)
            print("✅ Saved memory image: \(fileName)")
            return fileURL.lastPathComponent
        } catch {
            print("❌ Failed to save memory image: \(error)")
            throw error
        }
    }
    
    func saveMemoryThumbnail(_ thumbnailData: Data, memoryId: String) async throws -> String? {
        let fileName = "\(memoryId)_thumbnail.jpg"
        let fileURL = thumbnailsDirectory.appendingPathComponent(fileName)
        
        do {
            try thumbnailData.write(to: fileURL)
            print("✅ Saved memory thumbnail: \(fileName)")
            return fileURL.lastPathComponent
        } catch {
            print("❌ Failed to save memory thumbnail: \(error)")
            throw error
        }
    }
    
    // MARK: - Pet Photo Storage
    func savePetPhoto(_ imageData: Data, petId: String) async throws -> String? {
        let fileName = "\(petId)_\(UUID().uuidString).jpg"
        let fileURL = petPhotosDirectory.appendingPathComponent(fileName)
        
        do {
            try imageData.write(to: fileURL)
            print("✅ Saved pet photo: \(fileName)")
            return fileURL.lastPathComponent
        } catch {
            print("❌ Failed to save pet photo: \(error)")
            throw error
        }
    }
    
    // MARK: - Profile Image Upload
    func uploadPetProfile(from imageData: Data, fileName: String) async -> String? {
        do {
            let profileImageURL = petPhotosDirectory.appendingPathComponent(fileName)
            try imageData.write(to: profileImageURL)
            
            await MainActor.run {
                self.isUploading = false
                self.uploadProgress = 0.0
            }
            
            return profileImageURL.absoluteString
        } catch {
            print("❌ Failed to save profile image: \(error)")
            return nil
        }
    }
    
    // MARK: - Image Retrieval
    func getMemoryImage(fileName: String) -> UIImage? {
        let fileURL = memoryImagesDirectory.appendingPathComponent(fileName)
        return UIImage(contentsOfFile: fileURL.path)
    }
    
    func getMemoryThumbnail(fileName: String) -> UIImage? {
        let fileURL = thumbnailsDirectory.appendingPathComponent(fileName)
        return UIImage(contentsOfFile: fileURL.path)
    }
    
    func getPetPhoto(fileName: String) -> UIImage? {
        let fileURL = petPhotosDirectory.appendingPathComponent(fileName)
        return UIImage(contentsOfFile: fileURL.path)
    }
    
    // MARK: - File URLs for SwiftUI
    func getMemoryImageURL(fileName: String) -> URL? {
        let fileURL = memoryImagesDirectory.appendingPathComponent(fileName)
        return FileManager.default.fileExists(atPath: fileURL.path) ? fileURL : nil
    }
    
    func getPetPhotoURL(fileName: String) -> URL? {
        let fileURL = petPhotosDirectory.appendingPathComponent(fileName)
        return FileManager.default.fileExists(atPath: fileURL.path) ? fileURL : nil
    }
    
    // MARK: - File Management
    func deleteMemoryImage(fileName: String) throws {
        let fileURL = memoryImagesDirectory.appendingPathComponent(fileName)
        try FileManager.default.removeItem(at: fileURL)
        print("✅ Deleted memory image: \(fileName)")
    }
    
    func deletePetPhoto(fileName: String) throws {
        let fileURL = petPhotosDirectory.appendingPathComponent(fileName)
        try FileManager.default.removeItem(at: fileURL)
        print("✅ Deleted pet photo: \(fileName)")
    }
    
    // MARK: - Storage Info
    func getStorageInfo() -> (totalSize: Int64, freeSpace: Int64) {
        let fileManager = FileManager.default
        do {
            let attributes = try fileManager.attributesOfFileSystem(forPath: documentsDirectory.path)
            let freeSpace = attributes[.systemFreeSize] as? Int64 ?? 0
            let totalSize = attributes[.systemSize] as? Int64 ?? 0
            return (totalSize, freeSpace)
        } catch {
            return (0, 0)
        }
    }
    
    // MARK: - Utility Methods
    func compressImage(_ image: UIImage, maxSize: Int = Config.Storage.maxFileSize) -> Data? {
        var compression: CGFloat = 1.0
        guard var imageData = image.jpegData(compressionQuality: compression) else { return nil }
        
        while imageData.count > maxSize && compression > 0.1 {
            compression -= 0.1
            guard let compressedData = image.jpegData(compressionQuality: compression) else { break }
            imageData = compressedData
        }
        
        return imageData
    }
    
    func generateThumbnail(from image: UIImage, size: CGSize = CGSize(width: 200, height: 200)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        image.draw(in: CGRect(origin: .zero, size: size))
        let thumbnail = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return thumbnail
    }
} 