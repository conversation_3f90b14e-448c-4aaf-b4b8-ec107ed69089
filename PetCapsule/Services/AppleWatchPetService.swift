//
//  AppleWatchPetService.swift
//  PetCapsule
//
//  🍎 Phase 3: Apple Watch Pet Service
//  Wrist-based pet monitoring with Apple Intelligence
//

import Foundation
import SwiftUI
import WatchConnectivity
import HealthKit
import CoreMotion

@available(iOS 18.0, *)
@MainActor
class AppleWatchPetService: NSObject, ObservableObject {
    static let shared = AppleWatchPetService()
    
    // MARK: - Published Properties
    @Published var isWatchConnected = false
    @Published var watchBatteryLevel: Double = 0.0
    @Published var currentPetStatus: WatchPetStatus?
    @Published var todayActivity: WatchActivitySummary = WatchActivitySummary()
    @Published var healthAlerts: [WatchHealthAlert] = []
    @Published var quickActions: [WatchQuickAction] = []
    
    // MARK: - Private Properties
    private var session: WCSession?
    private var appleIntelligenceService = EnhancedAppleIntelligenceService.shared
    private var healthStore = HKHealthStore()
    private var motionManager = CMMotionManager()
    
    // Data synchronization
    private var lastDataSync = Date()
    private var pendingUpdates: [String: Any] = [:]
    
    override init() {
        super.init()
        setupWatchConnectivity()
        setupHealthKit()
        setupQuickActions()
        startActivityMonitoring()
    }
    
    // MARK: - Public Methods
    
    func getCurrentPetStatus(pet: Pet) async -> WatchPetStatus {
        let status = WatchPetStatus(
            petName: pet.name,
            petEmoji: getPetEmoji(for: pet.species),
            overallHealth: .good,
            briefDescription: "Your pet is doing well!",
            nextAction: "Next feeding in 2 hours",
            lastUpdated: Date()
        )
        
        currentPetStatus = status
        return status
    }
    
    func sendEmergencyAlert(pet: Pet, emergency: String) async {
        let alert = WatchHealthAlert(
            id: UUID(),
            petName: pet.name,
            severity: .emergency,
            message: emergency,
            timestamp: Date(),
            actionRequired: true
        )
        
        healthAlerts.append(alert)
        await sendAlertToWatch(alert)
        
        // Generate emergency response with Apple Intelligence
        let emergencyResponse = await appleIntelligenceService.sendMessage(
            to: AIAgent.healthGuardian,
            message: "EMERGENCY: \(emergency) for \(pet.name). Provide immediate action steps.",
            pet: pet
        )
        
        await sendEmergencyResponseToWatch(emergencyResponse)
    }
    
    func updateActivityData(steps: Int, heartRate: Double, calories: Int) async {
        let summary = WatchActivitySummary(
            steps: steps,
            heartRate: heartRate,
            caloriesBurned: calories,
            activeMinutes: calculateActiveMinutes(),
            sleepQuality: assessSleepQuality(),
            lastUpdated: Date()
        )
        
        todayActivity = summary
        await sendActivityToWatch(summary)
    }
    
    func executeQuickAction(_ action: WatchQuickAction) async {
        switch action.actionType {
        case .feedingReminder:
            await sendFeedingReminder()
        case .walkTime:
            await startWalkTracking()
        case .healthCheck:
            await performQuickHealthCheck()
        case .emergency:
            await triggerEmergencyProtocol()
        }
    }
    
    // MARK: - Private Methods
    
    private func setupWatchConnectivity() {
        guard WCSession.isSupported() else {
            print("⌚ WatchConnectivity not supported")
            return
        }
        
        session = WCSession.default
        session?.delegate = self
        session?.activate()
    }
    
    private func setupHealthKit() {
        guard HKHealthStore.isHealthDataAvailable() else {
            print("ℹ️ HealthKit not available on this device")
            return
        }

        // Check if HealthKit entitlements are available
        guard Bundle.main.object(forInfoDictionaryKey: "NSHealthShareUsageDescription") != nil else {
            print("ℹ️ HealthKit entitlements not configured - skipping HealthKit setup")
            return
        }
        
        let typesToRead: Set<HKObjectType> = [
            HKObjectType.quantityType(forIdentifier: .heartRate)!,
            HKObjectType.quantityType(forIdentifier: .stepCount)!,
            HKObjectType.quantityType(forIdentifier: .activeEnergyBurned)!
        ]
        
        healthStore.requestAuthorization(toShare: nil, read: typesToRead) { success, error in
            DispatchQueue.main.async {
                if success {
                    print("✅ HealthKit authorization granted")
                    self.startHealthDataMonitoring()
                } else {
                    print("❌ HealthKit authorization failed: \(error?.localizedDescription ?? "Unknown error")")
                }
            }
        }
    }
    
    private func setupQuickActions() {
        quickActions = [
            WatchQuickAction(
                id: "feeding",
                title: "Feeding Time",
                icon: "🍽️",
                actionType: .feedingReminder,
                priority: .normal
            ),
            WatchQuickAction(
                id: "walk",
                title: "Walk Time",
                icon: "🚶",
                actionType: .walkTime,
                priority: .normal
            ),
            WatchQuickAction(
                id: "health",
                title: "Health Check",
                icon: "🏥",
                actionType: .healthCheck,
                priority: .normal
            ),
            WatchQuickAction(
                id: "emergency",
                title: "Emergency",
                icon: "🚨",
                actionType: .emergency,
                priority: .high
            )
        ]
    }
    
    private func startActivityMonitoring() {
        // Monitor device motion for activity detection
        if motionManager.isDeviceMotionAvailable {
            motionManager.deviceMotionUpdateInterval = 1.0
            motionManager.startDeviceMotionUpdates(to: .main) { [weak self] motion, error in
                guard let motion = motion else { return }
                
                // Process motion data for pet activity insights
                Task {
                    await self?.processMotionData(motion)
                }
            }
        }
    }
    
    private func startHealthDataMonitoring() {
        // Start observing health data changes
        let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
        
        let query = HKObserverQuery(sampleType: heartRateType, predicate: nil) { [weak self] query, completionHandler, error in
            DispatchQueue.main.async {
                Task {
                    await self?.handleHealthDataUpdate()
                }
            }
            completionHandler()
        }
        
        healthStore.execute(query)
    }
    
    private func processMotionData(_ motion: CMDeviceMotion) async {
        // Analyze motion for pet-related activities
        let acceleration = motion.userAcceleration
        _ = motion.rotationRate // Not currently used, but available for future analysis
        
        // Detect patterns that might indicate pet activities
        let activityIntensity = sqrt(pow(acceleration.x, 2) + pow(acceleration.y, 2) + pow(acceleration.z, 2))
        
        if activityIntensity > 0.5 {
            // High activity detected - possibly walking or playing with pet
            await updateActivityData(
                steps: Int(activityIntensity * 100),
                heartRate: 75 + (activityIntensity * 20),
                calories: Int(activityIntensity * 10)
            )
        }
    }
    
    private func handleHealthDataUpdate() async {
        // Fetch latest health data and correlate with pet care
        print("📊 Health data updated - correlating with pet activities")
        
        // This would fetch actual HealthKit data
        // For demo, we'll simulate data
        await updateActivityData(
            steps: Int.random(in: 1000...8000),
            heartRate: Double.random(in: 60...100),
            calories: Int.random(in: 200...600)
        )
    }
    
    private func sendStatusToWatch(_ status: WatchPetStatus) async {
        guard let session = session, session.isReachable else { return }
        
        let message: [String: Any] = [
            "type": "petStatus",
            "petName": status.petName,
            "emoji": status.petEmoji,
            "health": status.overallHealth.rawValue,
            "description": status.briefDescription,
            "nextAction": status.nextAction,
            "timestamp": status.lastUpdated.timeIntervalSince1970
        ]
        
        session.sendMessage(message, replyHandler: nil) { error in
            print("❌ Failed to send status to watch: \(error.localizedDescription)")
        }
    }
    
    private func sendAlertToWatch(_ alert: WatchHealthAlert) async {
        guard let session = session, session.isReachable else { return }
        
        let message: [String: Any] = [
            "type": "healthAlert",
            "petName": alert.petName,
            "severity": alert.severity.rawValue,
            "message": alert.message,
            "actionRequired": alert.actionRequired,
            "timestamp": alert.timestamp.timeIntervalSince1970
        ]
        
        session.sendMessage(message, replyHandler: nil) { error in
            print("❌ Failed to send alert to watch: \(error.localizedDescription)")
        }
    }
    
    private func sendActivityToWatch(_ activity: WatchActivitySummary) async {
        guard let session = session, session.isReachable else { return }
        
        let message: [String: Any] = [
            "type": "activitySummary",
            "steps": activity.steps,
            "heartRate": activity.heartRate,
            "calories": activity.caloriesBurned,
            "activeMinutes": activity.activeMinutes,
            "sleepQuality": activity.sleepQuality.rawValue,
            "timestamp": activity.lastUpdated.timeIntervalSince1970
        ]
        
        session.sendMessage(message, replyHandler: nil) { error in
            print("❌ Failed to send activity to watch: \(error.localizedDescription)")
        }
    }
    
    private func sendEmergencyResponseToWatch(_ response: String) async {
        guard let session = session, session.isReachable else { return }
        
        let message: [String: Any] = [
            "type": "emergencyResponse",
            "response": response,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        session.sendMessage(message, replyHandler: nil) { error in
            print("❌ Failed to send emergency response to watch: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Quick Actions
    
    private func sendFeedingReminder() async {
        print("🍽️ Sending feeding reminder to watch")
        // Implementation for feeding reminder
    }
    
    private func startWalkTracking() async {
        print("🚶 Starting walk tracking on watch")
        // Implementation for walk tracking
    }
    
    private func performQuickHealthCheck() async {
        print("🏥 Performing quick health check")
        // Implementation for health check
    }
    
    private func triggerEmergencyProtocol() async {
        print("🚨 Triggering emergency protocol")
        // Implementation for emergency protocol
    }
    
    // MARK: - Helper Methods
    
    private func getPetEmoji(for species: String) -> String {
        switch species.lowercased() {
        case "dog": return "��"
        case "cat": return "🐱"
        case "bird": return "🐦"
        case "fish": return "🐠"
        case "rabbit": return "🐰"
        case "hamster": return "🐹"
        default: return "🐾"
        }
    }
    
    private func calculateActiveMinutes() -> Int {
        // Calculate based on motion data
        return Int.random(in: 30...120)
    }
    
    private func assessSleepQuality() -> SleepQuality {
        // Assess sleep quality based on activity patterns
        return .good
    }
}

// MARK: - WCSessionDelegate

@available(iOS 18.0, *)
extension AppleWatchPetService: WCSessionDelegate {
    nonisolated func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        DispatchQueue.main.async {
            self.isWatchConnected = (activationState == .activated)
            if let error = error {
                print("❌ Watch session activation failed: \(error.localizedDescription)")
            } else {
                print("✅ Watch session activated successfully")
            }
        }
    }
    
    nonisolated func sessionDidBecomeInactive(_ session: WCSession) {
        DispatchQueue.main.async {
            self.isWatchConnected = false
        }
    }
    
    nonisolated func sessionDidDeactivate(_ session: WCSession) {
        DispatchQueue.main.async {
            self.isWatchConnected = false
        }
    }
    
    nonisolated func session(_ session: WCSession, didReceiveMessage message: [String : Any], replyHandler: @escaping ([String : Any]) -> Void) {
        DispatchQueue.main.async {
            Task {
                await self.handleWatchMessage(message, replyHandler: replyHandler)
            }
        }
    }
    
    private func handleWatchMessage(_ message: [String: Any], replyHandler: @escaping ([String: Any]) -> Void) async {
        guard let messageType = message["type"] as? String else {
            replyHandler(["error": "Invalid message type"])
            return
        }
        
        switch messageType {
        case "requestPetStatus":
            if let _ = message["petId"] as? String {
                // Fetch pet and return status
                replyHandler(["status": "Pet status retrieved"])
            }
        case "quickAction":
            if let actionId = message["actionId"] as? String {
                if let action = quickActions.first(where: { $0.id == actionId }) {
                    await executeQuickAction(action)
                    replyHandler(["result": "Action executed"])
                }
            }
        case "emergencyTrigger":
            if let _ = message["emergency"] as? String,
               let _ = message["petId"] as? String {
                // Handle emergency from watch
                replyHandler(["result": "Emergency protocol activated"])
            }
        default:
            replyHandler(["error": "Unknown message type"])
        }
    }
}

// MARK: - Supporting Types

struct WatchPetStatus {
    let petName: String
    let petEmoji: String
    let overallHealth: HealthStatus
    let briefDescription: String
    let nextAction: String
    let lastUpdated: Date
    
    enum HealthStatus: String, CaseIterable {
        case excellent = "excellent"
        case good = "good"
        case fair = "fair"
        case needsAttention = "needs_attention"
        case emergency = "emergency"
        
        var emoji: String {
            switch self {
            case .excellent: return "💚"
            case .good: return "💛"
            case .fair: return "🧡"
            case .needsAttention: return "❤️"
            case .emergency: return "🚨"
            }
        }
    }
}

struct WatchActivitySummary {
    var steps: Int = 0
    var heartRate: Double = 0.0
    var caloriesBurned: Int = 0
    var activeMinutes: Int = 0
    var sleepQuality: SleepQuality = .good
    var lastUpdated: Date = Date()
}

enum SleepQuality: String, CaseIterable {
    case excellent = "excellent"
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    
    var emoji: String {
        switch self {
        case .excellent: return "😴"
        case .good: return "😊"
        case .fair: return "😐"
        case .poor: return "😟"
        }
    }
}

struct WatchHealthAlert {
    let id: UUID
    let petName: String
    let severity: AlertSeverity
    let message: String
    let timestamp: Date
    let actionRequired: Bool
    
    enum AlertSeverity: String, CaseIterable {
        case info = "info"
        case warning = "warning"
        case emergency = "emergency"
        
        var emoji: String {
            switch self {
            case .info: return "ℹ️"
            case .warning: return "⚠️"
            case .emergency: return "🚨"
            }
        }
    }
}

struct WatchQuickAction {
    let id: String
    let title: String
    let icon: String
    let actionType: ActionType
    let priority: Priority
    
    enum ActionType {
        case feedingReminder
        case walkTime
        case healthCheck
        case emergency
    }
    
    enum Priority {
        case low, normal, high
    }
} 