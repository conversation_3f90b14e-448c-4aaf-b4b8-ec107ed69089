//
//  AuthenticationService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftUI
import AuthenticationServices
import SwiftData

// MARK: - Main Authentication Service (Updated for Apple Native)
@MainActor
class AuthenticationService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = AuthenticationService()
    
    // MARK: - Properties
    @Published var isAuthenticated: Bool = false
    @Published var currentUser: User?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var authenticationState: AuthenticationState = .unknown
    
    // Native Apple services
    private let appleIDService = AppleIDAuthenticationService.shared
    private let appleDataService: AppleNativeDataService
    
    // MARK: - Types
    enum AuthenticationState {
        case unknown
        case authenticated
        case unauthenticated
        case loading
        case error(String)
    }
    
    // MARK: - Initialization
    init() {
        self.appleDataService = AppleNativeDataService.shared
        setupObservers()
        checkAuthenticationStatus()
    }
    
    // MARK: - Setup
    private func setupObservers() {
        // Observe Apple ID authentication service changes
        appleIDService.$isAuthenticated
            .receive(on: DispatchQueue.main)
            .assign(to: &$isAuthenticated)
        
        appleIDService.$authenticationState
            .receive(on: DispatchQueue.main)
            .map { appleState in
                switch appleState {
                case .unknown:
                    return .unknown
                case .authenticated:
                    return .authenticated
                case .unauthenticated:
                    return .unauthenticated
                case .authenticating:
                    return .loading
                case .error(let message):
                    return .error(message)
                }
            }
            .assign(to: &$authenticationState)
        
        appleIDService.$errorMessage
            .receive(on: DispatchQueue.main)
            .assign(to: &$errorMessage)
    }
    
    // MARK: - Apple ID Authentication
    func signInWithApple() async throws {
        isLoading = true
        errorMessage = nil
        
        // Trigger Apple ID sign-in (not async)
        appleIDService.signInWithApple()
        
        // The actual authentication happens in the delegate
        // We'll update the user when the delegate callback occurs
        isLoading = false
    }
    
    // MARK: - Legacy Authentication (redirects to Apple ID)
    func signIn(email: String, password: String) async throws {
        // For Apple ecosystem, redirect to Apple ID sign-in
        try await signInWithApple()
    }
    
    func signUp(email: String, password: String, fullName: String?) async throws {
        // For Apple ecosystem, redirect to Apple ID sign-in
        try await signInWithApple()
    }
    
    // MARK: - Sign Out
    func signOut() async {
        isLoading = true
        
        // Sign out from Apple ID service (not async)
        await appleIDService.signOut()
        
        // Clear current user
        currentUser = nil
        isAuthenticated = false
        authenticationState = .unauthenticated
        errorMessage = nil
        isLoading = false
        
        // Notify observers
        NotificationCenter.default.post(name: .authenticationStateChanged, object: nil)
    }
    
    // MARK: - User Management
    func getCurrentUser() async throws -> User? {
        if let user = currentUser {
            return user
        }
        
        // Try to fetch from Apple data service
        return try await appleDataService.fetchCurrentUser()
    }
    
    func updateUserProfile(fullName: String?, bio: String?, location: String?) async throws {
        guard let user = currentUser else {
            throw AuthenticationError.userNotFound
        }
        
        isLoading = true
        
        // Note: Current User model doesn't have bio/location fields
        // Updating only available fields for now
        if let fullName = fullName {
            user.fullName = fullName
            user.updatedAt = Date()
            currentUser = user
        }
        
        isLoading = false
    }
    
    // MARK: - Password Management
    func forgotPassword(email: String) async throws {
        // Apple ID password reset is handled through Apple's interface
        print("⚠️ Password reset for Apple ID should be done through Apple's Settings")
    }
    
    func resetPassword(token: String, newPassword: String) async throws {
        // Apple ID password reset is handled through Apple's interface
        print("⚠️ Password reset for Apple ID should be done through Apple's Settings")
    }
    
    // MARK: - Session Management
    func refreshSession() async throws {
        // Check Apple ID authentication status
        appleIDService.checkAuthenticationStatus()
    }
    
    // MARK: - User Data
    func getAppleUser() async -> User? {
        return currentUser
    }
    
    // MARK: - Helper Methods
    private func handleAuthenticationSuccess(_ user: User) {
        currentUser = user
        isAuthenticated = true
        authenticationState = .authenticated
        isLoading = false
        errorMessage = nil
        
        // Notify observers
        NotificationCenter.default.post(name: .authenticationStateChanged, object: nil)
    }
    
    private func handleAuthenticationError(_ error: Error) {
        currentUser = nil
        isAuthenticated = false
        authenticationState = .error(error.localizedDescription)
        errorMessage = error.localizedDescription
        isLoading = false
        
        // Notify observers
        NotificationCenter.default.post(name: .authenticationStateChanged, object: nil)
    }
    
    // MARK: - Auth State Handling
    func checkAuthenticationStatus() {
        appleIDService.checkAuthenticationStatus()
        
        // Update local state based on Apple ID service
        isAuthenticated = appleIDService.isAuthenticated
        authenticationState = mapAppleAuthState(appleIDService.authenticationState)
        
        if isAuthenticated, let _ = appleIDService.currentUser {
            // Convert Apple user to local user model if needed
            Task {
                do {
                    if let user = try await appleDataService.fetchCurrentUser() {
                        await MainActor.run {
                            self.currentUser = user
                        }
                    }
                } catch {
                    print("Error fetching user: \(error)")
                }
            }
        }
    }
    
    private func mapAppleAuthState(_ state: AppleIDAuthenticationService.AuthenticationState) -> AuthenticationState {
        switch state {
        case .unknown:
            return .unknown
        case .authenticated:
            return .authenticated
        case .unauthenticated:
            return .unauthenticated
        case .authenticating:
            return .loading
        case .error(let message):
            return .error(message)
        }
    }
}

// MARK: - User Information (Legacy Compatibility)
extension AuthenticationService {
    var userEmail: String? {
        return currentUser?.email
    }
    
    var userID: String? {
        return currentUser?.id
    }
    
    // MARK: - Error Types
    enum AuthenticationError: LocalizedError {
        case userNotFound
        case invalidCredentials
        case networkError
        case appleIDNotAvailable
        case migrationFailed
        
        var errorDescription: String? {
            switch self {
            case .userNotFound:
                return "User not found"
            case .invalidCredentials:
                return "Invalid credentials"
            case .networkError:
                return "Network error occurred"
            case .appleIDNotAvailable:
                return "Apple ID authentication is not available"
            case .migrationFailed:
                return "Failed to migrate user data"
            }
        }
    }
    
    // MARK: - Device Info
    var deviceName: String {
        return UIDevice.current.name
    }
    
    var systemVersion: String {
        return UIDevice.current.systemVersion
    }
    
    var appVersion: String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    }
}

// MARK: - SwiftUI Environment
extension AuthenticationService {
    
    // MARK: - Preview Support
    static let previewService: AuthenticationService = {
        let service = AuthenticationService()
        service.isAuthenticated = true
        service.currentUser = User(
            id: UUID().uuidString,
            email: "<EMAIL>", 
            displayName: "Preview User",
            fullName: "Preview User"
        )
        return service
    }()
}

// MARK: - Notification Methods
extension AuthenticationService {
    
    func registerForNotifications() async {
        guard currentUser != nil else { return }
        
        // Register device token with Apple data service
        if let deviceToken = await getDeviceToken() {
            // TODO: Implement device token registration when the method is added back
            print("Device token received: \(deviceToken)")
            /* 
            do {
                try await appleDataService.registerDeviceToken(
                    deviceToken,
                    deviceType: "ios",
                    deviceName: deviceName,
                    appVersion: appVersion,
                    platform: "ios",
                    for: user
                )
            } catch {
                print("Failed to register device token: \(error)")
            }
            */
        }
    }
    
    private func getDeviceToken() async -> String? {
        // This would get the actual device token from APNs
        // For now, return a placeholder
        return "device_token_placeholder"
    }
}

// MARK: - Migration Support
extension AuthenticationService {
    
    func migrateFromLegacy() async throws {
        // This would handle migration from legacy systems to Apple native
        // For now, since we're building new, this is not needed
        print("Migration from legacy systems not needed - building fresh Apple native system")
    }
    
    func hasMigrationData() -> Bool {
        // Check if there's any legacy data to migrate
        return false
    }
}

// MARK: - Account Management
extension AuthenticationService {
    
    // MARK: - Account Management
    func deleteUserAccount() async throws -> Bool {
        // TODO: Implement user account deletion with Apple native backend
        print("⚠️ Account deletion not yet implemented in Apple native migration")
        return false
    }
    
    func updateUserPassword(currentPassword: String, newPassword: String) async throws -> Bool {
        // TODO: Apple ID password changes are handled through Apple's Settings
        print("⚠️ Password updates handled through Apple ID Settings")
        return false
    }
}
