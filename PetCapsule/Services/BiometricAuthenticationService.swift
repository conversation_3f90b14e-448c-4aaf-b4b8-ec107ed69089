//
//  BiometricAuthenticationService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import LocalAuthentication
import SwiftUI

/// Biometric authentication service following Apple's security guidelines
class BiometricAuthenticationService: ObservableObject {

    // MARK: - Published Properties

    @Published var biometricType: BiometricType = .none
    @Published var isAvailable = false
    @Published var errorMessage: String?

    // MARK: - Biometric Types

    enum BiometricType {
        case none
        case touchID
        case faceID
        case opticID

        var displayName: String {
            switch self {
            case .none:
                return "None"
            case .touchID:
                return "Touch ID"
            case .faceID:
                return "Face ID"
            case .opticID:
                return "Optic ID"
            }
        }

        var icon: String {
            switch self {
            case .none:
                return "lock"
            case .touchID:
                return "touchid"
            case .faceID:
                return "faceid"
            case .opticID:
                return "opticid"
            }
        }
    }

    // MARK: - Initialization

    init() {
        checkBiometricAvailability()
    }

    // MARK: - Availability Check

    func checkBiometricAvailability() {
        let context = LAContext()
        var error: NSError?

        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            isAvailable = true

            switch context.biometryType {
            case .none:
                biometricType = .none
                isAvailable = false
            case .touchID:
                biometricType = .touchID
            case .faceID:
                biometricType = .faceID
            case .opticID:
                biometricType = .opticID
            @unknown default:
                biometricType = .none
                isAvailable = false
            }
        } else {
            isAvailable = false
            biometricType = .none

            // Only handle the error if it's not a normal "not enrolled" case
            if let error = error, let laError = error as? LAError {
                switch laError.code {
                case .biometryNotAvailable, .biometryNotEnrolled:
                    // These are normal states - don't log as errors
                    break
                default:
                    handleBiometricError(error)
                }
            }
        }

        // Only print successful availability, not normal unavailable states
        if isAvailable {
            print("✅ Biometric availability: \(biometricType.displayName) is available")
        }
    }

    // MARK: - Static Availability Check

    static func isBiometricAvailable() -> Bool {
        let context = LAContext()
        var error: NSError?
        return context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
    }

    // MARK: - Authentication

    func authenticateUser(reason: String = "Authenticate to access your pet data securely") async -> Bool {
        let context = LAContext()
        context.localizedCancelTitle = "Use Passcode"
        context.localizedFallbackTitle = "Use Passcode"

        do {
            let success = try await context.evaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics,
                localizedReason: reason
            )

            if success {
                print("✅ Biometric authentication successful")
                await MainActor.run {
                    errorMessage = nil
                }
                return true
            } else {
                print("❌ Biometric authentication failed")
                return false
            }

        } catch {
            print("❌ Biometric authentication error: \(error)")
            await MainActor.run {
                handleBiometricError(error)
            }
            return false
        }
    }

    // MARK: - Passcode Fallback

    func authenticateWithPasscode(reason: String = "Enter your device passcode to access your pet data") async -> Bool {
        let context = LAContext()
        context.localizedCancelTitle = "Cancel"

        do {
            let success = try await context.evaluatePolicy(
                .deviceOwnerAuthentication,
                localizedReason: reason
            )

            if success {
                print("✅ Passcode authentication successful")
                await MainActor.run {
                    errorMessage = nil
                }
                return true
            } else {
                print("❌ Passcode authentication failed")
                return false
            }

        } catch {
            print("❌ Passcode authentication error: \(error)")
            await MainActor.run {
                handleBiometricError(error)
            }
            return false
        }
    }

    // MARK: - Settings Check

    func isBiometricAuthenticationEnabled() async -> Bool {
        let keychain = KeychainService()
        return keychain.isBiometricAuthenticationEnabled()
    }

    func setBiometricAuthenticationEnabled(_ enabled: Bool) {
        let keychain = KeychainService()
        keychain.setBiometricAuthenticationEnabled(enabled)
        print("✅ Biometric authentication \(enabled ? "enabled" : "disabled")")
    }

    // MARK: - Error Handling

    private func handleBiometricError(_ error: Error) {
        if let laError = error as? LAError {
            switch laError.code {
            case .biometryNotAvailable:
                errorMessage = "\(biometricType.displayName) is not available on this device"
                // Don't print error for normal simulator behavior
                if biometricType != .none {
                    print("ℹ️ Biometric info: \(biometricType.displayName) is not available on this device")
                }
            case .biometryNotEnrolled:
                errorMessage = "\(biometricType.displayName) is not set up. Please set it up in Settings"
                // Don't print anything for normal unenrolled state - it's not an error
            case .biometryLockout:
                errorMessage = "\(biometricType.displayName) is locked. Please use your passcode"
                print("⚠️ Biometric warning: \(biometricType.displayName) is locked")
            case .userCancel:
                errorMessage = "Authentication was cancelled"
                print("ℹ️ Biometric info: Authentication was cancelled by user")
            case .userFallback:
                errorMessage = "User chose to use passcode"
                print("ℹ️ Biometric info: User chose to use passcode")
            case .systemCancel:
                errorMessage = "Authentication was cancelled by the system"
                print("ℹ️ Biometric info: Authentication was cancelled by the system")
            case .passcodeNotSet:
                errorMessage = "Passcode is not set on this device"
                print("⚠️ Biometric warning: Passcode is not set on this device")
            case .invalidContext:
                errorMessage = "Authentication context is invalid"
            case .notInteractive:
                errorMessage = "Authentication is not interactive"
            default:
                errorMessage = "Authentication failed: \(laError.localizedDescription)"
            }
        } else {
            errorMessage = "Authentication failed: \(error.localizedDescription)"
        }

        // Only print actual errors, not normal states like "not enrolled"
        if let laError = error as? LAError {
            switch laError.code {
            case .biometryNotAvailable, .biometryNotEnrolled:
                // These are normal states, not errors - don't log them
                break
            default:
                print("❌ Biometric error: \(errorMessage ?? "Unknown error")")
            }
        } else {
            print("❌ Biometric error: \(errorMessage ?? "Unknown error")")
        }
    }

    // MARK: - Privacy and Security Compliance

    /// Returns privacy-compliant biometric status for display
    func getPrivacyCompliantStatus() -> String {
        switch biometricType {
        case .none:
            return "Device authentication not available"
        case .touchID:
            return "Touch ID available for secure authentication"
        case .faceID:
            return "Face ID available for secure authentication"
        case .opticID:
            return "Optic ID available for secure authentication"
        }
    }

    /// Returns user-friendly setup instructions
    func getSetupInstructions() -> String {
        switch biometricType {
        case .none:
            return "Set up a passcode in Settings to secure your pet data"
        case .touchID:
            return "Enable Touch ID in Settings > Touch ID & Passcode for quick access"
        case .faceID:
            return "Enable Face ID in Settings > Face ID & Passcode for quick access"
        case .opticID:
            return "Enable Optic ID in Settings > Optic ID & Passcode for quick access"
        }
    }
}

// MARK: - SwiftUI Integration

extension BiometricAuthenticationService {

    /// SwiftUI view modifier for biometric authentication
    func biometricAuthenticationModifier<Content: View>(
        @ViewBuilder content: @escaping () -> Content,
        onSuccess: @escaping () -> Void,
        onFailure: @escaping (String) -> Void
    ) -> some View {
        content()
            .onAppear { [self] in
                Task {
                    let success = await self.authenticateUser()
                    await MainActor.run {
                        if success {
                            onSuccess()
                        } else {
                            onFailure(self.errorMessage ?? "Authentication failed")
                        }
                    }
                }
            }
    }
}

// MARK: - Privacy Compliance

extension BiometricAuthenticationService {

    /// Privacy-compliant biometric data handling
    /// Note: This service never stores or transmits biometric data
    /// All biometric operations are handled by the Secure Enclave
    func getPrivacyStatement() -> String {
        return """
        PetTime Capsule uses your device's built-in security features like \(biometricType.displayName)
        to protect your pet data. Your biometric information never leaves your device and is processed
        entirely within Apple's Secure Enclave. We cannot access, store, or transmit your biometric data.
        """
    }

    /// GDPR/CCPA compliant data usage description
    func getDataUsageDescription() -> String {
        return """
        Biometric authentication is used solely for securing access to your pet data on this device.
        No biometric data is collected, stored, or shared with PetTime Capsule or any third parties.
        You can disable biometric authentication at any time in the app settings.
        """
    }
}
