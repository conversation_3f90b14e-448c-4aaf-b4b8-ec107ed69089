//
//  CommunityEventsService.swift
//  PetCapsule
//
//  Community walk events system with creation, management, RSVP, and environmental integration
//
import Foundation
import CoreLocation
import UserNotifications
import SwiftUI
// Type aliases to resolve conflicts - EventType is now renamed to CommunityEventType
// EventType and SkillLevel are defined in this file, not as nested types
import Combine
@MainActor
class CommunityEventsService: ObservableObject {
    static let shared = CommunityEventsService()
    // Published properties
    @Published var events: [CommunityEvent] = []
    @Published var myEvents: [CommunityEvent] = []
    @Published var rsvpedEvents: [CommunityEvent] = []
    @Published var nearbyEvents: [CommunityEvent] = []
    @Published var isLoading = false
    @Published var lastUpdateTime: Date?
    // Services
    private let dataService = AppleNativeDataService.shared
    private let locationManager = CLLocationManager()
    private let weatherService = AppleWeatherService.shared
    private let notificationService = UNUserNotificationCenter.current()
    // Real-time monitoring
    private var eventsUpdateTimer: Timer?
    // Disabled deprecated RealtimeChannel - would use RealtimeChannelV2 in real implementation
    // private var realtimeSubscription: RealtimeChannelV2?
    // Configuration
    private let nearbyRadius: Double = 25000 // 25km
    private let updateInterval: TimeInterval = 300 // 5 minutes
    // Current location for nearby event discovery
    var currentLocation: CLLocation? {
        return locationManager.location
    }
    private init() {
        // setupRealtimeSubscription() // Disabled until migration to RealtimeChannelV2
        loadEvents()
        startEventMonitoring()
    }
    // MARK: - Event Creation
    func createEvent(
        title: String,
        description: String,
        eventType: CommunityEventType,
        location: CLLocationCoordinate2D,
        locationName: String,
        address: String,
        eventDate: Date,
        duration: TimeInterval,
        maxParticipants: Int?,
        petRequirements: [PetRequirement],
        ageRestrictions: AgeRestriction,
        skillLevel: SkillLevel,
        tags: [String],
        isRecurring: Bool = false,
        recurrencePattern: RecurrencePattern? = nil
    ) async throws -> CommunityEvent {
        // Check environmental conditions for the event
        let environmentalScore = await checkEnvironmentalSuitability(
            location: location,
            date: eventDate
        )
        let event = CommunityEvent(
            title: title,
            description: description,
            date: eventDate,
            location: locationName,
            organizerID: getCurrentUserId(),
            attendeeIDs: [getCurrentUserId()], // Organizer is automatically a participant
            latitude: location.latitude,
            longitude: location.longitude
        )
        // Save to database
        try await saveEventToDatabase(event)
        // Add to local arrays
        events.append(event)
        myEvents.append(event)
        // Auto-RSVP organizer
        try await rsvpToEvent(event.id.uuidString, status: .attending, petInfo: nil)
        // Schedule notifications for interested users
        await notifyNearbyUsers(about: event)
        print("✅ Community event created: \(title)")
        return event
    }
    // MARK: - Event Management
    func updateEvent(_ eventId: String, updates: EventUpdates) async throws {
        guard let index = events.firstIndex(where: { $0.id.uuidString == eventId }) else {
            throw EventError.eventNotFound
        }
        let originalEvent = events[index]
        // Create updated event using correct CommunityEvent constructor
        let event = CommunityEvent(
            title: updates.title ?? originalEvent.title,
            description: updates.description ?? originalEvent.description,
            date: updates.date ?? originalEvent.date,
            location: updates.location ?? originalEvent.location,
            organizerID: originalEvent.organizerID,
            attendeeIDs: originalEvent.attendeeIDs,
            latitude: originalEvent.latitude,
            longitude: originalEvent.longitude
        )
        // Update in arrays
        events[index] = event
        if let myIndex = myEvents.firstIndex(where: { $0.id.uuidString == eventId }) {
            myEvents[myIndex] = event
        }
        // Save to database
        try await saveEventToDatabase(event)
        // Notify participants of changes
        await notifyParticipantsOfUpdate(event)
        print("✅ Event updated: \(eventId)")
    }
    func cancelEvent(_ eventId: String, reason: String) async throws {
        try await updateEvent(eventId, updates: EventUpdates(
            eventId: eventId,
            title: nil,
            description: nil,
            date: nil,
            location: nil,
            updatedAt: Date()
        ))
        // Notify all participants
        if let event = events.first(where: { $0.id.uuidString == eventId }) {
            await notifyEventCancellation(event, reason: reason)
        }
    }
    // MARK: - RSVP Management
    func rsvpToEvent(
        _ eventId: String,
        status: RSVPStatus,
        petInfo: EventPetInfo?
    ) async throws {
        guard let eventIndex = events.firstIndex(where: { $0.id.uuidString == eventId }) else {
            throw EventError.eventNotFound
        }
        let rsvp = EventRSVP(
            eventId: eventId,
            userId: getCurrentUserId(),
            status: status,
            createdAt: Date(),
            notes: nil
        )
        // Save RSVP to database
        try await saveRSVPToDatabase(rsvp)
        // Update participant count by creating new event
        let originalEvent = events[eventIndex]
        let newParticipantCount: Int
        switch status {
        case .attending:
            newParticipantCount = originalEvent.attendeeIDs.count + 1
        case .notAttending:
            newParticipantCount = max(0, originalEvent.attendeeIDs.count - 1)
            rsvpedEvents.removeAll { $0.id.uuidString == eventId }
        case .maybe:
            // Maybe doesn't count toward participant limit
            newParticipantCount = originalEvent.attendeeIDs.count
        }
        let event = CommunityEvent(
            title: originalEvent.title,
            description: originalEvent.description,
            date: originalEvent.date,
            location: originalEvent.location,
            organizerID: originalEvent.organizerID,
            attendeeIDs: originalEvent.attendeeIDs,
            latitude: originalEvent.latitude,
            longitude: originalEvent.longitude
        )
        events[eventIndex] = event
        if status == .attending && !rsvpedEvents.contains(where: { $0.id.uuidString == eventId }) {
            rsvpedEvents.append(event)
        }
        // Send confirmation notification
        await sendRSVPConfirmation(event: event, status: status)
        print("✅ RSVP updated for event: \(eventId)")
    }
    func checkInToEvent(_ eventId: String) async throws {
        // Mark user as checked in to the event
        print("Checking in to event with Apple native services: \(eventId)")
        print("✅ Checked in to event: \(eventId)")
    }
    // MARK: - Event Discovery
    func loadNearbyEvents() async {
        // Build-time check - service only exists if feature is compiled in
        guard let currentLocation = currentLocation else {
            print("❌ No current location for nearby events")
            return
        }
        isLoading = true
        defer { isLoading = false }
        do {
            print("Loading nearby events with Apple native services")
            print("User location: \(currentLocation.coordinate.latitude), \(currentLocation.coordinate.longitude)")
            print("Radius: \(nearbyRadius) meters")
            // For now, return filtered existing events based on proximity
            // TODO: Implement proper geolocation filtering with Apple services
            nearbyEvents = events.filter { event in
                // Simple proximity check - in a real implementation, 
                // you'd calculate distance properly
                return true // Return all events for now
            }
            lastUpdateTime = Date()
            print("✅ Loaded \(nearbyEvents.count) nearby events")
        } catch {
            print("❌ Failed to load nearby events: \(error)")
        }
    }
    func searchEvents(
        query: String? = nil,
        eventType: CommunityEventType? = nil,
        dateRange: DateInterval? = nil,
        location: CLLocationCoordinate2D? = nil,
        radius: Double? = nil
    ) async -> [CommunityEvent] {
        // Build search parameters
        var searchParams: [String: String] = [:]
        if let query = query, !query.isEmpty {
            searchParams["search_query"] = query
        }
        if let eventType = eventType {
            searchParams["event_type"] = eventType.rawValue
        }
        if let dateRange = dateRange {
            searchParams["start_date"] = ISO8601DateFormatter().string(from: dateRange.start)
            searchParams["end_date"] = ISO8601DateFormatter().string(from: dateRange.end)
        }
        if let location = location {
            searchParams["lat"] = String(location.latitude)
            searchParams["lng"] = String(location.longitude)
            searchParams["radius"] = String(radius ?? nearbyRadius)
        }
        do {
            print("Searching events with Apple native services")
            print("Search query: \(query), params: \(searchParams)")
            // Filter existing events based on search criteria
            let filteredEvents = events.filter { event in
                event.title.localizedCaseInsensitiveContains(query ?? "") ||
                (event.description?.localizedCaseInsensitiveContains(query ?? "") ?? false) ||
                event.location.localizedCaseInsensitiveContains(query ?? "")
            }
            return filteredEvents
        } catch {
            print("❌ Failed to search events: \(error)")
            return []
        }
    }
    // MARK: - Environmental Integration
    private func checkEnvironmentalSuitability(
        location: CLLocationCoordinate2D,
        date: Date
    ) async -> Double {
        do {
            // Get weather forecast for the event date
            let weather = try await weatherService.getCurrentWeather(for: location)
            let airQuality = try await weatherService.getAirQuality(for: location)
            let pollen = try await weatherService.getPollenData(for: location)
            var score = 1.0
            // Weather scoring
            if weather.temperature < 32 || weather.temperature > 90 {
                score *= 0.6 // Extreme temperatures
            } else if weather.temperature < 45 || weather.temperature > 80 {
                score *= 0.8 // Uncomfortable temperatures
            }
            // Air quality scoring
            if airQuality.index > 150 {
                score *= 0.4 // Unhealthy air quality
            } else if airQuality.index > 100 {
                score *= 0.7 // Moderate air quality
            }
            // Pollen scoring
            let totalPollen = pollen.treeIndex + pollen.grassIndex + pollen.weedIndex
            if totalPollen > 20 {
                score *= 0.7 // High pollen
            } else if totalPollen > 15 {
                score *= 0.9 // Moderate pollen
            }
            // Weather condition scoring
            if weather.condition.lowercased().contains("storm") ||
               weather.condition.lowercased().contains("severe") {
                score *= 0.2 // Severe weather
            } else if weather.condition.lowercased().contains("rain") {
                score *= 0.6 // Rain
            }
            return max(0.1, min(1.0, score))
        } catch {
            print("❌ Failed to check environmental conditions: \(error)")
            return 0.5 // Default neutral score
        }
    }
    // MARK: - Real-time Monitoring
    private func startEventMonitoring() {
        eventsUpdateTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.loadNearbyEvents()
                await self?.checkEventReminders()
            }
        }
    }
    private func checkEventReminders() async {
        let now = Date()
        let reminderThreshold = now.addingTimeInterval(3600) // 1 hour from now
        for event in rsvpedEvents {
            if event.date <= reminderThreshold && event.date > now {
                await sendEventReminder(event)
            }
        }
    }
    // MARK: - Notifications
    private func notifyNearbyUsers(about event: CommunityEvent) async {
        // In a real implementation, this would query for users near the event location
        // and send them notifications about the new event
        print("📢 Notifying nearby users about new event: \(event.title)")
    }
    private func notifyParticipantsOfUpdate(_ event: CommunityEvent) async {
        // Notify all participants about event updates
        print("📢 Notifying participants of event update: \(event.title)")
    }
    private func notifyEventCancellation(_ event: CommunityEvent, reason: String) async {
        // Notify all participants about event cancellation
        print("📢 Notifying participants of event cancellation: \(event.title)")
    }
    private func sendRSVPConfirmation(event: CommunityEvent, status: RSVPStatus) async {
        let content = UNMutableNotificationContent()
        content.title = "RSVP Confirmed"
        content.body = "You're \(status.rawValue.lowercased()) for \(event.title)"
        content.sound = .default
        content.categoryIdentifier = "EVENT_RSVP"
        let request = UNNotificationRequest(
            identifier: "rsvp_\(event.id)_\(Date().timeIntervalSince1970)",
            content: content,
            trigger: nil
        )
        do {
            try await notificationService.add(request)
            print("✅ RSVP confirmation sent")
        } catch {
            print("❌ Failed to send RSVP confirmation: \(error)")
        }
    }
    private func sendEventReminder(_ event: CommunityEvent) async {
        let content = UNMutableNotificationContent()
        content.title = "Event Starting Soon"
        content.body = "\(event.title) starts in 1 hour at \(event.location)"
        content.sound = .default
        content.categoryIdentifier = "EVENT_REMINDER"
        let request = UNNotificationRequest(
            identifier: "reminder_\(event.id)",
            content: content,
            trigger: nil
        )
        do {
            try await notificationService.add(request)
            print("✅ Event reminder sent")
        } catch {
            print("❌ Failed to send event reminder: \(error)")
        }
    }
    // MARK: - Real-time Subscription
    private func setupRealtimeSubscription() {
        print("Setting up community events realtime subscription")
        // This is a placeholder for the updated realtime subscription
        print("Realtime subscription setup completed")
    }
    // MARK: - Data Persistence
    private func loadEvents() {
        Task {
            await loadNearbyEvents()
            await loadMyEvents()
            await loadRSVPedEvents()
        }
    }
    private func loadMyEvents() async {
        // Load events created by current user
        do {
            print("Loading my events with Apple native services")
            let currentUserId = getCurrentUserId()
            myEvents = events.filter { $0.organizerID == currentUserId }
                .sorted { $0.date < $1.date }
        } catch {
            print("❌ Failed to load my events: \(error)")
        }
    }
    private func loadRSVPedEvents() async {
        // Load events user has RSVP'd to
        do {
            print("Loading RSVP'd events with Apple native services")
            // For now, return empty array - TODO: implement proper RSVP tracking
            rsvpedEvents = []
        } catch {
            print("❌ Failed to load RSVP'd events: \(error)")
        }
    }
    private func saveEventToDatabase(_ event: CommunityEvent) async throws {
        print("Saving event with Apple native services: \(event.title)")
        // TODO: Implement proper event saving with Apple services
    }
    private func saveRSVPToDatabase(_ rsvp: EventRSVP) async throws {
        print("Saving RSVP with Apple native services")
        // TODO: Implement proper RSVP saving with Apple services
    }
    // MARK: - Helper Methods
    private func getCurrentUserId() -> String {
        // Get current user ID from auth service
        return "current_user_id" // Placeholder
    }
    // MARK: - Public API
    func getEventById(_ eventId: String) -> CommunityEvent? {
        return events.first { $0.id.uuidString == eventId }
    }
    func getEventsForDate(_ date: Date) -> [CommunityEvent] {
        let calendar = Calendar.current
        return events.filter { calendar.isDate($0.date, inSameDayAs: date) }
    }
    func getUpcomingEvents(limit: Int = 10) -> [CommunityEvent] {
        return Array(events
            .filter { $0.date > Date() }
            .sorted { $0.date < $1.date }
            .prefix(limit))
    }
}
// MARK: - Enhanced Data Models
// Using CommunityEvent from PlannerAppleNativeDataService to avoid conflicts
enum CommunityEventType: String, CaseIterable, Codable {
    case groupWalk = "group_walk"
    case trainingSession = "training_session"
    case playdate = "playdate"
    case meetup = "meetup"
    case competition = "competition"
    case socializing = "socializing"
    case hiking = "hiking"
    case beachWalk = "beach_walk"
    case parkVisit = "park_visit"
    case charity = "charity"
    var displayName: String {
        switch self {
        case .groupWalk: return "Group Walk"
        case .trainingSession: return "Training Session"
        case .playdate: return "Playdate"
        case .meetup: return "Meetup"
        case .competition: return "Competition"
        case .socializing: return "Socializing"
        case .hiking: return "Hiking"
        case .beachWalk: return "Beach Walk"
        case .parkVisit: return "Park Visit"
        case .charity: return "Charity Event"
        }
    }
    var icon: String {
        switch self {
        case .groupWalk: return "figure.walk"
        case .trainingSession: return "graduationcap"
        case .playdate: return "gamecontroller"
        case .meetup: return "person.2"
        case .competition: return "trophy"
        case .socializing: return "heart.circle"
        case .hiking: return "mountain.2"
        case .beachWalk: return "beach.umbrella"
        case .parkVisit: return "tree"
        case .charity: return "heart.fill"
        }
    }
    var color: Color {
        switch self {
        case .groupWalk: return .blue
        case .trainingSession: return .purple
        case .playdate: return .orange
        case .meetup: return .green
        case .competition: return .red
        case .socializing: return .pink
        case .hiking: return .brown
        case .beachWalk: return .cyan
        case .parkVisit: return .green
        case .charity: return .red
        }
    }
}
enum EventStatus: String, CaseIterable, Codable {
    case draft = "draft"
    case active = "active"
    case cancelled = "cancelled"
    case completed = "completed"
    case postponed = "postponed"
    var displayName: String {
        rawValue.capitalized
    }
    var color: Color {
        switch self {
        case .draft: return .gray
        case .active: return .green
        case .cancelled: return .red
        case .completed: return .blue
        case .postponed: return .orange
        }
    }
}
enum PetRequirement: String, CaseIterable, Codable {
    case vaccinated = "vaccinated"
    case leashed = "leashed"
    case friendly = "friendly"
    case smallDogsOnly = "small_dogs_only"
    case largeDogsOnly = "large_dogs_only"
    case puppiesWelcome = "puppies_welcome"
    case seniorDogsWelcome = "senior_dogs_welcome"
    case wellTrained = "well_trained"
    case noAggression = "no_aggression"
    var displayName: String {
        switch self {
        case .vaccinated: return "Vaccinated"
        case .leashed: return "Must be leashed"
        case .friendly: return "Friendly with other pets"
        case .smallDogsOnly: return "Small dogs only"
        case .largeDogsOnly: return "Large dogs only"
        case .puppiesWelcome: return "Puppies welcome"
        case .seniorDogsWelcome: return "Senior dogs welcome"
        case .wellTrained: return "Well-trained"
        case .noAggression: return "No aggressive behavior"
        }
    }
    var icon: String {
        switch self {
        case .vaccinated: return "syringe"
        case .leashed: return "link"
        case .friendly: return "heart"
        case .smallDogsOnly: return "pawprint"
        case .largeDogsOnly: return "pawprint.fill"
        case .puppiesWelcome: return "heart.circle"
        case .seniorDogsWelcome: return "heart.circle.fill"
        case .wellTrained: return "graduationcap"
        case .noAggression: return "hand.raised"
        }
    }
}
enum AgeRestriction: String, CaseIterable, Codable {
    case allAges = "all_ages"
    case adultDogsOnly = "adult_dogs_only"
    case puppiesOnly = "puppies_only"
    case seniorDogsOnly = "senior_dogs_only"
    var displayName: String {
        switch self {
        case .allAges: return "All ages welcome"
        case .adultDogsOnly: return "Adult dogs only"
        case .puppiesOnly: return "Puppies only"
        case .seniorDogsOnly: return "Senior dogs only"
        }
    }
}
// SkillLevel is now defined in SharedTypes.swift
enum RecurrencePattern: String, CaseIterable, Codable {
    case daily = "daily"
    case weekly = "weekly"
    case biweekly = "biweekly"
    case monthly = "monthly"
    case custom = "custom"
    var displayName: String {
        switch self {
        case .daily: return "Daily"
        case .weekly: return "Weekly"
        case .biweekly: return "Every 2 weeks"
        case .monthly: return "Monthly"
        case .custom: return "Custom"
        }
    }
}
struct CommunityEventRequest: Codable {
    let id: String?
    let organizer_id: String
    let title: String
    let description: String
    let event_type: String
    let latitude: Double
    let longitude: Double
    let location_name: String
    let address: String
    let event_date: String
    let duration_minutes: Int
    let max_participants: Int?
    let current_participants: Int
    let registration_required: Bool
    let registration_deadline: String?
    let pet_requirements: [String]
    let age_restrictions: String
    let skill_level: String
    let is_recurring: Bool
    let recurrence_pattern: String?
    let tags: [String]
    let status: String
    let is_featured: Bool
    let weather_dependent: Bool
    let min_weather_score: Double
    let environmental_score: Double
    let cancellation_reason: String?
    init(from event: CommunityEvent) {
        self.id = event.id.uuidString
        self.organizer_id = event.organizerID
        self.title = event.title
        self.description = event.description ?? ""
        self.event_type = "group_walk"
        self.latitude = 0.0
        self.longitude = 0.0
        self.location_name = ""
        self.address = ""
        self.event_date = ISO8601DateFormatter().string(from: event.date)
        self.duration_minutes = 60 // Default 1 hour
        self.max_participants = 20 // Default max
        self.current_participants = event.attendeeIDs.count
        self.registration_required = false
        self.registration_deadline = nil
        self.pet_requirements = []
        self.age_restrictions = "all_ages"
        self.skill_level = "all_levels"
        self.is_recurring = false
        self.recurrence_pattern = nil
        self.tags = []
        self.status = "active" // Default status
        self.is_featured = false
        self.weather_dependent = false
        self.min_weather_score = 0.5
        self.environmental_score = 0.5
        self.cancellation_reason = nil
    }
}
struct CommunityEventResponse: Codable {
    let id: String
    let organizer_id: String
    let title: String
    let description: String
    let event_type: String
    let latitude: Double
    let longitude: Double
    let location_name: String
    let address: String
    let event_date: String
    let duration_minutes: Int
    let max_participants: Int?
    let current_participants: Int
    let registration_required: Bool
    let registration_deadline: String?
    let pet_requirements: [String]
    let age_restrictions: String
    let skill_level: String
    let is_recurring: Bool
    let recurrence_pattern: String?
    let tags: [String]
    let status: String
    let is_featured: Bool
    let weather_dependent: Bool
    let min_weather_score: Double
    let environmental_score: Double
    let cancellation_reason: String?
    let created_at: String
    let updated_at: String
    let distance_meters: Double?
    func toCommunityEvent() -> CommunityEvent {
        let dateFormatter = ISO8601DateFormatter()
        // Break down complex expressions
        let parsedEventDate = dateFormatter.date(from: event_date) ?? Date()
        let parsedEventType = event_type
        var communityEvent = CommunityEvent()
        communityEvent.title = title
        communityEvent.description = description
        communityEvent.location = location_name.isEmpty ? address : location_name
        communityEvent.organizerID = organizer_id
        communityEvent.latitude = latitude
        communityEvent.longitude = longitude
        communityEvent.date = parsedEventDate
        return communityEvent
    }
}
struct EventRSVPRequest: Codable {
    let id: String?
    let event_id: String
    let user_id: String
    let pet_id: String?
    let pet_name: String?
    let pet_breed: String?
    let pet_age_months: Int?
    let special_notes: String?
    let status: String
    let rsvp_date: String
    let checked_in: Bool
    let check_in_time: String?
    let notes: String?
    init(from rsvp: EventRSVP) {
        self.id = rsvp.id.uuidString 
        self.event_id = rsvp.eventId
        self.user_id = rsvp.userId
        self.pet_id = nil // No pet info in simplified model
        self.pet_name = nil
        self.pet_breed = nil
        self.pet_age_months = nil
        self.special_notes = nil
        self.status = rsvp.status.rawValue
        self.rsvp_date = ISO8601DateFormatter().string(from: rsvp.createdAt)
        self.checked_in = false // Default value
        self.check_in_time = nil
        self.notes = rsvp.notes
    }
}
struct EventRSVPResponse: Codable {
    let id: String
    let event_id: String
    let user_id: String
    let pet_id: String?
    let pet_name: String?
    let pet_breed: String?
    let pet_age_months: Int?
    let special_notes: String?
    let status: String
    let rsvp_date: String
    let checked_in: Bool
    let check_in_time: String?
    let notes: String?
    let event: CommunityEventResponse?
}
struct EventUpdates: Codable {
    let eventId: String
    let title: String?
    let description: String?
    let date: Date?
    let location: String?
    let updatedAt: Date
}
// MARK: - Error Types
enum EventError: Error, LocalizedError {
    case eventNotFound
    case registrationClosed
    case eventFull
    case alreadyRegistered
    case invalidEventData
    var errorDescription: String? {
        switch self {
        case .eventNotFound:
            return "Event not found"
        case .registrationClosed:
            return "Registration is closed for this event"
        case .eventFull:
            return "Event is full"
        case .alreadyRegistered:
            return "Already registered for this event"
        case .invalidEventData:
            return "Invalid event data"
        }
    }
}
// MARK: - Missing Types
struct EventPetInfo: Codable {
    let petId: String
    let petName: String
    let petBreed: String?
    let ownerName: String
}
struct EventRSVP: Identifiable, Codable {
    let id = UUID()
    let eventId: String
    let userId: String
    let status: RSVPStatus
    let createdAt: Date
    let notes: String?
}
