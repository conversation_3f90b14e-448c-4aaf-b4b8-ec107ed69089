//
//  ComprehensiveHealthMonitoringService.swift
//  PetCapsule
//
//  Comprehensive health monitoring with symptoms, medications, and vet scheduling
//
import Foundation
import SwiftUI
import UserNotifications
import Combine
// Type alias to resolve ambiguity
typealias MonitoringAlertSeverity = SharedAlertSeverity
@MainActor
class ComprehensiveHealthMonitoringService: ObservableObject {
    static let shared = ComprehensiveHealthMonitoringService()
    // Published properties
    @Published var healthRecords: [HealthMonitoringRecord] = []
    @Published var symptoms: [SymptomRecord] = []
    @Published var medications: [MedicationRecord] = []
    @Published var vetAppointments: [VetAppointment] = []
    @Published var healthAlerts: [HealthAlert] = []
    @Published var isLoading = false
    // Services
    private let dataService = AppleNativeDataService.shared
    private let calendarService = PetCalendarService.shared
    private let notificationService = UNUserNotificationCenter.current()
    // Real-time monitoring
    private var healthMonitoringTimer: Timer?
    private var medicationReminderTimer: Timer?
    private init() {
        setupNotificationCategories()
        loadHealthData()
        startHealthMonitoring()
    }
    // MARK: - Health Records Management
    func addHealthRecord(
        petId: String,
        type: MonitoringHealthRecordType,
        value: Double,
        unit: String,
        notes: String? = nil,
        date: Date = Date()
    ) async throws {
        let record = HealthMonitoringRecord(
            id: UUID().uuidString,
            petId: petId,
            type: type,
            value: value,
            unit: unit,
            notes: notes,
            date: date,
            recordedBy: getCurrentUserId(),
            isVerified: false
        )
        // Save to local array
        healthRecords.append(record)
        // Save to database
        try await saveHealthRecordToDatabase(record)
        // Check for health alerts
        await checkHealthAlerts(for: record)
        print("✅ Health record added: \(type.displayName)")
    }
    func updateHealthRecord(_ record: HealthMonitoringRecord) async throws {
        if let index = healthRecords.firstIndex(where: { $0.id == record.id }) {
            healthRecords[index] = record
            try await saveHealthRecordToDatabase(record)
        }
    }
    func deleteHealthRecord(_ recordId: String) async throws {
        healthRecords.removeAll { $0.id == recordId }
        // Delete from local storage and sync with CloudKit
    }
    // MARK: - Symptoms Tracking
    func logSymptom(
        petId: String,
        symptom: SymptomType,
        severity: SymptomSeverity,
        description: String,
        duration: String? = nil,
        triggers: [String] = [],
        mediaUrls: [String] = []
    ) async throws {
        let symptomRecord = SymptomRecord(
            id: UUID().uuidString,
            petId: petId,
            symptom: symptom,
            severity: severity,
            description: description,
            duration: duration,
            triggers: triggers,
            mediaUrls: mediaUrls,
            observedAt: Date(),
            reportedBy: getCurrentUserId()
        )
        symptoms.append(symptomRecord)
        // Save to database
        try await saveSymptomToDatabase(symptomRecord)
        // Check if symptom requires immediate attention
        if symptom.isEmergency || severity == .severe {
            await createEmergencyAlert(for: symptomRecord)
        }
        // Suggest vet visit if needed
        await evaluateVetVisitNeed(for: petId, symptom: symptomRecord)
        print("✅ Symptom logged: \(symptom.displayName)")
    }
    func updateSymptomStatus(_ symptomId: String, status: SymptomStatus, notes: String? = nil) async throws {
        if let index = symptoms.firstIndex(where: { $0.id == symptomId }) {
            symptoms[index].status = status
            symptoms[index].resolvedAt = status == .resolved ? Date() : nil
            symptoms[index].notes = notes
            try await saveSymptomToDatabase(symptoms[index])
        }
    }
    // MARK: - Medications Management
    func addMedication(
        petId: String,
        name: String,
        dosage: String,
        frequency: MonitoringMedicationFrequency,
        startDate: Date,
        endDate: Date?,
        prescribedBy: String,
        instructions: String,
        sideEffects: [String] = []
    ) async throws {
        let medication = MedicationRecord(
            id: UUID().uuidString,
            petId: petId,
            name: name,
            dosage: dosage,
            frequency: frequency,
            startDate: startDate,
            endDate: endDate,
            prescribedBy: prescribedBy,
            instructions: instructions,
            sideEffects: sideEffects,
            isActive: true,
            adherenceRate: 1.0
        )
        medications.append(medication)
        // Save to database
        try await saveMedicationToDatabase(medication)
        // Schedule medication reminders
        await scheduleMedicationReminders(for: medication)
        print("✅ Medication added: \(name)")
    }
    func recordMedicationDose(
        medicationId: String,
        givenAt: Date = Date(),
        notes: String? = nil
    ) async throws {
        let dose = MedicationDose(
            id: UUID().uuidString,
            medicationId: medicationId,
            scheduledTime: givenAt,
            actualTime: givenAt,
            wasGiven: true,
            notes: notes,
            givenBy: getCurrentUserId()
        )
        // Update medication record
        if let index = medications.firstIndex(where: { $0.id == medicationId }) {
            medications[index].doses.append(dose)
            medications[index].lastGiven = givenAt
            // Update adherence rate
            medications[index].adherenceRate = calculateAdherenceRate(for: medications[index])
            try await saveMedicationToDatabase(medications[index])
        }
        print("✅ Medication dose recorded")
    }
    // MARK: - Vet Appointments
    func scheduleVetAppointment(
        petId: String,
        type: AppointmentType,
        veterinarian: String,
        clinic: String,
        date: Date,
        duration: TimeInterval = 3600,
        reason: String,
        notes: String? = nil,
        isEmergency: Bool = false
    ) async throws -> VetAppointment {
        let appointment = VetAppointment(
            id: UUID().uuidString,
            petId: petId,
            type: type,
            veterinarian: veterinarian,
            clinic: clinic,
            scheduledDate: date,
            duration: duration,
            reason: reason,
            notes: notes,
            status: .scheduled,
            isEmergency: isEmergency,
            reminderSent: false
        )
        vetAppointments.append(appointment)
        // Save to database
        try await saveAppointmentToDatabase(appointment)
        // Schedule calendar event
        _ = try await calendarService.scheduleVetAppointment(
            for: getPet(id: petId),
            title: "\(type.displayName) - \(veterinarian)",
            date: date,
            duration: duration,
            veterinarian: veterinarian,
            notes: reason,
            location: clinic
        )
        // Schedule reminders
        await scheduleAppointmentReminders(for: appointment)
        print("✅ Vet appointment scheduled")
        return appointment
    }
    func updateAppointmentStatus(
        _ appointmentId: String,
        status: String, // Simplified to avoid type conflicts
        notes: String? = nil,
        followUpNeeded: Bool = false
    ) async throws {
        if let index = vetAppointments.firstIndex(where: { $0.id == appointmentId }) {
            // Convert string status to AppointmentStatus enum
            let appointmentStatus = AppointmentStatus(rawValue: status) ?? .scheduled
            vetAppointments[index].status = appointmentStatus
            vetAppointments[index].completedAt = status == "completed" ? Date() : nil
            vetAppointments[index].followUpNeeded = followUpNeeded
            if let notes = notes {
                vetAppointments[index].notes = (vetAppointments[index].notes ?? "") + "\n\(notes)"
            }
            try await saveAppointmentToDatabase(vetAppointments[index])
            // Schedule follow-up if needed
            if followUpNeeded && status == "completed" {
                await suggestFollowUpAppointment(for: vetAppointments[index])
            }
        }
    }
    // MARK: - Health Monitoring & Alerts
    private func startHealthMonitoring() {
        healthMonitoringTimer = Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performHealthCheck()
            }
        }
        medicationReminderTimer = Timer.scheduledTimer(withTimeInterval: 1800, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.checkMedicationReminders()
            }
        }
    }
    private func performHealthCheck() async {
        // Check for overdue appointments
        await checkOverdueAppointments()
        // Check medication adherence
        await checkMedicationAdherence()
        // Check for concerning symptom patterns
        await analyzeSymptomPatterns()
        // Check for missing health records
        await checkMissingHealthRecords()
    }
    private func checkHealthAlerts(for record: HealthMonitoringRecord) async {
        var alertsToCreate: [MonitoringHealthAlert] = []
        switch record.type {
        case .weight:
            if let alert = checkWeightAlert(record) {
                alertsToCreate.append(alert)
            }
        case .temperature:
            if let alert = checkTemperatureAlert(record) {
                alertsToCreate.append(alert)
            }
        case .heartRate:
            if let alert = checkHeartRateAlert(record) {
                alertsToCreate.append(alert)
            }
        default:
            break
        }
        for alert in alertsToCreate {
            let sharedAlert = HealthAlert(
                id: alert.id,
                petId: alert.petId,
                type: alert.type.rawValue,
                severity: alert.severity,
                title: alert.title,
                message: alert.message,
                triggeredAt: alert.triggeredAt,
                isActive: alert.isActive,
                isAcknowledged: false
            )
            healthAlerts.append(sharedAlert)
            await sendHealthAlert(alert)
        }
    }
    private func checkWeightAlert(_ record: HealthMonitoringRecord) -> MonitoringHealthAlert? {
        // Get recent weight records for comparison
        let recentWeights = healthRecords
            .filter { $0.petId == record.petId && $0.type == .weight }
            .sorted { $0.date > $1.date }
            .prefix(5)
        guard recentWeights.count >= 2 else { return nil }
        let currentWeight = record.value
        let previousWeight = recentWeights.dropFirst().first?.value ?? currentWeight
        let weightChange = abs(currentWeight - previousWeight)
        let percentChange = (weightChange / previousWeight) * 100
        if percentChange > 10 { // 10% weight change
            return MonitoringHealthAlert(
                id: UUID().uuidString,
                petId: record.petId,
                type: .weightChange,
                severity: percentChange > 20 ? .high : .moderate,
                title: "Significant Weight Change",
                message: "Weight changed by \(String(format: "%.1f", percentChange))% from \(String(format: "%.1f", previousWeight)) to \(String(format: "%.1f", currentWeight)) lbs",
                triggeredAt: Date(),
                isActive: true
            )
        }
        return nil
    }
    private func checkTemperatureAlert(_ record: HealthMonitoringRecord) -> MonitoringHealthAlert? {
        let normalRange = 101.0...102.5 // Normal dog temperature range
        if !normalRange.contains(record.value) {
            let severity: SharedAlertSeverity = record.value < 99 || record.value > 104 ? .high : .moderate
            return MonitoringHealthAlert(
                id: UUID().uuidString,
                petId: record.petId,
                type: .abnormalVitals,
                severity: severity,
                title: "Abnormal Temperature",
                message: "Temperature is \(String(format: "%.1f", record.value))°F (normal: 101-102.5°F)",
                triggeredAt: Date(),
                isActive: true
            )
        }
        return nil
    }
    private func checkHeartRateAlert(_ record: HealthMonitoringRecord) -> MonitoringHealthAlert? {
        // Normal heart rate varies by size, but generally 60-140 BPM for dogs
        let normalRange = 60.0...140.0
        if !normalRange.contains(record.value) {
            return MonitoringHealthAlert(
                id: UUID().uuidString,
                petId: record.petId,
                type: .abnormalVitals,
                severity: .moderate,
                title: "Abnormal Heart Rate",
                message: "Heart rate is \(Int(record.value)) BPM (normal: 60-140 BPM)",
                triggeredAt: Date(),
                isActive: true
            )
        }
        return nil
    }
    // MARK: - Emergency Handling
    private func createEmergencyAlert(for symptom: SymptomRecord) async {
        let alert = MonitoringHealthAlert(
            id: UUID().uuidString,
            petId: symptom.petId,
            type: .emergencySymptom,
            severity: .high,
            title: "Emergency Symptom Detected",
            message: "\(symptom.symptom.displayName) - \(symptom.description)",
            triggeredAt: Date(),
            isActive: true
        )
        let sharedAlert = HealthAlert(
            id: alert.id,
            petId: alert.petId,
            type: alert.type.rawValue,
            severity: alert.severity,
            title: alert.title,
            message: alert.message,
            triggeredAt: alert.triggeredAt,
            isActive: alert.isActive,
            isAcknowledged: false
        )
        healthAlerts.append(sharedAlert)
        await sendEmergencyNotification(alert)
    }
    private func sendEmergencyNotification(_ alert: MonitoringHealthAlert) async {
        let content = UNMutableNotificationContent()
        content.title = "🚨 Pet Emergency Alert"
        content.body = alert.message
        content.sound = .defaultCritical
        content.categoryIdentifier = "EMERGENCY_ALERT"
        content.interruptionLevel = .critical
        let request = UNNotificationRequest(
            identifier: "emergency_\(alert.id)",
            content: content,
            trigger: nil
        )
        do {
            try await notificationService.add(request)
            print("✅ Emergency notification sent")
        } catch {
            print("❌ Failed to send emergency notification: \(error)")
        }
    }
    // MARK: - Helper Methods
    private func calculateAdherenceRate(for medication: MedicationRecord) -> Double {
        let totalExpectedDoses = calculateExpectedDoses(for: medication)
        let actualDoses = medication.doses.filter { $0.wasGiven }.count
        return totalExpectedDoses > 0 ? Double(actualDoses) / Double(totalExpectedDoses) : 1.0
    }
    private func calculateExpectedDoses(for medication: MedicationRecord) -> Int {
        let daysSinceStart = Calendar.current.dateComponents([.day], from: medication.startDate, to: Date()).day ?? 0
        switch medication.frequency {
        case .onceDaily: return daysSinceStart
        case .twiceDaily: return daysSinceStart * 2
        case .threeTimesDaily: return daysSinceStart * 3
        case .everyOtherDay: return daysSinceStart / 2
        case .weekly: return daysSinceStart / 7
        case .asNeeded: return medication.doses.count // For PRN, actual = expected
        }
    }
    private func getCurrentUserId() -> String {
        // Get current user ID from auth service
        return "current_user_id" // Placeholder
    }
    private func getPet(id: String) -> Pet {
        // Get pet from pet service
        // Create a placeholder pet
        return Pet(
            name: "Placeholder Pet",
            species: "Dog",
            breed: "Unknown", 
            age: 1,
            profileImageURL: nil,
            dateOfBirth: Date().addingTimeInterval(-365*24*60*60) // 1 year ago
        )
    }
    // MARK: - Data Persistence
    private func loadHealthData() {
        // Load from database
        Task {
        }
    }
    private func saveHealthRecordToDatabase(_ record: HealthMonitoringRecord) async throws {
        print("Saving health record to database")
    }
    private func saveSymptomToDatabase(_ symptom: SymptomRecord) async throws {
        print("Saving symptom to database")
    }
    private func saveMedicationToDatabase(_ medication: MedicationRecord) async throws {
        print("Saving medication to database")
    }
    private func saveAppointmentToDatabase(_ appointment: VetAppointment) async throws {
        print("Saving appointment to database")
    }
    // MARK: - Notification Setup
    private func setupNotificationCategories() {
        let emergencyCategory = UNNotificationCategory(
            identifier: "EMERGENCY_ALERT",
            actions: [
                UNNotificationAction(identifier: "CALL_VET", title: "Call Vet", options: [.foreground]),
                UNNotificationAction(identifier: "VIEW_DETAILS", title: "View Details", options: [.foreground])
            ],
            intentIdentifiers: [],
            options: []
        )
        let medicationCategory = UNNotificationCategory(
            identifier: "MEDICATION_REMINDER",
            actions: [
                UNNotificationAction(identifier: "MARK_GIVEN", title: "Mark as Given", options: []),
                UNNotificationAction(identifier: "SNOOZE", title: "Remind Later", options: [])
            ],
            intentIdentifiers: [],
            options: []
        )
        notificationService.setNotificationCategories([emergencyCategory, medicationCategory])
    }
    // MARK: - Placeholder Methods
    private func evaluateVetVisitNeed(for petId: String, symptom: SymptomRecord) async {
        // Analyze if vet visit is needed based on symptom
    }
    private func scheduleMedicationReminders(for medication: MedicationRecord) async {
        // Schedule medication reminder notifications
    }
    private func scheduleAppointmentReminders(for appointment: VetAppointment) async {
        // Schedule appointment reminder notifications
    }
    private func suggestFollowUpAppointment(for appointment: VetAppointment) async {
        // Suggest follow-up appointment
    }
    private func checkOverdueAppointments() async {
        // Check for overdue appointments
    }
    private func checkMedicationAdherence() async {
        // Check medication adherence
    }
    private func analyzeSymptomPatterns() async {
        // Analyze symptom patterns
    }
    private func checkMissingHealthRecords() async {
        // Check for missing health records
    }
    private func checkMedicationReminders() async {
        // Check for medication reminders
    }
    private func sendHealthAlert(_ alert: MonitoringHealthAlert) async {
        // Send health alert notification
    }
}
// MARK: - Supporting Types
struct HealthMonitoringRecord: Identifiable, Codable {
    let id: String
    let petId: String
    let type: MonitoringHealthRecordType
    let value: Double
    let unit: String
    let notes: String?
    let date: Date
    let recordedBy: String
    var isVerified: Bool
}
enum MonitoringHealthRecordType: String, CaseIterable, Codable {
    case weight = "weight"
    case temperature = "temperature"
    case heartRate = "heart_rate"
    case bloodPressure = "blood_pressure"
    case respiratoryRate = "respiratory_rate"
    case bloodGlucose = "blood_glucose"
    case medication = "medication"
    case exercise = "exercise"
    case mood = "mood"
    case appetite = "appetite"
    case sleep = "sleep"
    case hydration = "hydration"
    var displayName: String {
        switch self {
        case .weight: return "Weight"
        case .temperature: return "Temperature"
        case .heartRate: return "Heart Rate"
        case .bloodPressure: return "Blood Pressure"
        case .respiratoryRate: return "Respiratory Rate"
        case .bloodGlucose: return "Blood Glucose"
        case .medication: return "Medication"
        case .exercise: return "Exercise"
        case .mood: return "Mood"
        case .appetite: return "Appetite"
        case .sleep: return "Sleep"
        case .hydration: return "Hydration"
        }
    }
    var unit: String {
        switch self {
        case .weight: return "kg"
        case .temperature: return "°C"
        case .heartRate: return "bpm"
        case .bloodPressure: return "mmHg"
        case .respiratoryRate: return "rpm"
        case .bloodGlucose: return "mg/dL"
        case .medication: return "dose"
        case .exercise: return "minutes"
        case .mood: return "1-10"
        case .appetite: return "1-10"
        case .sleep: return "hours"
        case .hydration: return "mL"
        }
    }
}
struct SymptomRecord: Identifiable, Codable {
    let id: String
    let petId: String
    let symptom: SymptomType
    let severity: SymptomSeverity
    let description: String
    let duration: String?
    let triggers: [String]
    let mediaUrls: [String]
    let observedAt: Date
    let reportedBy: String
    var status: SymptomStatus = .active
    var resolvedAt: Date?
    var notes: String?
}
enum SymptomType: String, CaseIterable, Codable {
    case vomiting = "vomiting"
    case diarrhea = "diarrhea"
    case lethargy = "lethargy"
    case lossOfAppetite = "loss_of_appetite"
    case excessiveThirst = "excessive_thirst"
    case difficulty_breathing = "difficulty_breathing"
    case limping = "limping"
    case seizure = "seizure"
    case skinIrritation = "skin_irritation"
    case excessiveScratching = "excessive_scratching"
    case coughing = "coughing"
    case sneezing = "sneezing"
    case bloating = "bloating"
    case aggression = "aggression"
    case hiding = "hiding"
    case restlessness = "restlessness"
    var displayName: String {
        switch self {
        case .vomiting: return "Vomiting"
        case .diarrhea: return "Diarrhea"
        case .lethargy: return "Lethargy"
        case .lossOfAppetite: return "Loss of Appetite"
        case .excessiveThirst: return "Excessive Thirst"
        case .difficulty_breathing: return "Difficulty Breathing"
        case .limping: return "Limping"
        case .seizure: return "Seizure"
        case .skinIrritation: return "Skin Irritation"
        case .excessiveScratching: return "Excessive Scratching"
        case .coughing: return "Coughing"
        case .sneezing: return "Sneezing"
        case .bloating: return "Bloating"
        case .aggression: return "Aggression"
        case .hiding: return "Hiding"
        case .restlessness: return "Restlessness"
        }
    }
    var isEmergency: Bool {
        switch self {
        case .seizure, .difficulty_breathing, .bloating:
            return true
        default:
            return false
        }
    }
}
enum SymptomSeverity: String, CaseIterable, Codable {
    case mild = "mild"
    case moderate = "moderate"
    case severe = "severe"
    var displayName: String {
        switch self {
        case .mild: return "Mild"
        case .moderate: return "Moderate"
        case .severe: return "Severe"
        }
    }
    var color: Color {
        switch self {
        case .mild: return .green
        case .moderate: return .orange
        case .severe: return .red
        }
    }
}
enum SymptomStatus: String, CaseIterable, Codable {
    case active = "active"
    case improving = "improving"
    case resolved = "resolved"
    case worsening = "worsening"
    var color: Color {
        switch self {
        case .active: return .orange
        case .improving: return .blue
        case .resolved: return .green
        case .worsening: return .red
        }
    }
    var displayName: String {
        switch self {
        case .active: return "Active"
        case .improving: return "Improving"
        case .resolved: return "Resolved"
        case .worsening: return "Worsening"
        }
    }
}
struct MedicationRecord: Identifiable, Codable {
    let id: String
    let petId: String
    let name: String
    let dosage: String
    let frequency: MonitoringMedicationFrequency
    let startDate: Date
    let endDate: Date?
    let prescribedBy: String
    let instructions: String
    let sideEffects: [String]
    var isActive: Bool
    var lastGiven: Date?
    var adherenceRate: Double
    var doses: [MedicationDose] = []
}
enum MonitoringMedicationFrequency: String, CaseIterable, Codable {
    case onceDaily = "once_daily"
    case twiceDaily = "twice_daily"
    case threeTimesDaily = "three_times_daily"
    case everyOtherDay = "every_other_day"
    case weekly = "weekly"
    case asNeeded = "as_needed"
    var displayName: String {
        switch self {
        case .onceDaily: return "Once Daily"
        case .twiceDaily: return "Twice Daily"
        case .threeTimesDaily: return "Three Times Daily"
        case .everyOtherDay: return "Every Other Day"
        case .weekly: return "Weekly"
        case .asNeeded: return "As Needed"
        }
    }
}
struct MedicationDose: Identifiable, Codable {
    let id: String
    let medicationId: String
    let scheduledTime: Date
    let actualTime: Date?
    let wasGiven: Bool
    let notes: String?
    let givenBy: String
}
struct VetAppointment: Identifiable, Codable {
    let id: String
    let petId: String
    let type: AppointmentType
    let veterinarian: String
    let clinic: String
    let scheduledDate: Date
    let duration: TimeInterval
    let reason: String
    var notes: String?
    var status: AppointmentStatus
    let isEmergency: Bool
    var reminderSent: Bool
    var completedAt: Date?
    var followUpNeeded: Bool = false
}
enum AppointmentType: String, CaseIterable, Codable {
    case checkup = "checkup"
    case vaccination = "vaccination"
    case emergency = "emergency"
    case surgery = "surgery"
    case dental = "dental"
    case grooming = "grooming"
    case consultation = "consultation"
    case followUp = "follow_up"
    var displayName: String {
        switch self {
        case .checkup: return "Check-up"
        case .vaccination: return "Vaccination"
        case .emergency: return "Emergency"
        case .surgery: return "Surgery"
        case .dental: return "Dental"
        case .grooming: return "Grooming"
        case .consultation: return "Consultation"
        case .followUp: return "Follow-up"
        }
    }
    var icon: String {
        switch self {
        case .checkup: return "stethoscope"
        case .vaccination: return "syringe"
        case .emergency: return "cross.circle.fill"
        case .surgery: return "scissors"
        case .dental: return "mouth"
        case .grooming: return "scissors.badge.ellipsis"
        case .consultation: return "person.2"
        case .followUp: return "arrow.clockwise"
        }
    }
}
enum AppointmentStatus: String, CaseIterable, Codable {
    case scheduled = "scheduled"
    case confirmed = "confirmed"
    case inProgress = "in_progress"
    case completed = "completed"
    case cancelled = "cancelled"
    case noShow = "no_show"
    var color: Color {
        switch self {
        case .scheduled: return .blue
        case .confirmed: return .green
        case .inProgress: return .orange
        case .completed: return .green
        case .cancelled: return .red
        case .noShow: return .gray
        }
    }
    var displayName: String {
        switch self {
        case .scheduled: return "Scheduled"
        case .confirmed: return "Confirmed"
        case .inProgress: return "In Progress"
        case .completed: return "Completed"
        case .cancelled: return "Cancelled"
        case .noShow: return "No Show"
        }
    }
}
struct MonitoringHealthAlert: Identifiable, Codable {
    let id: String
    let petId: String
    let type: HealthAlertType
    let severity: MonitoringAlertSeverity
    let title: String
    let message: String
    let triggeredAt: Date
    var isActive: Bool
    var acknowledgedAt: Date?
}
enum HealthAlertType: String, CaseIterable, Codable {
    case weightChange = "weight_change"
    case abnormalVitals = "abnormal_vitals"
    case emergencySymptom = "emergency_symptom"
    case medicationMissed = "medication_missed"
    case appointmentOverdue = "appointment_overdue"
    case vaccinationDue = "vaccination_due"
    var displayName: String {
        switch self {
        case .weightChange: return "Weight Change"
        case .abnormalVitals: return "Abnormal Vitals"
        case .emergencySymptom: return "Emergency Symptom"
        case .medicationMissed: return "Medication Missed"
        case .appointmentOverdue: return "Appointment Overdue"
        case .vaccinationDue: return "Vaccination Due"
        }
    }
    var icon: String {
        switch self {
        case .weightChange: return "scalemass"
        case .abnormalVitals: return "heart.text.square"
        case .emergencySymptom: return "exclamationmark.triangle.fill"
        case .medicationMissed: return "pills"
        case .appointmentOverdue: return "calendar.badge.exclamationmark"
        case .vaccinationDue: return "syringe"
        }
    }
}
