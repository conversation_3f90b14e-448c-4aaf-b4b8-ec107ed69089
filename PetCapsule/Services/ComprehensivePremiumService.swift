//
//  ComprehensivePremiumService.swift
//  PetCapsule
//
//  Complete premium subscription system with billing integration
//
import Foundation
import StoreKit
import SwiftUI
@MainActor
class ComprehensivePremiumService: ObservableObject {
    static let shared = ComprehensivePremiumService()
    // MARK: - Subscription State
    @Published var isSubscribed = false
    @Published var currentSubscription: SubscriptionTier = .pawStarter
    @Published var subscriptionExpiryDate: Date?
    @Published var availableProducts: [Product] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    // MARK: - Product Identifiers
    private let productIdentifiers = [
        "com.petcapsule.premium.monthly",
        "com.petcapsule.premium.yearly",
        "com.petcapsule.premium.lifetime"
    ]
    // MARK: - StoreKit Configuration
    private var updateListenerTask: Task<Void, Error>?
    private init() {
        updateListenerTask = listenForTransactions()
        Task {
            await loadProducts()
            await checkSubscriptionStatus()
        }
    }
    deinit {
        updateListenerTask?.cancel()
    }
    // MARK: - Product Loading
    func loadProducts() async {
        isLoading = true
        defer { isLoading = false }
        do {
            let products = try await Product.products(for: productIdentifiers)
            await MainActor.run {
                self.availableProducts = products.sorted { $0.price < $1.price }
            }
            print("✅ Loaded \(products.count) premium products")
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load products: \(error.localizedDescription)"
            }
            print("❌ Failed to load products: \(error)")
        }
    }
    // MARK: - Purchase Flow
    func purchase(_ product: Product) async -> Bool {
        isLoading = true
        defer { isLoading = false }
        do {
            let result = try await product.purchase()
            switch result {
            case .success(let verification):
                let transaction = try checkVerified(verification)
                await updateSubscriptionStatus(from: transaction)
                await transaction.finish()
                return true
            case .userCancelled:
                print("ℹ️ User cancelled purchase")
                return false
            case .pending:
                print("⏳ Purchase pending")
                return false
            @unknown default:
                print("❌ Unknown purchase result")
                return false
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Purchase failed: \(error.localizedDescription)"
            }
            print("❌ Purchase failed: \(error)")
            return false
        }
    }
    // MARK: - Restore Purchases
    func restorePurchases() async {
        isLoading = true
        defer { isLoading = false }
        do {
            try await AppStore.sync()
            await checkSubscriptionStatus()
            print("✅ Purchases restored")
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to restore purchases: \(error.localizedDescription)"
            }
            print("❌ Failed to restore purchases: \(error)")
        }
    }
    // MARK: - Subscription Status
    func checkSubscriptionStatus() async {
        var hasActiveSubscription = false
        var latestExpiryDate: Date?
        var currentTier: SubscriptionTier = .pawStarter
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)
                if let expirationDate = transaction.expirationDate {
                    if expirationDate > Date() {
                        hasActiveSubscription = true
                        if latestExpiryDate == nil || expirationDate > latestExpiryDate! {
                            latestExpiryDate = expirationDate
                            currentTier = subscriptionTier(for: transaction.productID)
                        }
                    }
                } else {
                    // Lifetime subscription
                    hasActiveSubscription = true
                    currentTier = .growingBond // Use growing bond for lifetime
                }
            } catch {
                print("❌ Failed to verify transaction: \(error)")
            }
        }
        await MainActor.run {
            self.isSubscribed = hasActiveSubscription
            self.subscriptionExpiryDate = latestExpiryDate
            self.currentSubscription = currentTier
        }
        print("📱 Subscription status: \(hasActiveSubscription ? "Active" : "Inactive") - \(currentTier)")
    }
    // MARK: - Transaction Verification
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw PremiumError.failedVerification
        case .verified(let safe):
            return safe
        }
    }
    // MARK: - Transaction Listener
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            for await result in Transaction.updates {
                do {
                    let transaction = try await self.checkVerified(result)
                    await self.updateSubscriptionStatus(from: transaction)
                    await transaction.finish()
                } catch {
                    print("❌ Transaction update failed: \(error)")
                }
            }
        }
    }
    // MARK: - Subscription Management
    private func updateSubscriptionStatus(from transaction: StoreKit.Transaction) async {
        await checkSubscriptionStatus()
        // Update user profile in Apple native storage
        await updateUserSubscriptionInDatabase()
        // Unlock premium features
        await unlockPremiumFeatures()
    }
    private func updateUserSubscriptionInDatabase() async {
        print("Updating user subscription with Apple native services")
        do {
            let updateData: [String: String] = [
                "subscription_tier": currentSubscription.rawValue,
                "subscription_expires_at": subscriptionExpiryDate?.ISO8601Format() ?? "",
                "updated_at": Date().ISO8601Format()
            ]
            // Update user subscription status in CloudKit or local storage
            print("✅ Updated user subscription in database")
        } catch {
            print("❌ Failed to update subscription in database: \(error)")
        }
    }
    private func unlockPremiumFeatures() async {
        // Update feature flags based on subscription
        let featureFlags = FeatureFlags.shared
        if isSubscribed {
            featureFlags.setFlag(.premiumSubscription, enabled: true)
            featureFlags.setFlag(.unlimitedMemories, enabled: true)
            featureFlags.setFlag(.advancedAnalytics, enabled: true)
            featureFlags.setFlag(.advancedMemoryFilters, enabled: true)
            if currentSubscription == .growingBond || currentSubscription == .familyCircle || currentSubscription == .premiumPro {
                featureFlags.setFlag(.betaFeatures, enabled: true)
            }
        } else {
            featureFlags.setFlag(.premiumSubscription, enabled: false)
            featureFlags.setFlag(.unlimitedMemories, enabled: false)
            featureFlags.setFlag(.advancedAnalytics, enabled: false)
            featureFlags.setFlag(.betaFeatures, enabled: false)
        }
        print("🔓 Premium features updated")
    }
    // MARK: - Helper Methods
    private func subscriptionTier(for productID: String) -> SubscriptionTier {
        switch productID {
        case "com.petcapsule.premium.monthly":
            return .growingBond
        case "com.petcapsule.premium.yearly":
            return .growingBond
        case "com.petcapsule.premium.lifetime":
            return .growingBond
        default:
            return .pawStarter
        }
    }
    func getProduct(for tier: SubscriptionTier) -> Product? {
        let productID: String
        switch tier {
        case .growingBond:
            productID = "com.petcapsule.premium.monthly"
        case .familyCircle:
            productID = "com.petcapsule.premium.yearly"
        case .premiumPro:
            productID = "com.petcapsule.premium.lifetime"
        case .pawStarter:
            return nil
        }
        return availableProducts.first { $0.id == productID }
    }
    // MARK: - Feature Access
    func hasAccess(to feature: PremiumFeature) -> Bool {
        switch feature {
        case .unlimitedStorage, .aiCuration, .premiumThemes:
            return isSubscribed
        case .familySharing, .businessTools:
            return isSubscribed && (currentSubscription == .growingBond || currentSubscription == .familyCircle || currentSubscription == .premiumPro)
        case .apiAccess:
            return currentSubscription == .familyCircle || currentSubscription == .premiumPro
        case .videoMontages:
            return isSubscribed
        }
    }
    func getRemainingDays() -> Int? {
        guard let expiryDate = subscriptionExpiryDate else { return nil }
        let calendar = Calendar.current
        let days = calendar.dateComponents([.day], from: Date(), to: expiryDate).day
        return max(0, days ?? 0)
    }
}
// MARK: - Supporting Types
// Using existing SubscriptionTier from User.swift
// Using existing PremiumFeature from SubscriptionService.swift
enum PremiumError: Error {
    case failedVerification
    case productNotFound
    case purchaseFailed
    case restoreFailed
    var localizedDescription: String {
        switch self {
        case .failedVerification:
            return "Failed to verify purchase"
        case .productNotFound:
            return "Product not found"
        case .purchaseFailed:
            return "Purchase failed"
        case .restoreFailed:
            return "Failed to restore purchases"
        }
    }
}
