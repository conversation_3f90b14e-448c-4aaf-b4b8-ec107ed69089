//
//  DataAnalyticsService.swift
//  PetCapsule
//
//  Comprehensive data analytics service with insights, trends, and predictive analytics
//
import Foundation
import CoreML
import Combine
import SwiftUI
// Type aliases to resolve ambiguity
typealias AnalyticsHealthTrend = HealthTrend
@available(iOS 18.0, *)
@MainActor
class DataAnalyticsService: ObservableObject {
    static let shared = DataAnalyticsService()
    // Published properties
    @Published var petInsights: [PetInsight] = []
    @Published var healthTrends: [AnalyticsHealthTrend] = []
    @Published var activityAnalytics: ActivityAnalytics?
    @Published var behaviorAnalytics: BehaviorAnalytics?
    @Published var environmentalAnalytics: EnvironmentalAnalytics?
    @Published var socialAnalytics: SocialAnalytics?
    @Published var predictiveInsights: [PredictiveInsight] = []
    @Published var dashboardMetrics: DashboardMetrics?
    @Published var isAnalyzing = false
    @Published var lastAnalysisTime: Date?
    // Services
    private let dataService = AppleNativeDataService.shared
    private let petService = RealDataService()
    private let dataManager = PetDataManager.shared
    private let activityService = ActivityTrackingService.shared
    private let healthService = ComprehensiveHealthMonitoringService.shared
    private let environmentalService = UnifiedEnvironmentalMonitoringService.shared
    private let socialService = SocialInteractionService.shared
    // ML Models
    private var healthPredictionModel: MLModel?
    private var behaviorAnalysisModel: MLModel?
    private var activityRecommendationModel: MLModel?
    private init() {
        loadMLModels()
        setupDataSubscriptions()
    }
    // MARK: - Analytics Generation
    func generateComprehensiveAnalytics(for petId: String) async {
        isAnalyzing = true
        defer { isAnalyzing = false }
        do {
            // Generate all analytics concurrently
            async let activityAnalysis = generateActivityAnalytics(for: petId)
            async let behaviorAnalysis = generateBehaviorAnalytics(for: petId)
            async let healthAnalysis = generateHealthAnalytics(for: petId)
            async let environmentalAnalysis = generateEnvironmentalAnalytics(for: petId)
            async let socialAnalysis = generateSocialAnalytics(for: petId)
            async let predictiveAnalysis = generatePredictiveInsights(for: petId)
            let (activity, behavior, health, environmental, social, predictive) = try await (
                activityAnalysis, behaviorAnalysis, healthAnalysis, 
                environmentalAnalysis, socialAnalysis, predictiveAnalysis
            )
            // Update published properties
            activityAnalytics = activity
            behaviorAnalytics = behavior
            environmentalAnalytics = environmental
            socialAnalytics = social
            predictiveInsights = predictive
            // Generate health trends
            healthTrends = await generateHealthTrends(for: petId)
            // Generate pet insights
            petInsights = await generatePetInsights(for: petId)
            // Update dashboard metrics
            dashboardMetrics = generateDashboardMetrics(for: petId)
            lastAnalysisTime = Date()
            // Save analytics to database
            await saveAnalyticsToDatabase(petId: petId)
            print("✅ Comprehensive analytics generated for pet: \(petId)")
        } catch {
            print("❌ Failed to generate analytics: \(error)")
        }
    }
    // MARK: - Activity Analytics
    private func generateActivityAnalytics(for petId: String) async throws -> ActivityAnalytics {
        let activities = activityService.getActivitiesForPet(petId)
        let last30Days = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        let recentActivities = activities.filter { $0.createdAt >= last30Days }
        // Calculate metrics
        let totalDuration = recentActivities.reduce(0) { $0 + $1.duration }
        let totalDistance = recentActivities.reduce(into: 0.0) { total, _ in total += 0.0 } // ExerciseLog doesn't have distance
        let totalSteps = recentActivities.reduce(into: 0) { total, _ in total += 0 } // ExerciseLog doesn't have steps
        let averageDuration = recentActivities.isEmpty ? 0 : totalDuration / Double(recentActivities.count)
        // Activity frequency analysis
        let activityFrequency = Dictionary(grouping: recentActivities) { activity in
            Calendar.current.component(.weekday, from: activity.createdAt)
        }.mapValues { $0.count }
        // Most active time analysis
        let hourlyActivity = Dictionary(grouping: recentActivities) { activity in
            Calendar.current.component(.hour, from: activity.createdAt)
        }.mapValues { $0.count }
        let mostActiveHour = hourlyActivity.max { $0.value < $1.value }?.key ?? 12
        // Convert activities first
        let convertedActivities = recentActivities.map { exercise in
            ActivityLog(
                id: exercise.id,
                petId: exercise.petId,
                activityType: exercise.activityType,
                startTime: exercise.createdAt,
                endTime: exercise.createdAt.addingTimeInterval(exercise.duration),
                duration: exercise.duration,
                distance: nil,
                caloriesBurned: exercise.caloriesBurned,
                averageHeartRate: nil,
                maxHeartRate: nil,
                steps: nil,
                location: nil,
                latitude: nil,
                longitude: nil,
                notes: exercise.notes,
                weatherConditions: nil,
                temperature: nil,
                humidity: nil,
                createdAt: exercise.createdAt
            )
        }
        // Activity consistency score
        let consistencyScore = calculateActivityConsistency(convertedActivities)
        // Weekly trends
        let weeklyTrends = calculateWeeklyActivityTrends(convertedActivities)
        return ActivityAnalytics(
            petId: petId,
            totalDuration: totalDuration,
            totalDistance: totalDistance,
            totalSteps: totalSteps,
            averageDuration: averageDuration,
            activityFrequency: activityFrequency,
            mostActiveHour: mostActiveHour,
            consistencyScore: consistencyScore,
            weeklyTrends: weeklyTrends,
            analysisDate: Date()
        )
    }
    // MARK: - Behavior Analytics
    private func generateBehaviorAnalytics(for petId: String) async throws -> BehaviorAnalytics {
        let behaviors = activityService.getBehaviorsForPet(petId)
        let last30Days = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        let recentBehaviors = behaviors.filter { $0.timestamp >= last30Days }
        // Behavior frequency analysis
        let behaviorFrequency = Dictionary(grouping: recentBehaviors) { $0.behaviorType }
            .mapValues { $0.count }
        // Concerning behaviors analysis
        let concerningBehaviorTypes = ["aggression", "anxiety", "pacing", "hiding"]
        let concerningBehaviors = recentBehaviors.filter { concerningBehaviorTypes.contains($0.behaviorType) }
        let concerningBehaviorTrend = calculateBehaviorTrend(concerningBehaviors)
        // Time-based patterns
        let hourlyPatterns = Dictionary(grouping: recentBehaviors) { behavior in
            Calendar.current.component(.hour, from: behavior.timestamp)
        }.mapValues { $0.count }
        // Trigger analysis
        let triggerAnalysis = analyzeBehaviorTriggers(recentBehaviors)
        // Behavior improvement score
        let improvementScore = calculateBehaviorImprovementScore(recentBehaviors)
        return BehaviorAnalytics(
            petId: petId,
            behaviorFrequency: behaviorFrequency,
            concerningBehaviorTrend: concerningBehaviorTrend,
            hourlyPatterns: hourlyPatterns,
            triggerAnalysis: triggerAnalysis,
            improvementScore: improvementScore,
            analysisDate: Date()
        )
    }
    // MARK: - Health Analytics
    private func generateHealthAnalytics(for petId: String) async throws -> HealthAnalytics {
        // This would integrate with HealthTrackingService
        // For now, return a placeholder
        return HealthAnalytics(
            petId: petId,
            overallHealthScore: 0.85,
            vitalTrends: [:],
            symptomFrequency: [:],
            medicationAdherence: 0.9,
            vetVisitFrequency: 2,
            healthRiskFactors: [],
            analysisDate: Date()
        )
    }
    // MARK: - Environmental Analytics
    private func generateEnvironmentalAnalytics(for petId: String) async throws -> EnvironmentalAnalytics {
        let environmentalHistory = environmentalService.getHistoryForTimeRange(2592000) // 30 days
        // Calculate average environmental scores
        let averageScore = environmentalHistory.isEmpty ? 0.0 : 
            environmentalHistory.reduce(0) { $0 + $1.data.environmentalScore.overallScore } / Double(environmentalHistory.count)
        // Risk exposure analysis
        let highRiskDays = environmentalHistory.filter { $0.riskLevel == .high || $0.riskLevel == .critical }.count
        let riskExposure = Double(highRiskDays) / Double(max(environmentalHistory.count, 1))
        // Weather impact analysis
        let weatherImpact = analyzeWeatherImpact(environmentalHistory)
        // Air quality trends
        let airQualityTrend = calculateAirQualityTrend(environmentalHistory)
        return EnvironmentalAnalytics(
            petId: petId,
            averageEnvironmentalScore: averageScore,
            riskExposure: riskExposure,
            weatherImpact: weatherImpact,
            airQualityTrend: airQualityTrend,
            pollenExposure: 0.3, // Placeholder
            optimalWalkingTimes: ["7:00-9:00 AM", "6:00-8:00 PM"],
            analysisDate: Date()
        )
    }
    // MARK: - Social Analytics
    private func generateSocialAnalytics(for petId: String) async throws -> SocialAnalytics {
        guard let metrics = socialService.socialMetrics else {
            return SocialAnalytics(
                petId: petId,
                socialEngagementScore: 0.0,
                playdateFrequency: 0,
                socialConnectionGrowth: 0.0,
                communityParticipation: 0.0,
                analysisDate: Date()
            )
        }
        // Calculate social engagement score
        let engagementScore = metrics.engagementScore
        // Playdate frequency (per month)
        let playdateFrequency = Double(metrics.playdatesCount) / 1.0 // Assuming 1 month of data
        // Social connection growth (placeholder)
        let connectionGrowth = 0.15 // 15% growth
        // Community participation score
        let participationScore = calculateCommunityParticipation(metrics)
        return SocialAnalytics(
            petId: petId,
            socialEngagementScore: engagementScore,
            playdateFrequency: playdateFrequency,
            socialConnectionGrowth: connectionGrowth,
            communityParticipation: participationScore,
            analysisDate: Date()
        )
    }
    // MARK: - Predictive Insights
    private func generatePredictiveInsights(for petId: String) async throws -> [PredictiveInsight] {
        var insights: [PredictiveInsight] = []
        // Health prediction
        if let healthModel = healthPredictionModel {
            let healthPrediction = try await predictHealthRisks(for: petId, using: healthModel)
            insights.append(contentsOf: healthPrediction)
        }
        // Behavior prediction
        if let behaviorModel = behaviorAnalysisModel {
            let behaviorPrediction = try await predictBehaviorChanges(for: petId, using: behaviorModel)
            insights.append(contentsOf: behaviorPrediction)
        }
        // Activity recommendation
        if let activityModel = activityRecommendationModel {
            let activityRecommendation = try await generateActivityRecommendations(for: petId, using: activityModel)
            insights.append(contentsOf: activityRecommendation)
        }
        return insights
    }
    // MARK: - Health Trends
    private func generateHealthTrends(for petId: String) async -> [AnalyticsHealthTrend] {
        // This would integrate with health data
        // For now, return placeholder trends
        return []
    }
    // MARK: - Pet Insights
        private func generatePetInsights(for petId: String) async -> [PetInsight] {
        var insights: [PetInsight] = []
        // Find the pet using comprehensive data
        guard let pet = dataManager.pets.first(where: { $0.id == petId }) else {
            return insights
        }
        // Comprehensive health insights
        let healthScore = dataManager.getHealthScore(for: pet)
        if healthScore > 90.0 {
            insights.append(PetInsight(
                id: UUID().uuidString,
                petId: petId,
                category: .health,
                title: "Exceptional Health Status",
                description: "Your \(pet.name) is in excellent health with optimal nutrition, exercise, and wellness indicators.",
                priority: .low,
                actionable: false,
                recommendations: [],
                generatedAt: Date()
            ))
        } else if healthScore < 70.0 {
            insights.append(PetInsight(
                id: UUID().uuidString,
                petId: petId,
                category: .health,
                title: "Health Concerns Detected",
                description: "Multiple health indicators suggest \(pet.name) may benefit from veterinary attention and care plan adjustments.",
                priority: .high,
                actionable: true,
                recommendations: [
                    "Schedule comprehensive veterinary examination",
                    "Review current nutrition and exercise plans",
                    "Monitor daily health indicators more closely"
                ],
                generatedAt: Date()
            ))
        }
        // Nutrition insights from comprehensive data
        if let waterIntake = pet.waterIntakeML {
            let dailyNeeds = PetAISupportService.shared.calculateDailyWaterNeeds(for: pet)
            if Double(waterIntake) < dailyNeeds * 0.6 {
                insights.append(PetInsight(
                    id: UUID().uuidString,
                    petId: petId,
                    category: .nutrition,
                    title: "Critical Hydration Deficit",
                    description: "\(pet.name) is drinking significantly less water than recommended, which can lead to kidney issues.",
                    priority: .critical,
                    actionable: true,
                    recommendations: [
                        "Add multiple water sources around the house",
                        "Consider a water fountain",
                        "Monitor daily water intake",
                        "Consult veterinarian if intake doesn't improve"
                    ],
                    generatedAt: Date()
                ))
            }
        }
        // Exercise insights
        if let exerciseMinutes = pet.exerciseMinutesDaily {
            let idealExercise = PetAISupportService.shared.calculateIdealExerciseMinutes(for: pet)
            if Double(exerciseMinutes) < idealExercise * 0.5 {
                insights.append(PetInsight(
                    id: UUID().uuidString,
                    petId: petId,
                    category: .activity,
                    title: "Insufficient Exercise Detected",
                    description: "\(pet.name) is getting less than half the recommended daily exercise, which can lead to behavioral and health issues.",
                    priority: .high,
                    actionable: true,
                    recommendations: [
                        "Gradually increase daily exercise to \(Int(idealExercise)) minutes",
                        "Add mental stimulation activities",
                        "Consider interactive toys for indoor exercise"
                    ],
                    generatedAt: Date()
                ))
            } else if Double(exerciseMinutes) > idealExercise * 1.5 {
                insights.append(PetInsight(
                    id: UUID().uuidString,
                    petId: petId,
                    category: .activity,
                    title: "Excellent Activity Level",
                    description: "\(pet.name) is getting plenty of exercise, promoting excellent physical and mental health.",
                    priority: .low,
                    actionable: false,
                    recommendations: [],
                    generatedAt: Date()
                ))
            }
        }
        // Training insights
        if pet.knownCommands.isEmpty && pet.species == "dog" {
            insights.append(PetInsight(
                id: UUID().uuidString,
                petId: petId,
                category: .behavior,
                title: "Training Opportunity",
                description: "Teaching basic commands to \(pet.name) will improve safety, communication, and strengthen your bond.",
                priority: .medium,
                actionable: true,
                recommendations: [
                    "Start with basic commands: sit, stay, come",
                    "Use positive reinforcement techniques",
                    "Practice for 5-10 minutes daily",
                    "Consider professional training classes"
                ],
                generatedAt: Date()
            ))
        } else if pet.knownCommands.count >= 5 {
            insights.append(PetInsight(
                id: UUID().uuidString,
                petId: petId,
                category: .behavior,
                title: "Well-Trained Pet",
                description: "\(pet.name) knows \(pet.knownCommands.count) commands, indicating excellent training and communication.",
                priority: .low,
                actionable: false,
                recommendations: [],
                generatedAt: Date()
            ))
        }
        // Activity insights
        if let activityAnalytics = activityAnalytics {
            if activityAnalytics.consistencyScore > 0.8 {
                insights.append(PetInsight(
                    id: UUID().uuidString,
                    petId: petId,
                    category: .activity,
                    title: "Excellent Activity Consistency",
                    description: "Your pet maintains a very consistent exercise routine",
                    priority: .low,
                    actionable: false,
                    recommendations: [],
                    generatedAt: Date()
                ))
            }
        }
        // Behavior insights
        if let behaviorAnalytics = behaviorAnalytics {
            if behaviorAnalytics.improvementScore > 0.7 {
                insights.append(PetInsight(
                    id: UUID().uuidString,
                    petId: petId,
                    category: .behavior,
                    title: "Positive Behavior Trends",
                    description: "Your pet's behavior has been improving consistently",
                    priority: .low,
                    actionable: false,
                    recommendations: [],
                    generatedAt: Date()
                ))
            }
        }
        // Environmental insights
        if let environmentalAnalytics = environmentalAnalytics {
            if environmentalAnalytics.riskExposure > 0.3 {
                insights.append(PetInsight(
                    id: UUID().uuidString,
                    petId: petId,
                    category: .environmental,
                    title: "High Environmental Risk Exposure",
                    description: "Your pet has been exposed to poor environmental conditions frequently",
                    priority: .high,
                    actionable: true,
                    recommendations: [
                        "Consider indoor activities during high-risk periods",
                        "Use air purifiers at home",
                        "Plan walks during optimal times"
                    ],
                    generatedAt: Date()
                ))
            }
        }
        return insights
    }
    // MARK: - Dashboard Metrics
    private func generateDashboardMetrics(for petId: String) -> DashboardMetrics {
        // Find the pet in our comprehensive data
        let pet = dataManager.pets.first { $0.id == petId }
        let healthScore = pet.map { dataManager.getHealthScore(for: $0) } ?? 0.85
        return DashboardMetrics(
            petId: petId,
            overallWellnessScore: calculateOverallWellnessScore(),
            activityScore: activityAnalytics?.consistencyScore ?? 0.0,
            healthScore: healthScore, // Use comprehensive health score
            behaviorScore: behaviorAnalytics?.improvementScore ?? 0.0,
            socialScore: socialAnalytics?.socialEngagementScore ?? 0.0,
            environmentalScore: environmentalAnalytics?.averageEnvironmentalScore ?? 0.0,
            trendsCount: healthTrends.count,
            insightsCount: petInsights.count,
            lastUpdated: Date()
        )
    }
    // MARK: - Helper Methods
    private func calculateOverallWellnessScore() -> Double {
        let activityScore = activityAnalytics?.consistencyScore ?? 0.0
        let healthScore = 0.85 // Placeholder
        let behaviorScore = behaviorAnalytics?.improvementScore ?? 0.0
        let socialScore = socialAnalytics?.socialEngagementScore ?? 0.0
        let environmentalScore = environmentalAnalytics?.averageEnvironmentalScore ?? 0.0
        return (activityScore + healthScore + behaviorScore + socialScore + environmentalScore) / 5.0
    }
    private func calculateActivityConsistency(_ activities: [ActivityLog]) -> Double {
        // Simplified consistency calculation
        guard !activities.isEmpty else { return 0.0 }
        let dailyActivities = Dictionary(grouping: activities) { activity in
            Calendar.current.startOfDay(for: activity.startTime)
        }
        let activeDays = dailyActivities.count
        let totalDays = 30 // Last 30 days
        return Double(activeDays) / Double(totalDays)
    }
    private func calculateWeeklyActivityTrends(_ activities: [ActivityLog]) -> [WeeklyTrend] {
        // Implementation would calculate weekly trends
        return []
    }
    private func calculateBehaviorTrend(_ behaviors: [BehaviorLog]) -> TrendDirection {
        // Simplified trend calculation
        guard behaviors.count >= 2 else { return .stable }
        let sortedBehaviors = behaviors.sorted { $0.timestamp < $1.timestamp }
        let firstHalf = Array(sortedBehaviors.prefix(sortedBehaviors.count / 2))
        let secondHalf = Array(sortedBehaviors.suffix(sortedBehaviors.count / 2))
        if secondHalf.count > firstHalf.count {
            return .increasing
        } else if secondHalf.count < firstHalf.count {
            return .decreasing
        } else {
            return .stable
        }
    }
    private func analyzeBehaviorTriggers(_ behaviors: [BehaviorLog]) -> [String: Int] {
        // Analyze common triggers
        let allTriggers = behaviors.flatMap { $0.triggers }
        return Dictionary(grouping: allTriggers) { $0 }.mapValues { $0.count }
    }
    private func calculateBehaviorImprovementScore(_ behaviors: [BehaviorLog]) -> Double {
        // Simplified improvement score calculation
        let concerningBehaviorTypes = ["aggression", "anxiety", "pacing", "hiding"]
        let concerningBehaviors = behaviors.filter { concerningBehaviorTypes.contains($0.behaviorType) }
        let totalBehaviors = behaviors.count
        guard totalBehaviors > 0 else { return 1.0 }
        return 1.0 - (Double(concerningBehaviors.count) / Double(totalBehaviors))
    }
    private func analyzeWeatherImpact(_ history: [EnvironmentalSnapshot]) -> WeatherImpact {
        // Analyze how weather affects the pet
        return WeatherImpact(
            temperatureSensitivity: 0.6,
            humidityTolerance: 0.7,
            windSensitivity: 0.4,
            precipitationImpact: 0.8
        )
    }
    private func calculateAirQualityTrend(_ history: [EnvironmentalSnapshot]) -> TrendDirection {
        // Calculate air quality trend
        guard history.count >= 2 else { return .stable }
        let sortedHistory = history.sorted { $0.timestamp < $1.timestamp }
        let firstHalf = Array(sortedHistory.prefix(sortedHistory.count / 2))
        let secondHalf = Array(sortedHistory.suffix(sortedHistory.count / 2))
        let firstAverage = firstHalf.reduce(0) { $0 + $1.data.airQuality.index } / firstHalf.count
        let secondAverage = secondHalf.reduce(0) { $0 + $1.data.airQuality.index } / secondHalf.count
        if secondAverage > firstAverage + 5 {
            return .increasing
        } else if secondAverage < firstAverage - 5 {
            return .decreasing
        } else {
            return .stable
        }
    }
    private func calculateCommunityParticipation(_ metrics: SocialMetrics) -> Double {
        // Calculate community participation score
        let postsWeight = 0.3
        let likesWeight = 0.2
        let commentsWeight = 0.3
        let friendsWeight = 0.2
        let normalizedPosts = min(Double(metrics.postsCount) / 10.0, 1.0)
        let normalizedLikes = min(Double(metrics.totalLikes) / 50.0, 1.0)
        let normalizedComments = min(Double(metrics.totalComments) / 25.0, 1.0)
        let normalizedFriends = min(Double(metrics.friendsCount) / 20.0, 1.0)
        return (normalizedPosts * postsWeight) + 
               (normalizedLikes * likesWeight) + 
               (normalizedComments * commentsWeight) + 
               (normalizedFriends * friendsWeight)
    }
    // MARK: - ML Model Integration
    private func loadMLModels() {
        // Load Core ML models for predictions
        // Implementation would load actual ML models
        print("📱 Loading ML models for analytics")
    }
    private func predictHealthRisks(for petId: String, using model: MLModel) async throws -> [PredictiveInsight] {
        // Implementation would use ML model for health predictions
        return []
    }
    private func predictBehaviorChanges(for petId: String, using model: MLModel) async throws -> [PredictiveInsight] {
        // Implementation would use ML model for behavior predictions
        return []
    }
    private func generateActivityRecommendations(for petId: String, using model: MLModel) async throws -> [PredictiveInsight] {
        // Implementation would use ML model for activity recommendations
        return []
    }
    // MARK: - Data Subscriptions
    private func setupDataSubscriptions() {
        // Setup subscriptions to data changes for real-time analytics updates
        print("📡 Setting up data subscriptions for analytics")
    }
    // MARK: - Data Persistence
    private func saveAnalyticsToDatabase(petId: String) async {
        // Save analytics results to database
        print("💾 Saving analytics to database for pet: \(petId)")
    }
    // MARK: - Public API
    func getInsightsForCategory(_ category: InsightCategory) -> [PetInsight] {
        return petInsights.filter { $0.category == category }
    }
    func getHighPriorityInsights() -> [PetInsight] {
        return petInsights.filter { $0.priority == .high || $0.priority == .critical }
    }
    func getTrendsForMetric(_ metric: HealthMetric) -> [AnalyticsHealthTrend] {
        return healthTrends
    }
    func refreshAnalytics(for petId: String) async {
        await generateComprehensiveAnalytics(for: petId)
    }
}
// MARK: - Data Models
struct ActivityAnalytics: Codable {
    let petId: String
    let totalDuration: TimeInterval
    let totalDistance: Double
    let totalSteps: Int
    let averageDuration: TimeInterval
    let activityFrequency: [Int: Int] // Weekday -> Count
    let mostActiveHour: Int
    let consistencyScore: Double
    let weeklyTrends: [WeeklyTrend]
    let analysisDate: Date
}
struct BehaviorAnalytics: Codable {
    let petId: String
    let behaviorFrequency: [String: Int] // Using String to avoid type conflicts
    let concerningBehaviorTrend: TrendDirection
    let hourlyPatterns: [Int: Int]
    let triggerAnalysis: [String: Int]
    let improvementScore: Double
    let analysisDate: Date
}
struct HealthAnalytics: Codable {
    let petId: String
    let overallHealthScore: Double
    let vitalTrends: [String: TrendDirection]
    let symptomFrequency: [String: Int]
    let medicationAdherence: Double
    let vetVisitFrequency: Int
    let healthRiskFactors: [String]
    let analysisDate: Date
}
struct EnvironmentalAnalytics: Codable {
    let petId: String
    let averageEnvironmentalScore: Double
    let riskExposure: Double
    let weatherImpact: WeatherImpact
    let airQualityTrend: TrendDirection
    let pollenExposure: Double
    let optimalWalkingTimes: [String]
    let analysisDate: Date
}
struct SocialAnalytics: Codable {
    let petId: String
    let socialEngagementScore: Double
    let playdateFrequency: Double
    let socialConnectionGrowth: Double
    let communityParticipation: Double
    let analysisDate: Date
}
struct PetInsight: Identifiable, Codable {
    let id: String
    let petId: String
    let category: InsightCategory
    let title: String
    let description: String
    let priority: InsightPriority
    let actionable: Bool
    let recommendations: [String]
    let generatedAt: Date
}
// HealthTrend is defined in ComprehensiveHealthMonitoringService
// ActivityLog type alias removed to avoid redeclaration - using SharedActivityLog directly
struct PredictiveInsight: Identifiable, Codable {
    let id: String
    let petId: String
    let type: PredictionType
    let title: String
    let description: String
    let confidence: Double
    let timeframe: String
    let recommendations: [String]
    let generatedAt: Date
}
struct DashboardMetrics: Codable {
    let petId: String
    let overallWellnessScore: Double
    let activityScore: Double
    let healthScore: Double
    let behaviorScore: Double
    let socialScore: Double
    let environmentalScore: Double
    let trendsCount: Int
    let insightsCount: Int
    let lastUpdated: Date
}
struct WeeklyTrend: Codable {
    let week: Date
    let value: Double
    let change: Double
    let trend: TrendDirection
}
struct WeatherImpact: Codable {
    let temperatureSensitivity: Double
    let humidityTolerance: Double
    let windSensitivity: Double
    let precipitationImpact: Double
}
// MARK: - Enums
enum InsightCategory: String, CaseIterable, Codable {
    case activity = "activity"
    case behavior = "behavior"
    case health = "health"
    case environmental = "environmental"
    case social = "social"
    case nutrition = "nutrition"
    case sleep = "sleep"
    var displayName: String {
        rawValue.capitalized
    }
    var icon: String {
        switch self {
        case .activity: return "figure.walk"
        case .behavior: return "brain.head.profile"
        case .health: return "heart.fill"
        case .environmental: return "leaf.fill"
        case .social: return "person.2.fill"
        case .nutrition: return "fork.knife"
        case .sleep: return "bed.double.fill"
        }
    }
    var color: Color {
        switch self {
        case .activity: return .blue
        case .behavior: return .purple
        case .health: return .red
        case .environmental: return .green
        case .social: return .orange
        case .nutrition: return .yellow
        case .sleep: return .indigo
        }
    }
}
enum InsightPriority: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    var displayName: String {
        rawValue.capitalized
    }
    var color: Color {
        switch self {
        case .low: return .blue
        case .medium: return .orange
        case .high: return .red
        case .critical: return .purple
        }
    }
    var icon: String {
        switch self {
        case .low: return "info.circle"
        case .medium: return "exclamationmark.triangle"
        case .high: return "exclamationmark.octagon"
        case .critical: return "exclamationmark.octagon.fill"
        }
    }
}
enum TrendDirection: String, CaseIterable, Codable {
    case increasing = "increasing"
    case decreasing = "decreasing"
    case stable = "stable"
    case fluctuating = "fluctuating"
    var displayName: String {
        rawValue.capitalized
    }
    var icon: String {
        switch self {
        case .increasing: return "arrow.up.right"
        case .decreasing: return "arrow.down.right"
        case .stable: return "arrow.right"
        case .fluctuating: return "waveform.path.ecg"
        }
    }
    var color: Color {
        switch self {
        case .increasing: return .green
        case .decreasing: return .red
        case .stable: return .blue
        case .fluctuating: return .orange
        }
    }
}
// HealthMetric is defined elsewhere to avoid conflicts
enum TrendTimeframe: String, CaseIterable, Codable {
    case day = "day"
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    case year = "year"
    var displayName: String {
        switch self {
        case .day: return "Daily"
        case .week: return "Weekly"
        case .month: return "Monthly"
        case .quarter: return "Quarterly"
        case .year: return "Yearly"
        }
    }
}
enum TrendSignificance: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    var displayName: String {
        rawValue.capitalized
    }
    var color: Color {
        switch self {
        case .low: return .blue
        case .medium: return .orange
        case .high: return .red
        }
    }
}
enum PredictionType: String, CaseIterable, Codable {
    case healthRisk = "health_risk"
    case behaviorChange = "behavior_change"
    case activityRecommendation = "activity_recommendation"
    case environmentalAlert = "environmental_alert"
    case socialOpportunity = "social_opportunity"
    var displayName: String {
        switch self {
        case .healthRisk: return "Health Risk"
        case .behaviorChange: return "Behavior Change"
        case .activityRecommendation: return "Activity Recommendation"
        case .environmentalAlert: return "Environmental Alert"
        case .socialOpportunity: return "Social Opportunity"
        }
    }
    var icon: String {
        switch self {
        case .healthRisk: return "cross.fill"
        case .behaviorChange: return "brain.head.profile"
        case .activityRecommendation: return "figure.run"
        case .environmentalAlert: return "exclamationmark.triangle.fill"
        case .socialOpportunity: return "person.2.fill"
        }
    }
    var color: Color {
        switch self {
        case .healthRisk: return .red
        case .behaviorChange: return .purple
        case .activityRecommendation: return .blue
        case .environmentalAlert: return .orange
        case .socialOpportunity: return .green
        }
    }
}
