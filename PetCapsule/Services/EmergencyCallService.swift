//
//  EmergencyCallService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import CallKit
import AVFoundation
import Contacts
import SwiftUI

// Using SharedEmergencyContact directly to avoid redeclaration

@MainActor
class EmergencyCallService: NSObject, ObservableObject {
    static let shared = EmergencyCallService()
    
    @Published var isCallActive = false
    @Published var currentCall: EmergencyCall?
    @Published var callHistory: [EmergencyCall] = []
    @Published var emergencyContacts: [EmergencyContact] = []
    
    private let callController = CXCallController()
    private let provider: CXProvider
    private nonisolated(unsafe) var audioSession: AVAudioSession?
    
    override init() {
        // Configure CallKit provider
        let providerConfiguration = CXProviderConfiguration()
        // Note: localizedName is read-only, set via bundle display name
        providerConfiguration.maximumCallGroups = 1
        providerConfiguration.maximumCallsPerCallGroup = 1
        providerConfiguration.supportedHandleTypes = [.phoneNumber]
        providerConfiguration.supportsVideo = false
        
        // Set app icon for CallKit UI
        if let iconImage = UIImage(named: "AppIcon") {
            providerConfiguration.iconTemplateImageData = iconImage.pngData()
        }
        
        provider = CXProvider(configuration: providerConfiguration)
        
        super.init()
        
        provider.setDelegate(self, queue: nil)
        setupEmergencyContacts()
        setupAudioSession()
    }
    
    // MARK: - Setup
    
    private func setupEmergencyContacts() {
        emergencyContacts = [] // Temporarily disabled - build issues
    }
    
    private func setupAudioSession() {
        audioSession = AVAudioSession.sharedInstance()
        
        do {
            try audioSession?.setCategory(.playAndRecord, mode: .voiceChat, options: [])
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    // MARK: - Emergency Calling
    
    func initiateEmergencyCall(to contact: EmergencyContact, for pet: Pet?, emergencyType: EmergencyType) {
        let call = EmergencyCall(
            id: UUID().uuidString,
            contactId: contact.id,
            contactName: contact.name,
            contactPhone: contact.phoneNumber,
            emergencyType: emergencyType,
            petId: pet?.id,
            startTime: Date(),
            status: .initiated,
            location: nil,
            notes: nil
        )
        
        currentCall = call
        
        // Create CallKit call
        let handle = CXHandle(type: .phoneNumber, value: contact.phoneNumber)
        let startCallAction = CXStartCallAction(call: UUID(uuidString: call.id) ?? UUID(), handle: handle)
        
        startCallAction.contactIdentifier = call.contactName
        startCallAction.isVideo = false
        
        let transaction = CXTransaction(action: startCallAction)
        
        callController.request(transaction) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Failed to start call: \(error)")
                    self?.currentCall?.status = .failed
                } else {
                    self?.currentCall?.status = .connected
                    Task {
                        await self?.logEmergencyCall(call)
                    }
                }
            }
        }
    }
    
    func endCurrentCall() {
        guard let call = currentCall else { return }
        
        let endCallAction = CXEndCallAction(call: UUID(uuidString: call.id) ?? UUID())
        let transaction = CXTransaction(action: endCallAction)
        
        callController.request(transaction) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Failed to end call: \(error)")
                } else {
                    self?.currentCall?.endTime = Date()
                    self?.currentCall?.status = .ended
                    self?.isCallActive = false
                    
                    if let completedCall = self?.currentCall {
                        self?.callHistory.append(completedCall)
                    }
                    
                    self?.currentCall = nil
                }
            }
        }
    }
    
    // MARK: - Contact Management
    
    func addEmergencyContact(_ contact: EmergencyContact) {
        emergencyContacts.append(contact)
        saveEmergencyContacts()
    }
    
    func removeEmergencyContact(_ contact: EmergencyContact) {
        emergencyContacts.removeAll { $0.id == contact.id }
        saveEmergencyContacts()
    }
    
    func updateEmergencyContact(_ contact: EmergencyContact) {
        if let index = emergencyContacts.firstIndex(where: { $0.id == contact.id }) {
            emergencyContacts[index] = contact
            saveEmergencyContacts()
        }
    }
    
    func getDefaultContact(for type: String) -> EmergencyContact? {
        // Map string types to EmergencyContactType and find first matching contact
        let contactType: EmergencyContactType?
        switch type.lowercased() {
        case "emergency":
            contactType = .emergency
        case "poison":
            contactType = .emergency
        case "veterinarian":
            contactType = .veterinarian
        case "animal hospital":
            contactType = .veterinarian
        default:
            contactType = nil
        }
        
        if let contactType = contactType {
            return emergencyContacts.first { $0.type == contactType }
        }
        
        return emergencyContacts.first
    }
    
    // MARK: - Quick Actions
    
    func callEmergencyVet(for pet: Pet?) {
        guard let contact = getDefaultContact(for: "emergency") else {
            print("No default emergency vet contact found")
            return
        }
        
        initiateEmergencyCall(to: contact, for: pet, emergencyType: .veterinary)
    }
    
    func callPoisonControl(for pet: Pet?) {
        guard let contact = getDefaultContact(for: "poison") else {
            print("No poison control contact found")
            return
        }
        
        initiateEmergencyCall(to: contact, for: pet, emergencyType: .poison)
    }
    
    func callPrimaryVet(for pet: Pet?) {
        guard let contact = getDefaultContact(for: "veterinarian") else {
            print("No primary vet contact found")
            return
        }
        
        initiateEmergencyCall(to: contact, for: pet, emergencyType: .veterinary)
    }
    
    // MARK: - Call History
    
    private func logEmergencyCall(_ call: EmergencyCall) async {
        // Log call for analytics and history
        print("Emergency call initiated: \(call.contactName) for \(call.emergencyType.rawValue)")
        
        // Send analytics event
        await AnalyticsService.shared.trackEvent("emergency_call_initiated", parameters: [
            "contact_name": call.contactName,
            "emergency_type": call.emergencyType.rawValue,
            "has_pet": call.petId != nil
        ])
    }
    
    // MARK: - Persistence
    
    private func saveEmergencyContacts() {
        // Save to UserDefaults or Core Data
        if let data = try? JSONEncoder().encode(emergencyContacts) {
            UserDefaults.standard.set(data, forKey: "emergency_contacts")
        }
    }
    
    private func loadEmergencyContacts() {
        if let data = UserDefaults.standard.data(forKey: "emergency_contacts"),
           let contacts = try? JSONDecoder().decode([EmergencyContact].self, from: data) {
            emergencyContacts = contacts
        }
    }
}

// MARK: - CXProviderDelegate

extension EmergencyCallService: CXProviderDelegate {

    nonisolated func providerDidReset(_ provider: CXProvider) {
        // Handle provider reset
        Task { @MainActor in
            currentCall = nil
            isCallActive = false
        }
    }
    
    nonisolated func provider(_ provider: CXProvider, perform action: CXStartCallAction) {
        // Configure audio session
        do {
            try audioSession?.setActive(true)
        } catch {
            print("Failed to activate audio session: \(error)")
        }
        
        // Report call as connected
        provider.reportOutgoingCall(with: action.callUUID, startedConnectingAt: Date())
        provider.reportOutgoingCall(with: action.callUUID, connectedAt: Date())
        
        DispatchQueue.main.async {
            self.isCallActive = true
            self.currentCall?.status = .connected
        }
        
        action.fulfill()
    }
    
    nonisolated func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
        // Deactivate audio session
        do {
            try audioSession?.setActive(false)
        } catch {
            print("Failed to deactivate audio session: \(error)")
        }
        
        DispatchQueue.main.async {
            self.isCallActive = false
            self.currentCall?.status = .ended
            self.currentCall?.endTime = Date()
            
            if let call = self.currentCall {
                self.callHistory.append(call)
            }
            
            self.currentCall = nil
        }
        
        action.fulfill()
    }
    
    nonisolated func provider(_ provider: CXProvider, perform action: CXSetHeldCallAction) {
        action.fulfill()
    }

    nonisolated func provider(_ provider: CXProvider, perform action: CXSetMutedCallAction) {
        action.fulfill()
    }
}
