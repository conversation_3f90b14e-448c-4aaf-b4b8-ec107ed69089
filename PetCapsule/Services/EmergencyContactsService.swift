//
//  EmergencyContactsService.swift
//  PetCapsule
//
//  Emergency contacts service with local SwiftData integration
//  Stores emergency contacts locally with CloudKit sync capability
//

import Foundation
import SwiftUI
import SwiftData
import Contacts

@MainActor
class EmergencyContactsService: ObservableObject {
    static let shared = EmergencyContactsService()
    
    @Published var emergencyContacts: [ServiceEmergencyContact] = []
    @Published var isLoading = false
    @Published var error: String?
    
    // Computed properties for compatibility
    var currentCountryContacts: [ServiceEmergencyContact] {
        return emergencyContacts.filter { $0.isVeterinarian }
    }
    
    var customContacts: [ServiceEmergencyContact] {
        return emergencyContacts.filter { !$0.isVeterinarian }
    }
    
    var allEmergencyContacts: [ServiceEmergencyContact] {
        return emergencyContacts
    }
    
    var isSimulator: Bool {
        #if targetEnvironment(simulator)
        return true
        #else
        return false
        #endif
    }
    
    // Additional properties for compatibility
    var primaryContacts: [ServiceEmergencyContact] {
        return emergencyContacts.filter { $0.isVeterinarian }
    }
    
    var userCountryCode: String {
        return "US" // Default to US
    }
    
    // Conversion method for compatibility
    func convertToEmergencyContact(_ serviceContact: ServiceEmergencyContact) -> EmergencyContact {
        return EmergencyContact(
            id: serviceContact.id.uuidString,
            name: serviceContact.name,
            phoneNumber: serviceContact.primaryPhone,
            type: serviceContact.isVeterinarian ? .veterinarian : .family,
            country: "US",
            isEditable: true,
            description: serviceContact.notes,
            isPrimary: false
        )
    }
    
    // Additional methods for compatibility
    func cleanupDuplicateContacts() async {
        // Remove duplicate contacts based on phone number
        let uniqueContacts = Array(Set(emergencyContacts.map { $0.primaryPhone }))
        emergencyContacts = emergencyContacts.filter { contact in
            uniqueContacts.contains(contact.primaryPhone)
        }
    }
    
    func callEmergencyContact(_ contact: EmergencyContact) {
        // Placeholder for emergency call functionality
        print("Calling emergency contact: \(contact.name) at \(contact.phoneNumber)")
    }
    
    func fetchContacts() async -> [CNContact] {
        // This would fetch from the device's contact store
        // For now, return empty array as placeholder
        return []
    }
    
    func importContactAsEmergencyContact(_ contact: CNContact, type: EmergencyContactType) async -> Bool {
        // Convert CNContact to ServiceEmergencyContact and save
        if let phoneNumber = contact.phoneNumbers.first?.value.stringValue {
            let emergencyContact = ServiceEmergencyContact(
                name: "\(contact.givenName) \(contact.familyName)".trimmingCharacters(in: .whitespaces),
                relationship: type.displayName,
                primaryPhone: phoneNumber,
                email: contact.emailAddresses.first?.value as String?,
                isVeterinarian: type == .veterinarian || type == .animalHospital
            )
            
            do {
                try await addEmergencyContact(
                    name: emergencyContact.name,
                    relationship: emergencyContact.relationship,
                    primaryPhone: emergencyContact.primaryPhone,
                    secondaryPhone: emergencyContact.secondaryPhone,
                    email: emergencyContact.email,
                    address: emergencyContact.address,
                    notes: emergencyContact.notes,
                    isVeterinarian: emergencyContact.isVeterinarian,
                    clinicName: emergencyContact.clinicName
                )
                return true
            } catch {
                print("Failed to import contact: \(error)")
                return false
            }
        }
        return false
    }
    
    private let dataService = AppleNativeDataService.shared
    
    private init() {
        loadEmergencyContacts()
    }
    
    // MARK: - Data Loading
    func loadEmergencyContacts() {
        isLoading = true
        error = nil
        
        Task {
            do {
                let contacts = await dataService.getEmergencyContacts()
                await MainActor.run {
                    self.emergencyContacts = contacts
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.error = "Failed to load emergency contacts: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    // MARK: - Contact Management
    func addEmergencyContact(
        name: String,
        relationship: String,
        primaryPhone: String,
        secondaryPhone: String? = nil,
        email: String? = nil,
        address: String? = nil,
        notes: String? = nil,
        isVeterinarian: Bool = false,
        clinicName: String? = nil
    ) async -> Bool {
        isLoading = true
        defer { isLoading = false }
        
        do {
            let contact = ServiceEmergencyContact(
                name: name,
                relationship: relationship,
                primaryPhone: primaryPhone,
                secondaryPhone: secondaryPhone,
                email: email,
                address: address,
                notes: notes,
                isVeterinarian: isVeterinarian,
                clinicName: clinicName
            )
            
            try await dataService.saveEmergencyContact(contact)
            
            await MainActor.run {
                self.emergencyContacts.append(contact)
            }
            return true
        } catch {
            print("Failed to add emergency contact: \(error)")
            return false
        }
    }
    
    func updateEmergencyContact(_ contact: ServiceEmergencyContact) async -> Bool {
        isLoading = true
        defer { isLoading = false }
        
        do {
            try await dataService.updateEmergencyContact(contact)
            
            await MainActor.run {
                if let index = self.emergencyContacts.firstIndex(where: { $0.id == contact.id }) {
                    self.emergencyContacts[index] = contact
                }
            }
            return true
        } catch {
            print("Failed to update emergency contact: \(error)")
            return false
        }
    }
    
    func deleteEmergencyContact(_ contact: ServiceEmergencyContact) async throws {
        isLoading = true
        defer { isLoading = false }
        
        try await dataService.deleteEmergencyContact(contact)
        
        await MainActor.run {
            self.emergencyContacts.removeAll { $0.id == contact.id }
        }
    }
    
    // MARK: - Convenience Methods
    func getVeterinarians() -> [ServiceEmergencyContact] {
        return emergencyContacts.filter { $0.isVeterinarian }
    }

    func getFamilyContacts() -> [ServiceEmergencyContact] {
        return emergencyContacts.filter { !$0.isVeterinarian }
    }
    
    func importFromContacts() async throws {
        let store = CNContactStore()
        
        // Request permission
        let status = CNContactStore.authorizationStatus(for: .contacts)
        if status == .notDetermined {
            _ = try await store.requestAccess(for: .contacts)
        }
        
        guard CNContactStore.authorizationStatus(for: .contacts) == .authorized else {
            throw EmergencyContactError.contactsPermissionDenied
        }
        
        // Fetch contacts
        let keys = [CNContactGivenNameKey, CNContactFamilyNameKey, CNContactPhoneNumbersKey, CNContactEmailAddressesKey] as [CNKeyDescriptor]
        let request = CNContactFetchRequest(keysToFetch: keys)
        
        var importedContacts: [ServiceEmergencyContact] = []

        try store.enumerateContacts(with: request) { contact, _ in
            if let phoneNumber = contact.phoneNumbers.first?.value.stringValue {
                let emergencyContact = ServiceEmergencyContact(
                    name: "\(contact.givenName) \(contact.familyName)".trimmingCharacters(in: .whitespaces),
                    relationship: "Contact",
                    primaryPhone: phoneNumber,
                    email: contact.emailAddresses.first?.value as String?
                )
                importedContacts.append(emergencyContact)
            }
        }
        
        // Save imported contacts
        for contact in importedContacts {
            try await dataService.saveEmergencyContact(contact)
        }
        
        await MainActor.run {
            self.emergencyContacts.append(contentsOf: importedContacts)
        }
    }
}

// MARK: - Data Models
struct ServiceEmergencyContact: Identifiable, Codable {
    let id = UUID()
    var name: String
    var relationship: String
    var primaryPhone: String
    var secondaryPhone: String?
    var email: String?
    var address: String?
    var notes: String?
    var isVeterinarian: Bool
    var clinicName: String?
    var createdAt: Date
    var updatedAt: Date
    
    init(
        name: String,
        relationship: String,
        primaryPhone: String,
        secondaryPhone: String? = nil,
        email: String? = nil,
        address: String? = nil,
        notes: String? = nil,
        isVeterinarian: Bool = false,
        clinicName: String? = nil
    ) {
        self.name = name
        self.relationship = relationship
        self.primaryPhone = primaryPhone
        self.secondaryPhone = secondaryPhone
        self.email = email
        self.address = address
        self.notes = notes
        self.isVeterinarian = isVeterinarian
        self.clinicName = clinicName
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Errors
enum EmergencyContactError: Error, LocalizedError {
    case contactsPermissionDenied
    case invalidPhoneNumber
    case contactNotFound
    
    var errorDescription: String? {
        switch self {
        case .contactsPermissionDenied:
            return "Permission to access contacts was denied"
        case .invalidPhoneNumber:
            return "Invalid phone number format"
        case .contactNotFound:
            return "Emergency contact not found"
        }
    }
}

// MARK: - Extension for AppleNativeDataService
extension AppleNativeDataService {
    func getEmergencyContacts() async -> [ServiceEmergencyContact] {
        // This would be implemented to fetch from SwiftData
        // For now, return empty array
        return []
    }

    func saveEmergencyContact(_ contact: ServiceEmergencyContact) async throws {
        // This would be implemented to save to SwiftData
        print("Saving emergency contact: \(contact.name)")
    }

    func updateEmergencyContact(_ contact: ServiceEmergencyContact) async throws {
        // This would be implemented to update in SwiftData
        print("Updating emergency contact: \(contact.name)")
    }
    
    func deleteEmergencyContact(_ contact: ServiceEmergencyContact) async throws {
        // This would be implemented to delete from SwiftData
        print("Deleting emergency contact: \(contact.name)")
    }
}