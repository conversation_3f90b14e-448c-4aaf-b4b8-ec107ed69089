//
//  EmergencyPlanningService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//
import Foundation
import CoreLocation
// Simulates a backend service for managing emergency plans and contacts
@MainActor
class EmergencyPlanningService: ObservableObject {
    static let shared = EmergencyPlanningService()
    @Published var emergencyPlan: EmergencyPlan?
    @Published var emergencyContacts: [EmergencyContact] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    private let dataService = AppleNativeDataService.shared
    func initialize() async {
        self.isLoading = true
        defer { self.isLoading = false }
        emergencyContacts = await loadEmergencyContacts()
        emergencyPlan = await loadEmergencyPlan()
    }
    // MARK: - Contact Management
    func addEmergencyContact(name: String, phone: String, type: EmergencyContactType, notes: String?, isPrimary: Bool) async throws -> EmergencyContact {
        // Create emergency contact using local service
        let contact = EmergencyContact(
            name: name,
            phoneNumber: phone,
            type: type,
            description: notes,
            isPrimary: isPrimary
        )
        // Save to local storage via dataService
        _ = try await EmergencyContactsService.shared.addEmergencyContact(
            name: name,
            relationship: type.displayName,
            primaryPhone: phone,
            notes: notes,
            isVeterinarian: type == .veterinarian
        )
        return contact
    }
    func updateEmergencyContact(_ contact: EmergencyContact) async {
        await saveContactToDatabase(contact)
        if let index = emergencyContacts.firstIndex(where: { $0.id == contact.id }) {
            emergencyContacts[index] = contact
        }
    }
    func deleteEmergencyContact(_ contact: EmergencyContact) async {
        // Placeholder implementation - would delete from database
        emergencyContacts.removeAll { $0.id == contact.id }
    }
    func getPrimaryEmergencyContact() -> EmergencyContact? {
        // Since the new structure doesn't have isPrimary, return the first emergency type contact
        return emergencyContacts.first { $0.type == .emergency }
    }
    func getVeterinaryContacts() -> [EmergencyContact] {
        return emergencyContacts.filter { $0.type == .veterinarian }
    }
    // MARK: - Plan Management
    func createOrUpdateEmergencyPlan(
        homeAccessInstructions: String,
        petCareInstructions: String,
        evacuationKitLocation: String,
        meetupLocation: String
    ) async {
        let plan = EmergencyPlan(
            id: emergencyPlan?.id ?? UUID().uuidString,
            petId: "default-pet",
            evacuationPlan: EvacuationPlan(
                meetingPoint: meetupLocation,
                evacuationRoute: homeAccessInstructions,
                shelterInPlaceInstructions: petCareInstructions
            ),
            emergencyKit: EmergencyKit(id: UUID().uuidString, items: []),
            emergencyContacts: emergencyContacts.map { contact in
                SharedEmergencyContact(
                    id: contact.id,
                    name: contact.name,
                    phoneNumber: contact.phoneNumber,
                    relationship: contact.type.displayName
                )
            },
            notes: evacuationKitLocation
        )
        await saveEmergencyPlanToDatabase(plan)
        self.emergencyPlan = plan
    }
    // MARK: - Database Operations
    private func saveContactToDatabase(_ contact: EmergencyContact) async {
        // Placeholder implementation - would save to database
    }
    private func saveEmergencyPlanToDatabase(_ plan: EmergencyPlan) async {
        // Placeholder implementation - would save to database
    }
    private func loadEmergencyContacts() async -> [EmergencyContact] {
        // Placeholder implementation - would load from database
        return []
    }
    private func loadEmergencyPlan() async -> EmergencyPlan? {
        // Placeholder implementation - would load from database
        return nil
    }
} 