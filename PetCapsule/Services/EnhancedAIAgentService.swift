//
//  EnhancedAIAgentService.swift
//  PetCapsule
//
//  🍎 10/10 Production-Ready Apple Intelligence System - NO EXTERNAL AI
//  🤖 Advanced multi-agent system powered by Apple's local foundation models
//
import Foundation
import SwiftUI
import Speech
import AVFoundation
@available(iOS 18.0, *)
@MainActor
class EnhancedAIAgentService: ObservableObject {
    
    static let shared = EnhancedAIAgentService()
    @Published var isLoading = false
    @Published var currentAgent: AIAgent?
    @Published var conversationHistory: [String: [ChatMessage]] = [:]
    @Published var availableAgents: [AIAgent] = []
    @Published var lastError: String?
    @Published var agentAvailabilityStatus: [String: Bool] = [:]
    // Voice capabilities
    @Published var isListening = false
    @Published var isProcessingVoice = false
    // Conversation service integration
    private let conversationService = AIConversationService.shared
    private let knowledgeBaseService = KnowledgeBaseService.shared
    private let webSearchService = WebSearchService.shared
    private weak var realDataService: RealDataService?
    private let petDataManager = PetDataManager.shared
    private let responseValidator = AIResponseValidator.shared
    private let speechRecognizer = SFSpeechRecognizer()
    private let audioEngine = AVAudioEngine()
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let synthesizer = AVSpeechSynthesizer()
    // Using Apple native storage for conversation history
    // Context management
    private var petContexts: [String: Pet] = [:]
    private var conversationContexts: [String: [String: Any]] = [:]
    // Enhanced features
    @Published var allUserPets: [Pet] = []
    @Published var agentDataCache: [String: [String: Any]] = [:]
    // Apple Intelligence Integration
    private var enhancedAppleIntelligenceService: EnhancedAppleIntelligenceService? {
        if #available(iOS 18.0, *) {
            return EnhancedAppleIntelligenceService.shared
        }
        return nil
    }
    
    // Vector Database for semantic search
    private var vectorDatabaseService: LocalVectorDatabaseService? {
        if #available(iOS 18.0, *) {
            return LocalVectorDatabaseService.shared
        }
        return nil
    }
    private init() {
        setupAgents()
        Task {
            await loadUserPets()
            await loadConversationHistory()
        }
    }
    // MARK: - Configuration
    func setRealDataService(_ service: RealDataService) {
        self.realDataService = service
    }
    // MARK: - Agent Setup
    private func setupAgents() {
        availableAgents = [
            // 👑 Pet Master - Ultimate AI Agent (Featured First)
            AIAgent(
                name: "Pet Master",
                iconName: "crown.fill",
                description: "Ultimate AI companion with comprehensive pet care knowledge and access to all data sources",
                specialty: "Master",
                specialties: ["All Specialties", "Data Integration", "Knowledge Base", "Comprehensive Analysis"],
                gradientColors: ["#FF6B35", "#F7931E"],
                isPremium: false,
                systemPrompt: """
                You are Pet Master, the ultimate AI pet care companion with access to all specialized knowledge areas including health, nutrition, training, grooming, and shopping. 
                
                PET MASTER INSTRUCTIONS:
                1. Provide comprehensive, well-researched answers using all available resources
                2. Access cross-agent conversation history for context
                3. Integrate insights from all other AI agents
                4. Offer holistic pet care recommendations
                5. Coordinate between different care aspects
                6. Provide emergency guidance when needed
                7. Use Apple Intelligence for local processing
                8. Maintain conversation context across all interactions
                
                SPECIAL CAPABILITIES:
                - Cross-Agent Conversation History
                - Recent Agent Insights
                - Comprehensive Pet Analysis
                - Emergency Response Coordination
                - Multi-Specialty Recommendations
                """,
                conversationStarters: [
                    "Give me a complete analysis of my pet",
                    "Help with a complex pet situation",
                    "What's the best overall care plan?",
                    "I need expert advice on multiple topics",
                    "Coordinate care between different specialists"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 1200,
                    temperature: 0.6,
                    tone: "comprehensive",
                    responseStyle: "detailed",
                    expertise: "expert"
                )
            ),
            // 🏥 Health Guardian - Medical & Emergency Specialist
            AIAgent(
                name: "Health Guardian",
                iconName: "heart.circle.fill",
                description: "Your pet's health monitoring specialist with emergency guidance",
                specialty: "Health & Emergency",
                specialties: ["Health monitoring", "Emergency guidance", "Symptom analysis", "Vet coordination"],
                gradientColors: ["#FF6B6B", "#4ECDC4"],
                isPremium: false,
                systemPrompt: """
                You are Health Guardian, a specialized veterinary AI assistant focused on pet health monitoring and emergency guidance.
                
                HEALTH GUARDIAN INSTRUCTIONS:
                1. Monitor pet health indicators and symptoms
                2. Provide emergency guidance for urgent situations
                3. Analyze health patterns and trends
                4. Coordinate with veterinary care
                5. Assess urgency levels accurately
                6. Provide preventive health recommendations
                7. Use Apple Intelligence for local health analysis
                8. Maintain detailed health conversation history
                
                EMERGENCY PROTOCOLS:
                - Immediate assessment of symptom severity
                - Clear action steps for urgent situations
                - Contact information for emergency services
                - Pre-vet visit preparation guidance
                """,
                conversationStarters: [
                    "How is my pet's health today?",
                    "I'm worried about my pet's symptoms",
                    "What should I do in an emergency?",
                    "Help me prepare for a vet visit",
                    "Monitor my pet's health patterns"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.3,
                    tone: "caring",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            ),
            // 🍽️ Dr. Nutrition - Nutritional Expert
            AIAgent(
                name: "Dr. Nutrition",
                iconName: "leaf.circle.fill",
                description: "Expert nutritional guidance with personalized meal planning",
                specialty: "Nutrition",
                specialties: ["Diet planning", "Weight management", "Nutritional analysis", "Meal scheduling"],
                gradientColors: ["#4CAF50", "#8BC34A"],
                isPremium: false,
                systemPrompt: """
                You are Dr. Nutrition, a specialized pet nutrition expert providing dietary guidance and meal planning.
                
                NUTRITION SPECIALIST INSTRUCTIONS:
                1. Create personalized meal plans based on pet data
                2. Calculate appropriate caloric intake
                3. Recommend high-quality food options
                4. Monitor weight management goals
                5. Address dietary restrictions and allergies
                6. Provide supplement recommendations
                7. Use Apple Intelligence for nutritional calculations
                8. Track feeding schedules and portion control
                
                NUTRITION CAPABILITIES:
                - Caloric requirement calculations
                - Portion size recommendations
                - Food quality analysis
                - Supplement guidance
                - Weight management tracking
                """,
                conversationStarters: [
                    "What should I feed my pet today?",
                    "Help me plan my pet's meals",
                    "My pet needs to lose/gain weight",
                    "Recommend high-quality food options",
                    "Calculate proper portion sizes"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.4,
                    tone: "professional",
                    responseStyle: "step-by-step",
                    expertise: "intermediate"
                )
            ),
            // 🎓 Trainer Pro - Behavioral Training Expert
            AIAgent(
                name: "Trainer Pro",
                iconName: "figure.walk.circle.fill",
                description: "Professional training guidance with positive reinforcement methods",
                specialty: "Training",
                specialties: ["Behavior training", "Obedience", "Problem solving", "Socialization"],
                gradientColors: ["#2196F3", "#03DAC5"],
                isPremium: false,
                systemPrompt: """
                You are Trainer Pro, a professional pet trainer specializing in positive reinforcement training methods.
                
                TRAINING SPECIALIST INSTRUCTIONS:
                1. Develop personalized training plans
                2. Address behavioral issues with positive methods
                3. Provide step-by-step training instructions
                4. Monitor training progress and milestones
                5. Recommend socialization opportunities
                6. Handle problem behaviors effectively
                7. Use Apple Intelligence for training analysis
                8. Track training sessions and achievements
                
                TRAINING CAPABILITIES:
                - Basic obedience training
                - Behavioral modification
                - Socialization guidance
                - Problem-solving strategies
                - Training progress tracking
                """,
                conversationStarters: [
                    "Help me train my pet",
                    "My pet has behavioral issues",
                    "Teach me positive reinforcement",
                    "Plan a training session",
                    "Address specific behavior problems"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.5,
                    tone: "encouraging",
                    responseStyle: "step-by-step",
                    expertise: "intermediate"
                )
            ),
            // ✂️ Style Guru - Grooming & Care Specialist
            AIAgent(
                name: "Style Guru",
                iconName: "scissors.circle.fill",
                description: "Expert grooming advice and pet care styling recommendations",
                specialty: "Grooming",
                specialties: ["Grooming", "Styling", "Coat care", "Hygiene"],
                gradientColors: ["#E91E63", "#9C27B0"],
                isPremium: false,
                systemPrompt: """
                You are Style Guru, a pet grooming and care specialist providing expert advice on pet styling and maintenance.
                
                GROOMING SPECIALIST INSTRUCTIONS:
                1. Provide breed-specific grooming recommendations
                2. Create grooming schedules and routines
                3. Recommend grooming tools and products
                4. Address coat and skin care needs
                5. Suggest styling options and trends
                6. Monitor grooming health indicators
                7. Use Apple Intelligence for grooming analysis
                8. Track grooming appointments and care
                
                GROOMING CAPABILITIES:
                - Breed-specific grooming guidance
                - Seasonal coat care recommendations
                - Grooming tool and product advice
                - Styling and appearance tips
                - Hygiene and maintenance schedules
                """,
                conversationStarters: [
                    "How should I groom my pet?",
                    "Recommend grooming products",
                    "Create a grooming schedule",
                    "Help with coat care",
                    "Suggest styling options"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 700,
                    temperature: 0.6,
                    tone: "stylish",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            ),
            // 🛒 Shopping Assistant - Product Recommendations
            AIAgent(
                name: "Shopping Assistant",
                iconName: "cart.circle.fill",
                description: "Smart product recommendations and shopping guidance",
                specialty: "Shopping",
                specialties: ["Product recommendations", "Price comparison", "Shopping lists", "Reviews"],
                gradientColors: ["#FF9800", "#FF5722"],
                isPremium: false,
                systemPrompt: """
                You are Shopping Assistant, a pet product expert providing smart recommendations and shopping guidance.
                
                SHOPPING SPECIALIST INSTRUCTIONS:
                1. Recommend high-quality pet products
                2. Compare prices and value options
                3. Create shopping lists and budgets
                4. Provide product reviews and ratings
                5. Suggest seasonal and special occasion items
                6. Monitor product safety and recalls
                7. Use Apple Intelligence for product analysis
                8. Track shopping history and preferences
                
                SHOPPING CAPABILITIES:
                - Product recommendation engine
                - Price comparison analysis
                - Shopping list management
                - Product safety monitoring
                - Budget-friendly alternatives
                """,
                conversationStarters: [
                    "Recommend products for my pet",
                    "Help me find the best deals",
                    "Create a shopping list",
                    "Compare product options",
                    "Find budget-friendly alternatives"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 700,
                    temperature: 0.5,
                    tone: "helpful",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            ),
            // 🛡️ Insurance Advisor - Insurance & Financial Planning
            AIAgent(
                name: "Insurance Advisor",
                iconName: "shield.circle.fill",
                description: "Pet insurance guidance and financial planning for pet care",
                specialty: "Insurance",
                specialties: ["Insurance guidance", "Financial planning", "Cost analysis", "Coverage comparison"],
                gradientColors: ["#607D8B", "#455A64"],
                isPremium: false,
                systemPrompt: """
                You are Insurance Advisor, a pet insurance and financial planning specialist for pet care expenses.
                
                INSURANCE SPECIALIST INSTRUCTIONS:
                1. Provide pet insurance guidance and comparisons
                2. Analyze pet care costs and budgeting
                3. Recommend financial planning strategies
                4. Explain coverage options and limitations
                5. Help with claims and reimbursement
                6. Monitor insurance market trends
                7. Use Apple Intelligence for cost analysis
                8. Track insurance policies and renewals
                
                INSURANCE CAPABILITIES:
                - Insurance policy comparison
                - Cost-benefit analysis
                - Financial planning guidance
                - Claims assistance
                - Budget optimization
                """,
                conversationStarters: [
                    "Help me choose pet insurance",
                    "Analyze pet care costs",
                    "Plan for emergency expenses",
                    "Compare insurance policies",
                    "Budget for pet care"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 800,
                    temperature: 0.4,
                    tone: "professional",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            ),
            // 🌟 Wellness Coach - Mental Health & Lifestyle
            AIAgent(
                name: "Wellness Coach",
                iconName: "sparkles.circle.fill",
                description: "Mental health support and lifestyle optimization for pets",
                specialty: "Wellness",
                specialties: ["Mental health", "Lifestyle optimization", "Stress management", "Enrichment"],
                gradientColors: ["#9C27B0", "#673AB7"],
                isPremium: false,
                systemPrompt: """
                You are Wellness Coach, a pet mental health and lifestyle specialist focusing on overall well-being.
                
                WELLNESS SPECIALIST INSTRUCTIONS:
                1. Assess pet mental health and stress levels
                2. Recommend enrichment activities and toys
                3. Create lifestyle optimization plans
                4. Address anxiety and behavioral issues
                5. Suggest stress management techniques
                6. Monitor overall well-being indicators
                7. Use Apple Intelligence for wellness analysis
                8. Track mental health and lifestyle progress
                
                WELLNESS CAPABILITIES:
                - Mental health assessment
                - Enrichment activity planning
                - Stress management techniques
                - Lifestyle optimization
                - Well-being monitoring
                """,
                conversationStarters: [
                    "Help with my pet's mental health",
                    "Recommend enrichment activities",
                    "Address stress and anxiety",
                    "Optimize my pet's lifestyle",
                    "Create a wellness plan"
                ],
                responseConfig: AIResponseConfig(
                    maxTokens: 700,
                    temperature: 0.6,
                    tone: "supportive",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            )
        ]
        
        // Set all agents as available
        for agent in availableAgents {
            agentAvailabilityStatus[agent.id.uuidString] = true
        }
    }
    private func markAllAgentsAsAvailable() {
        for agent in availableAgents {
            agentAvailabilityStatus[agent.id.uuidString] = true
        }
    }
    func isAgentAvailable(_ agent: AIAgent) -> Bool {
        return agentAvailabilityStatus[agent.id.uuidString] ?? true
    }
    // MARK: - Core Chat Functions
    // MARK: - Enhanced Message Processing with Vector Database
    
    func sendMessage(to agent: AIAgent, message: String, pet: Pet? = nil, includeImage: UIImage? = nil) async throws -> String {
        // Store user message in vector database for semantic search
        vectorDatabaseService?.addChatMessage(
            agentId: agent.id,
            petId: pet?.id,
            content: message,
            messageType: "user",
            timestamp: Date()
        )
        
        // Add to conversation history
        let userMessage = ChatMessage(
            content: message,
            isFromUser: true,
            timestamp: Date(),
            agentId: agent.id
        )
        
        let conversationKey = agent.id.uuidString
        if conversationHistory[conversationKey] == nil {
            conversationHistory[conversationKey] = []
        }
        conversationHistory[conversationKey]?.append(userMessage)
        
        // Build enhanced context with vector search
        let _ = await buildEnhancedContextWithVectorSearch(for: agent, message: message, pet: pet)
        
        // Generate AI response
        let aiResponse: String
        if agent.name == "Pet Master" {
            aiResponse = await handlePetMasterRequest(message: message, pet: pet, includeImage: includeImage)
        } else {
            aiResponse = await enhancedAppleIntelligenceService?.sendMessage(
                to: agent,
                message: message,
                pet: pet
            ) ?? generateFallbackResponse(for: agent, message: message, pet: pet)
        }
        
        // Store AI response in vector database
        vectorDatabaseService?.addChatMessage(
            agentId: agent.id,
            petId: pet?.id,
            content: aiResponse,
            messageType: "ai",
            timestamp: Date()
        )
        
        // Add AI response to conversation history
        let aiMessage = ChatMessage(
            content: aiResponse,
            isFromUser: false,
            timestamp: Date(),
            agentId: agent.id
        )
        conversationHistory[conversationKey]?.append(aiMessage)
        
        // Cache agent insight for Pet Master access
        cacheAgentInsight(agentId: agent.id, insight: aiResponse)
        
        return aiResponse
    }
    
    // Enhanced context building with vector search
    private func buildEnhancedContextWithVectorSearch(for agent: AIAgent, message: String, pet: Pet?) async -> [String: Any] {
        var context = await buildEnhancedContext(for: agent, message: message, pet: pet)
        
        // Add semantic search results from vector database
        if let vectorService = vectorDatabaseService {
            // Search for relevant chat history
            let relevantHistory = vectorService.searchChatHistory(
                query: message,
                agentId: agent.id,
                petId: pet?.id,
                limit: 5
            )
            
            if !relevantHistory.isEmpty {
                context["semantic_chat_history"] = relevantHistory.map { vector in
                    [
                        "content": vector.content,
                        "message_type": vector.metadata.messageType,
                        "timestamp": vector.timestamp,
                        "relevance_score": vector.relevanceScore
                    ]
                }
            }
            
            // Search for relevant pet context
            if let pet = pet {
                let petContext = vectorService.searchPetContext(
                    query: message,
                    petId: pet.id,
                    limit: 3
                )
                
                if !petContext.isEmpty {
                    context["semantic_pet_context"] = petContext.map { vector in
                        [
                            "content": vector.content,
                            "context_type": vector.metadata.contextType,
                            "timestamp": vector.timestamp
                        ]
                    }
                }
            }
            
            // For Pet Master, search across all agents
            if agent.name == "Pet Master" {
                let crossAgentResults = vectorService.searchCrossAgentHistory(
                    query: message,
                    petId: pet?.id,
                    limit: 10
                )
                
                if !crossAgentResults.isEmpty {
                    context["cross_agent_semantic_results"] = crossAgentResults
                }
            }
        }
        
        return context
    }
    // MARK: - Voice Integration
    func startVoiceRecognition(for agent: AIAgent) async {
        guard speechRecognizer?.isAvailable == true else {
            lastError = "Speech recognition not available"
            return
        }
        isListening = true
        do {
            try await startSpeechRecognition { [weak self] recognizedText in
                Task { @MainActor in
                    if !recognizedText.isEmpty {
                        self?.isProcessingVoice = true
                        let response = try await self?.sendMessage(to: agent, message: recognizedText) ?? ""
                        await self?.speakResponse(response)
                        self?.isProcessingVoice = false
                    }
                }
            }
        } catch {
            lastError = "Voice recognition failed: \(error.localizedDescription)"
            isListening = false
        }
    }
    func stopVoiceRecognition() {
        recognitionTask?.cancel()
        recognitionTask = nil
        recognitionRequest = nil
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        isListening = false
    }
    private func speakResponse(_ text: String) async {
        // Stop any current speech before starting new one
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)
        }
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.0
        utterance.volume = 0.8
        synthesizer.speak(utterance)
    }
    // MARK: - Personalized Recommendations
    func generateRecommendations(
        for pet: Pet,
        agent: AIAgent,
        category: AppleIntelligenceRecommendationCategory
    ) async -> [PersonalizedRecommendation] {
        do {
            // Apple Intelligence personalized recommendations using local processing
            // Fallback implementation using available AI logic
            return [
                PersonalizedRecommendation(
                    title: "Personalized Recommendation",
                    description: "Based on \(pet.name)'s profile and \(agent.name) expertise",
                    priority: .medium,
                    estimatedCost: 0.0,
                    timeframe: "Immediate",
                    benefits: ["Tailored to your pet's needs"]
                )
            ]
        }
    }
    
    // MARK: - Multi-language Support
    func translateLastResponse(to language: String, for agent: AIAgent) async -> String? {
        guard let lastMessage = conversationHistory[agent.id.uuidString]?.last,
              !lastMessage.isFromUser else {
            return nil
        }
        do {
            // Apple Intelligence local translation using on-device models
            // Fallback translation (simplified)
            return "Translated: \(lastMessage.content) [to \(language)]"
        }
    }
    // MARK: - Context Management
    func clearConversation(for agent: AIAgent) {
        let agentKey = agent.id.uuidString
        conversationHistory.removeValue(forKey: agentKey)
        conversationContexts.removeValue(forKey: agentKey)
        // Clear conversation history managed locally
    }
    func updatePetContext(pet: Pet, for agent: AIAgent) {
        let agentKey = agent.id.uuidString
        petContexts[agentKey] = pet
        // Update pet context managed locally
    }
    // Public method to refresh pets when new ones are added
    func refreshUserPets() async {
        // First refresh the PetDataManager to ensure latest data
        print("Refreshing pet data...")
        // Then update AI agent contexts
        await loadUserPets()
    }
    // Public method to cache agent insights for Pet Master
    func cacheAgentInsight(agentId: UUID, insight: String) {
        agentDataCache[agentId.uuidString] = ["last_insight": insight, "timestamp": Date()]
    }
    // MARK: - Conversation Management
    
    func clearConversationHistory(for agentId: UUID) {
        conversationHistory.removeValue(forKey: agentId.uuidString)
        petContexts.removeValue(forKey: agentId.uuidString)
        conversationContexts.removeValue(forKey: agentId.uuidString)
    }
    // MARK: - Helper Methods
    private func determineAnalysisType(for agent: AIAgent) -> ImageAnalysisType {
        switch agent.name {
        case "Health and Emergency":
            return .health
        case "Style Guru":
            return .grooming
        case "Trainer Pro":
            return .behavior
        case "Dr. Nutrition":
            return .nutrition
        default:
            return .general
        }
    }
    private func requestSpeechPermission() {
        SFSpeechRecognizer.requestAuthorization { status in
            DispatchQueue.main.async {
                // Handle permission status
            }
        }
    }
    private func startSpeechRecognition(completion: @escaping (String) -> Void) async throws {
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw NSError(domain: "SpeechError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Unable to create recognition request"])
        }
        recognitionRequest.shouldReportPartialResults = true
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { result, error in
            if let result = result {
                completion(result.bestTranscription.formattedString)
            }
            if error != nil {
                self.stopVoiceRecognition()
            }
        }
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        audioEngine.prepare()
        try audioEngine.start()
    }
    // MARK: - Enhanced AI Features
    // Load all user pets dynamically with comprehensive data
    private func loadUserPets() async {
        // Load pets from centralized PetDataManager for comprehensive data access
        allUserPets = petDataManager.pets
        print("🤖 AI Agents loaded \(allUserPets.count) pets with comprehensive data")
        // Update pet contexts for all agents when new pets are added
        for agent in availableAgents {
            for pet in allUserPets {
                updatePetContext(pet: pet, for: agent)
            }
        }
    }
    // Build enhanced context with comprehensive pet data and knowledge base integration
    @available(iOS 18.0, *)
    private func buildEnhancedContext(for agent: AIAgent, message: String, pet: Pet?) async -> [String: Any] {
        var context = conversationContexts[agent.id.uuidString] ?? [:]
        // Add comprehensive pet data context using PetDataManager (simplified for compiler)
        var allPetsData: [[String: Any]] = []
        for userPet in allUserPets {
            let petData = buildComprehensivePetData(for: userPet)
            allPetsData.append(petData)
        }
        context["all_pets"] = allPetsData
        // Add selected pet comprehensive summary for AI agents
        if let selectedPet = pet ?? petDataManager.currentPet {
            context["selected_pet_ai_summary"] = petDataManager.selectedPetAISummary
            context["selected_pet_comprehensive_data"] = [
                "name": selectedPet.name,
                "comprehensive_summary": petDataManager.buildComprehensiveAISummary(for: selectedPet),
                "health_score": petDataManager.getHealthScore(for: selectedPet),
                "nutrition_status": petDataManager.evaluateNutritionStatus(for: selectedPet).rawValue,
                "exercise_status": petDataManager.evaluateExerciseStatus(for: selectedPet).rawValue,
                "training_status": petDataManager.evaluateTrainingStatus(for: selectedPet).rawValue,
                "vaccination_status": petDataManager.evaluateVaccinationStatus(for: selectedPet).isUpToDate ? "up_to_date" : "needs_update"
            ]
        }
        // Search knowledge base for relevant information
        let knowledgeResults = knowledgeBaseService.searchDocuments(query: message)
        if !knowledgeResults.isEmpty {
            context["knowledge_base"] = knowledgeResults.map { doc in
                [
                    "title": doc.title,
                    "content": doc.content,
                    "type": doc.type.rawValue,
                    "tags": doc.tags
                ]
            }
        }
        return context
    }
    // MARK: - Pet Master Enhanced Context with Cross-Agent Chat History
    
    private func buildPetMasterEnhancedContext(message: String, pet: Pet?) async -> [String: Any] {
        var context = await buildEnhancedContext(for: availableAgents.first { $0.name == "Pet Master" }!, message: message, pet: pet)
        
        // Add cross-agent chat history for comprehensive context
        let crossAgentHistory = await getCrossAgentChatHistory(for: pet)
        if !crossAgentHistory.isEmpty {
            context["cross_agent_chat_history"] = crossAgentHistory
        }
        
        // Add agent-specific insights from recent conversations
        let agentInsights = await getRecentAgentInsights(for: pet)
        if !agentInsights.isEmpty {
            context["recent_agent_insights"] = agentInsights
        }
        
        return context
    }
    
    // MARK: - Cross-Agent Helper Methods
    
    // Get cross-agent chat history for Pet Master
    private func getCrossAgentChatHistory(for pet: Pet?) async -> [[String: Any]] {
        var crossAgentData: [[String: Any]] = []
        
        // Get conversation history for all agents
        for agent in availableAgents where agent.name != "Pet Master" {
            let agentKey = agent.id.uuidString
            let conversations = conversationHistory[agentKey] ?? []
            
            if !conversations.isEmpty {
                let recentConversations = conversations.suffix(6) // Last 6 messages
                let conversationData = recentConversations.map { message in
                    [
                        "content": message.content,
                        "is_from_user": message.isFromUser,
                        "timestamp": message.timestamp,
                        "agent_id": agent.id.uuidString
                    ]
                }
                
                crossAgentData.append([
                    "agent_name": agent.name,
                    "agent_specialty": agent.specialty,
                    "conversations": conversationData,
                    "total_messages": conversations.count,
                    "last_interaction": conversations.last?.timestamp ?? Date()
                ])
            }
        }
        
        // Sort by most recent activity
        return crossAgentData.sorted { 
            ($0["last_interaction"] as? Date ?? Date()) > ($1["last_interaction"] as? Date ?? Date())
        }
    }
    
    // Get recent agent insights for Pet Master
    private func getRecentAgentInsights(for pet: Pet?) async -> [[String: Any]] {
        var insights: [[String: Any]] = []
        
        // Get insights from agent data cache
        for (agentId, data) in agentDataCache {
            if let agent = availableAgents.first(where: { $0.id.uuidString == agentId }),
               agent.name != "Pet Master" {
                
                let lastInsight = data["last_insight"] as? String ?? ""
                let lastInteraction = data["last_interaction"] as? Date ?? Date()
                let relevanceScore = calculateInsightRelevance(
                    insight: lastInsight,
                    agentSpecialty: agent.specialty,
                    pet: pet
                )
                
                if !lastInsight.isEmpty && relevanceScore > 0.3 {
                    insights.append([
                        "agent_name": agent.name,
                        "agent_specialty": agent.specialty,
                        "insight": lastInsight,
                        "relevance_score": relevanceScore,
                        "timestamp": lastInteraction
                    ])
                }
            }
        }
        
        // Sort by relevance score
        return insights.sorted { 
            ($0["relevance_score"] as? Double ?? 0.0) > ($1["relevance_score"] as? Double ?? 0.0)
        }
    }
    
    // Calculate insight relevance for Pet Master
    private func calculateInsightRelevance(insight: String, agentSpecialty: String, pet: Pet?) -> Double {
        var relevanceScore = 0.0
        
        // Base relevance from agent specialty
        if let pet = pet {
            switch agentSpecialty.lowercased() {
            case "health", "emergency":
                if pet.healthAlerts.contains(where: { $0.isActive }) {
                    relevanceScore += 0.4
                }
                if pet.chronicConditions.count > 0 {
                    relevanceScore += 0.3
                }
            case "nutrition":
                if pet.currentFood != nil {
                    relevanceScore += 0.4
                }
                if pet.dietaryRestrictions.count > 0 {
                    relevanceScore += 0.3
                }
            case "training":
                if pet.trainingLevel != nil {
                    relevanceScore += 0.4
                }
                if pet.behaviorIssues.count > 0 {
                    relevanceScore += 0.3
                }
            case "grooming":
                relevanceScore += 0.3 // General relevance for grooming
            case "shopping":
                relevanceScore += 0.2 // Lower relevance for shopping
            case "insurance":
                if pet.insuranceInfo != nil {
                    relevanceScore += 0.4
                }
            case "wellness":
                relevanceScore += 0.3 // General wellness relevance
            default:
                relevanceScore += 0.2
            }
        }
        
        // Content relevance based on keywords
        let insightLower = insight.lowercased()
        let petName = pet?.name.lowercased() ?? ""
        
        if !petName.isEmpty && insightLower.contains(petName) {
            relevanceScore += 0.3
        }
        
        // Time-based relevance (more recent = higher relevance)
        if let lastInteraction = agentDataCache.values.first?["last_interaction"] as? Date {
            let timeSince = Date().timeIntervalSince(lastInteraction)
            let daysSince = timeSince / (24 * 60 * 60)
            if daysSince < 7 {
                relevanceScore += 0.2
            } else if daysSince < 30 {
                relevanceScore += 0.1
            }
        }
        
        return min(relevanceScore, 1.0)
    }
    
    // Get agent chat history for specific agent
    private func getAgentChatHistory(for agentName: String, limit: Int = 10) -> [ChatMessage] {
        if let agent = availableAgents.first(where: { $0.name == agentName }) {
            let agentKey = agent.id.uuidString
            let conversations = conversationHistory[agentKey] ?? []
            return Array(conversations.suffix(limit))
        }
        return []
    }
    

    
    // MARK: - Enhanced Pet Master request handler with cross-agent context
    private func handlePetMasterRequest(message: String, pet: Pet?, includeImage: UIImage?) async -> String {
        var enhancedPrompt = """
        You are Pet Master, the ultimate AI agent with access to comprehensive pet data, cross-agent insights, and complete conversation history.
        
        User Query: \(message)
        
        Available Resources:
        """
        
        // Add comprehensive pets context
        if !allUserPets.isEmpty {
            enhancedPrompt += "\n\nComprehensive Pet Portfolio:\n"
            for userPet in allUserPets {
                let healthScore = petDataManager.getHealthScore(for: userPet)
                enhancedPrompt += """
                🐾 \(userPet.name) (\(userPet.species), \(userPet.breed ?? "Mixed"))
                   • Age: \(String(format: "%.1f", userPet.ageInYears)) years | Health Score: \(Int(healthScore * 100))%
                   • Nutrition Status: \(petDataManager.evaluateNutritionStatus(for: userPet).rawValue)
                   • Exercise Status: \(petDataManager.evaluateExerciseStatus(for: userPet).rawValue)
                   • Training Status: \(petDataManager.evaluateTrainingStatus(for: userPet).rawValue)
                   • Active Health Alerts: \(userPet.healthAlerts.filter { $0.isActive }.count)
                   • Medications: \(userPet.medications.count) active
                   • Special Needs: \(userPet.specialInstructions ?? "None")
                """
            }
        }
        
        // Add cross-agent chat history context
        let crossAgentHistory = await getCrossAgentChatHistory(for: pet)
        if !crossAgentHistory.isEmpty {
            enhancedPrompt += "\n\n📚 Cross-Agent Conversation History:\n"
            for agentData in crossAgentHistory {
                let agentName = agentData["agent_name"] as? String ?? "Unknown Agent"
                let conversations = agentData["conversations"] as? [[String: Any]] ?? []
                let totalMessages = agentData["total_messages"] as? Int ?? 0
                
                enhancedPrompt += "\n🤖 \(agentName) (\(totalMessages) recent messages):\n"
                for conversation in conversations.prefix(3) { // Limit to 3 most recent per agent
                    let content = conversation["content"] as? String ?? ""
                    let isFromUser = conversation["is_from_user"] as? Bool ?? false
                    let _ = conversation["timestamp"] as? Date ?? Date()
                    
                    enhancedPrompt += "   \(isFromUser ? "👤 User" : "🤖 \(agentName)"): \(content.prefix(200))...\n"
                }
            }
        }
        
        // Add recent agent insights
        let agentInsights = await getRecentAgentInsights(for: pet)
        if !agentInsights.isEmpty {
            enhancedPrompt += "\n\n💡 Recent Agent Insights:\n"
            for insight in agentInsights.prefix(5) { // Limit to 5 most relevant insights
                let agentName = insight["agent_name"] as? String ?? "Unknown Agent"
                let insightText = insight["insight"] as? String ?? ""
                let relevanceScore = insight["relevance_score"] as? Double ?? 0.0
                
                if relevanceScore > 0.6 { // Only include highly relevant insights
                    enhancedPrompt += "   🎯 \(agentName): \(insightText.prefix(150))...\n"
                }
            }
        }
        
        // Add vector database semantic search results
        if let vectorService = vectorDatabaseService {
            let semanticResults = vectorService.searchCrossAgentHistory(
                query: message,
                petId: pet?.id,
                limit: 8
            )
            
            if !semanticResults.isEmpty {
                enhancedPrompt += "\n\n🔍 Semantic Search Results (Cross-Agent):\n"
                for result in semanticResults.prefix(5) {
                    let agentName = result.agentName ?? "Unknown Agent"
                    let content = result.content
                    let relevanceScore = result.relevanceScore
                    
                    if relevanceScore > 0.7 { // Only include highly relevant results
                        enhancedPrompt += "   📊 \(agentName) (Relevance: \(Int(relevanceScore * 100))%): \(content.prefix(150))...\n"
                    }
                }
            }
        }
        
        // Add knowledge base search results
        let knowledgeResults = knowledgeBaseService.searchDocuments(query: message)
        if !knowledgeResults.isEmpty {
            enhancedPrompt += "\n\n📖 Knowledge Base Information:\n"
            for doc in knowledgeResults.prefix(3) {
                enhancedPrompt += "- \(doc.title): \(doc.content.prefix(200))...\n"
            }
        }
        
        // Add internet search results
        let internetResults = await performInternetSearch(query: message)
        if !internetResults.isEmpty {
            enhancedPrompt += "\n\n🌐 Latest Internet Information:\n\(internetResults)"
        }
        
        enhancedPrompt += """
        
        🎯 PET MASTER INSTRUCTIONS:
        1. Analyze the cross-agent conversation history to understand previous discussions
        2. Consider insights from other specialized agents when formulating your response
        3. Provide comprehensive advice that synthesizes information from all sources
        4. Reference specific conversations or insights when relevant
        5. Offer coordinated care recommendations that leverage multiple agent perspectives
        6. Maintain context continuity across different agent interactions
        7. Use semantic search results to provide more relevant and contextual responses
        8. Coordinate between different care aspects (health, nutrition, training, etc.)
        
        SPECIAL CAPABILITIES:
        - Access to all agent conversation histories
        - Cross-agent insight integration
        - Semantic search across all conversations
        - Comprehensive pet data analysis
        - Coordinated care recommendations
        
        Provide a comprehensive response using all available information and cross-agent context.
        """
        
        let petMasterAgent = availableAgents.first { $0.name == "Pet Master" }!
        return await enhancedAppleIntelligenceService?.sendMessage(
            to: petMasterAgent,
            message: enhancedPrompt,
            pet: pet
        ) ?? "I apologize, but I'm having trouble accessing all the information sources right now. Please try again in a moment."
    }
    // Gather data from other agents
    private func gatherDataFromOtherAgents(query: String) async -> String {
        var agentInsights = ""
        // Check cached data from other agents
        for (agentId, data) in agentDataCache {
            if let agentName = availableAgents.first(where: { $0.id.uuidString == agentId })?.name {
                agentInsights += "From \(agentName): \(data["last_insight"] as? String ?? "")\n"
            }
        }
        return agentInsights
    }
    // Perform internet search
    private func performInternetSearch(query: String) async -> String {
        let searchResults = await webSearchService.searchPetSpecificInfo(query: query)
        if searchResults.isEmpty {
            return ""
        }
        var internetInfo = ""
        for result in searchResults.prefix(3) {
            internetInfo += "• \(result.title)\n  \(result.snippet)\n  Source: \(result.source)\n\n"
        }
        return internetInfo
    }
    // Save conversation to new AI conversation system
    private func saveToConversationHistory(agent: AIAgent, userMessage: ChatMessage, aiMessage: ChatMessage, pet: Pet?) async {
        // Get agent database ID
        guard let agentId = getAgentDatabaseId(for: agent.name) else {
            print("❌ Could not find agent database ID for: \(agent.name)")
            return
        }
        // Get or create conversation
        let conversation = await conversationService.getOrCreateConversation(
            agentId: agentId,
            petId: pet?.id != nil ? UUID(uuidString: pet!.id) : nil
        )
        guard let conversation = conversation else {
            print("❌ Could not create conversation for agent: \(agent.name)")
            return
        }
        // Save both messages
        _ = await conversationService.saveMessage(
            conversationId: conversation.id,
            content: userMessage.content,
            isFromUser: true
        )
        _ = await conversationService.saveMessage(
            conversationId: conversation.id,
            content: aiMessage.content,
            isFromUser: false
        )
    }
    private func getAgentDatabaseId(for agentName: String) -> UUID? {
        // Map agent names to their database IDs
        switch agentName {
        case "Dr. Nutrition":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Health and Emergency":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Trainer Pro":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Style Guru":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Shopping Assistant":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Pet Master":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        case "Pet Insurance Advisor":
            return UUID(uuidString: "550e8400-e29b-41d4-a716-************")
        default:
            return nil
        }
    }
    // Load conversation history from Apple native storage
    private func loadConversationHistory() async {
        do {
            let userId = getCurrentUserId()
            let conversations = await AIConversationService.shared.getConversations(for: userId)
            // Organize by agent
            for conversation in conversations {
                let agentId = conversation.agentId
                // Since AIConversation doesn't have userMessage/aiResponse,
                // we'll need to load the actual messages for this conversation
                // For now, create placeholder messages based on conversation title
                let userMsg = ChatMessage(content: conversation.title, isFromUser: true, agentId: agentId)
                let aiMsg = ChatMessage(content: "AI response for \(conversation.title)", isFromUser: false, agentId: agentId)
                if conversationHistory[agentId.uuidString] == nil {
                    conversationHistory[agentId.uuidString] = []
                }
                conversationHistory[agentId.uuidString]?.append(contentsOf: [userMsg, aiMsg])
            }
        } catch {
            print("Failed to load conversation history: \(error)")
        }
    }
    private func getCurrentUserId() -> UUID {
        return UUID(uuidString: "550e8400-e29b-41d4-a716-************") ?? UUID() // Development mode
    }
    // MARK: - Helper Methods
    @available(iOS 18.0, *)
    private func buildComprehensivePetData(for pet: Pet) -> [String: Any] {
        return [
            // Basic info
            "name": pet.name,
            "species": pet.species,
            "breed": pet.breed ?? "Mixed",
            "age": pet.age,
            "weight": pet.weight ?? 0.0,
            "gender": pet.gender ?? "Unknown",
            "spayed_neutered": pet.isSpayedNeutered,
            // Health data
            "health_score": petDataManager.getHealthScore(for: pet),
            "active_health_alerts": pet.healthAlerts.filter { $0.isActive }.count,
            "chronic_conditions": pet.chronicConditions.count,
            "current_medications": pet.medications.count,
            "allergies": pet.allergies,
            "last_checkup": pet.lastCheckupDate?.formatted(date: .abbreviated, time: .omitted) ?? "Not recorded",
            // Nutrition data  
            "current_food": pet.currentFood ?? "Not specified",
            "feeding_schedule_count": pet.feedingSchedule.count,
            "water_intake_ml": pet.waterIntakeML ?? 0,
            "dietary_restrictions": pet.dietaryRestrictions,
            "nutrition_status": petDataManager.evaluateNutritionStatus(for: pet).rawValue,
            // Exercise & Activity
            "activity_level": pet.activityLevel,
            "daily_exercise_minutes": pet.exerciseMinutesDaily ?? 0,
            "walking_frequency": pet.walkingFrequency ?? "Not set",
            "favorite_activities": pet.favoriteActivities,
            "exercise_status": petDataManager.evaluateExerciseStatus(for: pet).rawValue,
            // Training & Behavior
            "training_level": pet.trainingLevel ?? "Not assessed",
            "known_commands": pet.knownCommands,
            "behavior_issues": pet.behaviorIssues,
            "social_behavior": pet.socialBehavior ?? "Not assessed",
            "training_status": petDataManager.evaluateTrainingStatus(for: pet).rawValue,
            // Veterinary info
            "veterinarian_clinic": pet.veterinarianInfo?.displayName ?? "Not set",
            "insurance_provider": pet.insuranceInfo?.displayProvider ?? "Not set",
            "vaccination_count": pet.vaccinationRecords.count,
            "vaccination_status": petDataManager.evaluateVaccinationStatus(for: pet).isUpToDate ? "up_to_date" : "needs_update",
            // Additional context
            "personality_traits": pet.personalityTraits,
            "special_instructions": pet.specialInstructions ?? "None",
            "emergency_contacts_count": pet.emergencyContacts.count
        ]
    }
    // MARK: - Fallback Response Generation
    
    private func generateFallbackResponse(for agent: AIAgent, message: String, pet: Pet?) -> String {
        let petName = pet?.name ?? "your pet"
        let _ = pet?.breed ?? "pet"
        
        switch agent.name {
        case "Pet Master":
            return """
            🐾 **Hello! I'm Pet Master, your ultimate pet care companion!**
            
            I can help you with comprehensive care for \(petName). Here's what I can assist with:
            
            • **Health & Wellness**: Monitoring, nutrition, exercise
            • **Training & Behavior**: Obedience, socialization, problem-solving
            • **Grooming & Care**: Coat care, hygiene, styling
            • **Emergency Planning**: Safety, first aid, vet contacts
            • **Memory Preservation**: Photo organization, milestone tracking
            
            **What would you like to know about \(petName)?**
            """
            
        case "Health Guardian":
            return """
            🏥 **Health Guardian here! I'm your pet's medical advocate.**
            
            I can help you monitor \(petName)'s health and provide medical guidance:
            
            • **Health Monitoring**: Track symptoms, behavior changes
            • **Emergency Response**: First aid, when to call vet
            • **Preventive Care**: Vaccinations, checkups, screenings
            • **Medication Management**: Dosages, schedules, side effects
            • **Chronic Condition Support**: Ongoing care and monitoring
            
            **How can I help with \(petName)'s health today?**
            """
            
        case "Dr. Nutrition":
            return """
            🥗 **Dr. Nutrition at your service! I'm your pet's dietary expert.**
            
            I can help you optimize \(petName)'s nutrition and feeding:
            
            • **Diet Planning**: Balanced meals, portion control
            • **Food Selection**: Quality ingredients, dietary restrictions
            • **Feeding Schedules**: Timing, frequency, special needs
            • **Weight Management**: Healthy weight goals, exercise
            • **Special Diets**: Medical conditions, allergies, preferences
            
            **What nutrition questions do you have about \(petName)?**
            """
            
        case "Trainer Pro":
            return """
            🎾 **Trainer Pro here! I'm your pet's behavior specialist.**
            
            I can help you train \(petName) and address behavior issues:
            
            • **Basic Training**: Commands, obedience, house training
            • **Behavior Modification**: Problem behaviors, socialization
            • **Activity Planning**: Exercise routines, mental stimulation
            • **Training Techniques**: Positive reinforcement, consistency
            • **Specialized Training**: Service tasks, therapy work
            
            **What training or behavior help does \(petName) need?**
            """
            
        case "Style Guru":
            return """
            ✂️ **Style Guru here! I'm your pet's grooming expert.**
            
            I can help you keep \(petName) looking and feeling great:
            
            • **Coat Care**: Brushing, bathing, trimming
            • **Nail Care**: Trimming, filing, maintenance
            • **Dental Hygiene**: Brushing, dental treats, checkups
            • **Styling Tips**: Breed-specific grooming, seasonal care
            • **Grooming Tools**: Product recommendations, techniques
            
            **How can I help with \(petName)'s grooming today?**
            """
            
        case "Shopping Assistant":
            return """
            🛍️ **Shopping Assistant here! I'm your pet product expert.**
            
            I can help you find the best products for \(petName):
            
            • **Food & Treats**: Quality brands, dietary needs
            • **Toys & Enrichment**: Age-appropriate, safe options
            • **Grooming Supplies**: Tools, shampoos, accessories
            • **Health Products**: Supplements, medications, first aid
            • **Safety Gear**: Collars, leashes, identification
            
            **What products are you looking for for \(petName)?**
            """
            
        case "Insurance Advisor":
            return """
            🛡️ **Insurance Advisor here! I'm your pet insurance expert.**
            
            I can help you navigate insurance options for \(petName):
            
            • **Policy Comparison**: Coverage, deductibles, premiums
            • **Claim Guidance**: Filing, documentation, reimbursement
            • **Coverage Options**: Accident, illness, wellness
            • **Provider Recommendations**: Reputable companies
            • **Cost Analysis**: Value assessment, savings tips
            
            **How can I help with \(petName)'s insurance needs?**
            """
            
        case "Wellness Coach":
            return """
            🏠 **Wellness Coach here! I'm your pet's lifestyle specialist.**
            
            I can help you create a healthy lifestyle for \(petName):
            
            • **Exercise Planning**: Activity routines, fitness goals
            • **Mental Stimulation**: Enrichment, play, learning
            • **Environmental Health**: Home safety, comfort, stress
            • **Social Wellness**: Interaction, bonding, relationships
            • **Preventive Care**: Wellness routines, early detection
            
            **How can I help improve \(petName)'s overall wellness?**
            """
            
        default:
            return """
            🐾 **Hello! I'm here to help with \(petName)!**
            
            I can assist you with pet care, health, training, and more. What would you like to know?
            """
        }
    }
}

// MARK: - Data Models for Apple Native Storage
struct ConversationHistoryData: Codable {
    let id: String
    let userId: String
    let agentId: String
    let userMessage: String
    let aiResponse: String
    let timestamp: Date
}
