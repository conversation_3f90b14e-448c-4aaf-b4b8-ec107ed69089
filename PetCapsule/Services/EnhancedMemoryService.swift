import Foundation
import SwiftUI
import AVKit
import Photos
import LocalAuthentication
@available(iOS 17.0, *)
@MainActor
class EnhancedMemoryService: ObservableObject {
    static let shared = EnhancedMemoryService()
    @Published var memories: [EnhancedMemory] = []
    @Published var isLoading = false
    @Published var selectedCollection: MemoryCollection = .all
    @Published var searchText = ""
    @Published var selectedEventTypes: Set<MemoryEventType> = []
    @Published var selectedMoods: Set<MemoryMood> = []
    @Published var dateRange: DateRange?
    @Published var selectedPets: Set<String> = []
    // Secure Vault
    @Published var isVaultUnlocked = false
    @Published var vaultAttempts = 0
    private let maxVaultAttempts = 3
    // Memorial
    @Published var memorialPets: [Pet] = []
    @Published var showMemorialCreation = false
    // Video Support
    @Published var currentlyPlayingVideo: String?
    @Published var videoPlayerItems: [String: AVPlayerItem] = [:]
    private init() {
        setupObservers()
    }
    private func setupObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    @objc private func appWillEnterForeground() {
        // Lock vault when app enters foreground
        isVaultUnlocked = false
    }
    // MARK: - Memory Management
    func loadMemories() async {
        isLoading = true
        do {
            // Load from database
            memories = try await loadMemoriesFromDatabase()
            // Load video player items for videos
            await loadVideoPlayerItems()
            print("✅ Loaded \(memories.count) memories")
        } catch {
            print("❌ Failed to load memories: \(error)")
            // Fallback to sample data
            memories = createSampleMemories()
        }
        isLoading = false
    }
    private func loadMemoriesFromDatabase() async throws -> [EnhancedMemory] {
        // For now, return sample data
        return createSampleMemories()
    }
    func createMemory(_ memory: EnhancedMemory) async {
        isLoading = true
        do {
            // Save to database
            try await saveMemoryToDatabase(memory)
            // Add to local array
            memories.append(memory)
            memories.sort { $0.createdAt > $1.createdAt }
            print("✅ Created memory: \(memory.title)")
        } catch {
            print("❌ Failed to create memory: \(error)")
        }
        isLoading = false
    }
    func updateMemory(_ memory: EnhancedMemory) async {
        isLoading = true
        do {
            // Update in database
            try await updateMemoryInDatabase(memory)
            // Update local array
            if let index = memories.firstIndex(where: { $0.id == memory.id }) {
                memories[index] = memory
            }
            print("✅ Updated memory: \(memory.title)")
        } catch {
            print("❌ Failed to update memory: \(error)")
        }
        isLoading = false
    }
    func deleteMemory(_ memory: EnhancedMemory) async {
        // Check if memory is protected
        if memory.isProtected || memory.isSecureVault {
            let authenticated = await authenticateForVault()
            if !authenticated {
                return
            }
        }
        isLoading = true
        do {
            // Delete from database
            try await deleteMemoryFromDatabase(memory.id)
            // Remove from local array
            memories.removeAll { $0.id == memory.id }
            print("✅ Deleted memory: \(memory.title)")
        } catch {
            print("❌ Failed to delete memory: \(error)")
        }
        isLoading = false
    }
    // MARK: - Database Operations
    private func saveMemoryToDatabase(_ memory: EnhancedMemory) async throws {
        // For now, just simulate success
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
    }
    private func updateMemoryInDatabase(_ memory: EnhancedMemory) async throws {
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
    }
    private func deleteMemoryFromDatabase(_ memoryId: String) async throws {
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
    }
    // MARK: - Secure Vault
    func unlockVault() async -> Bool {
        let context = LAContext()
        var error: NSError?
        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            do {
                let success = try await context.evaluatePolicy(
                    .deviceOwnerAuthenticationWithBiometrics,
                    localizedReason: "Unlock your secure pet memories vault"
                )
                if success {
                    isVaultUnlocked = true
                    vaultAttempts = 0
                    return true
                }
            } catch {
                print("❌ Biometric authentication failed: \(error)")
            }
        }
        // Fallback to passcode
        do {
            let success = try await context.evaluatePolicy(
                .deviceOwnerAuthentication,
                localizedReason: "Enter your passcode to access secure memories"
            )
            if success {
                isVaultUnlocked = true
                vaultAttempts = 0
                return true
            }
        } catch {
            print("❌ Passcode authentication failed: \(error)")
        }
        vaultAttempts += 1
        return false
    }
    func lockVault() {
        isVaultUnlocked = false
    }
    private func authenticateForVault() async -> Bool {
        if isVaultUnlocked {
            return true
        }
        if vaultAttempts >= maxVaultAttempts {
            print("❌ Too many vault attempts")
            return false
        }
        return await unlockVault()
    }
    // MARK: - Video Support
    private func loadVideoPlayerItems() async {
        let videoMemories = memories.filter { $0.hasVideo }
        for memory in videoMemories {
            for mediaItem in memory.mediaItems where mediaItem.type == .video {
                if let url = URL(string: mediaItem.url) {
                    videoPlayerItems[mediaItem.id] = AVPlayerItem(url: url)
                }
            }
        }
    }
    func getVideoPlayer(for mediaItemId: String) -> AVPlayer? {
        guard let item = videoPlayerItems[mediaItemId] else { return nil }
        return AVPlayer(playerItem: item)
    }
    func playVideo(_ mediaItemId: String) {
        currentlyPlayingVideo = mediaItemId
    }
    func stopVideo() {
        currentlyPlayingVideo = nil
    }
    // MARK: - Memorial Features
    func createMemorialMemory(for pet: Pet) async {
        let memorialMemory = EnhancedMemory(
            title: "In Loving Memory of \(pet.name)",
            content: "Forever in our hearts. \(pet.name) brought so much joy and love into our lives.",
            petIds: [pet.id],
            mood: .nostalgic,
            eventType: .memorial,
            isMemorial: true,
            tags: ["memorial", "remembrance", pet.name.lowercased()],
            isProtected: true
        )
        await createMemory(memorialMemory)
    }
    func getMemorialMemories() -> [EnhancedMemory] {
        return memories.filter { $0.isMemorial }
    }
    // MARK: - Filtering & Search
    var filteredMemories: [EnhancedMemory] {
        var result = memories
        // Collection filter
        switch selectedCollection {
        case .all:
            break
        case .recent:
            let oneWeekAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
            result = result.filter { $0.createdAt >= oneWeekAgo }
        case .favorites:
            result = result.filter { $0.tags.contains("favorite") }
        case .videos:
            result = result.filter { $0.hasVideo }
        case .milestones:
            result = result.filter { $0.milestoneData != nil }
        case .vault:
            result = result.filter { $0.isSecureVault }
        case .memorial:
            result = result.filter { $0.isMemorial }
        case .thisMonth:
            let startOfMonth = Calendar.current.dateInterval(of: .month, for: Date())?.start ?? Date()
            result = result.filter { $0.createdAt >= startOfMonth }
        case .thisYear:
            let startOfYear = Calendar.current.dateInterval(of: .year, for: Date())?.start ?? Date()
            result = result.filter { $0.createdAt >= startOfYear }
        }
        // Search filter
        if !searchText.isEmpty {
            result = result.filter { memory in
                memory.title.localizedCaseInsensitiveContains(searchText) ||
                memory.content.localizedCaseInsensitiveContains(searchText) ||
                memory.tags.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }
        // Event type filter
        if !selectedEventTypes.isEmpty {
            result = result.filter { selectedEventTypes.contains($0.eventType) }
        }
        // Mood filter
        if !selectedMoods.isEmpty {
            result = result.filter { selectedMoods.contains($0.mood) }
        }
        // Date range filter
        if let dateRange = dateRange {
            result = result.filter { memory in
                memory.createdAt >= dateRange.start && memory.createdAt <= dateRange.end
            }
        }
        // Pet filter
        if !selectedPets.isEmpty {
            result = result.filter { memory in
                !Set(memory.petIds).isDisjoint(with: selectedPets)
            }
        }
        // Hide vault memories if vault is locked
        if !isVaultUnlocked {
            result = result.filter { !$0.isSecureVault }
        }
        return result.sorted { $0.createdAt > $1.createdAt }
    }
    var groupedMemories: [String: [EnhancedMemory]] {
        Dictionary(grouping: filteredMemories) { memory in
            let formatter = DateFormatter()
            formatter.dateFormat = "MMMM yyyy"
            return formatter.string(from: memory.createdAt)
        }
    }
    // MARK: - Sample Data
    private func createSampleMemories() -> [EnhancedMemory] {
        let samplePets = ["pet1", "pet2", "pet3"] // Sample pet IDs
        var memories: [EnhancedMemory] = []
        // Create diverse sample memories
        let sampleData: [(title: String, content: String, eventType: MemoryEventType, mood: MemoryMood, isVault: Bool, isMemorial: Bool, mediaType: MemoryMediaItem.MediaType)] = [
            ("Luna's First Swimming Adventure", "Today Luna discovered her love for water! She was hesitant at first, but once she got in, she couldn't stop splashing around.", .adventure, .excited, false, false, .video),
            ("Shadow's Adoption Day", "Three years ago today, Shadow came into our lives. Best decision we ever made!", .adoption, .nostalgic, true, false, .photo),
            ("Max's Training Graduation", "Max successfully completed his advanced obedience training! So proud of his progress.", .achievement, .proud, false, false, .photo),
            ("Cozy Sunday Morning", "Nothing beats a lazy Sunday morning with my furry best friend.", .daily, .content, false, false, .photo),
            ("First Visit to the Dog Park", "Luna was so excited to meet all the other dogs! She made instant friends.", .socialization, .playful, false, false, .video),
            ("Vet Checkup - All Clear!", "Annual checkup went perfectly. Dr. Smith said Shadow is in excellent health.", .veterinary, .happy, false, false, .photo),
            ("Beach Day Adventure", "The perfect day at the beach! Sand, waves, and endless fetch.", .adventure, .ecstatic, false, false, .video),
            ("Remembering Buddy", "Today marks one year since Buddy crossed the rainbow bridge. Forever missed, forever loved.", .memorial, .nostalgic, true, true, .photo),
            ("First Birthday Celebration", "Max's first birthday party was a huge success! Lots of treats and presents.", .birthday, .happy, true, false, .photo),
            ("Puppy Training Success", "After weeks of practice, Luna finally mastered the 'stay' command!", .training, .proud, false, false, .photo),
            ("Grooming Day Glamour", "Shadow is looking absolutely stunning after his spa day!", .grooming, .content, false, false, .photo),
            ("Midnight Cuddles", "Sometimes the best memories happen in the quiet moments.", .daily, .calm, false, false, .photo),
            ("Agility Competition Win", "Max took first place in his agility class! What a champion!", .achievement, .ecstatic, true, false, .video),
            ("Favorite Toy Destruction", "RIP squeaky duck. You brought so much joy in your short life.", .play, .playful, false, false, .photo),
            ("Morning Walk Routine", "Our daily 6 AM walk has become the highlight of both our days.", .walk, .content, false, false, .photo)
        ]
        for (index, data) in sampleData.enumerated() {
            let createdDate = Calendar.current.date(byAdding: .day, value: -index * 3, to: Date()) ?? Date()
            let mediaItem = MemoryMediaItem(
                url: "https://picsum.photos/800/600?random=\(index)",
                thumbnailURL: "https://picsum.photos/200/200?random=\(index)",
                type: data.mediaType,
                duration: data.mediaType == .video ? Double.random(in: 15...120) : nil,
                dimensions: MediaDimensions(width: 800, height: 600),
                uploadedAt: createdDate
            )
            let memory = EnhancedMemory(
                title: data.title,
                content: data.content,
                mediaItems: [mediaItem],
                thumbnailURL: mediaItem.thumbnailURL,
                createdAt: createdDate,
                petIds: [samplePets.randomElement() ?? "pet1"],
                mood: data.mood,
                eventType: data.eventType,
                isSecureVault: data.isVault,
                isMemorial: data.isMemorial,
                tags: generateTags(for: data.eventType, mood: data.mood),
                isProtected: data.isVault || data.isMemorial
            )
            memories.append(memory)
        }
        return memories
    }
    private func generateTags(for eventType: MemoryEventType, mood: MemoryMood) -> [String] {
        var tags: [String] = []
        // Add event type tag
        tags.append(eventType.rawValue)
        // Add mood tag
        tags.append(mood.rawValue)
        // Add contextual tags
        switch eventType {
        case .adventure, .travel:
            tags.append("outdoor")
        case .training, .achievement:
            tags.append("progress")
        case .veterinary, .vaccination:
            tags.append("health")
        case .play, .exercise:
            tags.append("active")
        case .memorial, .remembrance:
            tags.append("remembrance")
        case .birthday, .adoption:
            tags.append("celebration")
        default:
            break
        }
        return tags
    }
    // MARK: - Utility
    func clearFilters() {
        selectedCollection = .all
        searchText = ""
        selectedEventTypes.removeAll()
        selectedMoods.removeAll()
        dateRange = nil
        selectedPets.removeAll()
    }
    func toggleFavorite(for memory: EnhancedMemory) async {
        var updatedMemory = memory
        if updatedMemory.tags.contains("favorite") {
            updatedMemory.tags.removeAll { $0 == "favorite" }
        } else {
            updatedMemory.tags.append("favorite")
        }
        await updateMemory(updatedMemory)
    }
    func addToSecureVault(_ memory: EnhancedMemory) async {
        let authenticated = await authenticateForVault()
        if !authenticated {
            return
        }
        var updatedMemory = memory
        updatedMemory.isSecureVault = true
        updatedMemory.isProtected = true
        await updateMemory(updatedMemory)
    }
    func removeFromSecureVault(_ memory: EnhancedMemory) async {
        let authenticated = await authenticateForVault()
        if !authenticated {
            return
        }
        var updatedMemory = memory
        updatedMemory.isSecureVault = false
        updatedMemory.isProtected = false
        await updateMemory(updatedMemory)
    }
}
// MARK: - Date Range Helper
struct DateRange {
    let start: Date
    let end: Date
    init(start: Date, end: Date) {
        self.start = start
        self.end = end
    }
    static func thisWeek() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let weekInterval = calendar.dateInterval(of: .weekOfYear, for: now)!
        return DateRange(start: weekInterval.start, end: weekInterval.end)
    }
    static func thisMonth() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let monthInterval = calendar.dateInterval(of: .month, for: now)!
        return DateRange(start: monthInterval.start, end: monthInterval.end)
    }
    static func thisYear() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let yearInterval = calendar.dateInterval(of: .year, for: now)!
        return DateRange(start: yearInterval.start, end: yearInterval.end)
    }
} 