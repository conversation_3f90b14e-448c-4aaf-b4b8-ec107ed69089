//
//  EnhancedPetHealthService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftUI

@MainActor
class EnhancedPetHealthService: ObservableObject {

    static let shared = EnhancedPetHealthService()

    @Published var healthCalculations: [String: PetHealthData] = [:]
    @Published var isCalculating = false

    // Production optimizations
    private let calculationQueue = DispatchQueue(label: "health.calculation", qos: .userInitiated)
    private var calculationCache: [String: (result: Double, timestamp: Date)] = [:]
    private let cacheValidityDuration: TimeInterval = 300 // 5 minutes
    private let maxConcurrentCalculations = 3
    private var activeCalculations: Set<String> = []

    // Performance monitoring
    private var calculationTimes: [TimeInterval] = []
    private let maxCalculationTimeHistory = 100

    private init() {
        // Start cache cleanup timer
        Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { _ in
            Task { @MainActor in
                self.cleanupCache()
            }
        }
    }

    // MARK: - Real-time Health Score Calculation

    func calculateRealTimeHealthScore(for pet: Pet) async -> Double {
        let startTime = CFAbsoluteTimeGetCurrent()

        // Check if already calculating for this pet
        guard !activeCalculations.contains(pet.id) else {
            return healthCalculations[pet.id]?.healthScore ?? 0.85
        }

        // Check cache first
        let cacheKey = generateCacheKey(for: pet)
        if let cached = calculationCache[cacheKey],
           Date().timeIntervalSince(cached.timestamp) < cacheValidityDuration {
            return cached.result
        }

        // Limit concurrent calculations
        guard activeCalculations.count < maxConcurrentCalculations else {
            return healthCalculations[pet.id]?.healthScore ?? 0.85
        }

        activeCalculations.insert(pet.id)
        isCalculating = true

        defer {
            activeCalculations.remove(pet.id)
            if activeCalculations.isEmpty {
                isCalculating = false
            }

            // Record calculation time for monitoring
            let calculationTime = CFAbsoluteTimeGetCurrent() - startTime
            recordCalculationTime(calculationTime)
        }

        var score = 1.0 // Start with perfect health

        // Age factor (more sophisticated)
        score *= ageHealthFactor(age: pet.age, species: pet.species)

        // Weight factor
        if let weight = pet.weight, weight > 0 {
            score *= weightHealthFactor(weight: weight, species: pet.species, age: pet.age)
        }

        // Activity level factor
        score *= activityHealthFactor(activityLevel: pet.activityLevel)

        // Medical history factor
        score *= medicalHistoryFactor(
            medications: pet.medications.map { $0.name },
            allergies: pet.allergies,
            chronicConditions: pet.chronicConditions
        )

        // Vaccination factor
        score *= vaccinationFactor(vaccinations: pet.vaccinations, age: pet.age)

        // Recent checkup factor
        score *= checkupFactor(lastCheckup: pet.lastCheckupDate)

        // Environmental factors
        score *= environmentalFactor(for: pet)

        let finalScore = max(0.5, min(1.0, score)) // Keep between 50% and 100%

        // Cache the calculation
        let healthData = PetHealthData(
            healthScore: finalScore,
            lastCalculated: Date(),
            factors: generateHealthFactors(for: pet, score: finalScore)
        )

        healthCalculations[pet.id] = healthData
        calculationCache[cacheKey] = (result: finalScore, timestamp: Date())

        return finalScore
    }

    // MARK: - Production Optimizations

    private func generateCacheKey(for pet: Pet) -> String {
        // Create cache key based on pet characteristics that affect health
        let keyComponents = [
            pet.id,
            String(pet.age),
            String(pet.weight ?? 0),
            pet.activityLevel,
            String(pet.medications.count),
            String(pet.allergies.count),
            String(pet.chronicConditions.count)
        ]
        return keyComponents.joined(separator: "_")
    }

    private func cleanupCache() {
        let now = Date()
        calculationCache = calculationCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < cacheValidityDuration
        }

        // Also cleanup old health calculations (keep last 24 hours)
        let dayAgo = now.addingTimeInterval(-86400)
        healthCalculations = healthCalculations.filter { _, value in
            value.lastCalculated > dayAgo
        }
    }

    private func recordCalculationTime(_ time: TimeInterval) {
        calculationTimes.append(time)
        if calculationTimes.count > maxCalculationTimeHistory {
            calculationTimes.removeFirst()
        }

        // Log slow calculations for monitoring
        if time > 0.5 {
            print("⚠️ Slow health calculation: \(String(format: "%.3f", time))s")
        }
    }

    // MARK: - Performance Monitoring

    func getPerformanceMetrics() -> HealthServiceMetrics {
        let averageTime = calculationTimes.isEmpty ? 0 : calculationTimes.reduce(0, +) / Double(calculationTimes.count)
        let maxTime = calculationTimes.max() ?? 0
        let cacheHitRate = Double(calculationCache.count) / Double(max(1, calculationTimes.count))

        return HealthServiceMetrics(
            averageCalculationTime: averageTime,
            maxCalculationTime: maxTime,
            cacheHitRate: cacheHitRate,
            activeCalculations: activeCalculations.count,
            cachedResults: calculationCache.count,
            totalCalculations: calculationTimes.count
        )
    }

    func clearCache() {
        calculationCache.removeAll()
        healthCalculations.removeAll()
        calculationTimes.removeAll()
    }

    // MARK: - Health Factor Calculations

    private func ageHealthFactor(age: Int, species: String) -> Double {
        let lifeExpectancy: Double = {
            switch species.lowercased() {
            case "dog": return 13.0
            case "cat": return 15.0
            case "bird": return 20.0
            case "rabbit": return 10.0
            case "hamster": return 3.0
            case "fish": return 5.0
            default: return 12.0
            }
        }()

        let ageRatio = (Double(age) / 12.0) / lifeExpectancy

        switch ageRatio {
        case 0.0..<0.1: return 0.95 // Very young
        case 0.1..<0.3: return 1.0  // Young adult
        case 0.3..<0.6: return 0.98 // Adult
        case 0.6..<0.8: return 0.92 // Senior
        case 0.8..<1.0: return 0.85 // Elderly
        default: return 0.75        // Very elderly
        }
    }

    private func weightHealthFactor(weight: Double, species: String, age: Int) -> Double {
        let idealWeightRange: ClosedRange<Double> = {
            switch species.lowercased() {
            case "dog":
                // Varies greatly by breed, using general ranges
                if age < 12 { return 2.0...15.0 } // Age in months, so < 12 months = < 1 year
                return 5.0...50.0
            case "cat":
                if age < 12 { return 0.5...2.0 } // Age in months, so < 12 months = < 1 year
                return 3.0...8.0
            case "bird": return 0.05...2.0
            case "rabbit": return 1.0...5.0
            case "hamster": return 0.1...0.2
            case "fish": return 0.01...1.0
            default: return 1.0...20.0
            }
        }()

        if idealWeightRange.contains(weight) {
            return 1.0
        } else if weight < idealWeightRange.lowerBound {
            let deficit = (idealWeightRange.lowerBound - weight) / idealWeightRange.lowerBound
            return max(0.8, 1.0 - deficit * 0.3)
        } else {
            let excess = (weight - idealWeightRange.upperBound) / idealWeightRange.upperBound
            return max(0.7, 1.0 - excess * 0.4)
        }
    }

    private func activityHealthFactor(activityLevel: String) -> Double {
        switch activityLevel.lowercased() {
        case "very_high": return 0.98 // Might be too much
        case "high": return 1.0
        case "moderate": return 0.95
        case "low": return 0.85
        default: return 0.9
        }
    }

    private func medicalHistoryFactor(medications: [String], allergies: [String], chronicConditions: [String]) -> Double {
        var factor = 1.0

        // Medications impact
        factor -= Double(medications.count) * 0.02

        // Allergies impact
        factor -= Double(allergies.count) * 0.015

        // Chronic conditions impact
        factor -= Double(chronicConditions.count) * 0.05

        return max(0.7, factor)
    }

    private func vaccinationFactor(vaccinations: [String], age: Int) -> Double {
        let expectedVaccinations = age < 12 ? 3 : 5 // Age in months, so < 12 months = < 1 year
        let vaccinationRatio = Double(vaccinations.count) / Double(expectedVaccinations)

        return min(1.0, max(0.8, 0.8 + vaccinationRatio * 0.2))
    }

    private func checkupFactor(lastCheckup: Date?) -> Double {
        guard let lastCheckup = lastCheckup else { return 0.9 }

        let daysSinceCheckup = Calendar.current.dateComponents([.day], from: lastCheckup, to: Date()).day ?? 0

        switch daysSinceCheckup {
        case 0...90: return 1.0      // Recent checkup
        case 91...180: return 0.98   // Within 6 months
        case 181...365: return 0.95  // Within a year
        case 366...730: return 0.9   // Within 2 years
        default: return 0.85         // Overdue
        }
    }

    private func environmentalFactor(for pet: Pet) -> Double {
        // This could be enhanced with actual environmental data
        // For now, use basic assumptions
        return 0.98
    }

    // MARK: - AI Recommendations Generation

    func generateEnhancedRecommendations(for pet: Pet) async -> [String] {
        var recommendations: [String] = []

        let healthScore = await calculateRealTimeHealthScore(for: pet)

        // Health score based recommendations
        if healthScore < 0.8 {
            recommendations.append("Schedule a vet checkup to address health concerns")
        }

        // Age-specific recommendations
        if pet.age < 1 {
            recommendations.append("Ensure complete puppy/kitten vaccination series")
            recommendations.append("Focus on socialization during critical period")
        } else if pet.age > 7 {
            recommendations.append("Consider senior wellness bloodwork")
            recommendations.append("Monitor for age-related joint issues")
        }

        // Weight-specific recommendations
        if let weight = pet.weight, weight > 0 {
            let weightFactor = weightHealthFactor(weight: weight, species: pet.species, age: pet.age)
            if weightFactor < 0.9 {
                recommendations.append("Consult vet about optimal weight management")
            }
        }

        // Activity-specific recommendations
        switch pet.activityLevel {
        case "low":
            recommendations.append("Gradually increase daily exercise")
        case "very_high":
            recommendations.append("Ensure adequate rest and recovery time")
        default:
            recommendations.append("Maintain current activity level")
        }

        // Medical history recommendations
        if !pet.medications.isEmpty {
            recommendations.append("Monitor for medication side effects")
        }

        if !pet.allergies.isEmpty {
            recommendations.append("Maintain allergen-free environment")
        }

        // Checkup recommendations
        if let lastCheckup = pet.lastCheckupDate {
            let daysSince = Calendar.current.dateComponents([.day], from: lastCheckup, to: Date()).day ?? 0
            if daysSince > 365 {
                recommendations.append("Annual checkup is overdue")
            }
        } else {
            recommendations.append("Schedule initial health assessment")
        }

        return Array(recommendations.prefix(5))
    }

    private func generateHealthFactors(for pet: Pet, score: Double) -> [HealthFactor] {
        var factors: [HealthFactor] = []

        factors.append(HealthFactor(
            name: "Age",
            impact: ageHealthFactor(age: pet.age, species: pet.species),
            description: "Age-related health considerations"
        ))

        if let weight = pet.weight, weight > 0 {
            factors.append(HealthFactor(
                name: "Weight",
                impact: weightHealthFactor(weight: weight, species: pet.species, age: pet.age),
                description: "Weight within healthy range"
            ))
        }

        factors.append(HealthFactor(
            name: "Activity",
            impact: activityHealthFactor(activityLevel: pet.activityLevel),
            description: "Activity level appropriateness"
        ))

        return factors
    }
}

// MARK: - Supporting Models

struct PetHealthData {
    let healthScore: Double
    let lastCalculated: Date
    let factors: [HealthFactor]
}

struct HealthFactor {
    let name: String
    let impact: Double
    let description: String

    var impactLevel: String {
        switch impact {
        case 0.95...1.0: return "Excellent"
        case 0.9..<0.95: return "Good"
        case 0.8..<0.9: return "Fair"
        default: return "Needs Attention"
        }
    }
}

struct HealthServiceMetrics {
    let averageCalculationTime: TimeInterval
    let maxCalculationTime: TimeInterval
    let cacheHitRate: Double
    let activeCalculations: Int
    let cachedResults: Int
    let totalCalculations: Int

    var performanceGrade: String {
        if averageCalculationTime < 0.1 && cacheHitRate > 0.8 {
            return "A+"
        } else if averageCalculationTime < 0.2 && cacheHitRate > 0.6 {
            return "A"
        } else if averageCalculationTime < 0.3 && cacheHitRate > 0.4 {
            return "B"
        } else {
            return "C"
        }
    }
}
