//
//  EnvironmentalNotificationService.swift
//  PetCapsule
//
//  Apple UserNotifications integration for environmental alerts
//  Rich notifications with custom sounds and actionable notifications
//
import Foundation
import UserNotifications
import CoreLocation
import SwiftUI
// Type aliases to resolve scope issues
typealias SharedEnvironmentalAlertType = EnvironmentalAlertType
//typealias SharedEnvironmentalAlert = EnvironmentalAlert
//typealias SharedAlertSeverity = AlertSeverity
typealias NotificationEnvironmentalAlertType = SharedEnvironmentalAlertType
// Using SharedEnvironmentalAlert directly to avoid redeclaration
@MainActor
class EnvironmentalNotificationService: NSObject, ObservableObject {
    static let shared = EnvironmentalNotificationService()
    private let notificationCenter = UNUserNotificationCenter.current()
    private let plannerDataService = AppleNativeDataService.shared
    private let appleWeatherService = AppleWeatherService.shared
    private let airQualityService = AppleAirQualityService.shared
    // Published properties
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    @Published var pendingNotifications: [UNNotificationRequest] = []
    @Published var deliveredNotifications: [UNNotification] = []
    @Published var deviceToken: String?
    @Published var notificationSettings: NotificationSettings?
    @Published var notificationHistory: [NotificationHistoryItem] = []
    // Monitoring state
    @Published var isMonitoring = false
    private var monitoringTimer: Timer?
    // APNs and server integration
    private let dataService = AppleNativeDataService.shared
    override init() {
        super.init()
        notificationCenter.delegate = self
        Task {
            _ = await requestNotificationPermission()
            await loadNotificationSettings()
        }
    }
    // MARK: - APNs Device Token Management
    func setDeviceToken(_ token: Data) {
        let tokenString = token.map { String(format: "%02.2hhx", $0) }.joined()
        deviceToken = tokenString
        Task {
            await saveDeviceTokenLocally(tokenString)
        }
        print("✅ APNs device token registered: \(tokenString)")
    }
    func registerForRemoteNotifications() {
        UIApplication.shared.registerForRemoteNotifications()
    }
    private func saveDeviceTokenLocally(_ token: String) async {
        guard let currentUserId = getCurrentUserId() else {
            print("❌ No current user ID available for device token save")
            return
        }
        struct DeviceTokenPayload: Codable {
            let user_id: String
            let device_token: String
            let platform: String
            let device_type: String
            let device_name: String
            let app_version: String
            let is_active: Bool
        }
        let _ = (
            user_id: currentUserId,
            device_token: token,
            platform: "ios",
            device_type: "ios",
            device_name: UIDevice.current.name,
            app_version: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0",
            is_active: true
        )
        print("📱 Device token registered locally: \(token)")
        // do {
        // } catch {
        //     print("❌ Failed to save device token: \(error)")
        // }
    }
    // MARK: - Permission Management
    func requestNotificationPermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            notificationCenter.requestAuthorization(options: [.alert, .sound, .badge, .criticalAlert]) { [weak self] granted, error in
                Task { @MainActor in
                    if granted {
                        self?.authorizationStatus = .authorized
                        self?.registerNotificationCategories()
                    } else {
                        self?.authorizationStatus = .denied
                    }
                    if let error = error {
                        print("❌ Notification permission error: \(error)")
                    }
                    continuation.resume(returning: granted)
                }
            }
        }
    }
    private func registerNotificationCategories() {
        // Environmental Alert Actions
        let viewDetailsAction = UNNotificationAction(
            identifier: "VIEW_DETAILS",
            title: "View Details",
            options: [.foreground]
        )
        let dismissAction = UNNotificationAction(
            identifier: "DISMISS",
            title: "Dismiss",
            options: []
        )
        let postponeAction = UNNotificationAction(
            identifier: "POSTPONE",
            title: "Remind in 1 Hour",
            options: []
        )
        // Environmental Alert Category
        let environmentalCategory = UNNotificationCategory(
            identifier: "ENVIRONMENTAL_ALERT",
            actions: [viewDetailsAction, postponeAction, dismissAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        // Walk Recommendation Category
        let walkRecommendationCategory = UNNotificationCategory(
            identifier: "WALK_RECOMMENDATION",
            actions: [viewDetailsAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
        // Community Event Category
        let eventReminderCategory = UNNotificationCategory(
            identifier: "EVENT_REMINDER",
            actions: [viewDetailsAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
        notificationCenter.setNotificationCategories([
            environmentalCategory,
            walkRecommendationCategory,
            eventReminderCategory
        ])
    }
    // MARK: - Environmental Monitoring
    func startEnvironmentalMonitoring() {
        guard authorizationStatus == .authorized else {
            print("❌ Notification permission not granted - requesting permission")
            Task {
                let granted = await requestNotificationPermission()
                if granted {
                    await startEnvironmentalMonitoring()
                }
            }
            return
        }
        isMonitoring = true
        // Check environmental conditions every 10 minutes for more responsive monitoring
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 600, repeats: true) { [weak self] _ in
            Task {
                await self?.checkEnvironmentalConditions()
            }
        }
        // Start real-time weather monitoring integration
        Task {
            await RealTimeWeatherService.shared.startRealTimeMonitoring()
        }
        // Initial check
        Task {
            await checkEnvironmentalConditions()
        }
        print("✅ Enhanced environmental monitoring started with real-time integration")
    }
    func stopEnvironmentalMonitoring() {
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
    }
    private func checkEnvironmentalConditions() async {
        do {
            // Get user's current location (simplified - would use location manager)
            let userLocation = CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194) // San Francisco
            // Load user's environmental alerts
            let _ = "current_user_id" // Would get from auth service
            print("Loading environmental alerts with Apple native services")
            // Get current environmental data
            let _ = try await appleWeatherService.getCurrentWeather(for: userLocation)
            let _ = try await appleWeatherService.getAirQuality(for: userLocation)
            let _ = try await airQualityService.getAirQualityData(for: userLocation)
            // Check each alert using Apple native data
            // TODO: Implement environmental alerts checking with Apple native services
            print("Checking environmental alerts with Apple native services")
        } catch {
            print("❌ Failed to check environmental conditions: \(error)")
        }
    }
    private func checkAlert(_ alert: EnvironmentalAlert, weather: WeatherData, airQuality: AirQualityData, pollen: PollenData) async {
        guard alert.isActive && alert.notificationEnabled else { return }
        // Check if current time is within notification window
        let currentTime = Calendar.current.dateComponents([.hour, .minute], from: Date())
        let startTime = parseTime(alert.notificationTimeStart)
        let endTime = parseTime(alert.notificationTimeEnd)
        if !isTimeInRange(currentTime, start: startTime, end: endTime) {
            return
        }
        var currentValue: Int = 0
        var alertTriggered = false
        var alertMessage = ""
        // Check alert condition based on type
        switch alert.alertType {
        case .airQuality:
            currentValue = airQuality.index
            alertTriggered = evaluateCondition(currentValue, alert.comparisonOperator, Int(alert.thresholdValue))
            alertMessage = "Air quality is \(airQuality.description) (AQI: \(currentValue))"
        case .temperature:
            currentValue = Int(weather.temperature)
            alertTriggered = evaluateCondition(currentValue, alert.comparisonOperator, Int(alert.thresholdValue))
            alertMessage = "Temperature is \(currentValue)°F"
        case .pollen:
            // Use simplified pollen data since PollenData structure varies
            let totalPollen = 50 // Placeholder - would calculate from actual pollen data
            currentValue = totalPollen
            alertTriggered = evaluateCondition(currentValue, alert.comparisonOperator, Int(alert.thresholdValue))
            alertMessage = "Pollen levels require attention (Index: \(totalPollen))"
        case .humidity:
            currentValue = Int(weather.humidity)
            alertTriggered = evaluateCondition(currentValue, alert.comparisonOperator, Int(alert.thresholdValue))
            alertMessage = "Humidity is \(currentValue)%"
        default:
            return
        }
        if alertTriggered {
            await sendEnvironmentalAlert(
                alert: alert,
                currentValue: currentValue,
                message: alertMessage
            )
        }
    }
    // MARK: - Notification Sending
    func sendEnvironmentalAlert(alert: EnvironmentalAlert, currentValue: Int, message: String) async {
        let content = UNMutableNotificationContent()
        content.title = "🌡️ Environmental Alert"
        content.body = message
        content.subtitle = "Consider adjusting your pet's outdoor activities"
        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "ENVIRONMENTAL_ALERT"
        // Add custom data
        content.userInfo = [
            "alert_id": alert.id,
            "alert_type": alert.alertType.rawValue,
            "current_value": currentValue,
            "threshold_value": alert.thresholdValue
        ]
        // Create request
        let identifier = "environmental_alert_\(alert.id)_\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: nil)
        do {
            try await notificationCenter.add(request)
            await logNotification(
                alertId: alert.id,
                userId: "current_user", // Using Apple native user system
                alertType: alert.alertType.rawValue,
                currentValue: currentValue,
                thresholdValue: Int(alert.thresholdValue),
                message: message
            )
        } catch {
            print("❌ Failed to send notification: \(error)")
        }
    }
    func sendWalkRecommendation(score: Int, timeSlot: String, reason: String) async {
        let content = UNMutableNotificationContent()
        content.title = "🐕 Perfect Walking Weather!"
        content.body = "Great conditions for a walk with your pet. \(reason)"
        content.subtitle = timeSlot
        content.sound = .default
        content.categoryIdentifier = "WALK_RECOMMENDATION"
        content.userInfo = [
            "type": "walk_recommendation",
            "score": score,
            "time_slot": timeSlot
        ]
        let identifier = "walk_recommendation_\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: nil)
        do {
            try await notificationCenter.add(request)
        } catch {
            print("❌ Failed to send walk recommendation: \(error)")
        }
    }
    func sendEventReminder(event: CommunityEvent, minutesBefore: Int) async {
        let content = UNMutableNotificationContent()
        content.title = "📅 Event Reminder"
        content.body = "\(event.title) starts in \(minutesBefore) minutes"
        content.subtitle = event.location
        content.sound = .default
        content.categoryIdentifier = "EVENT_REMINDER"
        content.userInfo = [
            "event_id": event.id,
            "type": "event_reminder"
        ]
        // Schedule notification
        let triggerDate = event.date.addingTimeInterval(-Double(minutesBefore * 60))
        let trigger = UNCalendarNotificationTrigger(
            dateMatching: Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: triggerDate),
            repeats: false
        )
        let identifier = "event_reminder_\(event.id)_\(minutesBefore)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        do {
            try await notificationCenter.add(request)
        } catch {
            print("❌ Failed to schedule event reminder: \(error)")
        }
    }
    // MARK: - Helper Methods
    private func evaluateCondition(_ currentValue: Int, _ operatorString: String, _ threshold: Int) -> Bool {
        switch operatorString {
        case ">":
            return currentValue > threshold
        case "<":
            return currentValue < threshold
        case ">=":
            return currentValue >= threshold
        case "<=":
            return currentValue <= threshold
        case "=":
            return currentValue == threshold
        default:
            return false
        }
    }
    private func parseTime(_ timeString: String) -> DateComponents {
        let components = timeString.split(separator: ":")
        guard components.count == 2,
              let hour = Int(components[0]),
              let minute = Int(components[1]) else {
            return DateComponents(hour: 0, minute: 0)
        }
        return DateComponents(hour: hour, minute: minute)
    }
    private func isTimeInRange(_ current: DateComponents, start: DateComponents, end: DateComponents) -> Bool {
        let currentMinutes = (current.hour ?? 0) * 60 + (current.minute ?? 0)
        let startMinutes = (start.hour ?? 0) * 60 + (start.minute ?? 0)
        let endMinutes = (end.hour ?? 0) * 60 + (end.minute ?? 0)
        if startMinutes <= endMinutes {
            return currentMinutes >= startMinutes && currentMinutes <= endMinutes
        } else {
            // Crosses midnight
            return currentMinutes >= startMinutes || currentMinutes <= endMinutes
        }
    }
    private func logNotification(alertId: String, userId: String, alertType: String, currentValue: Int, thresholdValue: Int, message: String) async {
        let notificationData: [String: String] = [
            "alert_id": alertId,
            "user_id": userId,
            "alert_type": alertType,
            "current_value": String(currentValue),
            "threshold_value": String(thresholdValue),
            "message": message,
            "delivery_status": "sent"
        ]
        print("📝 Logging notification with Apple native services: \(alertType)")
    }
    // MARK: - Server-Side Notification Integration
    func sendServerSideNotification(
        type: NotificationEnvironmentalAlertType,
        severity: SharedAlertSeverity,
        title: String,
        message: String,
        petId: String? = nil,
        data: [String: Any] = [:]
    ) async {
        guard let deviceToken = deviceToken else {
            print("❌ No device token available for server-side notification")
            return
        }
        let _: [String: Any] = [
            "device_token": deviceToken,
            "notification_id": UUID().uuidString,
            "type": type.rawValue,
            "severity": severity.rawValue,
            "title": title,
            "message": message,
            "pet_id": petId as Any,
            "data": data,
            "user_id": getCurrentUserId(),
            "timestamp": Date().timeIntervalSince1970
        ]
        do {
            print("Sending notification with Apple native services: send-environmental-notification")
            print("✅ Server-side notification sent")
        } catch {
            print("❌ Failed to send server-side notification: \(error)")
        }
    }
    // MARK: - Notification Settings Management
    private func loadNotificationSettings() async -> NotificationSettings {
        print("Loading notification settings from local storage")
        // Return proper NotificationSettings type with correct parameters
        return NotificationSettings(
            environmentalAlertsEnabled: true,
            criticalAlertsEnabled: true,
            weatherUpdatesEnabled: true,
            pollenAlertsEnabled: true,
            quietHoursEnabled: false,
            quietHoursStart: Calendar.current.date(bySettingHour: 22, minute: 0, second: 0, of: Date()) ?? Date(),
            quietHoursEnd: Calendar.current.date(bySettingHour: 7, minute: 0, second: 0, of: Date()) ?? Date(),
            alertFrequency: .immediate
        )
    }
    func updateNotificationSettings(_ settings: NotificationSettings) async {
        notificationSettings = settings
        do {
            try await saveNotificationSettings(settings)
            print("✅ Notification settings updated")
        } catch {
            print("❌ Failed to update notification settings: \(error)")
        }
    }
    private func saveNotificationSettings(_ settings: NotificationSettings) async throws {
        // Simplified implementation - just print for now
        print("Saving notification settings: \(settings)")
        // In a real implementation, this would save to local storage
        // For now, we'll just return without doing anything
    }
    private func getCurrentUserId() -> String? {
        return "current_user" // Using Apple native user system
    }
    func clearNotificationHistory() {
        notificationHistory.removeAll()
    }
}
// MARK: - UNUserNotificationCenterDelegate
extension EnvironmentalNotificationService: UNUserNotificationCenterDelegate {
    nonisolated func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
    nonisolated func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        switch response.actionIdentifier {
        case "VIEW_DETAILS":
            Task { @MainActor in
                handleViewDetailsAction(userInfo: userInfo)
            }
        case "DISMISS":
            Task { @MainActor in
                handleDismissAction(userInfo: userInfo)
            }
        case "POSTPONE":
            Task { @MainActor in
                handlePostponeAction(userInfo: userInfo)
            }
        default:
            break
        }
        completionHandler()
    }
    private func handleViewDetailsAction(userInfo: [AnyHashable: Any]) {
        // Navigate to appropriate view based on notification type
        if let alertId = userInfo["alert_id"] as? String {
            // Navigate to environmental alerts view
            NotificationCenter.default.post(name: .navigateToEnvironmentalAlerts, object: alertId)
        } else if let eventId = userInfo["event_id"] as? String {
            // Navigate to event details
            NotificationCenter.default.post(name: .navigateToEventDetails, object: eventId)
        }
    }
    private func handleDismissAction(userInfo: [AnyHashable: Any]) {
        // Mark notification as dismissed
        if let alertId = userInfo["alert_id"] as? String {
            Task {
                await markNotificationDismissed(alertId: alertId)
            }
        }
    }
    private func handlePostponeAction(userInfo: [AnyHashable: Any]) {
        // Schedule reminder in 1 hour
        if let alertId = userInfo["alert_id"] as? String {
            Task {
                await schedulePostponedReminder(alertId: alertId)
            }
        }
    }
    func markNotificationDismissed(alertId: String) async {
        print("📝 Marking notification as dismissed with Apple native services: \(alertId)")
    }
    private func schedulePostponedReminder(alertId: String) async {
        // Schedule a reminder notification in 1 hour
        let content = UNMutableNotificationContent()
        content.title = "🔔 Reminder"
        content.body = "Environmental conditions may still require attention"
        content.sound = .default
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 3600, repeats: false)
        let identifier = "postponed_reminder_\(alertId)_\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        do {
            try await notificationCenter.add(request)
        } catch {
            print("❌ Failed to schedule postponed reminder: \(error)")
        }
    }
    func scheduleSnoozeReminder(alertId: String) async {
        // Schedule a snooze reminder notification in 15 minutes
        let content = UNMutableNotificationContent()
        content.title = "🔔 Snooze Reminder"
        content.body = "Environmental alert reminder - please check conditions"
        content.sound = .default
        content.categoryIdentifier = "ENVIRONMENTAL_ALERT"
        content.userInfo = [
            "alert_id": alertId,
            "type": "snooze_reminder"
        ]
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 900, repeats: false) // 15 minutes
        let identifier = "snooze_reminder_\(alertId)_\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        do {
            try await notificationCenter.add(request)
            print("✅ Snooze reminder scheduled for 15 minutes")
        } catch {
            print("❌ Failed to schedule snooze reminder: \(error)")
        }
    }
}
// MARK: - Notification Names
extension Notification.Name {
    static let navigateToEnvironmentalAlerts = Notification.Name("navigateToEnvironmentalAlerts")
    static let navigateToEventDetails = Notification.Name("navigateToEventDetails")
}
// MARK: - Enhanced Data Models for Push Notifications
struct NotificationSettings {
    var environmentalAlertsEnabled: Bool
    var criticalAlertsEnabled: Bool
    var weatherUpdatesEnabled: Bool
    var pollenAlertsEnabled: Bool
    var quietHoursEnabled: Bool
    var quietHoursStart: Date
    var quietHoursEnd: Date
    var alertFrequency: AlertFrequency
    static func defaultSettings() -> NotificationSettings {
        return NotificationSettings(
            environmentalAlertsEnabled: true,
            criticalAlertsEnabled: true,
            weatherUpdatesEnabled: true,
            pollenAlertsEnabled: true,
            quietHoursEnabled: false,
            quietHoursStart: Calendar.current.date(bySettingHour: 22, minute: 0, second: 0, of: Date()) ?? Date(),
            quietHoursEnd: Calendar.current.date(bySettingHour: 7, minute: 0, second: 0, of: Date()) ?? Date(),
            alertFrequency: .immediate
        )
    }
}
struct PendingNotification {
    let id: String
    let type: SharedEnvironmentalAlertType
    let severity: SharedAlertSeverity
    let title: String
    let message: String
    let scheduledAt: Date
    let petId: String?
    let location: String?
    var isRead: Bool = false
}
struct NotificationHistoryItem {
    let id: String
    let type: NotificationEnvironmentalAlertType
    let severity: SharedAlertSeverity
    let title: String
    let message: String
    let sentAt: Date
    let petId: String?
    let location: String?
    var isRead: Bool
    var readAt: Date?
}
enum NotificationCategory: String, CaseIterable {
    case environmentalAlert = "ENVIRONMENTAL_ALERT"
    case criticalEnvironmentalAlert = "CRITICAL_ENVIRONMENTAL_ALERT"
    case weatherUpdate = "WEATHER_UPDATE"
    case pollenAlert = "POLLEN_ALERT"
    case walkRecommendation = "WALK_RECOMMENDATION"
    case eventReminder = "EVENT_REMINDER"
}
enum NotificationAction: String, CaseIterable {
    case viewDetails = "VIEW_DETAILS"
    case dismiss = "DISMISS"
    case snooze = "SNOOZE"
    case callVet = "CALL_VET"
    case viewForecast = "VIEW_FORECAST"
    case planWalk = "PLAN_WALK"
    case adjustSettings = "ADJUST_SETTINGS"
}
enum AlertFrequency: String, CaseIterable {
    case immediate = "immediate"
    case normal = "normal"
    case reduced = "reduced"
    case critical_only = "critical_only"
    var displayName: String {
        switch self {
        case .immediate: return "Immediate"
        case .normal: return "Normal"
        case .reduced: return "Reduced"
        case .critical_only: return "Critical Only"
        }
    }
    var cooldownPeriod: TimeInterval {
        switch self {
        case .immediate: return 300 // 5 minutes
        case .normal: return 1800 // 30 minutes
        case .reduced: return 3600 // 1 hour
        case .critical_only: return 0 // No cooldown for critical
        }
    }
}
struct NotificationSettingsResponse: Codable {
    let user_id: String
    let environmental_alerts_enabled: Bool
    let critical_alerts_enabled: Bool
    let weather_updates_enabled: Bool
    let pollen_alerts_enabled: Bool
    let quiet_hours_enabled: Bool
    let quiet_hours_start: String
    let quiet_hours_end: String
    let alert_frequency: String
    let updated_at: String
    func toNotificationSettings() -> NotificationSettings {
        // Convert string times to Date objects
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        let startDate = formatter.date(from: quiet_hours_start) ?? Calendar.current.date(bySettingHour: 22, minute: 0, second: 0, of: Date()) ?? Date()
        let endDate = formatter.date(from: quiet_hours_end) ?? Calendar.current.date(bySettingHour: 7, minute: 0, second: 0, of: Date()) ?? Date()
        return NotificationSettings(
            environmentalAlertsEnabled: environmental_alerts_enabled,
            criticalAlertsEnabled: critical_alerts_enabled,
            weatherUpdatesEnabled: weather_updates_enabled,
            pollenAlertsEnabled: pollen_alerts_enabled,
            quietHoursEnabled: quiet_hours_enabled,
            quietHoursStart: startDate,
            quietHoursEnd: endDate,
            alertFrequency: AlertFrequency(rawValue: alert_frequency) ?? .normal
        )
    }
}
struct NotificationSettingsRequest: Codable {
    let user_id: String
    let environmental_alerts_enabled: Bool
    let critical_alerts_enabled: Bool
    let weather_updates_enabled: Bool
    let pollen_alerts_enabled: Bool
    let quiet_hours_enabled: Bool
    let quiet_hours_start: String
    let quiet_hours_end: String
    let alert_frequency: String
    let updated_at: String
}
// MARK: - Error Types
enum NotificationError: Error, LocalizedError {
    case permissionDenied
    case settingsNotFound
    case deviceTokenNotAvailable
    case serverError(String)
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Notification permission denied"
        case .settingsNotFound:
            return "Notification settings not found"
        case .deviceTokenNotAvailable:
            return "Device token not available"
        case .serverError(let message):
            return "Server error: \(message)"
        }
    }
}
