//
//  EnvironmentalScoringService.swift
//  PetCapsule
//
//  Comprehensive environmental data scoring with personalized recommendations
//

import Foundation
import CoreLocation
import Combine
import SwiftUI

// Type aliases to resolve scope issues
typealias ScoringEnvironmentalAlert = EnvironmentalAlert

@MainActor
class EnvironmentalScoringService: ObservableObject {
    static let shared = EnvironmentalScoringService()
    
    // Published properties
    @Published var currentEnvironmentalScore: EnvironmentalScore?
    @Published var walkRecommendations: [PersonalizedWalkRecommendation] = []
    @Published var locationScores: [String: EnvironmentalScore] = [:]
    @Published var isCalculating = false
    @Published var lastUpdateTime: Date?
    
    // Services
    private let weatherService = AppleWeatherService.shared
    private let airQualityService = AppleAirQualityService.shared
    private let locationManager = CLLocationManager()
    @Published var currentLocation: CLLocation?
    private let petService = RealDataService()
    
    // Scoring configuration
    private let scoringWeights = ScoringWeights()
    
    private init() {}
    
    // MARK: - Public Methods
    
    func getCurrentEnvironmentalScore() async throws -> EnvironmentalScore? {
        guard let location = currentLocation?.coordinate else {
            throw NSError(domain: "LocationError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Location not available"])
        }
        
        return try await calculateEnvironmentalScore(for: location)
    }
    
    // MARK: - Main Scoring Functions
    
    func calculateEnvironmentalScore(
        for location: CLLocationCoordinate2D,
        pet: Pet? = nil,
        timeOfDay: Date = Date()
    ) async throws -> EnvironmentalScore {
        
        isCalculating = true
        defer { isCalculating = false }
        
        // Gather environmental data
        async let weather = weatherService.getCurrentWeather(for: location)
        async let airQuality = weatherService.getAirQuality(for: location)
                    async let pollen = airQualityService.getAirQualityData(for: location)
        
        let weatherData = try await weather
        let airQualityData = try await airQuality
        let pollenData = try await pollen
        
        // Calculate individual component scores
        let temperatureScore = calculateTemperatureScore(weatherData.temperature, pet: pet)
        let humidityScore = calculateHumidityScore(weatherData.humidity)
        let airQualityScore = calculateAirQualityScore(airQualityData.index)
        let pollenScore = calculatePollenScore(pollenData, pet: pet)
        let weatherConditionScore = calculateWeatherConditionScore(weatherData.condition)
        let windScore = calculateWindScore(weatherData.windSpeed)
        let timeScore = calculateTimeOfDayScore(timeOfDay)
        
        // Calculate weighted overall score
        let overallScore = calculateWeightedScore(
            temperature: temperatureScore,
            humidity: humidityScore,
            airQuality: airQualityScore,
            pollen: pollenScore,
            weatherCondition: weatherConditionScore,
            wind: windScore,
            timeOfDay: timeScore,
            pet: pet
        )
        
        // Generate recommendations
        _ = generateRecommendations(
            overallScore: overallScore,
            components: EnvironmentalComponents(
                temperature: temperatureScore,
                humidity: humidityScore,
                airQuality: airQualityScore,
                pollen: pollenScore,
                weatherCondition: weatherConditionScore,
                wind: windScore,
                timeOfDay: timeScore
            ),
            weatherData: weatherData,
            airQualityData: airQualityData,
            pollenData: pollenData,
            pet: pet
        )
        
        let environmentalScore = EnvironmentalScore(
            score: overallScore,
            components: EnvironmentalComponents(
                temperature: temperatureScore,
                humidity: humidityScore,
                airQuality: airQualityScore,
                pollen: pollenScore,
                weatherCondition: weatherConditionScore,
                wind: windScore,
                timeOfDay: timeScore
            )
        )
        
        // Cache the score
        let locationKey = "\(location.latitude),\(location.longitude)"
        locationScores[locationKey] = environmentalScore
        
        if let currentLoc = currentLocation?.coordinate,
           abs(location.latitude - currentLoc.latitude) < 0.001 &&
           abs(location.longitude - currentLoc.longitude) < 0.001 {
            currentEnvironmentalScore = environmentalScore
        }
        
        lastUpdateTime = Date()
        
        return environmentalScore
    }
    
    // MARK: - Component Scoring Functions
    
    private func calculateTemperatureScore(_ temperature: Int, pet: Pet?) -> ComponentScore {
        let temp = Double(temperature)
        var score: Double
        var message: String
        
        // Base temperature scoring
        switch temp {
        case ..<32:
            score = 0.2
            message = "Freezing temperature - dangerous for pets"
        case 32..<45:
            score = 0.4
            message = "Very cold - limit outdoor time"
        case 45..<55:
            score = 0.7
            message = "Cool temperature - consider pet clothing"
        case 55..<75:
            score = 1.0
            message = "Ideal temperature for walks"
        case 75..<85:
            score = 0.8
            message = "Warm but comfortable"
        case 85..<95:
            score = 0.5
            message = "Hot - provide water and shade"
        default:
            score = 0.2
            message = "Extreme heat - avoid outdoor activities"
        }
        
        // Pet-specific adjustments
        if let pet = pet {
            score = adjustScoreForPet(score, pet: pet, factor: .temperature)
            message = personalizeMessage(message, pet: pet, factor: .temperature)
        }
        
        return ComponentScore(
            score: score,
            message: message
        )
    }
    
    private func calculateHumidityScore(_ humidity: Int) -> ComponentScore {
        let score: Double
        let message: String
        
        switch humidity {
        case 0..<30:
            score = 0.7
            message = "Low humidity - may cause dry skin"
        case 30..<60:
            score = 1.0
            message = "Comfortable humidity levels"
        case 60..<80:
            score = 0.8
            message = "Slightly humid but manageable"
        default:
            score = 0.4
            message = "High humidity - pets may overheat easily"
        }
        
        return ComponentScore(
            score: score,
            message: message
        )
    }
    
    private func calculateAirQualityScore(_ aqi: Int) -> ComponentScore {
        let score: Double
        let message: String
        
        switch aqi {
        case 0...50:
            score = 1.0
            message = "Excellent air quality"
        case 51...100:
            score = 0.8
            message = "Good air quality"
        case 101...150:
            score = 0.5
            message = "Moderate air quality - sensitive pets may be affected"
        case 151...200:
            score = 0.3
            message = "Unhealthy air quality - limit outdoor activities"
        default:
            score = 0.1
            message = "Hazardous air quality - avoid outdoor activities"
        }
        
        return ComponentScore(
            score: score,
            message: message
        )
    }
    
    private func calculatePollenScore(_ pollenData: PollenData, pet: Pet?) -> ComponentScore {
        let totalPollen = pollenData.treeIndex + pollenData.grassIndex + pollenData.weedIndex
        var score: Double
        var message: String
        
        switch totalPollen {
        case 0...5:
            score = 1.0
            message = "Low pollen levels"
        case 6...10:
            score = 0.8
            message = "Moderate pollen levels"
        case 11...15:
            score = 0.6
            message = "High pollen levels - may affect sensitive pets"
        default:
            score = 0.3
            message = "Very high pollen levels - consider indoor activities"
        }
        
        if let pet = pet {
            score = adjustScoreForPet(score, pet: pet, factor: .pollen)
            message = personalizeMessage(message, pet: pet, factor: .pollen)
        }
        
        return ComponentScore(
            score: score,
            message: message
        )
    }
    
    private func calculateWeatherConditionScore(_ condition: String) -> ComponentScore {
        let score: Double

        switch condition.lowercased() {
        case "sunny", "clear":
            score = 1.0
        case "partly cloudy", "mostly cloudy":
            score = 0.9
        case "rain", "drizzle":
            score = 0.5
        case "thunderstorm":
            score = 0.2
        case "snow":
            score = 0.4
        default:
            score = 0.7
        }

        return ComponentScore(
            score: score,
            message: condition.capitalized
        )
    }
    
    private func calculateWindScore(_ windSpeed: Double) -> ComponentScore {
        let score: Double

        switch windSpeed {
        case 0..<10:
            score = 1.0
        case 10..<20:
            score = 0.8
        case 20..<30:
            score = 0.6
        default:
            score = 0.4
        }

        return ComponentScore(
            score: score,
            message: "Wind speed: \(Int(windSpeed)) mph"
        )
    }
    
    private func calculateTimeOfDayScore(_ time: Date) -> ComponentScore {
        let hour = Calendar.current.component(.hour, from: time)
        let score: Double
        
        switch hour {
        case 6...9, 17...19: // Morning and evening
            score = 1.0
        case 10...16: // Midday
            score = 0.8
        default: // Night
            score = 0.6
        }
        
        return ComponentScore(
            score: score,
            message: "Time of day consideration"
        )
    }
    
    // MARK: - Weighted Score Calculation
    
    private func calculateWeightedScore(
        temperature: ComponentScore,
        humidity: ComponentScore,
        airQuality: ComponentScore,
        pollen: ComponentScore,
        weatherCondition: ComponentScore,
        wind: ComponentScore,
        timeOfDay: ComponentScore,
        pet: Pet?
    ) -> Double {
        
        var weights = scoringWeights
        weights.normalize()
        
        var totalScore =
            temperature.score * weights.temperature +
            humidity.score * weights.humidity +
            airQuality.score * weights.airQuality +
            pollen.score * weights.pollen +
            weatherCondition.score * weights.weatherCondition +
            wind.score * weights.wind +
            timeOfDay.score * weights.timeOfDay
        
        // Adjust for pet sensitivities
        if let pet = pet {
            // Check allergies for sensitivity adjustments
            if pet.allergies.contains("pollen") || pet.foodAllergies.contains("pollen") {
                totalScore -= 0.1 // Additional penalty for pollen
            }
            if pet.allergies.contains("heat") || pet.healthAlerts.contains(where: { $0.type == "heat sensitivity" }) {
                totalScore -= 0.1 // Additional penalty for heat
            }
        }
        
        return max(0, min(1, totalScore)) // Clamp between 0 and 1
    }
    
    // MARK: - Recommendation Generation
    
    private func generateRecommendations(
        overallScore: Double,
        components: EnvironmentalComponents,
        weatherData: WeatherData,
        airQualityData: AirQualityData,
        pollenData: PollenData,
        pet: Pet?
    ) -> [WalkRecommendation] {
        var recommendations: [WalkRecommendation] = []
        
        recommendations += generateTemperatureRecommendations(Double(weatherData.temperature), pet: pet)
        recommendations += generateHumidityRecommendations(weatherData.humidity)
        recommendations += generateAirQualityRecommendations(airQualityData.index)
        recommendations += generatePollenRecommendations(pollenData, pet: pet)
        recommendations += generateWeatherConditionRecommendations(weatherData.condition)
        recommendations += generateWindRecommendations(weatherData.windSpeed)
        
        let hour = Calendar.current.component(.hour, from: Date())
        recommendations += generateTimeRecommendations(hour)
        
        return recommendations
    }
    
    // MARK: - Personalization
    
    private func adjustScoreForPet(_ score: Double, pet: Pet, factor: EnvironmentalFactor) -> Double {
        var adjustedScore = score
        
        switch factor {
        case .temperature:
            if (pet.allergies.contains("heat") || pet.healthAlerts.contains(where: { $0.type == "heat sensitivity" })) && score < 0.7 {
                adjustedScore -= 0.15
            }
        case .pollen:
            if (pet.allergies.contains("pollen") || pet.foodAllergies.contains("pollen")) && score < 0.7 {
                adjustedScore -= 0.2
            }
        default:
            break
        }
        
        return max(0, adjustedScore)
    }
    
    private func personalizeMessage(_ message: String, pet: Pet, factor: EnvironmentalFactor) -> String {
        var personalizedMessage = message
        
        switch factor {
        case .temperature:
            if pet.allergies.contains("heat") || pet.healthAlerts.contains(where: { $0.type == "heat sensitivity" }) {
                personalizedMessage += " - Be extra cautious as \(pet.name) is sensitive to heat."
            }
        case .pollen:
            if pet.allergies.contains("pollen") || pet.foodAllergies.contains("pollen") {
                personalizedMessage += " - Be extra cautious as \(pet.name) is sensitive to pollen."
            }
        default:
            break
        }
        
        return personalizedMessage
    }
}

// MARK: - Recommendation Generation Extensions

extension EnvironmentalScoringService {

    private func generateTemperatureRecommendations(_ temperature: Double, pet: Pet?) -> [WalkRecommendation] {
        var recommendations: [WalkRecommendation] = []

        if temperature < 32 {
            recommendations.append(WalkRecommendation(
                id: UUID().uuidString,
                petId: pet?.id ?? "",
                recommendedTime: Date(),
                duration: 1800,
                route: nil,
                weatherScore: 0.2,
                airQualityScore: 0,
                pollenScore: 0,
                overallScore: 0.2,
                reasoning: "Freezing Temperature",
                alternatives: ["Indoor play"],
                createdAt: Date()
            ))
        } else if temperature > 85 {
            recommendations.append(WalkRecommendation(
                id: UUID().uuidString,
                petId: pet?.id ?? "",
                recommendedTime: Date(),
                duration: 1800,
                route: nil,
                weatherScore: 0.2,
                airQualityScore: 0,
                pollenScore: 0,
                overallScore: 0.2,
                reasoning: "Hot Temperature",
                alternatives: ["Indoor play"],
                createdAt: Date()
            ))
        }

        return recommendations
    }

    private func generateHumidityRecommendations(_ humidity: Int) -> [WalkRecommendation] {
        var recommendations: [WalkRecommendation] = []

        if humidity > 80 {
            recommendations.append(WalkRecommendation(
                id: UUID().uuidString,
                petId: "",
                recommendedTime: Date(),
                duration: 1800,
                route: nil,
                weatherScore: 0.4,
                airQualityScore: 0,
                pollenScore: 0,
                overallScore: 0.4,
                reasoning: "High Humidity",
                alternatives: ["Indoor play"],
                createdAt: Date()
            ))
        }

        return recommendations
    }

    private func generateAirQualityRecommendations(_ aqi: Int) -> [WalkRecommendation] {
        var recommendations: [WalkRecommendation] = []

        if aqi > 150 {
            recommendations.append(WalkRecommendation(
                id: UUID().uuidString,
                petId: "",
                recommendedTime: Date(),
                duration: 1800,
                route: nil,
                weatherScore: 0,
                airQualityScore: 0.3,
                pollenScore: 0,
                overallScore: 0.3,
                reasoning: "Poor Air Quality",
                alternatives: ["Indoor play"],
                createdAt: Date()
            ))
        }

        return recommendations
    }

    private func generatePollenRecommendations(_ pollen: PollenData, pet: Pet?) -> [WalkRecommendation] {
        var recommendations: [WalkRecommendation] = []

        let totalPollen = pollen.treeIndex + pollen.grassIndex + pollen.weedIndex

        if totalPollen > 15 {
            recommendations.append(WalkRecommendation(
                id: UUID().uuidString,
                petId: pet?.id ?? "",
                recommendedTime: Date(),
                duration: 1800,
                route: nil,
                weatherScore: 0,
                airQualityScore: 0,
                pollenScore: 0.3,
                overallScore: 0.3,
                reasoning: "High Pollen Levels",
                alternatives: ["Indoor play"],
                createdAt: Date()
            ))
        }

        return recommendations
    }

    private func generateWeatherConditionRecommendations(_ condition: String) -> [WalkRecommendation] {
        var recommendations: [WalkRecommendation] = []

        if condition.lowercased().contains("rain") {
            recommendations.append(WalkRecommendation(
                id: UUID().uuidString,
                petId: "",
                recommendedTime: Date(),
                duration: 1800,
                route: nil,
                weatherScore: 0.5,
                airQualityScore: 0,
                pollenScore: 0,
                overallScore: 0.5,
                reasoning: "Rainy Weather",
                alternatives: ["Indoor play"],
                createdAt: Date()
            ))
        }

        return recommendations
    }

    private func generateWindRecommendations(_ windSpeed: Double) -> [WalkRecommendation] {
        var recommendations: [WalkRecommendation] = []

        if windSpeed > 25 {
            recommendations.append(WalkRecommendation(
                id: UUID().uuidString,
                petId: "",
                recommendedTime: Date(),
                duration: 1800,
                route: nil,
                weatherScore: 0.6,
                airQualityScore: 0,
                pollenScore: 0,
                overallScore: 0.6,
                reasoning: "Strong Winds",
                alternatives: ["Indoor play"],
                createdAt: Date()
            ))
        }

        return recommendations
    }

    private func generateTimeRecommendations(_ hour: Int) -> [WalkRecommendation] {
        var recommendations: [WalkRecommendation] = []

        if hour >= 22 || hour <= 5 {
            recommendations.append(WalkRecommendation(
                id: UUID().uuidString,
                petId: "",
                recommendedTime: Date(),
                duration: 1800,
                route: nil,
                weatherScore: 0.6,
                airQualityScore: 0,
                pollenScore: 0,
                overallScore: 0.6,
                reasoning: "Night Time Walking",
                alternatives: ["Indoor play"],
                createdAt: Date()
            ))
        }

        return recommendations
    }
}
