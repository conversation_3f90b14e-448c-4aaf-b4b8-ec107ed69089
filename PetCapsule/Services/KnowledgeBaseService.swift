//
//  KnowledgeBaseService.swift
//  PetCapsule
//
//  Service for managing knowledge base folders and documents
//
import Foundation
import SwiftUI
@MainActor
class KnowledgeBaseService: ObservableObject {
    static let shared = KnowledgeBaseService()
    @Published var folders: [KnowledgeFolder] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private init() {
        Task {
            await loadFolders()
        }
    }
    // MARK: - Folder Management
    func createFolder(
        name: String,
        description: String,
        icon: String,
        color: Color,
        isSecure: Bool
    ) async {
        isLoading = true
        defer { isLoading = false }
        let folder = KnowledgeFolder(
            id: UUID(),
            name: name,
            description: description,
            icon: icon,
            color: color,
            isSecure: isSecure,
            documents: [],
            createdAt: Date(),
            updatedAt: Date()
        )
        do {
            let folderData = KnowledgeFolderData(
                id: folder.id.uuidString,
                name: folder.name,
                description: folder.description,
                icon: folder.icon,
                colorHex: "#007AFF", // Default blue color
                isSecure: folder.isSecure,
                userId: getCurrentUserId().uuidString,
                createdAt: folder.createdAt,
                updatedAt: folder.updatedAt
            )
            print("📁 Creating folder: \(folder.name) for user: \(getCurrentUserId().uuidString)")
            // Save to local storage and sync with CloudKit
            print("✅ Folder created successfully in database")
            // Add to local array
            folders.append(folder)
            print("✅ Folder added to local array. Total folders: \(folders.count)")
        } catch {
            print("❌ Failed to create folder: \(error)")
            errorMessage = "Failed to create folder: \(error.localizedDescription)"
        }
    }
    func deleteFolder(_ folder: KnowledgeFolder) async {
        isLoading = true
        defer { isLoading = false }
        do {
            // Delete from Apple native storage
            // Remove from local storage and sync with CloudKit
            // Remove from local array
            folders.removeAll { $0.id == folder.id }
        } catch {
            errorMessage = "Failed to delete folder: \(error.localizedDescription)"
        }
    }
    func addDocument(to folder: KnowledgeFolder, document: KnowledgeDocument) async {
        isLoading = true
        defer { isLoading = false }
        do {
            let documentData = KnowledgeDocumentData(
                id: document.id.uuidString,
                folderId: folder.id.uuidString,
                title: document.title,
                content: document.content,
                type: document.type.rawValue,
                fileURL: document.fileURL,
                tags: document.tags,
                createdAt: document.createdAt,
                updatedAt: document.updatedAt
            )
            // Save to local storage and sync with CloudKit
            // Update local folder
            if let index = folders.firstIndex(where: { $0.id == folder.id }) {
                folders[index].documents.append(document)
            }
        } catch {
            errorMessage = "Failed to add document: \(error.localizedDescription)"
        }
    }
    // MARK: - Search
    func searchDocuments(query: String) -> [KnowledgeDocument] {
        var results: [KnowledgeDocument] = []
        for folder in folders {
            let matchingDocs = folder.documents.filter { document in
                document.title.localizedCaseInsensitiveContains(query) ||
                document.content.localizedCaseInsensitiveContains(query) ||
                document.tags.contains { $0.localizedCaseInsensitiveContains(query) }
            }
            results.append(contentsOf: matchingDocs)
        }
        return results
    }
    // MARK: - Private Methods
    private func loadFolders() async {
        let userId = getCurrentUserId()
        do {
            // Load from local storage and CloudKit
            let response: [KnowledgeFolderData] = []
            var loadedFolders: [KnowledgeFolder] = []
            for folderData in response {
                // Load documents for this folder from Apple native storage
                let documentsResponse: [KnowledgeDocumentData] = []
                let documents = documentsResponse.compactMap { docData in
                    KnowledgeDocument(
                        id: UUID(uuidString: docData.id) ?? UUID(),
                        title: docData.title,
                        content: docData.content,
                        type: KnowledgeDocumentType(rawValue: docData.type) ?? .text,
                        fileURL: docData.fileURL,
                        tags: docData.tags,
                        createdAt: docData.createdAt,
                        updatedAt: docData.updatedAt
                    )
                }
                let folder = KnowledgeFolder(
                    id: UUID(uuidString: folderData.id) ?? UUID(),
                    name: folderData.name,
                    description: folderData.description,
                    icon: folderData.icon,
                    color: Color(hex: folderData.colorHex),
                    isSecure: folderData.isSecure,
                    documents: documents,
                    createdAt: folderData.createdAt,
                    updatedAt: folderData.updatedAt
                )
                loadedFolders.append(folder)
            }
            folders = loadedFolders
        } catch {
            errorMessage = "Failed to load folders: \(error.localizedDescription)"
        }
    }
    private func getCurrentUserId() -> UUID {
        // Use the same development user ID as authentication service
        return UUID(uuidString: "550e8400-e29b-41d4-a716-************") ?? UUID()
    }
}
// MARK: - Data Models
struct KnowledgeFolder: Identifiable {
    let id: UUID
    let name: String
    let description: String
    let icon: String
    let color: Color
    let isSecure: Bool
    var documents: [KnowledgeDocument]
    let createdAt: Date
    let updatedAt: Date
}
struct KnowledgeDocument: Identifiable {
    let id: UUID
    let title: String
    let content: String
    let type: KnowledgeDocumentType
    let fileURL: String?
    let tags: [String]
    let createdAt: Date
    let updatedAt: Date
}
enum KnowledgeDocumentType: String, CaseIterable {
    case text = "text"
    case recipe = "recipe"
    case medical = "medical"
    case training = "training"
    case photo = "photo"
    case document = "document"
    var displayName: String {
        switch self {
        case .text: return "Text Note"
        case .recipe: return "Recipe"
        case .medical: return "Medical Record"
        case .training: return "Training Guide"
        case .photo: return "Photo"
        case .document: return "Document"
        }
    }
    var icon: String {
        switch self {
        case .text: return "doc.text.fill"
        case .recipe: return "leaf.fill"
        case .medical: return "stethoscope"
        case .training: return "graduationcap.fill"
        case .photo: return "photo.fill"
        case .document: return "doc.fill"
        }
    }
}
struct KnowledgeFolderData: Codable {
    let id: String
    let name: String
    let description: String
    let icon: String
    let colorHex: String
    let isSecure: Bool
    let userId: String
    let createdAt: Date
    let updatedAt: Date
}
struct KnowledgeDocumentData: Codable {
    let id: String
    let folderId: String
    let title: String
    let content: String
    let type: String
    let fileURL: String?
    let tags: [String]
    let createdAt: Date
    let updatedAt: Date
}
// MARK: - Color Extensions
// Color extension with init(hex:) is already defined in EnhancedAIAgentChatView.swift
