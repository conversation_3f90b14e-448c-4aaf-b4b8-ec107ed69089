import Foundation
import SwiftUI
import Photos
import PhotosUI
import UniformTypeIdentifiers
import CloudKit
import AVFoundation

@MainActor
class LocalMediaStorageService: ObservableObject {
    static let shared = LocalMediaStorageService()
    
    @Published var isSetup = false
    @Published var iCloudSyncEnabled = true
    @Published var storageUsage: StorageUsage = StorageUsage()
    @Published var uploadProgress: [String: Double] = [:]
    @Published var errorMessage: String?
    
    // Storage directories
    private let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    private let iCloudDirectory: URL?
    
    // Media type directories
    private let petPhotosDirectory: URL
    private let petVideosDirectory: URL
    private let documentsStorageDirectory: URL
    private let thumbnailsDirectory: URL
    private let cacheDirectory: URL
    
    private init() {
        // Setup iCloud Drive directory
        self.iCloudDirectory = FileManager.default.url(forUbiquityContainerIdentifier: nil)
        
        // Setup local directories
        self.petPhotosDirectory = documentsDirectory.appendingPathComponent("PetPhotos")
        self.petVideosDirectory = documentsDirectory.appendingPathComponent("PetVideos")
        self.documentsStorageDirectory = documentsDirectory.appendingPathComponent("Documents")
        self.thumbnailsDirectory = documentsDirectory.appendingPathComponent("Thumbnails")
        self.cacheDirectory = documentsDirectory.appendingPathComponent("Cache")
        
        Task {
            await setupDirectories()
            await calculateStorageUsage()
        }
    }
    
    // MARK: - Storage Usage Model
    
    struct StorageUsage {
        var totalSizeMB: Double = 0
        var photosSizeMB: Double = 0
        var videosSizeMB: Double = 0
        var documentsSizeMB: Double = 0
        var cacheSize: Double = 0
        var iCloudSyncStatus: String = "Unknown"
    }
    
    // MARK: - Media Types
    
    enum MediaType: String, CaseIterable {
        case photo = "photo"
        case video = "video"
        case document = "document"
        case audio = "audio"
        
        var directory: String {
            switch self {
            case .photo: return "PetPhotos"
            case .video: return "PetVideos"
            case .document: return "Documents"
            case .audio: return "Audio"
            }
        }
        
        var fileExtensions: [String] {
            switch self {
            case .photo: return ["jpg", "jpeg", "png", "heic", "webp"]
            case .video: return ["mp4", "mov", "avi", "mkv"]
            case .document: return ["pdf", "txt", "doc", "docx", "pages"]
            case .audio: return ["mp3", "m4a", "wav", "aac"]
            }
        }
    }
    
    // MARK: - Setup Methods
    
    private func setupDirectories() async {
        let directories = [
            petPhotosDirectory,
            petVideosDirectory,
            documentsStorageDirectory,
            thumbnailsDirectory,
            cacheDirectory
        ]
        
        for directory in directories {
            do {
                try FileManager.default.createDirectory(
                    at: directory,
                    withIntermediateDirectories: true,
                    attributes: nil
                )
                
                // Add .nosync to prevent iCloud sync for cache
                if directory == cacheDirectory {
                    try (directory as NSURL).setResourceValue(true, forKey: .isExcludedFromBackupKey)
                }
                
                print("✅ Created directory: \(directory.lastPathComponent)")
            } catch {
                print("❌ Failed to create directory: \(directory.lastPathComponent) - \(error)")
                await MainActor.run {
                    self.errorMessage = "Failed to setup storage: \(error.localizedDescription)"
                }
            }
        }
        
        await MainActor.run {
            self.isSetup = true
        }
        
        await checkiCloudStatus()
    }
    
    private func checkiCloudStatus() async {
        guard let iCloudDirectory = iCloudDirectory else {
            await MainActor.run {
                self.storageUsage.iCloudSyncStatus = "Not Available"
                self.iCloudSyncEnabled = false
            }
            return
        }
        
        do {
            // Check if iCloud is available using FileManager
            var isAvailable = false
            if let ubiquityIdentityToken = FileManager.default.ubiquityIdentityToken {
                isAvailable = true
            }
            
            await MainActor.run {
                self.storageUsage.iCloudSyncStatus = isAvailable ? "Available" : "Not Available"
                self.iCloudSyncEnabled = isAvailable
            }
            
            print("✅ iCloud Drive status: \(isAvailable ? "Available" : "Not Available")")
        } catch {
            await MainActor.run {
                self.storageUsage.iCloudSyncStatus = "Error: \(error.localizedDescription)"
                self.iCloudSyncEnabled = false
            }
            print("❌ iCloud Drive check failed: \(error)")
        }
    }
    
    // MARK: - Media Storage Methods
    
    func storeMedia(data: Data, type: MediaType, filename: String? = nil, petId: String? = nil) async throws -> String {
        let fileExtension = type.fileExtensions.first ?? "dat"
        let finalFilename = filename ?? "\(UUID().uuidString).\(fileExtension)"
        
        let targetDirectory = getDirectory(for: type)
        let fileURL = targetDirectory.appendingPathComponent(finalFilename)
        
        // Create subdirectory for pet if provided
        let finalURL: URL
        if let petId = petId {
            let petDirectory = targetDirectory.appendingPathComponent(petId)
            try FileManager.default.createDirectory(at: petDirectory, withIntermediateDirectories: true)
            finalURL = petDirectory.appendingPathComponent(finalFilename)
        } else {
            finalURL = fileURL
        }
        
        // Store file
        try data.write(to: finalURL)
        
        // Generate thumbnail for images and videos
        if type == .photo || type == .video {
            await generateThumbnail(for: finalURL, type: type)
        }
        
        // Sync to iCloud if enabled
        if iCloudSyncEnabled {
            await syncToiCloud(fileURL: finalURL, type: type)
        }
        
        await calculateStorageUsage()
        
        print("✅ Media stored: \(finalURL.lastPathComponent)")
        return finalURL.path
    }
    
    func storePhotoFromPicker(_ photoPickerItem: PhotosPickerItem, petId: String? = nil) async throws -> String {
        guard let data = try await photoPickerItem.loadTransferable(type: Data.self) else {
            throw MediaStorageError.invalidData
        }
        
        let filename = "\(UUID().uuidString).jpg"
        return try await storeMedia(data: data, type: .photo, filename: filename, petId: petId)
    }
    
    func storeImage(_ image: UIImage, petId: String? = nil, compressionQuality: CGFloat = 0.8) async throws -> String {
        guard let data = image.jpegData(compressionQuality: compressionQuality) else {
            throw MediaStorageError.invalidData
        }
        
        let filename = "\(UUID().uuidString).jpg"
        return try await storeMedia(data: data, type: .photo, filename: filename, petId: petId)
    }
    
    func storeVideo(from url: URL, petId: String? = nil) async throws -> String {
        let data = try Data(contentsOf: url)
        let filename = "\(UUID().uuidString).\(url.pathExtension)"
        return try await storeMedia(data: data, type: .video, filename: filename, petId: petId)
    }
    
    func storeDocument(data: Data, filename: String, petId: String? = nil) async throws -> String {
        return try await storeMedia(data: data, type: .document, filename: filename, petId: petId)
    }
    
    // MARK: - Media Retrieval Methods
    
    func getMedia(at path: String) -> Data? {
        let url = URL(fileURLWithPath: path)
        return try? Data(contentsOf: url)
    }
    
    func getImage(at path: String) -> UIImage? {
        guard let data = getMedia(at: path) else { return nil }
        return UIImage(data: data)
    }
    
    func getThumbnail(for path: String) -> UIImage? {
        let filename = URL(fileURLWithPath: path).lastPathComponent
        let thumbnailPath = thumbnailsDirectory.appendingPathComponent("thumb_\(filename)")
        
        guard let data = try? Data(contentsOf: thumbnailPath) else { return nil }
        return UIImage(data: data)
    }
    
    func getAllMediaForPet(_ petId: String, type: MediaType) -> [URL] {
        let petDirectory = getDirectory(for: type).appendingPathComponent(petId)
        
        guard FileManager.default.fileExists(atPath: petDirectory.path) else { return [] }
        
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: petDirectory, includingPropertiesForKeys: nil)
            return contents.filter { url in
                type.fileExtensions.contains(url.pathExtension.lowercased())
            }
        } catch {
            print("❌ Failed to get media for pet: \(error)")
            return []
        }
    }
    
    // MARK: - Media Management Methods
    
    func deleteMedia(at path: String) async throws {
        let url = URL(fileURLWithPath: path)
        
        // Delete main file
        try FileManager.default.removeItem(at: url)
        
        // Delete thumbnail if exists
        let filename = url.lastPathComponent
        let thumbnailPath = thumbnailsDirectory.appendingPathComponent("thumb_\(filename)")
        try? FileManager.default.removeItem(at: thumbnailPath)
        
        // Remove from iCloud if synced
        if iCloudSyncEnabled {
            await removeFromiCloud(fileURL: url)
        }
        
        await calculateStorageUsage()
        print("✅ Media deleted: \(filename)")
    }
    
    func moveMedia(from sourcePath: String, to destinationPath: String) async throws {
        let sourceURL = URL(fileURLWithPath: sourcePath)
        let destinationURL = URL(fileURLWithPath: destinationPath)
        
        try FileManager.default.moveItem(at: sourceURL, to: destinationURL)
        
        // Update iCloud sync
        if iCloudSyncEnabled {
            await removeFromiCloud(fileURL: sourceURL)
            await syncToiCloud(fileURL: destinationURL, type: .photo)
        }
        
        print("✅ Media moved: \(sourceURL.lastPathComponent) → \(destinationURL.lastPathComponent)")
    }
    
    func clearCache() async {
        do {
            let cacheContents = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            
            for fileURL in cacheContents {
                try FileManager.default.removeItem(at: fileURL)
            }
            
            await calculateStorageUsage()
            print("✅ Cache cleared: \(cacheContents.count) files removed")
        } catch {
            print("❌ Failed to clear cache: \(error)")
        }
    }
    
    // MARK: - Thumbnail Generation
    
    private func generateThumbnail(for fileURL: URL, type: MediaType) async {
        let filename = fileURL.lastPathComponent
        let thumbnailURL = thumbnailsDirectory.appendingPathComponent("thumb_\(filename)")
        
        do {
            let thumbnail: UIImage?
            
            switch type {
                         case .photo:
                 guard let image = UIImage(contentsOfFile: fileURL.path) else { return }
                 thumbnail = image.resizedForThumbnail(to: CGSize(width: 200, height: 200))
                
            case .video:
                thumbnail = try await generateVideoThumbnail(from: fileURL)
                
            default:
                return
            }
            
            guard let thumbnailImage = thumbnail,
                  let thumbnailData = thumbnailImage.jpegData(compressionQuality: 0.7) else { return }
            
            try thumbnailData.write(to: thumbnailURL)
            print("✅ Thumbnail generated: \(filename)")
            
        } catch {
            print("❌ Failed to generate thumbnail for \(filename): \(error)")
        }
    }
    
    private func generateVideoThumbnail(from videoURL: URL) async throws -> UIImage? {
        let asset = AVAsset(url: videoURL)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        
        let time = CMTime(seconds: 1.0, preferredTimescale: 600)
        let cgImage = try await imageGenerator.image(at: time).image
        
        return UIImage(cgImage: cgImage)
    }
    
    // MARK: - iCloud Drive Integration
    
    private func syncToiCloud(fileURL: URL, type: MediaType) async {
        guard iCloudSyncEnabled, let iCloudDirectory = iCloudDirectory else { return }
        
        let iCloudTypeDirectory = iCloudDirectory.appendingPathComponent("PetCapsule").appendingPathComponent(type.directory)
        
        do {
            try FileManager.default.createDirectory(at: iCloudTypeDirectory, withIntermediateDirectories: true)
            
            let iCloudFileURL = iCloudTypeDirectory.appendingPathComponent(fileURL.lastPathComponent)
            
            if FileManager.default.fileExists(atPath: iCloudFileURL.path) {
                try FileManager.default.removeItem(at: iCloudFileURL)
            }
            
            try FileManager.default.copyItem(at: fileURL, to: iCloudFileURL)
            
            // Start iCloud upload
            try (iCloudFileURL as NSURL).setResourceValue(true, forKey: .hasHiddenExtensionKey)
            
            print("✅ File synced to iCloud: \(fileURL.lastPathComponent)")
        } catch {
            print("❌ Failed to sync to iCloud: \(error)")
        }
    }
    
    private func removeFromiCloud(fileURL: URL) async {
        guard iCloudSyncEnabled, let iCloudDirectory = iCloudDirectory else { return }
        
        let filename = fileURL.lastPathComponent
        let iCloudFileURL = iCloudDirectory.appendingPathComponent("PetCapsule").appendingPathComponent(filename)
        
        try? FileManager.default.removeItem(at: iCloudFileURL)
        print("✅ File removed from iCloud: \(filename)")
    }
    
    // MARK: - Storage Management
    
    private func calculateStorageUsage() async {
        let directories = [
            (petPhotosDirectory, "photos"),
            (petVideosDirectory, "videos"),
            (documentsStorageDirectory, "documents"),
            (cacheDirectory, "cache")
        ]
        
        var usage = StorageUsage()
        usage.iCloudSyncStatus = storageUsage.iCloudSyncStatus
        
        for (directory, type) in directories {
            let size = calculateDirectorySize(directory)
            let sizeMB = Double(size) / (1024 * 1024)
            
            switch type {
            case "photos":
                usage.photosSizeMB = sizeMB
            case "videos":
                usage.videosSizeMB = sizeMB
            case "documents":
                usage.documentsSizeMB = sizeMB
            case "cache":
                usage.cacheSize = sizeMB
            default:
                break
            }
        }
        
        usage.totalSizeMB = usage.photosSizeMB + usage.videosSizeMB + usage.documentsSizeMB
        
        await MainActor.run {
            self.storageUsage = usage
        }
    }
    
    private func calculateDirectorySize(_ directory: URL) -> Int64 {
        var size: Int64 = 0
        
        guard let enumerator = FileManager.default.enumerator(at: directory, includingPropertiesForKeys: [.fileSizeKey]) else {
            return size
        }
        
        for case let fileURL as URL in enumerator {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                size += Int64(resourceValues.fileSize ?? 0)
            } catch {
                continue
            }
        }
        
        return size
    }
    
    // MARK: - Migration Support
    
    func migrateFromLegacyStorage(legacyUrls: [String], type: MediaType, petId: String? = nil) async throws -> [String] {
        var localPaths: [String] = []
        
        for (index, urlString) in legacyUrls.enumerated() {
            guard let url = URL(string: urlString) else { continue }
            
            do {
                let (data, _) = try await URLSession.shared.data(from: url)
                let filename = "\(UUID().uuidString).\(type.fileExtensions.first ?? "dat")"
                
                let localPath = try await storeMedia(data: data, type: type, filename: filename, petId: petId)
                localPaths.append(localPath)
                
                // Update progress
                await MainActor.run {
                    self.uploadProgress["migration"] = Double(index + 1) / Double(legacyUrls.count)
                }
                
                print("✅ Migrated media: \(url.lastPathComponent)")
                
                // Add small delay to prevent overwhelming the system
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                
            } catch {
                print("❌ Failed to migrate media from \(urlString): \(error)")
            }
        }
        
        await MainActor.run {
            self.uploadProgress.removeValue(forKey: "migration")
        }
        
        return localPaths
    }
    
    // MARK: - Additional compatibility methods
    func migrateFromLegacyStorage(legacyUrls: [String], userId: UUID) async throws -> [String] {
        print("🍎 Migrating media from external storage for user: \(userId)")
        // Stub implementation - would download and convert URLs
        return []
    }
    
    func uploadMemoryVideo(_ videoData: Data, memoryId: UUID, userId: UUID) async -> String? {
        print("🍎 Uploading memory video with Apple services")
        let fileName = "memory_video_\(memoryId.uuidString).mov"
        let videoURL = documentsDirectory.appendingPathComponent("videos").appendingPathComponent(fileName)
        
        do {
            try FileManager.default.createDirectory(at: videoURL.deletingLastPathComponent(), withIntermediateDirectories: true)
            try videoData.write(to: videoURL)
            return videoURL.path
        } catch {
            print("❌ Failed to save video: \(error)")
            return nil
        }
    }
    
    func deleteMemoryVideo(url: String) async -> Bool {
        do {
            let fileURL = URL(fileURLWithPath: url)
            try FileManager.default.removeItem(at: fileURL)
            return true
        } catch {
            print("❌ Failed to delete memory video: \(error)")
            return false
        }
    }
    
    // MARK: - Memory Image Management
    func uploadMemoryImage(_ imageData: Data, memoryId: UUID, userId: UUID) async -> String? {
        print("🍎 Uploading memory image with Apple services")
        let fileName = "memory_\(memoryId.uuidString).jpg"
        let imageURL = documentsDirectory.appendingPathComponent("memories").appendingPathComponent(fileName)
        
        do {
            try FileManager.default.createDirectory(at: imageURL.deletingLastPathComponent(), withIntermediateDirectories: true)
            try imageData.write(to: imageURL)
            return imageURL.path
        } catch {
            print("❌ Failed to save memory image: \(error)")
            return nil
        }
    }
    
    // MARK: - Profile Image Management
    func uploadProfileImage(_ imageData: Data, userId: UUID) async -> String? {
        print("🍎 Uploading profile image with Apple services")
        let fileName = "profile_\(userId.uuidString).jpg"
        let imageURL = documentsDirectory.appendingPathComponent("profiles").appendingPathComponent(fileName)
        
        do {
            try FileManager.default.createDirectory(at: imageURL.deletingLastPathComponent(), withIntermediateDirectories: true)
            try imageData.write(to: imageURL)
            return imageURL.path
        } catch {
            print("❌ Failed to save profile image: \(error)")
            return nil
        }
    }
    
    // MARK: - Pet Image Management
    func uploadPetImage(_ imageData: Data, petId: UUID, userId: UUID) async -> String? {
        print("🍎 Uploading pet image with Apple services")
        let fileName = "pet_\(petId.uuidString).jpg"
        let imageURL = documentsDirectory.appendingPathComponent("pets").appendingPathComponent(fileName)
        
        do {
            try FileManager.default.createDirectory(at: imageURL.deletingLastPathComponent(), withIntermediateDirectories: true)
            try imageData.write(to: imageURL)
            return imageURL.path
        } catch {
            print("❌ Failed to save pet image: \(error)")
            return nil
        }
    }
    
    // MARK: - Delete Methods
    func deleteMemoryImage(url: String) async -> Bool {
        do {
            let fileURL = URL(fileURLWithPath: url)
            try FileManager.default.removeItem(at: fileURL)
            return true
        } catch {
            print("❌ Failed to delete memory image: \(error)")
            return false
        }
    }
    
    func deleteProfileImage(url: String) async -> Bool {
        do {
            let fileURL = URL(fileURLWithPath: url)
            try FileManager.default.removeItem(at: fileURL)
            return true
        } catch {
            print("❌ Failed to delete profile image: \(error)")
            return false
        }
    }
    
    func deletePetImage(url: String) async -> Bool {
        do {
            let fileURL = URL(fileURLWithPath: url)
            try FileManager.default.removeItem(at: fileURL)
            return true
        } catch {
            print("❌ Failed to delete pet image: \(error)")
            return false
        }
    }
    
    // MARK: - Helper Methods
    
    private func getDirectory(for type: MediaType) -> URL {
        switch type {
        case .photo:
            return petPhotosDirectory
        case .video:
            return petVideosDirectory
        case .document:
            return documentsStorageDirectory
        case .audio:
            return documentsStorageDirectory
        }
    }
    
    // MARK: - Error Types
    
    enum MediaStorageError: LocalizedError {
        case invalidData
        case storageNotSetup
        case fileNotFound
        case permissionDenied
        case iCloudUnavailable
        
        var errorDescription: String? {
            switch self {
            case .invalidData:
                return "Invalid media data provided"
            case .storageNotSetup:
                return "Media storage not properly setup"
            case .fileNotFound:
                return "Media file not found"
            case .permissionDenied:
                return "Permission denied for media access"
            case .iCloudUnavailable:
                return "iCloud Drive is not available"
            }
        }
    }
}

// MARK: - UIImage Extension

extension UIImage {
    func resizedForThumbnail(to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        draw(in: CGRect(origin: .zero, size: size))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resizedImage
    }
} 