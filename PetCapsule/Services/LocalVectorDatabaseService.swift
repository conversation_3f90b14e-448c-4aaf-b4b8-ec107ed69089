//
//  LocalVectorDatabaseService.swift
//  PetCapsule
//
//  🧠 Local Vector Database for Semantic Search
//  📊 Apple Intelligence-powered conversation analysis
//

import Foundation
import NaturalLanguage
import CoreML

@available(iOS 18.0, *)
@MainActor
class LocalVectorDatabaseService: ObservableObject {
    static let shared = LocalVectorDatabaseService()
    
    // MARK: - Data Structures
    
    struct VectorEntry: Codable {
        let id: UUID
        let content: String
        let embedding: [Float]
        let metadata: VectorMetadata
        let timestamp: Date
        let relevanceScore: Double
        
        init(content: String, embedding: [Float], metadata: VectorMetadata) {
            self.id = UUID()
            self.content = content
            self.embedding = embedding
            self.metadata = metadata
            self.timestamp = Date()
            self.relevanceScore = 0.0
        }
    }
    
    struct VectorMetadata: Codable {
        let agentId: UUID?
        let petId: String?
        let messageType: String // "user", "ai", "pet_context", "cross_agent"
        let contextType: String? // "health", "nutrition", "training", etc.
        let agentName: String?
    }
    
    struct SearchResult {
        let content: String
        let relevanceScore: Double
        let metadata: VectorMetadata
        let timestamp: Date
        let agentName: String?
    }
    
    // MARK: - Storage
    
    private var chatVectors: [VectorEntry] = []
    private var petContextVectors: [VectorEntry] = []
    private var crossAgentVectors: [VectorEntry] = []
    
    // MARK: - Apple Intelligence Integration
    
    private let nlProcessor = NLLanguageRecognizer()
    private let embeddingGenerator = NLEmbedding()
    
    private init() {
        loadStoredVectors()
    }
    
    // MARK: - Core Vector Operations
    
    func generateEmbedding(for text: String) -> [Float] {
        // Use Apple's Natural Language framework for local embedding generation
        let words = text.components(separatedBy: .whitespacesAndNewlines)
        var embedding: [Float] = []
        
        // Simple word-based embedding (in production, use Apple's advanced embedding models)
        for word in words.prefix(100) { // Limit to 100 words
            let normalizedWord = word.lowercased().trimmingCharacters(in: .punctuationCharacters)
            if !normalizedWord.isEmpty {
                // Generate simple hash-based embedding
                let hash = normalizedWord.hashValue
                let normalizedHash = Float(hash % 1000) / 1000.0
                embedding.append(normalizedHash)
            }
        }
        
        // Pad or truncate to fixed size
        while embedding.count < 128 {
            embedding.append(0.0)
        }
        
        return Array(embedding.prefix(128))
    }
    
    func cosineSimilarity(_ vector1: [Float], _ vector2: [Float]) -> Double {
        guard vector1.count == vector2.count && !vector1.isEmpty else { return 0.0 }
        
        let dotProduct = zip(vector1, vector2).map(*).reduce(0, +)
        let magnitude1 = sqrt(vector1.map { $0 * $0 }.reduce(0, +))
        let magnitude2 = sqrt(vector2.map { $0 * $0 }.reduce(0, +))
        
        guard magnitude1 > 0 && magnitude2 > 0 else { return 0.0 }
        
        return Double(dotProduct / (magnitude1 * magnitude2))
    }
    
    // MARK: - Chat Message Operations
    
    func addChatMessage(
        agentId: UUID,
        petId: String?,
        content: String,
        messageType: String,
        timestamp: Date
    ) {
        let embedding = generateEmbedding(for: content)
        let metadata = VectorMetadata(
            agentId: agentId,
            petId: petId,
            messageType: messageType,
            contextType: nil,
            agentName: getAgentName(for: agentId)
        )
        
        let vectorEntry = VectorEntry(content: content, embedding: embedding, metadata: metadata)
        chatVectors.append(vectorEntry)
        
        // Also add to cross-agent vectors for Pet Master access
        if messageType == "ai" {
            crossAgentVectors.append(vectorEntry)
        }
        
        saveVectors()
    }
    
    func searchChatHistory(
        query: String,
        agentId: UUID? = nil,
        petId: String? = nil,
        limit: Int = 10
    ) -> [SearchResult] {
        let queryEmbedding = generateEmbedding(for: query)
        var results: [SearchResult] = []
        
        for vector in chatVectors {
            // Apply filters
            if let agentId = agentId, vector.metadata.agentId != agentId {
                continue
            }
            if let petId = petId, vector.metadata.petId != petId {
                continue
            }
            
            let similarity = cosineSimilarity(queryEmbedding, vector.embedding)
            if similarity > 0.3 { // Minimum similarity threshold
                results.append(SearchResult(
                    content: vector.content,
                    relevanceScore: similarity,
                    metadata: vector.metadata,
                    timestamp: vector.timestamp,
                    agentName: vector.metadata.agentName
                ))
            }
        }
        
        // Sort by relevance and return top results
        return results
            .sorted { $0.relevanceScore > $1.relevanceScore }
            .prefix(limit)
            .map { $0 }
    }
    
    // MARK: - Pet Context Operations
    
    func addPetContext(
        petId: String,
        content: String,
        contextType: String
    ) {
        let embedding = generateEmbedding(for: content)
        let metadata = VectorMetadata(
            agentId: nil,
            petId: petId,
            messageType: "pet_context",
            contextType: contextType,
            agentName: nil
        )
        
        let vectorEntry = VectorEntry(content: content, embedding: embedding, metadata: metadata)
        petContextVectors.append(vectorEntry)
        
        saveVectors()
    }
    
    func searchPetContext(
        query: String,
        petId: String,
        limit: Int = 5
    ) -> [SearchResult] {
        let queryEmbedding = generateEmbedding(for: query)
        var results: [SearchResult] = []
        
        for vector in petContextVectors {
            if vector.metadata.petId == petId {
                let similarity = cosineSimilarity(queryEmbedding, vector.embedding)
                if similarity > 0.3 {
                    results.append(SearchResult(
                        content: vector.content,
                        relevanceScore: similarity,
                        metadata: vector.metadata,
                        timestamp: vector.timestamp,
                        agentName: vector.metadata.agentName
                    ))
                }
            }
        }
        
        return results
            .sorted { $0.relevanceScore > $1.relevanceScore }
            .prefix(limit)
            .map { $0 }
    }
    
    // MARK: - Cross-Agent Operations
    
    func searchCrossAgentHistory(
        query: String,
        petId: String? = nil,
        limit: Int = 10
    ) -> [SearchResult] {
        let queryEmbedding = generateEmbedding(for: query)
        var results: [SearchResult] = []
        
        for vector in crossAgentVectors {
            // Apply pet filter if specified
            if let petId = petId, vector.metadata.petId != petId {
                continue
            }
            
            let similarity = cosineSimilarity(queryEmbedding, vector.embedding)
            if similarity > 0.4 { // Higher threshold for cross-agent search
                results.append(SearchResult(
                    content: vector.content,
                    relevanceScore: similarity,
                    metadata: vector.metadata,
                    timestamp: vector.timestamp,
                    agentName: vector.metadata.agentName
                ))
            }
        }
        
        return results
            .sorted { $0.relevanceScore > $1.relevanceScore }
            .prefix(limit)
            .map { $0 }
    }
    
    // MARK: - Conversation Analysis
    
    func getConversationSummary(
        agentId: UUID,
        petId: String? = nil,
        limit: Int = 20
    ) -> String {
        let recentMessages = chatVectors
            .filter { $0.metadata.agentId == agentId }
            .filter { petId == nil || $0.metadata.petId == petId }
            .sorted { $0.timestamp > $1.timestamp }
            .prefix(limit)
        
        let messageContents = recentMessages.map { $0.content }
        return messageContents.joined(separator: "\n\n")
    }
    
    func getPetContextSummary(petId: String) -> String {
        let petContexts = petContextVectors
            .filter { $0.metadata.petId == petId }
            .sorted { $0.timestamp > $1.timestamp }
            .prefix(10)
        
        let contextContents = petContexts.map { $0.content }
        return contextContents.joined(separator: "\n\n")
    }
    
    // MARK: - Utility Methods
    
    private func getAgentName(for agentId: UUID) -> String? {
        // This would be populated from the AI agent service
        // For now, return a placeholder
        return "AI Agent"
    }
    
    private func saveVectors() {
        // Save to local storage (UserDefaults for simplicity, but could use Core Data)
        do {
            let encoder = JSONEncoder()
            let chatData = try encoder.encode(chatVectors)
            let petContextData = try encoder.encode(petContextVectors)
            let crossAgentData = try encoder.encode(crossAgentVectors)
            
            UserDefaults.standard.set(chatData, forKey: "PetCapsule_ChatVectors")
            UserDefaults.standard.set(petContextData, forKey: "PetCapsule_PetContextVectors")
            UserDefaults.standard.set(crossAgentData, forKey: "PetCapsule_CrossAgentVectors")
        } catch {
            print("Failed to save vectors: \(error)")
        }
    }
    
    private func loadStoredVectors() {
        do {
            let decoder = JSONDecoder()
            
            if let chatData = UserDefaults.standard.data(forKey: "PetCapsule_ChatVectors") {
                chatVectors = try decoder.decode([VectorEntry].self, from: chatData)
            }
            
            if let petContextData = UserDefaults.standard.data(forKey: "PetCapsule_PetContextVectors") {
                petContextVectors = try decoder.decode([VectorEntry].self, from: petContextData)
            }
            
            if let crossAgentData = UserDefaults.standard.data(forKey: "PetCapsule_CrossAgentVectors") {
                crossAgentVectors = try decoder.decode([VectorEntry].self, from: crossAgentData)
            }
        } catch {
            print("Failed to load vectors: \(error)")
        }
    }
    
    // MARK: - Cleanup
    
    func cleanupOldVectors(olderThan days: Int = 30) {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        
        chatVectors = chatVectors.filter { $0.timestamp > cutoffDate }
        petContextVectors = petContextVectors.filter { $0.timestamp > cutoffDate }
        crossAgentVectors = crossAgentVectors.filter { $0.timestamp > cutoffDate }
        
        saveVectors()
    }
} 