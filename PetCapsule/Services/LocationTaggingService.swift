//
//  LocationTaggingService.swift
//  PetCapsule
//
//  Enhanced location tagging service for walk memories with environmental context
//

import Foundation
import CoreLocation
import MapKit

@MainActor
class LocationTaggingService: ObservableObject {
    static let shared = LocationTaggingService()

    private let locationManager = CLLocationManager()
    @Published var currentLocation: CLLocation?
    private let mapService = AppleMapService.shared
    private let weatherService = AppleWeatherService.shared
    private let plannerService = AppleNativeDataService.shared

    @Published var isProcessingLocation = false
    @Published var lastTaggedLocation: EnhancedLocationTag?

    private init() {
        setupLocationManager()
    }

    private func setupLocationManager() {
        locationManager.requestWhenInUseAuthorization()
        locationManager.startUpdatingLocation()
    }
    
    // MARK: - Enhanced Location Tagging
    
    func tagLocationForWalkMemory(
        coordinate: CLLocationCoordinate2D,
        includeEnvironmentalData: Bool = true,
        includeNearbyPOIs: Bool = true
    ) async throws -> EnhancedLocationTag {
        
        isProcessingLocation = true
        defer { isProcessingLocation = false }
        
        // Get basic location information
        async let locationInfo = try mapService.getDetailedLocationInfo(coordinate: coordinate)
        
        // Get nearby points of interest
        async let nearbyPOIs = includeNearbyPOIs ? 
            try mapService.searchNearbyPointsOfInterest(coordinate: coordinate) : []
        
        // Get environmental data
        async let environmentalData = includeEnvironmentalData ? 
            try getEnvironmentalContext(coordinate: coordinate) : nil
        
        // Wait for all async operations
        let info = try await locationInfo
        let pois = try await nearbyPOIs
        let envData = try await environmentalData
        
        // Create enhanced location tag
        let enhancedTag = EnhancedLocationTag(
            coordinate: coordinate,
            locationInfo: info,
            nearbyPointsOfInterest: pois,
            environmentalContext: envData,
            tags: generateSmartTags(info: info, pois: pois, envData: envData),
            walkabilityScore: calculateEnhancedWalkabilityScore(info: info, pois: pois, envData: envData),
            safetyScore: calculateSafetyScore(info: info, pois: pois, envData: envData),
            petFriendlinessScore: calculatePetFriendlinessScore(pois: pois),
            timestamp: Date()
        )
        
        lastTaggedLocation = enhancedTag
        return enhancedTag
    }
    
    func tagCurrentLocationForWalkMemory() async throws -> EnhancedLocationTag? {
        guard let currentLocation = currentLocation else {
            throw LocationTaggingError.locationNotAvailable
        }
        
        return try await tagLocationForWalkMemory(coordinate: currentLocation.coordinate)
    }
    
    func batchTagLocationsForRoute(coordinates: [CLLocationCoordinate2D]) async throws -> [EnhancedLocationTag] {
        var tags: [EnhancedLocationTag] = []
        
        // Sample key points along the route (start, middle, end)
        let keyPoints = sampleKeyPoints(from: coordinates)
        
        for coordinate in keyPoints {
            do {
                let tag = try await tagLocationForWalkMemory(
                    coordinate: coordinate,
                    includeEnvironmentalData: false, // Skip env data for batch to improve performance
                    includeNearbyPOIs: true
                )
                tags.append(tag)
            } catch {
                print("❌ Failed to tag location \(coordinate): \(error)")
            }
        }
        
        return tags
    }
    
    // MARK: - Environmental Context
    
    private func getEnvironmentalContext(coordinate: CLLocationCoordinate2D) async throws -> EnvironmentalContext? {
        async let weather = try? weatherService.getCurrentWeather(for: coordinate)
        async let airQuality = try? weatherService.getAirQuality(for: coordinate)
        async let pollen = try? weatherService.getPollenData(for: coordinate)
        
        guard let weatherData = await weather, let airQualityData = await airQuality, let pollenData = await pollen else {
            return nil
        }
        
        return EnvironmentalContext(
            temperature: Double(weatherData.temperature),
            humidity: Double(weatherData.humidity),
            airQuality: airQualityData.index,
            pollenCount: pollenData.treeIndex + pollenData.grassIndex + pollenData.weedIndex,
            weatherCondition: weatherData.condition,
            windSpeed: weatherData.windSpeed,
            uvIndex: nil, // UV Index not available in current weather data
            timestamp: Date(),
            location: nil,
            coordinates: coordinate
        )
    }
    
    // MARK: - Smart Tag Generation
    
    private func generateSmartTags(
        info: DetailedLocationInfo,
        pois: [PointOfInterest],
        envData: EnvironmentalContext?
    ) -> [String] {
        var tags: [String] = []
        
        // Location-based tags
        if let locality = info.locality {
            tags.append(locality)
        }
        
        if let subLocality = info.subLocality {
            tags.append(subLocality)
        }
        
        // POI-based tags
        let poiTags = generatePOITags(from: pois)
        tags.append(contentsOf: poiTags)
        
        // Environmental tags
        if let envData = envData {
            let envTags = generateEnvironmentalTags(from: envData)
            tags.append(contentsOf: envTags)
        }
        
        // Time-based tags
        let timeTags = generateTimeBasedTags()
        tags.append(contentsOf: timeTags)
        
        return Array(Set(tags)) // Remove duplicates
    }
    
    private func generatePOITags(from pois: [PointOfInterest]) -> [String] {
        var tags: [String] = []
        
        let categoryGroups = Dictionary(grouping: pois) { $0.category }
        
        for (category, poisInCategory) in categoryGroups {
            switch category {
            case let cat where cat.contains("Park"):
                tags.append("Park")
                if poisInCategory.count > 2 {
                    tags.append("Park District")
                }
            case let cat where cat.contains("Restaurant"):
                tags.append("Dining")
                if poisInCategory.count > 3 {
                    tags.append("Restaurant District")
                }
            case let cat where cat.contains("Store"):
                tags.append("Shopping")
            case let cat where cat.contains("Hospital") || cat.contains("Medical"):
                tags.append("Medical")
            case let cat where cat.contains("School"):
                tags.append("Educational")
            case let cat where cat.contains("Transit"):
                tags.append("Transit Hub")
            default:
                break
            }
        }
        
        return tags
    }
    
    private func generateEnvironmentalTags(from envData: EnvironmentalContext) -> [String] {
        var tags: [String] = []
        
        // Weather tags
        if let condition = envData.weatherCondition {
            tags.append(condition)
        }
        
        if let temperature = envData.temperature {
            if temperature > 75 {
                tags.append("Warm Weather")
            } else if temperature < 45 {
                tags.append("Cold Weather")
            }
        }
        
        // Air quality tags
        if let airQuality = envData.airQuality {
            switch airQuality {
            case 0...50:
                tags.append("Good Air Quality")
            case 51...100:
                tags.append("Moderate Air Quality")
            case 101...150:
                tags.append("Unhealthy for Sensitive Groups")
            default:
                tags.append("Poor Air Quality")
            }
        }
        
        // Pollen tags
        if let pollenCount = envData.pollenCount {
            if pollenCount > 15 {
                tags.append("High Pollen")
            } else if pollenCount > 8 {
                tags.append("Moderate Pollen")
            } else {
                tags.append("Low Pollen")
            }
        }
        
        return tags
    }
    
    private func generateTimeBasedTags() -> [String] {
        let hour = Calendar.current.component(.hour, from: Date())
        let dayOfWeek = Calendar.current.component(.weekday, from: Date())
        
        var tags: [String] = []
        
        // Time of day
        switch hour {
        case 5...11:
            tags.append("Morning Walk")
        case 12...17:
            tags.append("Afternoon Walk")
        case 18...21:
            tags.append("Evening Walk")
        default:
            tags.append("Night Walk")
        }
        
        // Day of week
        if dayOfWeek == 1 || dayOfWeek == 7 {
            tags.append("Weekend")
        } else {
            tags.append("Weekday")
        }
        
        return tags
    }
    
    // MARK: - Scoring Logic
    
    private func calculateEnhancedWalkabilityScore(
        info: DetailedLocationInfo,
        pois: [PointOfInterest],
        envData: EnvironmentalContext?
    ) -> Double {
        var score = 100.0
        
        // Environmental score
        if let envData = envData {
            // This is a placeholder. A proper implementation would use the EnvironmentalScoringService.
            if let temp = envData.temperature, let aq = envData.airQuality {
                var envScore = 1.0
                if temp > 85 || temp < 32 { envScore -= 0.3 }
                if aq > 100 { envScore -= 0.3 }
                score -= (1.0 - envScore) * 30
            }
        }
        
        // POI score
        let petFriendlyPOIs = pois.filter { poi in
            poi.category.lowercased().contains("park") || 
            poi.category.lowercased().contains("trail") ||
            poi.category.lowercased().contains("dog")
        }.count
        score += Double(petFriendlyPOIs * 5)
        
        // Location score
        // Pedestrian-friendly assessment based on available info
        if let locality = info.locality, locality.lowercased().contains("downtown") ||
           locality.lowercased().contains("plaza") ||
           locality.lowercased().contains("park") {
            score += 10
        }
        
        return max(0, min(100, score))
    }
    
    private func calculateSafetyScore(
        info: DetailedLocationInfo,
        pois: [PointOfInterest],
        envData: EnvironmentalContext?
    ) -> Double {
        var score = 100.0
        
        // Weather safety
        if let envData = envData {
            if let condition = envData.weatherCondition, condition.lowercased().contains("storm") {
                score -= 40
            }
            if let windSpeed = envData.windSpeed, windSpeed > 25 {
                score -= 20
            }
        }
        
        // Time safety
        let hour = Calendar.current.component(.hour, from: Date())
        if hour > 22 || hour < 5 {
            score -= 25 // Night time
        }
        
        // Add more safety factors like crime rate, etc.
        
        return max(0, min(100, score))
    }
    
    private func calculatePetFriendlinessScore(pois: [PointOfInterest]) -> Double {
        let petFriendlyCount = pois.filter { poi in
            poi.category.lowercased().contains("park") || 
            poi.category.lowercased().contains("trail") ||
            poi.category.lowercased().contains("dog")
        }.count
        let totalPois = max(1, pois.count)
        
        return Double(petFriendlyCount) / Double(totalPois) * 100
    }
    
    // MARK: - Helper Methods
    
    private func sampleKeyPoints(from coordinates: [CLLocationCoordinate2D]) -> [CLLocationCoordinate2D] {
        guard coordinates.count > 2 else {
            return coordinates
        }
        
        return [coordinates.first!, coordinates[coordinates.count / 2], coordinates.last!]
    }
}

// MARK: - Data Models

struct EnhancedLocationTag: Identifiable {
    let id = UUID()
    let coordinate: CLLocationCoordinate2D
    let locationInfo: DetailedLocationInfo
    let nearbyPointsOfInterest: [PointOfInterest]
    let environmentalContext: EnvironmentalContext?
    let tags: [String]
    let walkabilityScore: Double
    let safetyScore: Double
    let petFriendlinessScore: Double
    let timestamp: Date
}

// MARK: - Error Types
