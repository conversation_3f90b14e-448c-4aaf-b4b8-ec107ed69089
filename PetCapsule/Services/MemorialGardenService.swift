//
//  MemorialGardenService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//
import Foundation
import SwiftUI
// Type aliases to resolve ambiguity
//typealias ActivityType = SharedActivityType
class MemorialGardenService: ObservableObject {
    static let shared = MemorialGardenService()
    @Published var memorialGardens: [MemorialGarden] = []
    @Published var communityMemorials: [MemorialGarden] = []
    @Published var tributes: [MemorialTribute] = []
    private init() {}
    // MARK: - Memorial Garden Management
    func createMemorialGarden(
        for pet: Pet,
        message: String,
        theme: MemorialTheme,
        isPublic: Bool
    ) async {
        let memorial = MemorialGarden(
            id: UUID(),
            petId: UUID(uuidString: pet.id) ?? UUID(),
            petName: pet.name,
            petImageURL: pet.profileImageURL,
            dateOfPassing: Date(), // Use current date as default since Pet model doesn't have dateOfPassing
            memorialMessage: message,
            theme: theme,
            isPublic: isPublic,
            createdBy: "current-user", // Use default since Pet model doesn't have ownerID
            tributeCount: 0,
            visitCount: 0,
            createdAt: Date()
        )
        await MainActor.run {
            self.memorialGardens.append(memorial)
        }
    }
    func loadMemorialGardens() async {
        // Load user's memorial gardens
        let userMemorials = await fetchUserMemorialGardens()
        let communityMemorials = await fetchCommunityMemorialGardens()
        await MainActor.run {
            self.memorialGardens = userMemorials
            self.communityMemorials = communityMemorials
        }
    }
    func addTribute(to memorialId: UUID, message: String, authorId: String, isAnonymous: Bool = false) async {
        let tribute = MemorialTribute(
            id: UUID(),
            memorialId: memorialId,
            authorId: authorId,
            message: message,
            isAnonymous: isAnonymous,
            flowers: [],
            createdAt: Date()
        )
        await MainActor.run {
            self.tributes.append(tribute)
            // Update tribute count
            if let index = self.memorialGardens.firstIndex(where: { $0.id == memorialId }) {
                self.memorialGardens[index].tributeCount += 1
            }
            if let index = self.communityMemorials.firstIndex(where: { $0.id == memorialId }) {
                self.communityMemorials[index].tributeCount += 1
            }
        }
    }
    func leaveFlowers(at memorialId: UUID, flowerType: VirtualFlower, authorId: String) async {
        let _ = VirtualFlower(
            id: UUID(),
            type: flowerType.type,
            color: flowerType.color,
            message: flowerType.message,
            leftBy: authorId,
            createdAt: Date()
        )
        // Add flower to memorial
        await MainActor.run {
            // In real implementation, update the memorial's flower collection
        }
    }
    func visitMemorial(_ memorialId: UUID) async {
        await MainActor.run {
            // Update visit count
            if let index = self.memorialGardens.firstIndex(where: { $0.id == memorialId }) {
                self.memorialGardens[index].visitCount += 1
            }
            if let index = self.communityMemorials.firstIndex(where: { $0.id == memorialId }) {
                self.communityMemorials[index].visitCount += 1
            }
        }
    }
    // MARK: - Memorial Sharing
    func shareMemorial(_ memorial: MemorialGarden, platform: SharingPlatform) async {
        let _ = generateShareContent(for: memorial)
        switch platform {
        case .social:
            // Share to social media
            break
        case .email:
            // Share via email
            break
        case .message:
            // Share via messages
            break
        case .link:
            // Generate shareable link
            break
        }
    }
    // MARK: - Memorial Analytics
    func getMemorialAnalytics(for memorialId: UUID) async -> MemorialAnalytics {
        // Fetch analytics data
        return MemorialAnalytics(
            memorialId: memorialId,
            totalVisits: 0,
            totalTributes: 0,
            totalFlowers: 0,
            recentActivity: [],
            popularTimes: [],
            geographicReach: []
        )
    }
    // MARK: - Private Helper Methods
    private func fetchUserMemorialGardens() async -> [MemorialGarden] {
        return generateMockMemorialGardens()
    }
    private func fetchCommunityMemorialGardens() async -> [MemorialGarden] {
        return generateMockCommunityMemorials()
    }
    private func generateMockMemorialGardens() -> [MemorialGarden] {
        return [
            MemorialGarden(
                id: UUID(),
                petId: UUID(),
                petName: "Buddy",
                petImageURL: nil,
                dateOfPassing: Date().addingTimeInterval(-86400 * 30), // 30 days ago
                memorialMessage: "Buddy was the most loyal companion anyone could ask for. His gentle spirit and playful nature brought joy to everyone he met. He loved long walks in the park and always greeted us with the biggest smile. Forever in our hearts. 🐕❤️",
                theme: .peaceful,
                isPublic: true,
                createdBy: "current-user",
                tributeCount: 15,
                visitCount: 127,
                createdAt: Date().addingTimeInterval(-86400 * 25)
            ),
            MemorialGarden(
                id: UUID(),
                petId: UUID(),
                petName: "Luna",
                petImageURL: nil,
                dateOfPassing: Date().addingTimeInterval(-86400 * 90), // 90 days ago
                memorialMessage: "Our beautiful Luna crossed the rainbow bridge after 12 wonderful years. She was a gentle soul who loved sunbathing and chasing butterflies. Her purrs could heal any bad day. 🐱🌈",
                theme: .rainbow,
                isPublic: false,
                createdBy: "current-user",
                tributeCount: 8,
                visitCount: 45,
                createdAt: Date().addingTimeInterval(-86400 * 85)
            )
        ]
    }
    private func generateMockCommunityMemorials() -> [MemorialGarden] {
        return [
            MemorialGarden(
                id: UUID(),
                petId: UUID(),
                petName: "Max",
                petImageURL: nil,
                dateOfPassing: Date().addingTimeInterval(-86400 * 7), // 7 days ago
                memorialMessage: "Max was a hero dog who saved our family from a fire. His bravery and love will never be forgotten.",
                theme: .heroic,
                isPublic: true,
                createdBy: "other-user",
                tributeCount: 234,
                visitCount: 1567,
                createdAt: Date().addingTimeInterval(-86400 * 5)
            ),
            MemorialGarden(
                id: UUID(),
                petId: UUID(),
                petName: "Whiskers",
                petImageURL: nil,
                dateOfPassing: Date().addingTimeInterval(-86400 * 14), // 14 days ago
                memorialMessage: "Whiskers brought 15 years of joy and laughter to our home. His mischievous spirit lives on.",
                theme: .joyful,
                isPublic: true,
                createdBy: "community-user",
                tributeCount: 89,
                visitCount: 456,
                createdAt: Date().addingTimeInterval(-86400 * 12)
            )
        ]
    }
    private func generateShareContent(for memorial: MemorialGarden) -> String {
        return """
        In loving memory of \(memorial.petName) 🌈
        \(memorial.memorialMessage)
        Visit their memorial garden to leave a tribute.
        #PetMemorial #RainbowBridge #PetLove
        """
    }
}
// MARK: - Data Models
struct MemorialGarden: Identifiable {
    let id: UUID
    let petId: UUID
    let petName: String
    let petImageURL: String?
    let dateOfPassing: Date?
    let memorialMessage: String
    let theme: MemorialTheme
    let isPublic: Bool
    let createdBy: String
    var tributeCount: Int
    var visitCount: Int
    let createdAt: Date
}
enum MemorialTheme: String, CaseIterable {
    case peaceful = "peaceful"
    case rainbow = "rainbow"
    case garden = "garden"
    case heroic = "heroic"
    case joyful = "joyful"
    case elegant = "elegant"
    var displayName: String {
        switch self {
        case .peaceful:
            return "Peaceful"
        case .rainbow:
            return "Rainbow Bridge"
        case .garden:
            return "Garden"
        case .heroic:
            return "Heroic"
        case .joyful:
            return "Joyful"
        case .elegant:
            return "Elegant"
        }
    }
    var colors: [Color] {
        switch self {
        case .peaceful:
            return [Color.blue.opacity(0.3), Color.white, Color.green.opacity(0.3)]
        case .rainbow:
            return [Color.red, Color.orange, Color.yellow, Color.green, Color.blue, Color.purple]
        case .garden:
            return [Color.green.opacity(0.4), Color.pink.opacity(0.3), Color.white]
        case .heroic:
            return [Color.yellow, Color.orange, Color.red.opacity(0.3)]
        case .joyful:
            return [Color.yellow, Color.orange, Color.pink]
        case .elegant:
            return [Color.purple.opacity(0.3), Color.white, Color.gray]
        }
    }
    var emoji: String {
        switch self {
        case .peaceful:
            return "☁️"
        case .rainbow:
            return "🌈"
        case .garden:
            return "🌸"
        case .heroic:
            return "⭐"
        case .joyful:
            return "🌻"
        case .elegant:
            return "💫"
        }
    }
}
struct MemorialTribute: Identifiable {
    let id: UUID
    let memorialId: UUID
    let authorId: String
    let message: String
    let isAnonymous: Bool
    var flowers: [VirtualFlower]
    let createdAt: Date
}
struct VirtualFlower: Identifiable {
    let id: UUID
    let type: FlowerType
    let color: FlowerColor
    let message: String?
    let leftBy: String
    let createdAt: Date
}
enum FlowerType: String, CaseIterable {
    case rose = "rose"
    case lily = "lily"
    case tulip = "tulip"
    case sunflower = "sunflower"
    case daisy = "daisy"
    case carnation = "carnation"
    var emoji: String {
        switch self {
        case .rose:
            return "🌹"
        case .lily:
            return "🌺"
        case .tulip:
            return "🌷"
        case .sunflower:
            return "🌻"
        case .daisy:
            return "🌼"
        case .carnation:
            return "🌸"
        }
    }
}
enum FlowerColor: String, CaseIterable {
    case red = "red"
    case pink = "pink"
    case white = "white"
    case yellow = "yellow"
    case purple = "purple"
    case blue = "blue"
}
enum SharingPlatform: String, CaseIterable {
    case social = "social"
    case email = "email"
    case message = "message"
    case link = "link"
}
struct MemorialAnalytics {
    let memorialId: UUID
    let totalVisits: Int
    let totalTributes: Int
    let totalFlowers: Int
    let recentActivity: [MemorialActivity]
    let popularTimes: [PopularTime]
    let geographicReach: [GeographicData]
}
struct MemorialActivity {
    let type: ActivityType
    let timestamp: Date
    let authorId: String?
}
// ActivityType is now defined in SharedTypes.swift as SharedActivityType
struct PopularTime {
    let hour: Int
    let visitCount: Int
}
struct GeographicData {
    let country: String
    let visitCount: Int
}
