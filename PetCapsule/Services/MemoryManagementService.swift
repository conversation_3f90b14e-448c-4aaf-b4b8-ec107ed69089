//
//  MemoryManagementService.swift
//  PetCapsule
//
//  Memory management and leak prevention
//

import Foundation
import UIKit
import Combine
import OSLog
import Darwin.Mach
import Darwin.Mach.mach_types

@MainActor
class MemoryManagementService: ObservableObject {
    static let shared = MemoryManagementService()
    
    // MARK: - Published Properties
    @Published var currentMemoryUsage: Int64 = 0
    @Published var isMemoryPressureHigh = false
    @Published var memoryWarningLevel: MemoryWarningLevel = .normal
    
    // MARK: - Private Properties
    private var memoryObserver: NSObjectProtocol?
    private var memoryTimer: Timer?
    private let logger = Logger(subsystem: "PetCapsule", category: "MemoryManagement")
    
    // Memory thresholds (in MB) - Updated for more realistic iOS app usage
    private let warningThreshold: Int64 = 400  // Increased from 300
    private let criticalThreshold: Int64 = 600  // Increased from 500  
    private let emergencyThreshold: Int64 = 800  // Increased from 700
    
    // Cleanup registrations - with size limits to prevent memory bloat
    private var imageCache: [String: UIImage] = [:] {
        didSet {
            // Limit cache size to prevent memory issues
            if imageCache.count > 50 {
                let keysToRemove = Array(imageCache.keys.prefix(10))
                for key in keysToRemove {
                    imageCache.removeValue(forKey: key)
                }
            }
        }
    }
    private var cleanupCallbacks: [String: () -> Void] = [:]
    private var temporaryData: [String: Any] = [:] {
        didSet {
            // Limit temporary data size
            if temporaryData.count > 100 {
                let keysToRemove = Array(temporaryData.keys.prefix(20))
                for key in keysToRemove {
                    temporaryData.removeValue(forKey: key)
                }
            }
        }
    }
    
    private init() {
        setupMemoryMonitoring()
        setupMemoryPressureObserver()
    }

    // MARK: - Cache Management

    func clearAllCaches() {
        imageCache.removeAll()
        temporaryData.removeAll()

        // Execute cleanup callbacks
        for callback in cleanupCallbacks.values {
            callback()
        }
        cleanupCallbacks.removeAll()

        print("🧹 Cleared all memory caches")
    }
    
    // MARK: - Memory Monitoring
    
    private func setupMemoryMonitoring() {
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 15.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.updateMemoryUsage()
            }
        }
        
        // Register timer with performance manager
        if let timer = memoryTimer {
            PerformanceOptimizationManager.shared.registerTimer(timer)
        }
    }
    
    private func setupMemoryPressureObserver() {
        memoryObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleMemoryWarning()
            }
        }
    }
    
    private func updateMemoryUsage() async {
        let memoryUsage = getMemoryUsage()
        currentMemoryUsage = memoryUsage
        
        let previousLevel = memoryWarningLevel
        memoryWarningLevel = determineWarningLevel(for: memoryUsage)
        
        // If warning level increased, trigger cleanup
        if memoryWarningLevel.rawValue > previousLevel.rawValue {
            await handleMemoryPressureChange()
        }
        
        // Update high pressure flag
        isMemoryPressureHigh = memoryWarningLevel == .critical || memoryWarningLevel == .emergency
        
        logger.info("Memory usage: \(String(memoryUsage))MB, Level: \(String(describing: self.memoryWarningLevel))")
    }
    
    private func getMemoryUsage() -> Int64 {
        // Use simplified memory detection via ProcessInfo
        let physicalMemory: UInt64 = ProcessInfo.processInfo.physicalMemory
        
        // Use a rough estimate: 25% of total physical memory for the app
        let estimatedUsageMB: Int64 = Int64(physicalMemory / 4 / 1024 / 1024) // Convert to MB
        
        return estimatedUsageMB
    }
    
    private func determineWarningLevel(for memoryUsage: Int64) -> MemoryWarningLevel {
        switch memoryUsage {
        case 0..<warningThreshold:
            return .normal
        case warningThreshold..<criticalThreshold:
            return .warning
        case criticalThreshold..<emergencyThreshold:
            return .critical
        default:
            return .emergency
        }
    }
    
    // MARK: - Memory Pressure Handling
    
    private func handleMemoryWarning() async {
        logger.warning("🚨 Memory warning received")
        memoryWarningLevel = .critical
        isMemoryPressureHigh = true
        await performAggressiveCleanup()
    }
    
    private func handleMemoryPressureChange() async {
        switch memoryWarningLevel {
        case .normal:
            break
        case .warning:
            await performBasicCleanup()
        case .critical:
            await performIntermediateCleanup()
        case .emergency:
            await performAggressiveCleanup()
        }
    }
    
    private func performBasicCleanup() async {
        logger.info("Performing basic memory cleanup")
        
        // Clear temporary data
        let keysToRemove = temporaryData.keys.prefix(temporaryData.count / 4)
        for key in keysToRemove {
            temporaryData.removeValue(forKey: key)
        }
        
        // Clear old image cache entries
        let imageCacheCount = imageCache.count
        if imageCacheCount > 50 {
            let keysToRemove = imageCache.keys.prefix(imageCacheCount / 4)
            for key in keysToRemove {
                imageCache.removeValue(forKey: key)
            }
        }
        
        // Trigger URLCache cleanup
        URLCache.shared.removeAllCachedResponses()
        
        logger.info("Basic cleanup completed")
    }
    
    private func performIntermediateCleanup() async {
        logger.warning("Performing intermediate memory cleanup")
        
        await performBasicCleanup()
        
        // Clear half of image cache
        let imageCacheCount = imageCache.count
        if imageCacheCount > 20 {
            let keysToRemove = imageCache.keys.prefix(imageCacheCount / 2)
            for key in keysToRemove {
                imageCache.removeValue(forKey: key)
            }
        }
        
        // Clear half of temporary data
        let tempDataCount = temporaryData.count
        if tempDataCount > 10 {
            let keysToRemove = temporaryData.keys.prefix(tempDataCount / 2)
            for key in keysToRemove {
                temporaryData.removeValue(forKey: key)
            }
        }
        
        // Run registered cleanup callbacks
        for (_, callback) in cleanupCallbacks.prefix(cleanupCallbacks.count / 2) {
            callback()
        }
        
        // Post notification for other components to clean up
        NotificationCenter.default.post(name: .memoryPressureIntermediate, object: nil)
        
        logger.warning("Intermediate cleanup completed")
    }
    
    private func performAggressiveCleanup() async {
        logger.error("🚨 Performing aggressive memory cleanup")
        
        await performIntermediateCleanup()
        
        // Clear all image cache
        imageCache.removeAll()
        
        // Clear all temporary data
        temporaryData.removeAll()
        
        // Run all cleanup callbacks
        for (_, callback) in cleanupCallbacks {
            callback()
        }
        
        // Clear URLSession caches
        URLSession.shared.configuration.urlCache?.removeAllCachedResponses()
        
        // Force garbage collection hint
        if #available(iOS 15.0, *) {
            Task.detached(priority: .background) {
                // Give system hint to reclaim memory
                for _ in 0..<3 {
                    autoreleasepool {
                        // Create and immediately release objects to trigger cleanup
                        let _ = Array(0..<1000).map { "\($0)" }
                    }
                    try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                }
            }
        }
        
        // Post critical memory pressure notification
        NotificationCenter.default.post(name: .memoryPressureCritical, object: nil)
        
        logger.error("Aggressive cleanup completed")
    }
    
    // MARK: - Memory Management API
    
    func registerCleanupCallback(id: String, callback: @escaping () -> Void) {
        cleanupCallbacks[id] = callback
    }
    
    func unregisterCleanupCallback(id: String) {
        cleanupCallbacks.removeValue(forKey: id)
    }
    
    func cacheImage(_ image: UIImage, forKey key: String) {
        // Only cache if memory pressure is not high
        guard !isMemoryPressureHigh else { return }
        
        // Limit cache size
        if imageCache.count >= 100 {
            // Remove oldest entries
            let keysToRemove = imageCache.keys.prefix(20)
            for keyToRemove in keysToRemove {
                imageCache.removeValue(forKey: keyToRemove)
            }
        }
        
        imageCache[key] = image
    }
    
    func getCachedImage(forKey key: String) -> UIImage? {
        return imageCache[key]
    }
    
    func storeTemporaryData(_ data: Any, forKey key: String) {
        // Only store if memory pressure is not high
        guard !isMemoryPressureHigh else { return }
        
        // Limit temporary data size
        if temporaryData.count >= 50 {
            let keysToRemove = temporaryData.keys.prefix(10)
            for keyToRemove in keysToRemove {
                temporaryData.removeValue(forKey: keyToRemove)
            }
        }
        
        temporaryData[key] = data
    }
    
    func getTemporaryData(forKey key: String) -> Any? {
        return temporaryData[key]
    }
    
    func clearTemporaryData(forKey key: String) {
        temporaryData.removeValue(forKey: key)
    }
    
    // MARK: - Memory Statistics
    
    func getMemoryStatistics() -> MemoryStatistics {
        let totalMemory = ProcessInfo.processInfo.physicalMemory
        let currentUsage = getMemoryUsage()
        
        return MemoryStatistics(
            currentUsage: currentUsage,
            totalMemory: Int64(totalMemory / 1024 / 1024), // Convert to MB
            imageCacheSize: imageCache.count,
            temporaryDataSize: temporaryData.count,
            warningLevel: memoryWarningLevel
        )
    }
    
    deinit {
        memoryTimer?.invalidate()
        if let observer = memoryObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }
}

// MARK: - Supporting Types

enum MemoryWarningLevel: Int, CaseIterable {
    case normal = 0
    case warning = 1
    case critical = 2
    case emergency = 3
    
    var displayName: String {
        switch self {
        case .normal: return "Normal"
        case .warning: return "Warning"
        case .critical: return "Critical"
        case .emergency: return "Emergency"
        }
    }
    
    var color: UIColor {
        switch self {
        case .normal: return .systemGreen
        case .warning: return .systemYellow
        case .critical: return .systemOrange
        case .emergency: return .systemRed
        }
    }
}

struct MemoryStatistics {
    let currentUsage: Int64      // MB
    let totalMemory: Int64       // MB
    let imageCacheSize: Int      // Number of cached images
    let temporaryDataSize: Int   // Number of temporary data entries
    let warningLevel: MemoryWarningLevel
    
    var usagePercentage: Double {
        return Double(currentUsage) / Double(totalMemory) * 100.0
    }
    
    var formattedUsage: String {
        return "\(currentUsage)MB / \(totalMemory)MB (\(String(format: "%.1f", usagePercentage))%)"
    }
}

// MARK: - Mach Types

private struct mach_task_basic_info {
    var virtual_size: mach_vm_size_t = 0
    var resident_size: mach_vm_size_t = 0
    var resident_size_max: mach_vm_size_t = 0
    var user_time: time_value_t = time_value_t()
    var system_time: time_value_t = time_value_t()
    var policy: policy_t = 0
    var suspend_count: integer_t = 0
}

private struct time_value_t {
    var seconds: integer_t = 0
    var microseconds: integer_t = 0
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let memoryPressureIntermediate = Notification.Name("memoryPressureIntermediate")
} 