//
//  NotificationActionHandler.swift
//  PetCapsule
//
//  Handles notification actions and navigation routing
//

import Foundation
import SwiftUI

@MainActor
class NotificationActionHandler: ObservableObject {
    static let shared = NotificationActionHandler()
    
    @Published var shouldNavigateToEnvironmentalAlerts = false
    @Published var shouldNavigateToHealthMonitoring = false
    @Published var shouldNavigateToNotificationSettings = false
    @Published var shouldNavigateToWeatherForecast = false
    @Published var shouldNavigateToWalkPlanner = false
    @Published var shouldNavigateToCommunityEvents = false
    @Published var shouldShowVetCall = false
    
    // Store contextual data for navigation
    @Published var selectedAlertId: String?
    @Published var selectedPetId: String?
    @Published var selectedEventId: String?
    
    private init() {
        setupNotificationObservers()
    }
    
    // MARK: - Notification Observers
    
    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            forName: .navigateToEnvironmentalAlerts,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handleEnvironmentalAlertsNavigation(notification.object as? String)
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .navigateToPetHealth,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handlePetHealthNavigation(notification.object as? String)
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .navigateToNotificationSettings,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleNotificationSettingsNavigation()
            }
        }

        NotificationCenter.default.addObserver(
            forName: .navigateToWeatherForecast,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleWeatherForecastNavigation()
            }
        }

        NotificationCenter.default.addObserver(
            forName: .navigateToWalkPlanner,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleWalkPlannerNavigation()
            }
        }

        NotificationCenter.default.addObserver(
            forName: .navigateToCommunityEvents,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handleCommunityEventsNavigation(notification.object as? String)
            }
        }

        NotificationCenter.default.addObserver(
            forName: .initiateVetCall,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handleVetCallAction(notification.object as? String)
            }
        }
    }
    
    // MARK: - Navigation Handlers
    
    private func handleEnvironmentalAlertsNavigation(_ alertId: String?) {
        selectedAlertId = alertId
        shouldNavigateToEnvironmentalAlerts = true
        
        // Reset after a brief delay to allow navigation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.shouldNavigateToEnvironmentalAlerts = false
        }
    }
    
    private func handlePetHealthNavigation(_ petId: String?) {
        selectedPetId = petId
        shouldNavigateToHealthMonitoring = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.shouldNavigateToHealthMonitoring = false
        }
    }
    
    private func handleNotificationSettingsNavigation() {
        shouldNavigateToNotificationSettings = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.shouldNavigateToNotificationSettings = false
        }
    }
    
    private func handleWeatherForecastNavigation() {
        shouldNavigateToWeatherForecast = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.shouldNavigateToWeatherForecast = false
        }
    }
    
    private func handleWalkPlannerNavigation() {
        shouldNavigateToWalkPlanner = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.shouldNavigateToWalkPlanner = false
        }
    }
    
    private func handleCommunityEventsNavigation(_ eventId: String?) {
        selectedEventId = eventId
        shouldNavigateToCommunityEvents = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.shouldNavigateToCommunityEvents = false
        }
    }
    
    private func handleVetCallAction(_ petId: String?) {
        selectedPetId = petId
        shouldShowVetCall = true
    }
    
    // MARK: - Public Methods
    
    func dismissVetCall() {
        shouldShowVetCall = false
        selectedPetId = nil
    }
    
    func clearSelectedContext() {
        selectedAlertId = nil
        selectedPetId = nil
        selectedEventId = nil
    }
    
    // MARK: - Deep Link Navigation
    
    func handleDeepLink(_ url: URL) {
        let path = url.path
        let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
        
        switch path {
        case "/environmental-alerts":
            if let alertId = components?.queryItems?.first(where: { $0.name == "alertId" })?.value {
                handleEnvironmentalAlertsNavigation(alertId)
            } else {
                handleEnvironmentalAlertsNavigation(nil)
            }
            
        case "/pet-health":
            if let petId = components?.queryItems?.first(where: { $0.name == "petId" })?.value {
                handlePetHealthNavigation(petId)
            } else {
                handlePetHealthNavigation(nil)
            }
            
        case "/notification-settings":
            handleNotificationSettingsNavigation()
            
        case "/weather":
            handleWeatherForecastNavigation()
            
        case "/walk-planner":
            handleWalkPlannerNavigation()
            
        case "/community-events":
            if let eventId = components?.queryItems?.first(where: { $0.name == "eventId" })?.value {
                handleCommunityEventsNavigation(eventId)
            } else {
                handleCommunityEventsNavigation(nil)
            }
            
        default:
            print("⚠️ Unknown deep link path: \(path)")
        }
    }
} 