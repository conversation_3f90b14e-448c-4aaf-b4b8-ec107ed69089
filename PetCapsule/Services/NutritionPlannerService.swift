//
//  NutritionPlannerService.swift
//  PetCapsule
//
//  Comprehensive nutrition planning service for pets
//

import Foundation
import SwiftUI

@MainActor
class NutritionPlannerService: ObservableObject {
    static let shared = NutritionPlannerService()
    
    @Published var nutritionPlans: [NutritionPlan] = []
    @Published var mealSchedules: [MealSchedule] = []
    @Published var nutritionGoals: [NutritionGoal] = []
    @Published var foodDatabase: [FoodItem] = []
    @Published var isLoading = false
    
    private let petDataManager = PetDataManager.shared
    
    private init() {
        loadSampleData()
    }
    
    // MARK: - Nutrition Plan Generation
    
    func generateNutritionPlan(for pet: Pet) async -> NutritionPlan {
        isLoading = true
        defer { isLoading = false }
        
        // Calculate daily caloric needs based on pet characteristics
        let dailyCalories = calculateDailyCalories(for: pet)
        
        // Generate meal recommendations
        let meals = generateMealRecommendations(for: pet, dailyCalories: dailyCalories)
        
        // Create nutrition goals
        let goals = generateNutritionGoals(for: pet)
        
        let plan = NutritionPlan(
            id: UUID().uuidString,
            petId: pet.id,
            petName: pet.name,
            dailyCalories: dailyCalories,
            meals: meals,
            goals: goals,
            createdAt: Date(),
            lastUpdated: Date()
        )
        
        nutritionPlans.append(plan)
        return plan
    }
    
    func calculateDailyCalories(for pet: Pet) -> Int {
        // Use actual weight from comprehensive pet data, fallback to estimation
        let actualWeight = pet.weight ?? estimateWeight(for: pet)
        
        // Basic metabolic rate calculation using actual weight
        let baseCalories: Double
        
        switch pet.species.lowercased() {
        case "dog":
            // Dog calorie calculation: 70 * (weight in kg)^0.75
            baseCalories = 70 * pow(actualWeight, 0.75)
        case "cat":
            // Cat calorie calculation: 70 * (weight in kg)^0.67
            baseCalories = 70 * pow(actualWeight, 0.67)
        default:
            baseCalories = 200 // Default for other pets
        }
        
        // Comprehensive adjustments based on pet data (age is in months)
        let ageMultiplier = pet.age < 12 ? 2.0 : (pet.age > 84 ? 0.8 : 1.0) // < 12 months = < 1 year, > 84 months = > 7 years
        
        // Use comprehensive exercise data for activity multiplier
        let activityMultiplier: Double
        if let exerciseMinutes = pet.exerciseMinutesDaily {
            activityMultiplier = exerciseMinutes < 30 ? 1.0 : 
                                exerciseMinutes < 60 ? 1.2 : 1.4
        } else {
            activityMultiplier = pet.activityLevel.lowercased().contains("high") ? 1.4 :
                                pet.activityLevel.lowercased().contains("low") ? 1.0 : 1.2
        }
        
        // Health condition adjustments
        var healthMultiplier = 1.0
        if !pet.chronicConditions.isEmpty {
            healthMultiplier = 0.9 // Slightly reduce for chronic conditions
        }
        if pet.medications.filter({ $0.isActive }).count > 0 {
            healthMultiplier *= 0.95 // Account for medication effects
        }
        
        // Spay/neuter adjustment
        let spayNeuterMultiplier = pet.isSpayedNeutered ? 0.9 : 1.0
        
        let totalCalories = baseCalories * ageMultiplier * activityMultiplier * healthMultiplier * spayNeuterMultiplier
        
        print("🥗 Calculated calories for \(pet.name): \(Int(totalCalories)) (weight: \(actualWeight)kg, activity: \(activityMultiplier)x)")
        
        return Int(totalCalories)
    }
    
    private func estimateWeight(for pet: Pet) -> Double {
        // Estimate weight based on breed and age
        switch (pet.breed ?? "").lowercased() {
        case let breed where breed.contains("chihuahua"):
            return 2.5
        case let breed where breed.contains("golden retriever"):
            return 30.0
        case let breed where breed.contains("german shepherd"):
            return 35.0
        case let breed where breed.contains("labrador"):
            return 32.0
        case let breed where breed.contains("persian"):
            return 4.5
        case let breed where breed.contains("maine coon"):
            return 6.0
        case let breed where breed.contains("siamese"):
            return 4.0
        default:
            return pet.species.lowercased() == "dog" ? 25.0 : 4.5
        }
    }
    
    private func generateMealRecommendations(for pet: Pet, dailyCalories: Int) -> [MealRecommendation] {
        // Use existing feeding schedule if available, otherwise create default
        let existingSchedule = pet.feedingSchedule
        let mealsPerDay = !existingSchedule.isEmpty ? existingSchedule.count : (pet.age < 1 ? 3 : 2)
        let caloriesPerMeal = dailyCalories / mealsPerDay
        
        var meals: [MealRecommendation] = []
        
        if !existingSchedule.isEmpty {
            // Use existing feeding schedule with enhanced recommendations
            for (index, feedingTime) in existingSchedule.enumerated() {
                let meal = MealRecommendation(
                    id: feedingTime.id,
                    name: feedingTime.foodType,
                    time: formatTimeFromDate(feedingTime.time),
                    calories: caloriesPerMeal,
                    foodItems: generateFoodItems(for: pet, targetCalories: caloriesPerMeal, currentFood: pet.currentFood),
                    notes: enhanceMealNotes(for: pet, existingNotes: feedingTime.notes, mealIndex: index)
                )
                meals.append(meal)
            }
        } else {
            // Generate default schedule with comprehensive considerations
            for i in 0..<mealsPerDay {
                let mealTime = i == 0 ? "8:00 AM" : (i == 1 ? "6:00 PM" : "12:00 PM")
                
                let meal = MealRecommendation(
                    id: UUID().uuidString,
                    name: i == 0 ? "Breakfast" : (i == 1 ? "Dinner" : "Lunch"),
                    time: mealTime,
                    calories: caloriesPerMeal,
                    foodItems: generateFoodItems(for: pet, targetCalories: caloriesPerMeal, currentFood: pet.currentFood),
                    notes: generateComprehensiveMealNotes(for: pet, mealIndex: i)
                )
                
                meals.append(meal)
            }
        }
        
        return meals
    }
    
    private func generateFoodItems(for pet: Pet, targetCalories: Int, currentFood: String?) -> [FoodRecommendation] {
        // Use current food if available, otherwise recommend premium option
        let foodName = currentFood ?? "Premium \(pet.species.capitalized) Kibble"
        
        let primaryFood = FoodRecommendation(
            foodId: "food-\(pet.species)-\(UUID().uuidString.prefix(8))",
            name: foodName,
            amount: "\(calculateFoodAmount(calories: Int(Double(targetCalories) * 0.8), foodName: foodName)) cups",
            calories: Int(Double(targetCalories) * 0.8),
            macros: generateMacronutrients(for: pet, foodType: foodName)
        )
        
        // Check for dietary restrictions when recommending treats
        let treatName = generateSafeTreatName(for: pet)
        let treat = FoodRecommendation(
            foodId: "treat-\(pet.species)-\(UUID().uuidString.prefix(8))",
            name: treatName,
            amount: "2-3 pieces",
            calories: Int(Double(targetCalories) * 0.2),
            macros: Macronutrients(protein: 15, fat: 8, carbs: 20, fiber: 3)
        )
        
        return [primaryFood, treat]
    }
    
    private func calculateFoodAmount(calories: Int, foodName: String) -> String {
        // Different foods have different caloric densities
        let caloriesPerCup: Double
        if foodName.lowercased().contains("wet") || foodName.lowercased().contains("can") {
            caloriesPerCup = 250 // Wet food typically lower calorie density
        } else if foodName.lowercased().contains("premium") || foodName.lowercased().contains("high") {
            caloriesPerCup = 400 // Premium dry food higher density
        } else {
            caloriesPerCup = 350 // Standard dry food
        }
        
        let cups = Double(calories) / caloriesPerCup
        return String(format: "%.1f", cups)
    }
    
    private func generateMacronutrients(for pet: Pet, foodType: String) -> Macronutrients {
        // Adjust macros based on pet characteristics and health conditions
        var protein = 25.0
        var fat = 15.0
        var carbs = 45.0
        var fiber = 5.0
        
        // Age-based adjustments
        if pet.age < 1 {
            protein = 28.0 // Growing pets need more protein
            fat = 18.0
        } else if pet.age > 7 {
            protein = 22.0 // Senior pets may need less protein
            fat = 12.0
            fiber = 8.0 // More fiber for digestion
        }
        
        // Health condition adjustments
        if pet.chronicConditions.contains(where: { $0.lowercased().contains("kidney") }) {
            protein = 18.0 // Lower protein for kidney issues
        }
        
        if pet.chronicConditions.contains(where: { $0.lowercased().contains("diabetes") }) {
            carbs = 25.0 // Lower carbs for diabetes
            fiber = 12.0 // Higher fiber for blood sugar control
        }
        
        return Macronutrients(protein: Int(protein), fat: Int(fat), carbs: Int(carbs), fiber: Int(fiber))
    }
    
    private func generateSafeTreatName(for pet: Pet) -> String {
        // Consider dietary restrictions and allergies
        let allergies = pet.allergies.map { $0.lowercased() }
        let restrictions = pet.dietaryRestrictions.map { $0.lowercased() }
        
        if allergies.contains("chicken") || restrictions.contains("poultry") {
            return "Salmon Training Treats"
        } else if allergies.contains("beef") || restrictions.contains("beef") {
            return "Turkey Training Treats"
        } else if allergies.contains("grain") || restrictions.contains("grain-free") {
            return "Grain-Free Training Treats"
        } else {
            return "Healthy Training Treats"
        }
    }
    
    private func generateMealNotes(for pet: Pet, mealIndex: Int) -> String {
        let notes = [
            "Ensure fresh water is always available",
            "Monitor eating pace - slow feeding bowls recommended",
            "Check for food allergies or sensitivities",
            "Maintain consistent feeding schedule"
        ]
        
        return notes[mealIndex % notes.count]
    }
    
    private func enhanceMealNotes(for pet: Pet, existingNotes: String?, mealIndex: Int) -> String {
        var notes = existingNotes ?? ""
        
        // Add comprehensive guidance based on pet data
        let additionalNotes = generateComprehensiveMealNotes(for: pet, mealIndex: mealIndex)
        
        if !notes.isEmpty && !additionalNotes.isEmpty {
            notes += "\n\n" + additionalNotes
        } else if additionalNotes.isEmpty {
            notes = additionalNotes
        }
        
        return notes
    }
    
    private func generateComprehensiveMealNotes(for pet: Pet, mealIndex: Int) -> String {
        var notes: [String] = []
        
        // Water intake guidance
        if let waterIntake = pet.waterIntakeML {
            notes.append("💧 Daily water target: \(waterIntake)ml")
        }
        
        // Dietary restriction warnings
        if !pet.dietaryRestrictions.isEmpty {
            notes.append("⚠️ Dietary restrictions: \(pet.dietaryRestrictions.joined(separator: ", "))")
        }
        
        // Allergy warnings
        if !pet.allergies.isEmpty {
            notes.append("🚨 Allergies: \(pet.allergies.joined(separator: ", "))")
        }
        
        // Medication timing
        let activeMeds = pet.medications.filter { $0.isActive }
        if !activeMeds.isEmpty {
            let medsWithFood = activeMeds.filter { $0.instructions?.lowercased().contains("food") == true }
            if !medsWithFood.isEmpty {
                notes.append("💊 Give with food: \(medsWithFood.map { $0.name }.joined(separator: ", "))")
            }
        }
        
        // Weight management
        if let weight = pet.weight {
            let healthScore = petDataManager.getHealthScore(for: pet)
            if healthScore < 0.8 {
                notes.append("⚖️ Monitor portion sizes - current weight: \(weight)kg")
            }
        }
        
        // Age-specific guidance
        if pet.age < 1 {
            notes.append("🐾 Puppy/Kitten: Smaller, more frequent meals")
        } else if pet.age > 7 {
            notes.append("👴 Senior: Softer foods may be easier to digest")
        }
        
        return notes.joined(separator: "\n")
    }
    
    private func generateNutritionGoals(for pet: Pet) -> [NutritionGoal] {
        return [
            NutritionGoal(
                id: UUID().uuidString,
                type: .weight,
                target: "Maintain healthy weight",
                currentValue: 0,
                targetValue: 100,
                unit: "% of ideal weight",
                priority: .high
            ),
            NutritionGoal(
                id: UUID().uuidString,
                type: .hydration,
                target: "Adequate daily water intake",
                currentValue: 0,
                targetValue: Int(estimateWeight(for: pet) * 50), // 50ml per kg
                unit: "ml",
                priority: .medium
            ),
            NutritionGoal(
                id: UUID().uuidString,
                type: .activity,
                target: "Post-meal activity monitoring",
                currentValue: 0,
                targetValue: 30,
                unit: "minutes",
                priority: .medium
            )
        ]
    }
    
    // MARK: - Meal Scheduling
    
    func createMealSchedule(for pet: Pet, plan: NutritionPlan) -> MealSchedule {
        let schedule = MealSchedule(
            id: UUID().uuidString,
            petId: pet.id,
            planId: plan.id,
            meals: plan.meals.map { meal in
                ScheduledMeal(
                    id: UUID().uuidString,
                    mealId: meal.id,
                    scheduledTime: parseTime(meal.time),
                    isCompleted: false,
                    actualTime: nil,
                    notes: nil
                )
            },
            startDate: Date(),
            isActive: true
        )
        
        mealSchedules.append(schedule)
        return schedule
    }
    
    private func parseTime(_ timeString: String) -> Date {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        
        if let time = formatter.date(from: timeString) {
            let calendar = Calendar.current
            let now = Date()
            let timeComponents = calendar.dateComponents([.hour, .minute], from: time)
            return calendar.date(bySettingHour: timeComponents.hour ?? 8,
                               minute: timeComponents.minute ?? 0,
                               second: 0,
                               of: now) ?? now
        }
        
        return Date()
    }
    
    // MARK: - Food Database
    
    private func loadSampleData() {
        // Sample food database
        foodDatabase = [
            FoodItem(
                id: "premium-dog-kibble",
                name: "Premium Adult Dog Kibble",
                brand: "PetNutrition Pro",
                category: .dryFood,
                species: .dog,
                ageGroup: .adult,
                caloriesPerCup: 350,
                macros: Macronutrients(protein: 26, fat: 16, carbs: 45, fiber: 4),
                ingredients: ["Chicken", "Brown Rice", "Sweet Potato", "Peas"],
                allergens: [],
                price: 45.99,
                rating: 4.5
            ),
            FoodItem(
                id: "premium-cat-kibble",
                name: "Premium Adult Cat Kibble",
                brand: "FelineNutrition Pro",
                category: .dryFood,
                species: .cat,
                ageGroup: .adult,
                caloriesPerCup: 400,
                macros: Macronutrients(protein: 32, fat: 18, carbs: 35, fiber: 3),
                ingredients: ["Salmon", "Chicken Meal", "Rice", "Cranberries"],
                allergens: ["Fish"],
                price: 52.99,
                rating: 4.7
            ),
            FoodItem(
                id: "wet-dog-food",
                name: "Premium Wet Dog Food",
                brand: "PetNutrition Pro",
                category: .wetFood,
                species: .dog,
                ageGroup: .adult,
                caloriesPerCup: 250,
                macros: Macronutrients(protein: 28, fat: 12, carbs: 50, fiber: 2),
                ingredients: ["Beef", "Vegetables", "Gravy"],
                allergens: ["Beef"],
                price: 35.99,
                rating: 4.3
            )
        ]
    }
    
    // MARK: - Progress Tracking
    
    func updateMealCompletion(scheduleId: String, mealId: String, completed: Bool, actualTime: Date? = nil, notes: String? = nil) {
        if let scheduleIndex = mealSchedules.firstIndex(where: { $0.id == scheduleId }),
           let mealIndex = mealSchedules[scheduleIndex].meals.firstIndex(where: { $0.id == mealId }) {
            
            mealSchedules[scheduleIndex].meals[mealIndex].isCompleted = completed
            mealSchedules[scheduleIndex].meals[mealIndex].actualTime = actualTime
            mealSchedules[scheduleIndex].meals[mealIndex].notes = notes
        }
    }
    
    func getNutritionProgress(for petId: String) -> NutritionProgress {
        let _ = nutritionPlans.filter { $0.petId == petId } // May be used for future logic
        let activeSchedules = mealSchedules.filter { $0.petId == petId && $0.isActive }
        
        let totalMeals = activeSchedules.flatMap { $0.meals }.count
        let completedMeals = activeSchedules.flatMap { $0.meals }.filter { $0.isCompleted }.count
        
        let completionRate = totalMeals > 0 ? Double(completedMeals) / Double(totalMeals) : 0.0
        
        return NutritionProgress(
            completionRate: completionRate,
            totalMeals: totalMeals,
            completedMeals: completedMeals,
            currentStreak: calculateCurrentStreak(for: petId),
            weeklyGoalProgress: calculateWeeklyProgress(for: petId)
        )
    }
    
    private func calculateCurrentStreak(for petId: String) -> Int {
        // Calculate consecutive days of completed meal schedules
        return 5 // Placeholder
    }
    
    private func calculateWeeklyProgress(for petId: String) -> Double {
        // Calculate progress towards weekly nutrition goals
        return 0.75 // Placeholder
    }
    
    // MARK: - Helper Methods
    
    private func formatTimeFromDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}
