//
//  PasskeyAuthenticationService.swift
//  PetCapsule
//
//  iOS 18 Passkeys integration for secure authentication
//  Replaces traditional passwords with biometric authentication
//

import Foundation
import AuthenticationServices
import SwiftUI

@available(iOS 18.0, *)
class PasskeyAuthenticationService: NSObject, ObservableObject {
    static let shared = PasskeyAuthenticationService()
    
    @Published var isPasskeyAvailable = false
    @Published var isAuthenticated = false
    @Published var currentUser: PasskeyUser?
    
    private let domain = "petcapsule.app"
    private let relyingPartyIdentifier = "petcapsule.app"
    
    override init() {
        super.init()
        checkPasskeyAvailability()
    }
    
    // MARK: - Availability Check
    
    private func checkPasskeyAvailability() {
        Task { @MainActor in
            // Check if passkeys are available (simplified check for iOS 18)
            self.isPasskeyAvailable = ProcessInfo.processInfo.operatingSystemVersion.majorVersion >= 16
        }
    }
    
    // MARK: - Passkey Registration
    
    func registerPasskey(for userID: String, userName: String, displayName: String) async throws -> Bool {
        guard isPasskeyAvailable else {
            throw PasskeyError.notAvailable
        }
        
        let challenge = generateChallenge()
        let userIDData = userID.data(using: .utf8)!
        
        let platformProvider = ASAuthorizationPlatformPublicKeyCredentialProvider(relyingPartyIdentifier: relyingPartyIdentifier)
        
        let registrationRequest = platformProvider.createCredentialRegistrationRequest(
            challenge: challenge,
            name: userName,
            userID: userIDData
        )
        
        registrationRequest.displayName = displayName
        registrationRequest.userVerificationPreference = .required
        
        let authController = ASAuthorizationController(authorizationRequests: [registrationRequest])
        authController.delegate = self
        authController.presentationContextProvider = self
        
        return await withCheckedContinuation { continuation in
            self.registrationContinuation = continuation
            authController.performRequests()
        }
    }
    
    // MARK: - Passkey Authentication
    
    func authenticateWithPasskey() async throws -> PasskeyUser {
        guard isPasskeyAvailable else {
            throw PasskeyError.notAvailable
        }
        
        let challenge = generateChallenge()
        
        let platformProvider = ASAuthorizationPlatformPublicKeyCredentialProvider(relyingPartyIdentifier: relyingPartyIdentifier)
        
        let assertionRequest = platformProvider.createCredentialAssertionRequest(challenge: challenge)
        assertionRequest.userVerificationPreference = .required
        
        let authController = ASAuthorizationController(authorizationRequests: [assertionRequest])
        authController.delegate = self
        authController.presentationContextProvider = self
        
        return await withCheckedContinuation { continuation in
            self.authenticationContinuation = continuation
            authController.performRequests()
        }
    }
    
    // MARK: - Automatic Passkey Upgrade
    
    func offerPasskeyUpgrade(after passwordSignIn: Bool) async {
        guard isPasskeyAvailable && passwordSignIn else { return }
        
        // Show passkey upgrade prompt
        await MainActor.run {
            self.showPasskeyUpgradePrompt()
        }
    }
    
    @MainActor
    private func showPasskeyUpgradePrompt() {
        // This would show a native iOS prompt for passkey upgrade
        // For now, we'll simulate the upgrade flow
        NotificationCenter.default.post(
            name: .passkeyUpgradeAvailable,
            object: nil
        )
    }
    
    // MARK: - Vault Security with Passkeys
    
    func authenticateForVaultAccess() async throws -> Bool {
        guard isAuthenticated else {
            let user = try await authenticateWithPasskey()
            await MainActor.run {
                self.currentUser = user
                self.isAuthenticated = true
            }
            return true
        }
        return true
    }
    
    func createSecureVaultPasskey(vaultID: String) async throws -> Bool {
        let vaultUserID = "vault_\(vaultID)"
        let vaultUserName = "PetCapsule Vault"
        let vaultDisplayName = "Secure Pet Vault Access"
        
        return try await registerPasskey(
            for: vaultUserID,
            userName: vaultUserName,
            displayName: vaultDisplayName
        )
    }
    
    // MARK: - Biometric Authentication Fallback
    
    func authenticateWithBiometrics() async throws -> Bool {
        let context = LAContext()
        var error: NSError?
        
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            throw PasskeyError.biometricsNotAvailable
        }
        
        let reason = "Authenticate to access your pet's secure information"
        
        return try await withCheckedThrowingContinuation { continuation in
            context.evaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics,
                localizedReason: reason
            ) { success, error in
                if success {
                    continuation.resume(returning: true)
                } else {
                    continuation.resume(throwing: error ?? PasskeyError.authenticationFailed)
                }
            }
        }
    }
    
    // MARK: - Utility Methods
    
    private func generateChallenge() -> Data {
        var bytes = [UInt8](repeating: 0, count: 32)
        _ = SecRandomCopyBytes(kSecRandomDefault, bytes.count, &bytes)
        return Data(bytes)
    }
    
    func signOut() {
        Task { @MainActor in
            self.isAuthenticated = false
            self.currentUser = nil
        }
    }
    
    // MARK: - Continuation Properties
    
    private var registrationContinuation: CheckedContinuation<Bool, Never>?
    private var authenticationContinuation: CheckedContinuation<PasskeyUser, Never>?
}

// MARK: - ASAuthorizationControllerDelegate

@available(iOS 18.0, *)
extension PasskeyAuthenticationService: ASAuthorizationControllerDelegate {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        
        if let platformCredential = authorization.credential as? ASAuthorizationPlatformPublicKeyCredentialRegistration {
            // Handle registration success
            let user = PasskeyUser(
                id: String(data: platformCredential.rawClientDataJSON, encoding: .utf8) ?? "",
                name: "PetCapsule User",
                credentialID: platformCredential.credentialID.base64EncodedString()
            )
            
            Task { @MainActor in
                self.currentUser = user
                self.isAuthenticated = true
            }
            
            registrationContinuation?.resume(returning: true)
            registrationContinuation = nil
            
        } else if let platformCredential = authorization.credential as? ASAuthorizationPlatformPublicKeyCredentialAssertion {
            // Handle authentication success
            let user = PasskeyUser(
                id: String(data: platformCredential.rawClientDataJSON, encoding: .utf8) ?? "",
                name: "PetCapsule User", // Would be retrieved from server
                credentialID: platformCredential.credentialID.base64EncodedString()
            )
            
            Task { @MainActor in
                self.currentUser = user
                self.isAuthenticated = true
            }
            
            authenticationContinuation?.resume(returning: user)
            authenticationContinuation = nil
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        print("Passkey authentication failed: \(error.localizedDescription)")
        
        registrationContinuation?.resume(returning: false)
        registrationContinuation = nil
        
        if let continuation = authenticationContinuation {
            // Create a fallback user for error cases
            let fallbackUser = PasskeyUser(id: "", name: "", credentialID: "")
            continuation.resume(returning: fallbackUser)
            authenticationContinuation = nil
        }
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding

@available(iOS 18.0, *)
extension PasskeyAuthenticationService: ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return ASPresentationAnchor()
        }
        return window
    }
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct PasskeyUser {
    let id: String
    let name: String
    let credentialID: String
}

@available(iOS 18.0, *)
enum PasskeyError: LocalizedError {
    case notAvailable
    case authenticationFailed
    case biometricsNotAvailable
    case userCancelled
    
    var errorDescription: String? {
        switch self {
        case .notAvailable:
            return "Passkeys are not available on this device"
        case .authenticationFailed:
            return "Authentication failed"
        case .biometricsNotAvailable:
            return "Biometric authentication is not available"
        case .userCancelled:
            return "User cancelled authentication"
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let passkeyUpgradeAvailable = Notification.Name("passkeyUpgradeAvailable")
    static let passkeyAuthenticationSuccess = Notification.Name("passkeyAuthenticationSuccess")
    static let passkeyAuthenticationFailed = Notification.Name("passkeyAuthenticationFailed")
}

// MARK: - SwiftUI Integration

@available(iOS 18.0, *)
struct PasskeyAuthenticationView: View {
    @StateObject private var passkeyService = PasskeyAuthenticationService.shared
    @State private var showingUpgrade = false
    
    var body: some View {
        VStack(spacing: 20) {
            if passkeyService.isPasskeyAvailable {
                // Passkey Authentication Button
                Button(action: {
                    Task {
                        do {
                            _ = try await passkeyService.authenticateWithPasskey()
                        } catch {
                            print("Passkey authentication failed: \(error)")
                        }
                    }
                }) {
                    HStack {
                        Image(systemName: "faceid")
                            .font(.title2)
                        
                        Text("Sign in with Passkey")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(.blue)
                    .foregroundStyle(.white)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                
                // Passkey Registration Button
                if !passkeyService.isAuthenticated {
                    Button(action: {
                        Task {
                            do {
                                _ = try await passkeyService.registerPasskey(
                                    for: UUID().uuidString,
                                    userName: "petcapsule_user",
                                    displayName: "PetCapsule User"
                                )
                            } catch {
                                print("Passkey registration failed: \(error)")
                            }
                        }
                    }) {
                        HStack {
                            Image(systemName: "plus.circle")
                                .font(.title2)
                            
                            Text("Create Passkey")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.green)
                        .foregroundStyle(.white)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                }
            } else {
                // Fallback to biometric authentication
                Button(action: {
                    Task {
                        do {
                            _ = try await passkeyService.authenticateWithBiometrics()
                        } catch {
                            print("Biometric authentication failed: \(error)")
                        }
                    }
                }) {
                    HStack {
                        Image(systemName: "touchid")
                            .font(.title2)
                        
                        Text("Authenticate with Biometrics")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(.orange)
                    .foregroundStyle(.white)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
            }
        }
        .padding()
        .onReceive(NotificationCenter.default.publisher(for: .passkeyUpgradeAvailable)) { _ in
            showingUpgrade = true
        }
        .alert("Upgrade to Passkey", isPresented: $showingUpgrade) {
            Button("Create Passkey") {
                Task {
                    do {
                        _ = try await passkeyService.registerPasskey(
                            for: UUID().uuidString,
                            userName: "petcapsule_user",
                            displayName: "PetCapsule User"
                        )
                    } catch {
                        print("Passkey upgrade failed: \(error)")
                    }
                }
            }
            
            Button("Not Now", role: .cancel) { }
        } message: {
            Text("Create a passkey for faster, more secure sign-ins. Your passkey will be saved to your iCloud Keychain.")
        }
    }
}

// MARK: - Local Authentication Import

import LocalAuthentication
