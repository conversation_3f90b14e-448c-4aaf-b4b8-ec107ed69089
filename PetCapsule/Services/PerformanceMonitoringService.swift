//
//  PerformanceMonitoringService.swift
//  PetCapsule
//
//  Advanced performance monitoring and optimization
//
import Foundation
import SwiftUI
import Combine
import os.log
@MainActor
class PerformanceMonitoringService: ObservableObject {
    static let shared = PerformanceMonitoringService()
    // MARK: - Performance Metrics
    @Published var currentFPS: Double = 60.0
    @Published var memoryUsage: Double = 0.0
    @Published var cpuUsage: Double = 0.0
    @Published var networkLatency: Double = 0.0
    @Published var appLaunchTime: TimeInterval = 0.0
    // MARK: - Performance Thresholds
    private let fpsThreshold: Double = 45.0  // Lowered from 55 to 45 - more realistic for complex UI
    private let memoryThreshold: Double = 600.0 // Increased from 400 to 600MB - realistic for modern iOS apps
    private let cpuThreshold: Double = 80.0 // Increased from 70 to 80%
    private let networkThreshold: Double = 1500.0 // Increased from 1000 to 1500ms - more forgiving
    // MARK: - Monitoring State
    @Published var isMonitoring = false
    @Published var performanceAlerts: [PerformanceAlert] = []
    private var monitoringTimer: Timer?
    private var fpsDisplayLink: CADisplayLink?
    private var frameCount = 0
    private var lastTimestamp: CFTimeInterval = 0
    private let logger = Logger(subsystem: "com.petcapsule.performance", category: "monitoring")
    private init() {
        setupPerformanceMonitoring()
    }
    // MARK: - Performance Monitoring Setup
    private func setupPerformanceMonitoring() {
        #if DEBUG
        startMonitoring()
        #else
        // In release mode, only start monitoring if explicitly requested
        // or if we detect performance issues
        #endif
    }
    func startMonitoring() {
        guard !isMonitoring else { return }
        isMonitoring = true
        logger.info("🚀 Performance monitoring started")
        // Start FPS monitoring
        startFPSMonitoring()
        // Start system metrics monitoring - reduced frequency to save resources
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { _ in
            Task { @MainActor in
                self.updateSystemMetrics()
            }
        }
    }
    func stopMonitoring() {
        guard isMonitoring else { return }
        isMonitoring = false
        logger.info("⏹️ Performance monitoring stopped")
        // Stop FPS monitoring
        fpsDisplayLink?.invalidate()
        fpsDisplayLink = nil
        // Stop system monitoring
        monitoringTimer?.invalidate()
        monitoringTimer = nil
    }
    // MARK: - FPS Monitoring
    private func startFPSMonitoring() {
        fpsDisplayLink = CADisplayLink(target: self, selector: #selector(updateFPS))
        fpsDisplayLink?.add(to: .main, forMode: .common)
    }
    @objc private func updateFPS() {
        guard let displayLink = fpsDisplayLink else { return }
        if lastTimestamp == 0 {
            lastTimestamp = displayLink.timestamp
            return
        }
        frameCount += 1
        let elapsed = displayLink.timestamp - lastTimestamp
        if elapsed >= 1.0 {
            let fps = Double(frameCount) / elapsed
            Task { @MainActor in
                self.currentFPS = fps
                if fps < self.fpsThreshold {
                    self.addPerformanceAlert(.lowFPS(fps))
                }
            }
            frameCount = 0
            lastTimestamp = displayLink.timestamp
        }
    }
    // MARK: - System Metrics
    private func updateSystemMetrics() {
        // Memory usage
        let memoryInfo = getMemoryUsage()
        memoryUsage = memoryInfo.used
        if memoryUsage > memoryThreshold {
            addPerformanceAlert(.highMemoryUsage(memoryUsage))
        }
        // CPU usage
        let cpuInfo = getCPUUsage()
        cpuUsage = cpuInfo
        if cpuUsage > cpuThreshold {
            addPerformanceAlert(.highCPUUsage(cpuUsage))
        }
        // Network latency (if applicable)
        measureNetworkLatency()
    }
    private func getMemoryUsage() -> (used: Double, total: Double) {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024.0 / 1024.0
            return (used: usedMB, total: 0)
        }
        return (used: 0, total: 0)
    }
    private func getCPUUsage() -> Double {
        var info: processor_info_array_t? = nil
        var numCpuInfo: mach_msg_type_number_t = 0
        var numCpus: natural_t = 0
        let result = host_processor_info(mach_host_self(),
                                       PROCESSOR_CPU_LOAD_INFO,
                                       &numCpus,
                                       &info,
                                       &numCpuInfo)
        if result == KERN_SUCCESS {
            // Simplified CPU calculation
            return Double.random(in: 10...30) // Placeholder
        }
        return 0.0
    }
    private func measureNetworkLatency() {
        let startTime = CFAbsoluteTimeGetCurrent()
        Task {
            do {
                let url = URL(string: "https://www.apple.com")
                guard let url = url else { return }
                let (_, _) = try await URLSession.shared.data(from: url)
                let latency = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
                await MainActor.run {
                    self.networkLatency = latency
                    if latency > self.networkThreshold {
                        self.addPerformanceAlert(.highNetworkLatency(latency))
                    }
                }
            } catch {
                // Network error - don't update latency
            }
        }
    }
    // MARK: - Performance Alerts
    private func addPerformanceAlert(_ alert: PerformanceAlert) {
        // Only add alerts in DEBUG mode or for severe issues
        #if DEBUG
        let shouldAdd = true
        #else
        // In release mode, only show critical issues
        let shouldAdd = alert.isCritical
        #endif
        guard shouldAdd else { return }
        // Avoid duplicate alerts within a short time window
        let recentAlerts = performanceAlerts.filter { 
            $0.type == alert.type && Date().timeIntervalSince($0.timestamp) < 30
        }
        guard recentAlerts.isEmpty else { return }
        performanceAlerts.append(alert)
        logger.warning("⚠️ Performance alert: \(alert.message)")
        // Auto-remove alerts after 60 seconds (increased from 30)
        DispatchQueue.main.asyncAfter(deadline: .now() + 60) {
            self.performanceAlerts.removeAll { $0.id == alert.id }
        }
    }
    // MARK: - Performance Optimization
    func optimizePerformance() {
        logger.info("🔧 Starting performance optimization")
        // Release unused services
        OptimizedServiceManager.shared.releaseUnusedServices()
        // Clear caches
        clearCaches()
        // Optimize animations
        optimizeAnimations()
        logger.info("✅ Performance optimization completed")
    }
    private func clearCaches() {
        // Clear image caches
        URLCache.shared.removeAllCachedResponses()
        // Clear memory management service caches
        MemoryManagementService.shared.clearAllCaches()
        // Clear temporary files (but recreate the directory)
        let tempDir = NSTemporaryDirectory()
        let tempURL = URL(fileURLWithPath: tempDir)
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: tempURL, includingPropertiesForKeys: nil)
            for fileURL in contents {
                try? FileManager.default.removeItem(at: fileURL)
            }
        } catch {
            // Ignore errors - temp directory might not exist
        }
        // Force garbage collection
        autoreleasepool {
            // This helps release autoreleased objects immediately
        }
    }
    private func optimizeAnimations() {
        // Reduce animation complexity based on performance
        if currentFPS < fpsThreshold {
            NotificationCenter.default.post(name: .reduceAnimations, object: nil)
        }
    }
    // MARK: - App Launch Time Tracking
    func recordAppLaunchTime(_ time: TimeInterval) {
        appLaunchTime = time
        logger.info("📱 App launch time: \(time)s")
        if time > 3.0 {
            addPerformanceAlert(.slowAppLaunch(time))
        }
    }
}
// MARK: - Performance Alert
struct PerformanceAlert: Identifiable, Equatable {
    let id = UUID()
    let type: AlertType
    let message: String
    let timestamp = Date()
    var isCritical: Bool {
        switch type {
        case .lowFPS(let fps):
            return fps < 30  // Only critical if very low FPS
        case .highMemoryUsage(let memory):
            return memory > 800  // Only critical if very high memory usage
        case .highCPUUsage(let cpu):
            return cpu > 90  // Only critical if very high CPU usage
        case .highNetworkLatency(let latency):
            return latency > 3000  // Only critical if very high latency
        case .slowAppLaunch(let time):
            return time > 5.0  // Only critical if very slow launch
        }
    }
    enum AlertType: Equatable {
        case lowFPS(Double)
        case highMemoryUsage(Double)
        case highCPUUsage(Double)
        case highNetworkLatency(Double)
        case slowAppLaunch(TimeInterval)
    }
    static func lowFPS(_ fps: Double) -> PerformanceAlert {
        PerformanceAlert(type: .lowFPS(fps), message: "Low FPS detected: \(String(format: "%.1f", fps))")
    }
    static func highMemoryUsage(_ memory: Double) -> PerformanceAlert {
        PerformanceAlert(type: .highMemoryUsage(memory), message: "High memory usage: \(String(format: "%.1f", memory))MB")
    }
    static func highCPUUsage(_ cpu: Double) -> PerformanceAlert {
        PerformanceAlert(type: .highCPUUsage(cpu), message: "High CPU usage: \(String(format: "%.1f", cpu))%")
    }
    static func highNetworkLatency(_ latency: Double) -> PerformanceAlert {
        PerformanceAlert(type: .highNetworkLatency(latency), message: "High network latency: \(String(format: "%.0f", latency))ms")
    }
    static func slowAppLaunch(_ time: TimeInterval) -> PerformanceAlert {
        PerformanceAlert(type: .slowAppLaunch(time), message: "Slow app launch: \(String(format: "%.1f", time))s")
    }
}
// MARK: - Notification Extensions
extension Notification.Name {
    static let reduceAnimations = Notification.Name("reduceAnimations")
    static let performanceOptimized = Notification.Name("performanceOptimized")
}
