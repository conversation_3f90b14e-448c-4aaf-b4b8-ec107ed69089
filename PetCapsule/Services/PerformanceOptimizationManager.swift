//
//  PerformanceOptimizationManager.swift
//  PetCapsule
//
//  Centralized performance optimization and crash prevention
//
import Foundation
#if canImport(UIKit)
import UIKit
#endif
import Combine
import OSLog
@MainActor
class PerformanceOptimizationManager: ObservableObject {
    static let shared = PerformanceOptimizationManager()
    // MARK: - Published Properties
    @Published var performanceMetrics: PerformanceMetrics = PerformanceMetrics()
    @Published var memoryUsage: MemoryUsage = MemoryUsage()
    @Published var isOptimizing = false
    // MARK: - Private Properties
    private var registeredTimers: Set<Timer> = []
    private var registeredCancellables: Set<AnyCancellable> = []
    private var backgroundTasks: Set<UIBackgroundTaskIdentifier> = []
    private var performanceTimer: Timer?
    private let logger = Logger(subsystem: "PetCapsule", category: "Performance")
    // Memory pressure monitoring
    private var memoryPressureSource: DispatchSourceMemoryPressure?
    private init() {
        setupMemoryPressureMonitoring()
        startPerformanceMonitoring()
        setupAppLifecycleObservers()
    }
    deinit {
        cleanup()
    }
    // MARK: - Timer Management
    func registerTimer(_ timer: Timer) {
        self.registeredTimers
        logger.info("Registered timer. Total: \(self.registeredTimers.count)")
    }
    func unregisterTimer(_ timer: Timer) {
        timer.invalidate()
        self.registeredTimers.remove(timer)
        logger.info("Unregistered timer. Remaining: \(self.registeredTimers.count)")
    }
    func invalidateAllTimers() {
        for timer in registeredTimers {
            timer.invalidate()
        }
        registeredTimers.removeAll()
        logger.info("Invalidated all timers")
    }
    // MARK: - Combine Management
    func registerCancellable(_ cancellable: AnyCancellable) {
        self.registeredCancellables
        logger.info("Registered cancellable. Total: \(self.registeredCancellables.count)")
    }
    func cancelAllSubscriptions() {
        for cancellable in registeredCancellables {
            cancellable.cancel()
        }
        registeredCancellables.removeAll()
        logger.info("Cancelled all subscriptions")
    }
    // MARK: - Background Task Management
    func registerBackgroundTask(_ identifier: UIBackgroundTaskIdentifier) {
        self.backgroundTasks
        logger.info("Registered background task. Total: \(self.backgroundTasks.count)")
    }
    func endBackgroundTask(_ identifier: UIBackgroundTaskIdentifier) {
        if self.backgroundTasks.contains(identifier) {
            UIApplication.shared.endBackgroundTask(identifier)
            self.backgroundTasks.remove(identifier)
            logger.info("Ended background task. Remaining: \(self.backgroundTasks.count)")
        }
    }
    func endAllBackgroundTasks() {
        for taskId in backgroundTasks {
            UIApplication.shared.endBackgroundTask(taskId)
        }
        backgroundTasks.removeAll()
        logger.info("Ended all background tasks")
    }
    // MARK: - Memory Management
    func forceMemoryCleanup() {
        isOptimizing = true
        defer { isOptimizing = false }
        logger.info("Starting forced memory cleanup")
        // Cancel unnecessary operations
        cancelAllSubscriptions()
        // Clear caches
        URLCache.shared.removeAllCachedResponses()
        // Trigger garbage collection
        autoreleasepool {
            // Force deallocation of unused objects
        }
        // Update memory metrics
        updateMemoryUsage()
        logger.info("Completed memory cleanup")
    }
    private func setupMemoryPressureMonitoring() {
        memoryPressureSource = DispatchSource.makeMemoryPressureSource(
            eventMask: [.warning, .critical],
            queue: .main
        )
        memoryPressureSource?.setEventHandler { [weak self] in
            guard let self = self else { return }
            let event = self.memoryPressureSource?.data ?? []
            if event.contains(.warning) {
                self.logger.warning("Memory pressure warning detected")
                self.handleMemoryPressure(.warning)
            } else if event.contains(.critical) {
                self.logger.error("Critical memory pressure detected")
                self.handleMemoryPressure(.critical)
            }
        }
        memoryPressureSource?.resume()
    }
    private func handleMemoryPressure(_ level: MemoryPressureLevel) {
        switch level {
        case .warning:
            // Light cleanup
            URLCache.shared.removeAllCachedResponses()
        case .critical:
            // Aggressive cleanup
            forceMemoryCleanup()
            // Notify services to reduce memory usage
            NotificationCenter.default.post(
                name: .memoryPressureCritical,
                object: nil
            )
        }
        updateMemoryUsage()
    }
    // MARK: - Performance Monitoring
    private func startPerformanceMonitoring() {
        performanceTimer = Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { [weak self] timer in
            guard let self = self else {
                timer.invalidate()
                return
            }
            Task { @MainActor in
                self.updatePerformanceMetrics()
            }
        }
        self.registerTimer(performanceTimer!)
    }
    private func updatePerformanceMetrics() {
        updateMemoryUsage()
        updateCPUUsage()
        updateNetworkMetrics()
        // Log performance issues
        if self.memoryUsage.usagePercentage > 80 {
            logger.warning("High memory usage: \(self.memoryUsage.usagePercentage)%")
        }
        if self.performanceMetrics.cpuUsage > 80 {
            logger.warning("High CPU usage: \(self.performanceMetrics.cpuUsage)%")
        }
    }
    private func updateMemoryUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        if kerr == KERN_SUCCESS {
            let usedBytes = info.resident_size
            let totalBytes = ProcessInfo.processInfo.physicalMemory
            memoryUsage = MemoryUsage(
                usedBytes: usedBytes,
                totalBytes: totalBytes,
                usagePercentage: Double(usedBytes) / Double(totalBytes) * 100
            )
        }
    }
    private func updateCPUUsage() {
        var info = task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<task_basic_info>.size)/4
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        if kerr == KERN_SUCCESS {
            // Simplified CPU usage calculation
            performanceMetrics.cpuUsage = Double(info.policy) // Placeholder
        }
    }
    private func updateNetworkMetrics() {
        // Network metrics would be implemented here
        performanceMetrics.networkLatency = 0.0 // Placeholder
    }
    // MARK: - App Lifecycle
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillTerminate),
            name: UIApplication.willTerminateNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidReceiveMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    @objc private func appDidEnterBackground() {
        logger.info("App entering background - cleaning up resources")
        // Pause non-essential timers
        for timer in registeredTimers {
            if timer.isValid {
                timer.fireDate = Date.distantFuture
            }
        }
        // Clean up caches
        URLCache.shared.removeAllCachedResponses()
    }
    @objc private func appWillTerminate() {
        logger.info("App terminating - final cleanup")
        cleanup()
    }
    @objc private func appDidReceiveMemoryWarning() {
        logger.warning("Memory warning received")
        handleMemoryPressure(.warning)
    }
    // MARK: - Cleanup
    nonisolated private func cleanup() {
        logger.info("Performing final cleanup")
        Task { @MainActor in
            invalidateAllTimers()
            cancelAllSubscriptions()
            endAllBackgroundTasks()
            memoryPressureSource?.cancel()
            memoryPressureSource = nil
        }
        NotificationCenter.default.removeObserver(self)
    }
}
// MARK: - Supporting Types
struct PerformanceMetrics {
    var cpuUsage: Double = 0.0
    var networkLatency: TimeInterval = 0.0
    var lastUpdate: Date = Date()
}
struct MemoryUsage {
    var usedBytes: UInt64 = 0
    var totalBytes: UInt64 = 0
    var usagePercentage: Double = 0.0
    var formattedUsed: String {
        ByteCountFormatter.string(fromByteCount: Int64(usedBytes), countStyle: .memory)
    }
    var formattedTotal: String {
        ByteCountFormatter.string(fromByteCount: Int64(totalBytes), countStyle: .memory)
    }
}
enum MemoryPressureLevel {
    case warning
    case critical
}
// MARK: - Notifications
extension Notification.Name {
    static let memoryPressureCritical = Notification.Name("memoryPressureCritical")
    static let performanceOptimizationRequired = Notification.Name("performanceOptimizationRequired")
}
// MARK: - Mach Types
private struct mach_task_basic_info {
    var virtual_size: mach_vm_size_t = 0
    var resident_size: mach_vm_size_t = 0
    var resident_size_max: mach_vm_size_t = 0
    var user_time: time_value_t = time_value_t()
    var system_time: time_value_t = time_value_t()
    var policy: policy_t = 0
    var suspend_count: integer_t = 0
}
private struct task_basic_info {
    var suspend_count: integer_t = 0
    var virtual_size: vm_size_t = 0
    var resident_size: vm_size_t = 0
    var user_time: time_value_t = time_value_t()
    var system_time: time_value_t = time_value_t()
    var policy: policy_t = 0
}
private struct time_value_t {
    var seconds: integer_t = 0
    var microseconds: integer_t = 0
} 