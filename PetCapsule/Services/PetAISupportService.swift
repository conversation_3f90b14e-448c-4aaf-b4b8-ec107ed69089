//
//  PetAISupportService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@available(iOS 18.0, *)
class PetAISupportService: ObservableObject {
    static let shared = PetAISupportService()

    @Published var isAnalyzing = false
    @Published var lastAnalysisDate: Date?
    @Published var aiRecommendations: [AIRecommendation] = []
    @Published var healthInsights: [HealthInsight] = []
    @Published var nutritionAdvice: [NutritionRecommendation] = []
    @Published var behaviorAnalysis: [BehaviorAnalysisResult] = []

    // Apple Intelligence uses local processing - no API key needed
    private var appleIntelligenceService: AppleIntelligenceService? {
        if #available(iOS 18.0, *) {
            return MainActor.assumeIsolated {
                AppleIntelligenceService.shared
            }
        }
        return nil
    }

    // Access to comprehensive pet data
    private var dataManager: PetDataManager {
        MainActor.assumeIsolated {
            PetDataManager.shared
        }
    }

    private init() {}

    // MARK: - Comprehensive Pet Health Analysis

    func analyzeCompletePetHealth(for pet: Pet) async -> PetHealthAnalysisResult {
        isAnalyzing = true
        defer { isAnalyzing = false }

        // Simulate comprehensive AI analysis
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

        let healthAnalysis = generateHealthAnalysis(for: pet)
        let nutritionAnalysis = generateNutritionAnalysis(for: pet)
        let behaviorAnalysis = generateBehaviorAnalysis(for: pet)
        let recommendations = await MainActor.run {
            generateAIRecommendations(for: pet)
        }

        lastAnalysisDate = Date()

        return PetHealthAnalysisResult(
            pet: pet,
            healthAnalysis: healthAnalysis,
            nutritionAnalysis: nutritionAnalysis,
            behaviorAnalysis: behaviorAnalysis,
            recommendations: recommendations,
            overallScore: calculateOverallWellnessScore(for: pet),
            analysisDate: Date()
        )
    }

    // MARK: - Health Analysis

    private func generateHealthAnalysis(for pet: Pet) -> HealthAnalysis {
        var insights: [HealthInsight] = []
        var alerts: [HealthAlert] = []
        var predictions: [HealthPrediction] = []

        // Weight analysis
        if let weight = pet.weight {
            let idealWeight = getIdealWeight(for: pet.breed ?? "Mixed", species: pet.species)
            let weightDifference = abs(weight - idealWeight) / idealWeight

            if weightDifference > 0.15 {
                let isOverweight = weight > idealWeight
                insights.append(HealthInsight(
                    id: UUID(),
                    category: .weight,
                    title: isOverweight ? "Weight Management Needed" : "Underweight Concern",
                    description: isOverweight ?
                        "Your pet is \(Int((weight - idealWeight) * 2.2)) lbs above ideal weight. This can lead to joint issues and diabetes." :
                        "Your pet is \(Int((idealWeight - weight) * 2.2)) lbs below ideal weight. Consider increasing caloric intake.",
                    severity: weightDifference > 0.25 ? .high : .medium,
                    actionItems: isOverweight ?
                        ["Reduce daily calories by 10-15%", "Increase exercise duration", "Switch to weight management food"] :
                        ["Increase feeding frequency", "Add high-calorie supplements", "Check for underlying health issues"],
                    confidence: 0.85
                ))
            }
        }

        // Vaccination analysis
        if pet.vaccinations.isEmpty {
            alerts.append(HealthAlert(
                id: UUID().uuidString,
                petId: "sample_pet",
                type: "vaccination",
                severity: .high,
                title: "Vaccination Schedule Needed",
                message: "No vaccination records found. Essential vaccines are crucial for your pet's health.",
                triggeredAt: Date(),
                isActive: true,
                isAcknowledged: false
            ))
        }

        // Age-based predictions (convert months to years)
        if pet.ageInYears >= 7 {
            predictions.append(HealthPrediction(
                id: UUID(),
                condition: "Arthritis",
                probability: pet.species == "dog" ? 0.65 : 0.45,
                timeframe: "Next 2-3 years",
                preventionTips: [
                    "Maintain healthy weight",
                    "Regular low-impact exercise",
                    "Joint supplements (glucosamine)",
                    "Comfortable sleeping area"
                ],
                severity: .medium
            ))
        }

        return HealthAnalysis(
            insights: insights,
            alerts: alerts,
            predictions: predictions,
            overallHealthScore: pet.healthScore,
            lastUpdated: Date()
        )
    }

    // MARK: - Nutrition Analysis

    private func generateNutritionAnalysis(for pet: Pet) -> NutritionAnalysis {
        var recommendations: [NutritionRecommendation] = []
        var mealPlan: MealPlan?
        var supplementAdvice: [SupplementAdvice] = []

        // Calculate ideal calories
        let idealCalories = calculateIdealCalories(for: pet)
        let currentCalories = pet.dailyCalories

        if abs(currentCalories - idealCalories) > 100 {
            let isOverfeeding = currentCalories > idealCalories
            recommendations.append(NutritionRecommendation(
                id: UUID(),
                category: .calories,
                title: isOverfeeding ? "Reduce Daily Calories" : "Increase Daily Calories",
                description: isOverfeeding ?
                    "Current intake (\(currentCalories) cal) exceeds ideal (\(idealCalories) cal). This may lead to weight gain." :
                    "Current intake (\(currentCalories) cal) is below ideal (\(idealCalories) cal). Your pet may need more energy.",
                priority: .high,
                estimatedCost: 0,
                implementation: isOverfeeding ?
                    "Reduce portion sizes by 15% and increase exercise" :
                    "Add healthy snacks or increase meal portions gradually"
            ))
        }

        // Food quality analysis using comprehensive pet data
        if let currentFood = pet.currentFood, !currentFood.isEmpty {
            let foodQuality = analyzeFoodQuality(foodName: currentFood, petSpecies: pet.species)
            if foodQuality.score < 0.7 {
                recommendations.append(NutritionRecommendation(
                    id: UUID(),
                    category: .foodQuality,
                    title: "Consider Premium Food Upgrade",
                    description: "Current food quality score: \(Int(foodQuality.score * 100))%. Higher quality food can improve health outcomes.",
                    priority: .medium,
                    estimatedCost: 25.0,
                    implementation: "Gradually transition to premium food over 7-10 days"
                ))
            }
        }

        // Supplement recommendations
        if pet.age >= 7 {
            supplementAdvice.append(SupplementAdvice(
                id: UUID(),
                supplement: "Glucosamine & Chondroitin",
                purpose: "Joint health support for senior pets",
                dosage: "Based on weight: \(pet.weight ?? 0) kg",
                frequency: "Daily with food",
                estimatedCost: 15.0,
                vetApprovalNeeded: false
            ))
        }

        // Water intake analysis using comprehensive pet data
        if let waterIntakeML = pet.waterIntakeML {
            let dailyWaterNeeds = calculateDailyWaterNeeds(for: pet)
            if Double(waterIntakeML) < dailyWaterNeeds * 0.8 {
            supplementAdvice.append(SupplementAdvice(
                id: UUID(),
                    supplement: "Water Fountain or Additional Water Sources",
                    purpose: "Increase water intake from \(waterIntakeML)ml to \(Int(dailyWaterNeeds))ml daily",
                    dosage: "Fresh water daily, multiple sources",
                frequency: "Continuous access",
                estimatedCost: 35.0,
                vetApprovalNeeded: false
            ))
            }
        }

        // Generate meal plan
        mealPlan = generateMealPlan(for: pet, targetCalories: idealCalories)

        return NutritionAnalysis(
            recommendations: recommendations,
            mealPlan: mealPlan,
            supplementAdvice: supplementAdvice,
            idealDailyCalories: idealCalories,
            currentCalorieGap: currentCalories - idealCalories,
            hydrationStatus: analyzeHydrationStatus(for: pet),
            lastUpdated: Date()
        )
    }

    // MARK: - Behavior Analysis

    private func generateBehaviorAnalysis(for pet: Pet) -> BehaviorAnalysisResult {
        let patterns: [AIBehaviorPattern] = []
        var concerns: [BehaviorConcern] = []
        var trainingRecommendations: [TrainingRecommendation] = []

        // Analyze personality traits
        let personalityScore = analyzePersonality(traits: pet.personalityTraits)

        // Activity level analysis
        let activityAnalysis = analyzeActivityLevel(pet.activityLevel, species: pet.species, age: pet.age)
        if let concern = activityAnalysis.concern {
            concerns.append(concern)
        }

        // Training recommendations based on species and age
        if pet.species == "dog" {
            if pet.age < 2 {
                trainingRecommendations.append(TrainingRecommendation(
                    id: UUID(),
                    skill: "Basic Obedience",
                    description: "Essential commands: sit, stay, come, down",
                    difficulty: .beginner,
                    estimatedTime: "2-4 weeks",
                    benefits: ["Better communication", "Safety", "Bonding"],
                    resources: ["Local puppy classes", "Online tutorials", "Professional trainer"]
                ))
            }

            if pet.personalityTraits.contains("Energetic") {
                trainingRecommendations.append(TrainingRecommendation(
                    id: UUID(),
                    skill: "Mental Stimulation",
                    description: "Puzzle toys and brain games to channel energy",
                    difficulty: .intermediate,
                    estimatedTime: "Ongoing",
                    benefits: ["Reduced destructive behavior", "Mental exercise", "Problem-solving skills"],
                    resources: ["Puzzle feeders", "Interactive toys", "Agility training"]
                ))
            }
        }

        return BehaviorAnalysisResult(
            patterns: patterns,
            concerns: concerns,
            trainingRecommendations: trainingRecommendations,
            personalityProfile: personalityScore,
            socialScore: 85.0, // Default social score since not in Pet model
            lastUpdated: Date()
        )
    }

    // MARK: - AI Recommendations

    @MainActor
    private func generateAIRecommendations(for pet: Pet) -> [AIRecommendation] {
        var recommendations: [AIRecommendation] = []

        // Use comprehensive health scoring from PetDataManager
        let healthScore = dataManager.getHealthScore(for: pet)
        
        // Health-based recommendations with enhanced logic
        if healthScore < 85.0 {
            let urgency: AIRecommendation.Priority = healthScore < 70.0 ? .urgent : (healthScore < 80.0 ? .high : .medium)
            let timeFrame = healthScore < 70.0 ? "Within 24-48 hours" : (healthScore < 80.0 ? "Within 1 week" : "Within 2 weeks")
            
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .health,
                title: "Comprehensive Health Assessment Recommended",
                description: "Health score of \(Int(healthScore)) indicates potential concerns. Factors analyzed: vaccination status, weight, nutrition, exercise, and overall wellness indicators.",
                priority: urgency,
                estimatedCost: 125.0,
                timeToImplement: timeFrame,
                expectedBenefit: "Early detection and treatment of health issues, improved quality of life",
                confidence: 0.92
            ))
        }
        
        // Comprehensive nutrition analysis recommendations
        generateNutritionRecommendations(for: pet, recommendations: &recommendations)
        
        // Exercise and activity recommendations
        generateExerciseRecommendations(for: pet, recommendations: &recommendations)
        
        // Training and behavior recommendations
        generateTrainingRecommendations(for: pet, recommendations: &recommendations)

        // Vaccination recommendations
        if pet.vaccinations.isEmpty || pet.vaccinations.count < 3 {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .health,
                title: "Update Vaccination Schedule",
                description: "Your \(pet.species) needs core vaccinations for protection against common diseases.",
                priority: .high,
                estimatedCost: 120.0,
                timeToImplement: "Schedule within 2 weeks",
                expectedBenefit: "Protection against rabies, distemper, and other diseases",
                confidence: 0.95
            ))
        }

        // Weight management recommendations
        if let weight = pet.weight {
            let weightRange = PetBreedData.getWeightRange(for: pet.species, breed: pet.breed)
            if weight < weightRange.lowerBound {
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .nutrition,
                    title: "Weight Gain Program Needed",
                    description: "At \(String(format: "%.1f", weight)) lbs, your \(pet.name) is underweight. Ideal range: \(String(format: "%.1f", weightRange.lowerBound))-\(String(format: "%.1f", weightRange.upperBound)) lbs.",
                    priority: .high,
                    estimatedCost: 35.0,
                    timeToImplement: "Start immediately",
                    expectedBenefit: "Improved energy, immunity, and overall health",
                    confidence: 0.85
                ))
            } else if weight > weightRange.upperBound {
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .nutrition,
                    title: "Weight Management Required",
                    description: "At \(String(format: "%.1f", weight)) lbs, your \(pet.name) is overweight. Ideal range: \(String(format: "%.1f", weightRange.lowerBound))-\(String(format: "%.1f", weightRange.upperBound)) lbs.",
                    priority: .medium,
                    estimatedCost: 25.0,
                    timeToImplement: "Start this week",
                    expectedBenefit: "Reduced joint stress, improved mobility, longer lifespan",
                    confidence: 0.88
                ))
            }
        }

        // Age-specific recommendations
        let ageInYears = pet.ageInYears
        if ageInYears >= 7 {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .health,
                title: "Senior Pet Health Screening",
                description: "At \(String(format: "%.1f", ageInYears)) years old, your \(pet.species) should have regular senior health checks including blood work.",
                priority: .medium,
                estimatedCost: 180.0,
                timeToImplement: "Schedule within 1 month",
                expectedBenefit: "Early detection of age-related conditions",
                confidence: 0.82
            ))
            
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .nutrition,
                title: "Senior Diet Transition",
                description: "Consider switching to senior formula food designed for older pets' nutritional needs.",
                priority: .low,
                estimatedCost: 45.0,
                timeToImplement: "Gradual transition over 2 weeks",
                expectedBenefit: "Better digestion, joint support, cognitive health",
                confidence: 0.75
            ))
        }

        // Exercise recommendations
        let exerciseNeeds = calculateExerciseNeeds(for: pet)
        if pet.activityLevel == "low" && pet.species == "dog" {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .exercise,
                title: "Increase Daily Activity",
                description: "Your \(pet.breed ?? pet.species) needs at least \(exerciseNeeds.dailyMinutes) minutes of daily exercise for optimal health.",
                priority: .medium,
                estimatedCost: 0.0,
                timeToImplement: "Start today",
                expectedBenefit: "Improved fitness, behavior, and mental stimulation",
                confidence: 0.85
            ))
        }

        // Allergy management recommendations
        if !pet.allergies.isEmpty {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .health,
                title: "Allergy Management Plan",
                description: "With known allergies to \(pet.allergies.joined(separator: ", ")), consider environmental controls and hypoallergenic products.",
                priority: .medium,
                estimatedCost: 60.0,
                timeToImplement: "Implement gradually",
                expectedBenefit: "Reduced allergic reactions and improved comfort",
                confidence: 0.8
            ))
        }

        // Food allergy recommendations
        if !pet.foodAllergies.isEmpty {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .nutrition,
                title: "Specialized Diet Needed",
                description: "With food allergies to \(pet.foodAllergies.joined(separator: ", ")), switch to a limited ingredient or hypoallergenic diet.",
                priority: .high,
                estimatedCost: 55.0,
                timeToImplement: "Gradual transition over 2 weeks",
                expectedBenefit: "Eliminated allergic reactions and improved digestion",
                confidence: 0.9
            ))
        }

        // Medication adherence recommendations
        if !pet.medications.isEmpty {
            let activeMeds = pet.medications.filter { $0.isActive }
            if !activeMeds.isEmpty {
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .health,
                    title: "Medication Management",
                    description: "Set up reminders for \(activeMeds.count) active medication(s) to ensure proper treatment compliance.",
                    priority: .high,
                    estimatedCost: 15.0,
                    timeToImplement: "Set up today",
                    expectedBenefit: "Optimal treatment outcomes and health maintenance",
                    confidence: 0.95
                ))
            }
        }

        // Microchip recommendations
        if pet.microchipId?.isEmpty ?? true {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .preventive,
                title: "Microchip Your Pet",
                description: "Microchipping provides permanent identification and dramatically increases the chance of reunification if lost.",
                priority: .medium,
                estimatedCost: 45.0,
                timeToImplement: "Schedule within 1 month",
                expectedBenefit: "Peace of mind and increased safety",
                confidence: 0.9
            ))
        }

        // Spay/neuter recommendations
        if !pet.isSpayedNeutered && pet.ageInYears >= 0.5 {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .health,
                title: "Consider Spaying/Neutering",
                description: "Spaying/neutering provides health benefits and prevents unwanted breeding.",
                priority: .medium,
                estimatedCost: 350.0,
                timeToImplement: "Discuss with vet",
                expectedBenefit: "Reduced cancer risk, behavioral improvements",
                confidence: 0.85
            ))
        }

        // Dental care recommendations
        if pet.ageInYears >= 3 {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .health,
                title: "Dental Health Check",
                description: "Pets over 3 years old should have regular dental examinations and cleanings.",
                priority: .low,
                estimatedCost: 200.0,
                timeToImplement: "Schedule within 6 months",
                expectedBenefit: "Prevention of dental disease and improved overall health",
                confidence: 0.78
            ))
        }

        // Emergency preparedness
        if pet.emergencyContacts.isEmpty {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .preventive,
                title: "Emergency Contact Setup",
                description: "Add emergency contacts and veterinary information for quick access during emergencies.",
                priority: .low,
                estimatedCost: 0.0,
                timeToImplement: "Complete today",
                expectedBenefit: "Faster emergency response and better care coordination",
                confidence: 0.9
            ))
        }

        return recommendations.sorted { $0.priority.rawValue > $1.priority.rawValue }
    }
    
    // MARK: - Comprehensive Recommendation Generators
    
    private func generateNutritionRecommendations(for pet: Pet, recommendations: inout [AIRecommendation]) {
        // Water intake analysis
        if let waterIntake = pet.waterIntakeML {
            let dailyWaterNeeds = calculateDailyWaterNeeds(for: pet)
            if waterIntake < Int(dailyWaterNeeds * 0.8) {
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .nutrition,
                    title: "Increase Water Intake",
                    description: "Current intake (\(waterIntake)ml) is below optimal (\(Int(dailyWaterNeeds))ml). Dehydration can lead to kidney issues and poor digestion.",
                    priority: .high,
                    estimatedCost: 25.0,
                    timeToImplement: "Start immediately",
                    expectedBenefit: "Improved kidney function, better digestion, healthier coat",
                    confidence: 0.88
                ))
            }
        }
        
        // Feeding schedule analysis
        if pet.feedingSchedule.isEmpty {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .nutrition,
                title: "Establish Regular Feeding Schedule",
                description: "No feeding schedule detected. Consistent meal times improve digestion and help maintain healthy weight.",
                priority: .medium,
                estimatedCost: 0.0,
                timeToImplement: "Implement this week",
                expectedBenefit: "Better digestion, weight management, reduced anxiety",
                confidence: 0.85
            ))
        } else if pet.feedingSchedule.count < 2 && pet.ageInYears < 1 {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .nutrition,
                title: "Increase Feeding Frequency for Puppy/Kitten",
                description: "Young pets need 3-4 small meals daily for proper growth and energy levels.",
                priority: .medium,
                estimatedCost: 0.0,
                timeToImplement: "Adjust this week",
                expectedBenefit: "Optimal growth, stable energy, better behavior",
                confidence: 0.9
            ))
        }
        
        // Dietary restrictions analysis
        if !pet.dietaryRestrictions.isEmpty {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .nutrition,
                title: "Specialized Diet Management",
                description: "With dietary restrictions (\(pet.dietaryRestrictions.joined(separator: ", "))), ensure all foods and treats comply with these limitations.",
                priority: .high,
                estimatedCost: 35.0,
                timeToImplement: "Review immediately",
                expectedBenefit: "Avoided health complications, improved digestive health",
                confidence: 0.95
            ))
        }
    }
    
    private func generateExerciseRecommendations(for pet: Pet, recommendations: inout [AIRecommendation]) {
        let idealExercise = calculateIdealExerciseMinutes(for: pet)
        
        // Exercise duration analysis
        if let currentExercise = pet.exerciseMinutesDaily {
            if currentExercise < Int(idealExercise * 0.7) {
                let deficit = idealExercise - Double(currentExercise)
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .exercise,
                    title: "Increase Daily Exercise",
                    description: "Current exercise (\(currentExercise) min) is \(Int(deficit)) minutes below optimal. This can lead to behavioral issues and health problems.",
                    priority: .medium,
                    estimatedCost: 0.0,
                    timeToImplement: "Gradually increase over 2 weeks",
                    expectedBenefit: "Better behavior, weight management, improved mood",
                    confidence: 0.87
                ))
            } else if currentExercise > Int(idealExercise * 1.5) {
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .exercise,
                    title: "Monitor for Over-Exercise",
                    description: "Exercise level (\(currentExercise) min) is quite high. Ensure adequate rest and watch for signs of fatigue or injury.",
                    priority: .low,
                    estimatedCost: 0.0,
                    timeToImplement: "Monitor daily",
                    expectedBenefit: "Injury prevention, optimal fitness balance",
                    confidence: 0.82
                ))
            }
        }
        
        // Walking frequency analysis
        if let walkingFreq = pet.walkingFrequency {
            if pet.species == "dog" && (walkingFreq.contains("rarely") || walkingFreq.contains("never")) {
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .exercise,
                    title: "Establish Regular Walking Routine",
                    description: "Dogs need regular walks for physical health, mental stimulation, and socialization. Current frequency is insufficient.",
                    priority: .high,
                    estimatedCost: 15.0,
                    timeToImplement: "Start this week",
                    expectedBenefit: "Improved fitness, better behavior, stronger bond",
                    confidence: 0.93
                ))
            }
        }
        
        // Activity suggestions based on favorite activities
        if pet.favoriteActivities.isEmpty {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .exercise,
                title: "Discover Preferred Activities",
                description: "Try different activities to find what your \(pet.species) enjoys most. This improves engagement and exercise consistency.",
                priority: .low,
                estimatedCost: 25.0,
                timeToImplement: "Experiment over next month",
                expectedBenefit: "Higher exercise motivation, stronger pet-owner bond",
                confidence: 0.78
            ))
        }
    }
    
    private func generateTrainingRecommendations(for pet: Pet, recommendations: inout [AIRecommendation]) {
        // Training level assessment
        if let trainingLevel = pet.trainingLevel {
            if trainingLevel.contains("none") || trainingLevel.contains("untrained") {
                let speciesSpecificTraining = pet.species == "dog" ? "basic obedience commands" : 
                                            pet.species == "cat" ? "litter training and basic behaviors" :
                                            "species-appropriate behavioral training"
                
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .training,
                    title: "Start Basic Training Program",
                    description: "No training detected. \(speciesSpecificTraining.capitalized) will improve safety, communication, and quality of life.",
                    priority: .medium,
                    estimatedCost: 80.0,
                    timeToImplement: "Start within 2 weeks",
                    expectedBenefit: "Better communication, improved safety, stronger bond",
                    confidence: 0.9
                ))
            }
        }
        
        // Command knowledge analysis
        if pet.knownCommands.isEmpty && pet.species == "dog" {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .training,
                title: "Teach Essential Commands",
                description: "No known commands detected. Teaching sit, stay, come, and down is crucial for safety and communication.",
                priority: .medium,
                estimatedCost: 60.0,
                timeToImplement: "2-4 weeks of consistent practice",
                expectedBenefit: "Improved safety, better control in emergencies, enhanced bond",
                confidence: 0.92
            ))
        } else if pet.knownCommands.count < 3 && pet.species == "dog" {
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .training,
                title: "Expand Command Vocabulary",
                description: "Only \(pet.knownCommands.count) commands known. Teaching additional commands improves communication and mental stimulation.",
                priority: .low,
                estimatedCost: 40.0,
                timeToImplement: "Add 1 new command per week",
                expectedBenefit: "Better communication, mental exercise, improved obedience",
                confidence: 0.85
            ))
        }
        
        // Behavior issues analysis
        if !pet.behaviorIssues.isEmpty {
            let severityScore = assessBehaviorSeverity(pet.behaviorIssues)
            let priority: AIRecommendation.Priority = severityScore > 7 ? .high : (severityScore > 4 ? .medium : .low)
            
            recommendations.append(AIRecommendation(
                id: UUID(),
                category: .training,
                title: "Address Behavior Issues",
                description: "Behavior concerns detected: \(pet.behaviorIssues.joined(separator: ", ")). Early intervention prevents escalation.",
                priority: priority,
                estimatedCost: 150.0,
                timeToImplement: "Consult trainer within 2 weeks",
                expectedBenefit: "Improved household harmony, reduced stress, safer environment",
                confidence: 0.88
            ))
        }
        
        // Social behavior analysis
        if let socialBehavior = pet.socialBehavior {
            if socialBehavior.contains("aggressive") || socialBehavior.contains("fearful") {
                recommendations.append(AIRecommendation(
                    id: UUID(),
                    category: .training,
                    title: "Professional Behavior Consultation",
                    description: "Social behavior concerns (\(socialBehavior)) may require professional intervention for safety and quality of life.",
                    priority: .high,
                    estimatedCost: 200.0,
                    timeToImplement: "Schedule within 1 week",
                    expectedBenefit: "Improved social interactions, reduced risk, better quality of life",
                    confidence: 0.9
                ))
            }
        }
    }

    // MARK: - Helper Methods
    
    func calculateDailyWaterNeeds(for pet: Pet) -> Double {
        // Water needs in ml per kg of body weight per day
        let baseWaterNeeds: Double
        switch pet.species {
        case "dog":
            baseWaterNeeds = 50.0 // ml per kg
        case "cat":
            baseWaterNeeds = 60.0 // ml per kg (cats need more water per kg)
        default:
            baseWaterNeeds = 50.0
        }
        
        let weight = pet.weight ?? 10.0 // Default weight if not specified
        var totalNeeds = weight * baseWaterNeeds
        
        // Adjust for age (seniors need more)
        if pet.ageInYears > 7 {
            totalNeeds *= 1.2
        }
        
        // Adjust for activity level
        switch pet.activityLevel {
        case "high", "very_high":
            totalNeeds *= 1.3
        case "moderate":
            totalNeeds *= 1.1
        default:
            break
        }
        
        return totalNeeds
    }
    
    func calculateIdealExerciseMinutes(for pet: Pet) -> Double {
        var baseMinutes: Double
        
        switch pet.species {
        case "dog":
            // Base on breed size and energy level
            if let breed = pet.breed?.lowercased() {
                if breed.contains("retriever") || breed.contains("shepherd") || breed.contains("husky") {
                    baseMinutes = 90.0 // High energy breeds
                } else if breed.contains("poodle") || breed.contains("spaniel") {
                    baseMinutes = 60.0 // Moderate energy breeds
                } else if breed.contains("bulldog") || breed.contains("pug") {
                    baseMinutes = 30.0 // Lower energy breeds
                } else {
                    baseMinutes = 60.0 // Default for dogs
                }
            } else {
                baseMinutes = 60.0 // Default for dogs
            }
        case "cat":
            baseMinutes = 20.0 // Indoor cats need some play time
        default:
            baseMinutes = 30.0 // Default for other pets
        }
        
        // Adjust for age
        if pet.ageInYears < 1 {
            baseMinutes *= 0.7 // Puppies/kittens need shorter but more frequent exercise
        } else if pet.ageInYears > 7 {
            baseMinutes *= 0.8 // Senior pets need less intense exercise
        }
        
        // Adjust for current activity level
        switch pet.activityLevel {
        case "very_high":
            baseMinutes *= 1.5
        case "high":
            baseMinutes *= 1.2
        case "low":
            baseMinutes *= 0.8
        case "very_low":
            baseMinutes *= 0.6
        default:
            break
        }
        
        return baseMinutes
    }
    
    private func assessBehaviorSeverity(_ behaviorIssues: [String]) -> Int {
        var severityScore = 0
        
        for issue in behaviorIssues {
            let lowercaseIssue = issue.lowercased()
            
            // High severity issues (3-4 points each)
            if lowercaseIssue.contains("aggression") || lowercaseIssue.contains("biting") {
                severityScore += 4
            } else if lowercaseIssue.contains("destructive") || lowercaseIssue.contains("escape") {
                severityScore += 3
            }
            // Medium severity issues (2 points each)
            else if lowercaseIssue.contains("barking") || lowercaseIssue.contains("jumping") {
                severityScore += 2
            }
            // Low severity issues (1 point each)
            else {
                severityScore += 1
            }
        }
        
        return min(severityScore, 10) // Cap at 10
    }

    private func getIdealWeight(for breed: String, species: String) -> Double {
        // Simplified breed-based weight calculation
        switch breed.lowercased() {
        case "golden retriever": return 30.0
        case "border collie": return 22.0
        case "maine coon": return 6.0
        case "domestic shorthair": return 4.5
        default:
            return species == "dog" ? 25.0 : 4.5
        }
    }

    private func calculateIdealCalories(for pet: Pet) -> Int {
        guard let weight = pet.weight else { return 0 }

        let baseCalories = pet.species == "dog" ? (weight * 30 + 70) : (weight * 70)
        let activityMultiplier: Double = {
            switch pet.activityLevel {
            case "low": return 1.0
            case "moderate": return 1.2
            case "high": return 1.4
            case "very_high": return 1.6
            default: return 1.2
            }
        }()

        return Int(baseCalories * activityMultiplier)
    }

    private func calculateOverallWellnessScore(for pet: Pet) -> Double {
        var score = pet.healthScore

        // Adjust for nutrition
        if pet.dailyCalories > 0 {
            let idealCalories = calculateIdealCalories(for: pet)
            let calorieAccuracy = 1.0 - abs(Double(pet.dailyCalories - idealCalories)) / Double(idealCalories)
            score = (score + calorieAccuracy) / 2.0
        }

        // Adjust for social factors - using default since socialScore doesn't exist in Pet model
        let socialScore = 85.0 // Default social score
        score = (score + socialScore) / 2.0

        // Adjust for health alerts
        if !pet.healthAlerts.isEmpty {
            score -= 0.1
        }

        return max(0.0, min(1.0, score))
    }

    // MARK: - Additional Helper Methods

    private func analyzeFoodQuality(foodName: String, petSpecies: String) -> FoodQualityAnalysis {
        // Simplified food quality analysis
        let premiumBrands = ["orijen", "acana", "wellness", "blue buffalo", "hill's science diet"]
        let budgetBrands = ["purina", "pedigree", "friskies", "meow mix"]

        let foodLower = foodName.lowercased()

        if premiumBrands.contains(where: { foodLower.contains($0) }) {
            return FoodQualityAnalysis(score: 0.9, category: "Premium", notes: "High-quality ingredients")
        } else if budgetBrands.contains(where: { foodLower.contains($0) }) {
            return FoodQualityAnalysis(score: 0.6, category: "Budget", notes: "Consider upgrading for better nutrition")
        } else {
            return FoodQualityAnalysis(score: 0.75, category: "Standard", notes: "Moderate quality food")
        }
    }

    private func generateMealPlan(for pet: Pet, targetCalories: Int) -> MealPlan {
        let mealsPerDay = pet.species == "cat" ? 3 : 2
        let caloriesPerMeal = targetCalories / mealsPerDay

        var meals: [MealPlanItem] = []

        if pet.species == "dog" {
            meals = [
                MealPlanItem(
                    time: "07:00",
                    foodType: "Dry kibble",
                    amount: String(format: "%.1f cups", Double(caloriesPerMeal) / 350.0),
                    calories: caloriesPerMeal,
                    notes: "Morning meal with supplements"
                ),
                MealPlanItem(
                    time: "18:00",
                    foodType: "Dry kibble",
                    amount: String(format: "%.1f cups", Double(caloriesPerMeal) / 350.0),
                    calories: caloriesPerMeal,
                    notes: "Evening meal"
                )
            ]
        } else {
            meals = [
                MealPlanItem(
                    time: "07:00",
                    foodType: "Wet food",
                    amount: String(format: "%.1f oz", Double(caloriesPerMeal) / 80.0),
                    calories: caloriesPerMeal,
                    notes: "Morning meal"
                ),
                MealPlanItem(
                    time: "13:00",
                    foodType: "Dry kibble",
                    amount: String(format: "%.1f cups", Double(caloriesPerMeal) / 300.0),
                    calories: caloriesPerMeal,
                    notes: "Afternoon meal"
                ),
                MealPlanItem(
                    time: "19:00",
                    foodType: "Wet food",
                    amount: String(format: "%.1f oz", Double(caloriesPerMeal) / 80.0),
                    calories: caloriesPerMeal,
                    notes: "Evening meal"
                )
            ]
        }

        return MealPlan(
            meals: meals,
            totalDailyCalories: targetCalories,
            specialInstructions: generateSpecialInstructions(for: pet)
        )
    }

    private func generateSpecialInstructions(for pet: Pet) -> [String] {
        var instructions: [String] = []

        if !pet.foodAllergies.isEmpty {
            instructions.append("Avoid: \(pet.foodAllergies.joined(separator: ", "))")
        }

        if pet.age < 1 {
            instructions.append("Use age-appropriate kitten/puppy food")
        } else if pet.age >= 7 {
            instructions.append("Consider senior formula for easier digestion")
        }

        if pet.activityLevel == "high" || pet.activityLevel == "very_high" {
            instructions.append("May need additional calories on high-activity days")
        }

        return instructions
    }

    private func analyzeHydrationStatus(for pet: Pet) -> HydrationStatus {
        let idealWater = pet.species == "dog" ? (pet.weight ?? 25) * 0.06 : (pet.weight ?? 4) * 0.08
        // Using default water intake since waterIntake property doesn't exist in Pet model
        let currentIntake = 0.3 // Default water intake value

        if currentIntake < idealWater * 0.7 {
            return HydrationStatus(
                level: .low,
                recommendation: "Increase water intake with fountains or wet food",
                idealDaily: idealWater,
                currentDaily: currentIntake
            )
        } else if currentIntake > idealWater * 1.3 {
            return HydrationStatus(
                level: .high,
                recommendation: "Monitor for potential health issues causing excessive thirst",
                idealDaily: idealWater,
                currentDaily: currentIntake
            )
        } else {
            return HydrationStatus(
                level: .optimal,
                recommendation: "Maintain current water intake",
                idealDaily: idealWater,
                currentDaily: currentIntake
            )
        }
    }

    private func analyzePersonality(traits: [String]) -> PersonalityProfile {
        let energyTraits = ["energetic", "playful", "active"]
        let socialTraits = ["friendly", "social", "outgoing"]
        let calmTraits = ["calm", "gentle", "relaxed"]

        let energyScore = Double(traits.filter { trait in
            energyTraits.contains(trait.lowercased())
        }.count) / Double(max(traits.count, 1))

        let socialScore = Double(traits.filter { trait in
            socialTraits.contains(trait.lowercased())
        }.count) / Double(max(traits.count, 1))

        let calmScore = Double(traits.filter { trait in
            calmTraits.contains(trait.lowercased())
        }.count) / Double(max(traits.count, 1))

        return PersonalityProfile(
            energyLevel: energyScore,
            socialLevel: socialScore,
            calmLevel: calmScore,
            dominantTraits: Array(traits.prefix(3)),
            recommendations: generatePersonalityRecommendations(energy: energyScore, social: socialScore, calm: calmScore)
        )
    }

    private func generatePersonalityRecommendations(energy: Double, social: Double, calm: Double) -> [String] {
        var recommendations: [String] = []

        if energy > 0.6 {
            recommendations.append("Provide plenty of physical exercise and mental stimulation")
        }

        if social > 0.6 {
            recommendations.append("Arrange regular playdates and social interactions")
        }

        if calm > 0.6 {
            recommendations.append("Create quiet spaces for relaxation and rest")
        }

        return recommendations
    }

    private func analyzeActivityLevel(_ level: String, species: String, age: Int) -> ActivityAnalysis {
        let idealActivity = species == "dog" ? (age < 7 ? "high" : "moderate") : "moderate"

        if level != idealActivity {
            let concern = BehaviorConcern(
                id: UUID(),
                type: .activity,
                description: "Activity level (\(level)) may not be optimal for \(species) of age \(age)",
                severity: .medium,
                recommendations: [
                    level == "low" ? "Gradually increase exercise" : "Consider reducing intensity",
                    "Consult with veterinarian about appropriate activity levels"
                ]
            )
            return ActivityAnalysis(isOptimal: false, concern: concern)
        }

        return ActivityAnalysis(isOptimal: true, concern: nil)
    }

    private func calculateExerciseNeeds(for pet: Pet) -> ExerciseNeeds {
        let baseMinutes = pet.species == "dog" ? 60 : 30
        let ageMultiplier = pet.age < 2 ? 1.2 : (pet.age >= 7 ? 0.8 : 1.0)
        let activityMultiplier: Double = {
            switch pet.activityLevel {
            case "low": return 0.7
            case "moderate": return 1.0
            case "high": return 1.3
            case "very_high": return 1.6
            default: return 1.0
            }
        }()

        let recommendedMinutes = Int(Double(baseMinutes) * ageMultiplier * activityMultiplier)
        
        return ExerciseNeeds(
            dailyMinutes: recommendedMinutes,
            exerciseType: pet.species == "dog" ? ["walk", "play"] : ["play"],
            intensity: pet.activityLevel == "high" ? "high" : "moderate",
            restrictions: [],
            preferredTimes: []
        )
    }
}
