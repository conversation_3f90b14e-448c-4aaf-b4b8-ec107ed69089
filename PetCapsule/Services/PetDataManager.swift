//
//  PetDataManager.swift
//  PetCapsule
//
//  Centralized Pet Data Manager - Single Source of Truth
//  🎯 Provides unified access to comprehensive pet data across all app sections
//

import Foundation
import SwiftUI
import Combine

@MainActor
class PetDataManager: ObservableObject {
    static let shared = PetDataManager()
    
    // MARK: - Core Data Properties
    @Published var pets: [Pet] = []
    @Published var selectedPet: Pet?
    @Published var selectedPetIndex: Int = 0
    
    // MARK: - Loading States
    @Published var isLoading = false
    @Published var isRefreshing = false
    @Published var lastSyncTime: Date?
    @Published var errorMessage: String?
    
    // MARK: - Data Services
    private let dataService = AppleNativeDataService.shared
    
    // MARK: - Computed Properties for Easy Access
    
    /// Current selected pet with real-time updates
    var currentPet: Pet? {
        guard selectedPetIndex < pets.count else { return pets.first }
        return pets.isEmpty ? nil : pets[selectedPetIndex]
    }
    
    /// Dynamic health score based on comprehensive pet data
    var selectedPetHealthScore: Double {
        guard let pet = currentPet else { return 0.0 }
        return calculateDynamicHealthScore(for: pet)
    }
    
    /// Active health alerts count
    var selectedPetHealthAlerts: Int {
        guard let pet = currentPet else { return 0 }
        return pet.healthAlerts.filter { $0.isActive }.count
    }
    
    /// Comprehensive pet summary for AI agents
    var selectedPetAISummary: String {
        guard let pet = currentPet else { return "No pet selected" }
        return buildComprehensiveAISummary(for: pet)
    }
    
    /// Nutrition plan status
    var selectedPetNutritionStatus: NutritionStatus {
        guard let pet = currentPet else { return .notSet }
        return evaluateNutritionStatus(for: pet)
    }
    
    /// Training progress status  
    var selectedPetTrainingStatus: TrainingStatus {
        guard let pet = currentPet else { return .notStarted }
        return evaluateTrainingStatus(for: pet)
    }
    
    /// Exercise status
    var selectedPetExerciseStatus: ExerciseStatus {
        guard let pet = currentPet else { return .inactive }
        return evaluateExerciseStatus(for: pet)
    }
    
    /// Vaccination status
    @available(iOS 18.0, *)
    var selectedPetVaccinationStatus: PetVaccinationStatus {
        guard let pet = currentPet else {
            return PetVaccinationStatus(isUpToDate: false, lastVaccination: nil, nextDue: nil, overdue: [])
        }
        return evaluateVaccinationStatus(for: pet)
    }
    
    /// Get health score for any pet
    func getHealthScore(for pet: Pet) -> Double {
        return calculateDynamicHealthScore(for: pet)
    }
    
    // MARK: - Initialization
    
    private init() {
        setupSubscriptions()
        setupDataBinding()
        setupLoadingBinding()
        setupErrorBinding()
        Task {
            await loadInitialData()
        }
    }
    
    // MARK: - Data Loading
    
    private func setupSubscriptions() {
        // Using Apple native data service - no published properties to subscribe to
        // Data will be loaded directly when needed
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    private func setupDataBinding() {
        // Setup data binding with the data service
        // Note: Simplified implementation since AppleNativeDataService doesn't have @Published properties
        print("Setting up data binding with AppleNativeDataService")
    }
    
    private func setupLoadingBinding() {
        // Setup loading state binding
        print("Setting up loading state binding")
    }
    
    private func setupErrorBinding() {
        // Setup error state binding
        print("Setting up error state binding")
    }
    
    // MARK: - Data Loading
    func loadInitialData() async {
        isLoading = true
        defer { isLoading = false }
        
        // Load data using the data service
        print("Loading initial pet data")
        // In a real implementation, this would load pets from the data service
    }
    
    func refreshData() async {
        await loadInitialData()
    }
    
    // MARK: - Pet Selection Management
    
    func selectPet(_ pet: Pet) {
        if let index = pets.firstIndex(where: { $0.id == pet.id }) {
            selectedPet = pet
            selectedPetIndex = index
            notifyPetSelectionChanged()
        }
    }
    
    func selectPetByIndex(_ index: Int) {
        guard index >= 0 && index < pets.count else { return }
        selectedPetIndex = index
        selectedPet = pets[index]
        notifyPetSelectionChanged()
    }
    
    private func validateSelectedPet() {
        // Ensure selected pet is still valid
        if let selectedPet = selectedPet,
           !pets.contains(where: { $0.id == selectedPet.id }) {
            // Selected pet was removed, select first available
            if !pets.isEmpty {
                self.selectedPet = pets.first
                self.selectedPetIndex = 0
            } else {
                self.selectedPet = nil
                self.selectedPetIndex = 0
            }
        }
    }
    
    // MARK: - Dynamic Health Score Calculation
    
    private func calculateDynamicHealthScore(for pet: Pet) -> Double {
        var score = 1.0
        var factors = 0
        
        // Health conditions factor (0.0 - 1.0)
        let healthFactor = calculateHealthFactor(for: pet)
        score += healthFactor
        factors += 1
        
        // Nutrition factor (0.0 - 1.0)
        let nutritionFactor = calculateNutritionFactor(for: pet)
        score += nutritionFactor
        factors += 1
        
        // Exercise factor (0.0 - 1.0)
        let exerciseFactor = calculateExerciseFactor(for: pet)
        score += exerciseFactor
        factors += 1
        
        // Training factor (0.0 - 1.0)
        let trainingFactor = calculateTrainingFactor(for: pet)
        score += trainingFactor
        factors += 1
        
        // Vaccination factor (0.0 - 1.0)
        let vaccinationFactor = calculateVaccinationFactor(for: pet)
        score += vaccinationFactor
        factors += 1
        
        return factors > 0 ? score / Double(factors) : 0.5
    }
    
    private func calculateHealthFactor(for pet: Pet) -> Double {
        let activeHealthAlerts = pet.healthAlerts.filter { $0.isActive }.count
        let chronicConditions = pet.chronicConditions.count
        let activeMedications = pet.medications.count
        
        // Start with perfect health
        var healthFactor = 1.0
        
        // Deduct for active alerts
        healthFactor -= Double(activeHealthAlerts) * 0.1
        
        // Deduct for chronic conditions
        healthFactor -= Double(chronicConditions) * 0.05
        
        // Slight deduction for medications (not necessarily bad)
        healthFactor -= Double(activeMedications) * 0.02
        
        return max(0.0, min(1.0, healthFactor))
    }
    
    private func calculateNutritionFactor(for pet: Pet) -> Double {
        var nutritionFactor = 0.5 // Default neutral
        
        // Check if nutrition data is set
        if pet.currentFood != nil {
            nutritionFactor += 0.3
        }
        
        if !pet.feedingSchedule.isEmpty {
            nutritionFactor += 0.2
        }
        
        if !pet.dietaryRestrictions.isEmpty {
            nutritionFactor += 0.1 // Managed dietary restrictions is good
        }
        
        if let waterIntake = pet.waterIntakeML, waterIntake > 0 {
            nutritionFactor += 0.1
        }
        
        return max(0.0, min(1.0, nutritionFactor))
    }
    
    private func calculateExerciseFactor(for pet: Pet) -> Double {
        var exerciseFactor = 0.5 // Default neutral
        
        if let exerciseMinutes = pet.exerciseMinutesDaily {
            if exerciseMinutes >= 30 {
                exerciseFactor += 0.4
            } else if exerciseMinutes >= 15 {
                exerciseFactor += 0.2
            }
        }
        
        if pet.walkingFrequency != nil {
            exerciseFactor += 0.1
        }
        
        return max(0.0, min(1.0, exerciseFactor))
    }
    
    private func calculateTrainingFactor(for pet: Pet) -> Double {
        var trainingFactor = 0.5 // Default neutral
        
        if let trainingLevel = pet.trainingLevel {
            switch trainingLevel.lowercased() {
            case "advanced": trainingFactor += 0.4
            case "intermediate": trainingFactor += 0.3
            case "beginner": trainingFactor += 0.2
            default: trainingFactor += 0.1
            }
        }
        
        if !pet.knownCommands.isEmpty {
            trainingFactor += min(0.2, Double(pet.knownCommands.count) * 0.05)
        }
        
        return max(0.0, min(1.0, trainingFactor))
    }
    
    private func calculateVaccinationFactor(for pet: Pet) -> Double {
        let vaccinationCount = pet.vaccinationRecords.count
        let age = pet.age
        
        // Expected vaccinations based on age
        let expectedVaccinations = max(3, age / 12) // Minimum 3, then roughly yearly
        
        if vaccinationCount >= expectedVaccinations {
            return 1.0
        } else if vaccinationCount >= expectedVaccinations / 2 {
            return 0.7
        } else {
            return 0.3
        }
    }
    
    // MARK: - Status Evaluations
    
    func evaluateNutritionStatus(for pet: Pet) -> NutritionStatus {
        let hasCurrentFood = pet.currentFood != nil
        let hasSchedule = !pet.feedingSchedule.isEmpty
        let hasWaterTracking = pet.waterIntakeML != nil
        
        if hasCurrentFood && hasSchedule && hasWaterTracking {
            return .excellent
        } else if hasCurrentFood || hasSchedule {
            return .good
        } else {
            return .needsImprovement
        }
    }
    
    func evaluateTrainingStatus(for pet: Pet) -> TrainingStatus {
        let hasTrainingLevel = pet.trainingLevel != nil
        let hasCommands = !pet.knownCommands.isEmpty
        let hasBehaviorManagement = !pet.behaviorIssues.isEmpty
        
        if hasTrainingLevel && hasCommands && !hasBehaviorManagement {
            return .advanced
        } else if hasTrainingLevel || hasCommands {
            return .inProgress
        } else {
            return .notStarted
        }
    }
    
    func evaluateExerciseStatus(for pet: Pet) -> ExerciseStatus {
        if let exerciseMinutes = pet.exerciseMinutesDaily {
            if exerciseMinutes >= 60 {
                return .veryActive
            } else if exerciseMinutes >= 30 {
                return .active
            } else if exerciseMinutes >= 15 {
                return .moderate
            } else {
                return .lowActivity
            }
        }
        return .inactive
    }
    
    @available(iOS 18.0, *)
    func evaluateVaccinationStatus(for pet: Pet) -> PetVaccinationStatus {
        let vaccinationCount = pet.vaccinationRecords.count
        let age = pet.age

        let isUpToDate = vaccinationCount >= 3 && age > 12
        let lastVaccination = pet.vaccinationRecords.last
        let nextDue = lastVaccination?.nextDueDate
        let overdue: [String] = isUpToDate ? [] : ["Core vaccinations needed"]

        return PetVaccinationStatus(
            isUpToDate: isUpToDate,
            lastVaccination: nil, // Would need to convert VaccinationRecord to PetHealthRecord
            nextDue: nextDue,
            overdue: overdue
        )
    }
    
    // MARK: - Comprehensive AI Summary
    
    func buildComprehensiveAISummary(for pet: Pet) -> String {
        return """
        [COMPREHENSIVE PET PROFILE - \(pet.name)]
        ══════════════════════════════════════════════════════════
        
        📋 BASIC INFO:
        • Name: \(pet.name) | Species: \(pet.species.capitalized) | Breed: \(pet.breed ?? "Mixed")
        • Age: \(pet.age) years | Weight: \(pet.weight?.formatted(.number.precision(.fractionLength(1))) ?? "Unknown") kg
        • Gender: \(pet.gender ?? "Unknown") | Spayed/Neutered: \(pet.isSpayedNeutered ? "Yes" : "No")
        • Microchip: \(pet.microchipId ?? "Not set")
        
        🏥 HEALTH STATUS:
        • Overall Health Score: \(Int(selectedPetHealthScore * 100))%
        • Active Health Alerts: \(selectedPetHealthAlerts)
        • Chronic Conditions: \(pet.chronicConditions.count)
        • Current Medications: \(pet.medications.count)
        • Allergies: \(pet.allergies.isEmpty ? "None known" : pet.allergies.joined(separator: ", "))
        
        🍽️ NUTRITION:
        • Current Food: \(pet.currentFood ?? "Not specified")
        • Feeding Schedule: \(pet.feedingSchedule.isEmpty ? "Not set" : "\(pet.feedingSchedule.count) feeding times")
        • Water Intake: \(pet.waterIntakeML?.formatted() ?? "Not tracked") ml/day
        • Dietary Restrictions: \(pet.dietaryRestrictions.isEmpty ? "None" : pet.dietaryRestrictions.joined(separator: ", "))
        
        🏃 EXERCISE & ACTIVITY:
        • Daily Exercise: \(pet.exerciseMinutesDaily?.formatted() ?? "Not tracked") minutes
        • Walking Frequency: \(pet.walkingFrequency ?? "Not set")
        • Activity Level: \(pet.activityLevel.capitalized)
        • Favorite Activities: \(pet.favoriteActivities.isEmpty ? "None listed" : pet.favoriteActivities.joined(separator: ", "))
        
        🎓 TRAINING:
        • Training Level: \(pet.trainingLevel ?? "Not assessed")
        • Known Commands: \(pet.knownCommands.isEmpty ? "None listed" : pet.knownCommands.joined(separator: ", "))
        • Behavior Issues: \(pet.behaviorIssues.isEmpty ? "None reported" : pet.behaviorIssues.joined(separator: ", "))
        • Social Behavior: \(pet.socialBehavior ?? "Not assessed")
        
        🩺 VETERINARY:
        • Veterinarian: \(pet.veterinarianInfo?.displayName ?? "Not set")
        • Insurance: \(pet.insuranceInfo?.displayProvider ?? "Not set")
        • Last Checkup: \(pet.lastCheckupDate?.formatted(date: .abbreviated, time: .omitted) ?? "Not recorded")
        • Vaccinations: \(pet.vaccinationRecords.count) on record
        
        📱 ADDITIONAL INFO:
        • Personality: \(pet.personalityTraits.joined(separator: ", "))
        • Special Instructions: \(pet.specialInstructions ?? "None")
        • Emergency Contacts: \(pet.emergencyContacts.count) contacts
        """
    }
    
    // MARK: - Pet Management
    func updatePet(_ pet: Pet) async -> Bool {
        isLoading = true
        defer { isLoading = false }
        
        // Simplified implementation for now
        print("Updating pet: \(pet.name)")
        return true
    }
    
    func createPet(_ pet: Pet) async -> Bool {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // Use the simplified createPet method signature
            let newPet = try await dataService.createPet(
                name: pet.name,
                species: pet.species,
                breed: pet.breed,
                for: pet.ownerID != nil ? User(id: pet.ownerID!, email: "", displayName: "") : User(id: UUID().uuidString, email: "", displayName: "")
            )
            
            pets.append(newPet)
            print("✅ Created pet: \(newPet.name)")
            return true
        } catch {
            errorMessage = "Failed to create pet: \(error.localizedDescription)"
            return false
        }
    }
    
    func deletePet(_ pet: Pet) async -> Bool {
        isLoading = true
        defer { isLoading = false }
        
        do {
            try await dataService.deletePet(pet)
            if let index = pets.firstIndex(where: { $0.id == pet.id }) {
                pets.remove(at: index)
            }
            print("✅ Deleted pet: \(pet.name)")
            return true
        } catch {
            errorMessage = "Failed to delete pet: \(error.localizedDescription)"
            return false
        }
    }
    
    // MARK: - Notification Methods
    
    private func notifyDataUpdate() {
        // Notify all dependent services of data changes
        NotificationCenter.default.post(
            name: .petDataUpdated,
            object: nil,
            userInfo: [
                "timestamp": Date(),
                "selectedPet": selectedPet?.id ?? ""
            ]
        )
    }
    
    private func notifyPetSelectionChanged() {
        // Notify all dependent services of pet selection changes
        NotificationCenter.default.post(
            name: .petSelectionChanged,
            object: nil,
            userInfo: [
                "selectedPet": selectedPet?.id ?? "",
                "selectedPetIndex": selectedPetIndex
            ]
        )
    }
    
    // MARK: - Utility Methods
    
    private func getCurrentUserId() -> UUID? {
        guard let userId = dataService.currentUser?.id else { return nil }
        return UUID(uuidString: userId)
    }
    
    func getPetById(_ id: String) -> Pet? {
        return pets.first { $0.id == id }
    }
    
    func getPetsBySpecies(_ species: String) -> [Pet] {
        return pets.filter { $0.species.lowercased() == species.lowercased() }
    }
    
    func getPetsByHealthStatus(_ needsAttention: Bool) -> [Pet] {
        return pets.filter { pet in
            let hasActiveAlerts = !pet.healthAlerts.filter { $0.isActive }.isEmpty
            return needsAttention ? hasActiveAlerts : !hasActiveAlerts
        }
    }
}

// MARK: - Status Enums

enum NutritionStatus: String, CaseIterable, Sendable {
    case excellent = "Excellent"
    case good = "Good"
    case needsImprovement = "Needs Improvement"
    case notSet = "Not Set"
    
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .needsImprovement: return .orange
        case .notSet: return .gray
        }
    }
}

enum TrainingStatus: String, CaseIterable, Sendable {
    case advanced = "Advanced"
    case inProgress = "In Progress"
    case notStarted = "Not Started"
    
    var color: Color {
        switch self {
        case .advanced: return .green
        case .inProgress: return .blue
        case .notStarted: return .orange
        }
    }
}

enum ExerciseStatus: String, CaseIterable, Sendable {
    case veryActive = "Very Active"
    case active = "Active"
    case moderate = "Moderate"
    case lowActivity = "Low Activity"
    case inactive = "Inactive"
    
    var color: Color {
        switch self {
        case .veryActive: return .green
        case .active: return .mint
        case .moderate: return .blue
        case .lowActivity: return .orange
        case .inactive: return .red
        }
    }
}



// MARK: - Notification Names

extension Notification.Name {
    static let petDataUpdated = Notification.Name("PetDataUpdated")
    static let petSelectionChanged = Notification.Name("PetSelectionChanged")
} 