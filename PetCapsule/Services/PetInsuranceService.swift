//
//  PetInsuranceService.swift
//  PetCapsule
//
//  Service for managing pet insurance information
//

import Foundation
import SwiftUI

@MainActor
class PetInsuranceService: ObservableObject {
    static let shared = PetInsuranceService()
    
    @Published var availablePlans: [InsurancePlan] = []
    @Published var currentPolicy: InsurancePolicy?
    @Published var claims: [InsuranceClaim] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private init() {
        loadMockData()
    }
    
    private func loadMockData() {
        // Mock insurance plans
        availablePlans = [
            InsurancePlan(
                id: UUID(),
                name: "Essential Plan",
                provider: "PetSure Insurance",
                monthlyPrice: 29.99,
                annualLimit: 5000,
                deductible: 250,
                reimbursementPercentage: 80,
                features: [
                    "Accidents & Illnesses",
                    "Emergency Care",
                    "Diagnostic Tests",
                    "Prescription Medications"
                ],
                websiteURL: "https://petsure.com"
            ),
            
            InsurancePlan(
                id: UUID(),
                name: "Complete Care",
                provider: "PetGuard",
                monthlyPrice: 49.99,
                annualLimit: 10000,
                deductible: 200,
                reimbursementPercentage: 90,
                features: [
                    "Accidents & Illnesses",
                    "Emergency Care",
                    "Wellness Exams",
                    "Vaccinations",
                    "Dental Care",
                    "Prescription Medications"
                ],
                websiteURL: "https://petguard.com"
            ),
            
            InsurancePlan(
                id: UUID(),
                name: "Premium Protection",
                provider: "TotalPet Insurance",
                monthlyPrice: 79.99,
                annualLimit: 25000,
                deductible: 100,
                reimbursementPercentage: 95,
                features: [
                    "Accidents & Illnesses",
                    "Emergency Care",
                    "Wellness Exams",
                    "Vaccinations",
                    "Dental Care",
                    "Prescription Medications",
                    "Alternative Therapies",
                    "Behavioral Therapy",
                    "Hereditary Conditions"
                ],
                websiteURL: "https://totalpet.com"
            )
        ]
        
        // Mock current policy (uncomment to simulate having a policy)
        // currentPolicy = InsurancePolicy(
        //     id: UUID(),
        //     policyNumber: "PG-2024-001234",
        //     planName: "Complete Care",
        //     provider: "PetGuard",
        //     monthlyPremium: 49.99,
        //     deductible: 200,
        //     annualLimit: 10000,
        //     status: .active,
        //     startDate: Calendar.current.date(byAdding: .month, value: -6, to: Date()) ?? Date(),
        //     nextPaymentDate: Calendar.current.date(byAdding: .month, value: 1, to: Date()) ?? Date()
        // )
        
        // Mock claims
        claims = []
    }
}

// MARK: - Data Models

struct InsurancePlan: Identifiable {
    let id: UUID
    let name: String
    let provider: String
    let monthlyPrice: Double
    let annualLimit: Int
    let deductible: Int
    let reimbursementPercentage: Double
    let features: [String]
    let websiteURL: String
}

struct InsurancePolicy: Identifiable {
    let id: UUID
    let policyNumber: String
    let planName: String
    let provider: String
    let monthlyPremium: Double
    let deductible: Int
    let annualLimit: Int
    let status: PolicyStatus
    let startDate: Date
    let nextPaymentDate: Date
}

enum PolicyStatus: String, CaseIterable {
    case active = "active"
    case pending = "pending"
    case expired = "expired"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .active:
            return "Active"
        case .pending:
            return "Pending"
        case .expired:
            return "Expired"
        case .cancelled:
            return "Cancelled"
        }
    }
    
    var color: Color {
        switch self {
        case .active:
            return .green
        case .pending:
            return .orange
        case .expired:
            return .red
        case .cancelled:
            return .gray
        }
    }
}

struct InsuranceClaim: Identifiable {
    let id: UUID
    let claimNumber: String
    let description: String
    let amount: Double
    let status: ClaimStatus
    let submittedDate: Date
    let processedDate: Date?
}

enum ClaimStatus: String, CaseIterable {
    case submitted = "submitted"
    case underReview = "under_review"
    case approved = "approved"
    case denied = "denied"
    case paid = "paid"
    
    var displayName: String {
        switch self {
        case .submitted:
            return "Submitted"
        case .underReview:
            return "Under Review"
        case .approved:
            return "Approved"
        case .denied:
            return "Denied"
        case .paid:
            return "Paid"
        }
    }
    
    var color: Color {
        switch self {
        case .submitted:
            return .blue
        case .underReview:
            return .orange
        case .approved:
            return .green
        case .denied:
            return .red
        case .paid:
            return .purple
        }
    }
}
