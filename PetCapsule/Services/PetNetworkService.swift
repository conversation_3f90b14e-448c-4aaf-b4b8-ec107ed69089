//
//  PetNetworkService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//
import Foundation
import SwiftUI
import CoreLocation
// Type aliases to resolve ambiguity - using shared types
typealias NetworkCommunityPost = SharedCommunityPost
typealias NetworkSharedPostVisibility = SharedSharedPostVisibility
typealias NetworkPlaydate = SharedPlaydate
typealias NetworkConnectionType = ConnectionType
// typealias SharedPostVisibility = SharedSharedPostVisibility // Conflicts with SharedTypes.swift
class PetNetworkService: ObservableObject {
    static let shared = PetNetworkService()
    @Published var nearbyPets: [PetProfile] = []
    @Published var littermates: [PetProfile] = []
    @Published var breedCommunity: [PetProfile] = []
    @Published var friendRequests: [FriendRequest] = []
    @Published var petFriends: [PetFriendship] = []
    @Published var communityPosts: [NetworkCommunityPost] = []
    private init() {}
    // MARK: - Pet Discovery
    func discoverNearbyPets(location: CLLocation, radius: Double = 10.0) async throws {
        // In a real implementation, this would use location-based queries
        let mockNearbyPets = await loadNearbyPetsFromDatabase(location: location)
        await MainActor.run {
            self.nearbyPets = mockNearbyPets
        }
    }
    func findLittermates(for pet: Pet) async throws {
        // For now, use breed and age to find potential littermates
        // In future, implement actual lineage tracking
        let littermates = try await searchLittermates(breed: pet.breed ?? "", age: pet.age)
        await MainActor.run {
            self.littermates = littermates
        }
    }
    func findBreedCommunity(for breed: String) async throws {
        let breedPets = try await searchByBreed(breed: breed)
        await MainActor.run {
            self.breedCommunity = breedPets
        }
    }
    // MARK: - Friend Management
    func sendFriendRequest(to petId: UUID, from currentPetId: UUID, message: String = "") async throws {
        let _ = FriendRequest(
            id: UUID(),
            fromPetId: currentPetId,
            toPetId: petId,
            message: message,
            status: .pending,
            createdAt: Date()
        )
        await MainActor.run {
            // Add to mock data for demo
        }
    }
    func acceptFriendRequest(_ request: FriendRequest) async throws {
        let friendship = PetFriendship(
            id: UUID(),
            pet1Id: request.fromPetId,
            pet2Id: request.toPetId,
            connectionType: .friend,
            establishedAt: Date(),
            sharedMemories: []
        )
        await MainActor.run {
            self.petFriends.append(friendship)
            self.friendRequests.removeAll { $0.id == request.id }
        }
    }
    func declineFriendRequest(_ request: FriendRequest) async throws {
        await MainActor.run {
            self.friendRequests.removeAll { $0.id == request.id }
        }
    }
    // MARK: - Community Features
    func shareMemoryToCommunity(_ memory: Memory, caption: String, visibility: NetworkSharedPostVisibility) async throws {
        let post = NetworkCommunityPost(
            id: UUID().uuidString,
            authorId: "current_user", // Using Apple native user system
            authorName: "Current User",
            content: caption,
            createdAt: Date(),
            likesCount: 0,
            commentsCount: 0
        )
        await MainActor.run {
            self.communityPosts
        }
    }
    func loadCommunityFeed(for userId: String) async throws {
        // Load posts from friends and community
        let posts = try await fetchCommunityPosts(userId: userId)
        await MainActor.run {
            self.communityPosts = posts
        }
    }
    func likePost(_ postId: String) async throws {
        await MainActor.run {
            if let index = self.communityPosts.firstIndex(where: { $0.id == postId }) {
                self.communityPosts[index].likesCount += 1
            }
        }
    }
    func addComment(to postId: String, comment: String, authorId: String) async throws {
        let newComment = NetworkPostComment(
            id: UUID().uuidString,
            postId: postId,
            authorId: authorId,
            content: comment,
            createdAt: Date()
        )
        await MainActor.run {
            if let index = self.communityPosts.firstIndex(where: { $0.id == postId }) {
                self.communityPosts[index].commentsCount += 1
            }
        }
    }
    // MARK: - Playdates & Events
    func createPlaydate(
        title: String,
        description: String,
        location: String,
        dateTime: Date,
        maxParticipants: Int,
        invitedPets: [UUID]
    ) async throws -> NetworkPlaydate {
        let playdate = NetworkPlaydate(
            id: UUID().uuidString,
            organizerId: "current_user", // Using Apple native user system
            title: title,
            description: description,
            scheduledDate: dateTime,
            location: location,
            status: .pending
        )
        // Send invitations
        for petId in invitedPets {
            try await sendPlaydateInvitation(playdateId: playdate.id, petId: petId)
        }
        return playdate
    }
    func joinPlaydate(_ playdateId: UUID, petId: UUID) async throws {
        // Add pet to playdate participants
    }
    // MARK: - Memorial Support
    func createMemorialTribute(for pet: Pet, message: String, isPublic: Bool) async throws {
        let _ = NetworkMemorialTribute(
            id: UUID(),
            petId: UUID(uuidString: pet.id) ?? UUID(),
            authorId: "current_user", // Using Apple native user system
            message: message,
            isPublic: isPublic,
            condolences: [],
            createdAt: Date()
        )
        // Save to database and notify community if public
    }
    func sendCondolence(to tributeId: UUID, message: String, authorId: String) async throws {
        let _ = NetworkCondolence(
            id: UUID(),
            tributeId: tributeId,
            authorId: authorId,
            message: message,
            createdAt: Date()
        )
        // Save condolence message
    }
    // MARK: - Private Helper Methods
    private func loadNearbyPetsFromDatabase(location: CLLocation) async -> [PetProfile] {
        // TODO: Implement real location-based pet discovery
        // This would query the database for pets within a certain radius
        // For now, return empty array until feature is fully implemented
        print("🔍 Loading nearby pets from database for location: \(location)")
        return []
    }
    private func searchLittermates(breed: String, age: Int) async throws -> [PetProfile] {
        // Search for pets with similar breed and age (potential littermates)
        // This would involve complex genetic matching algorithms in a real implementation
        return []
    }
    private func searchByBreed(breed: String) async throws -> [PetProfile] {
        // Search for pets of the same breed in the community
        return generateMockBreedCommunity(breed: breed)
    }
    private func generateMockBreedCommunity(breed: String) -> [PetProfile] {
        return [
            PetProfile(
                id: UUID(),
                name: "Charlie",
                breed: breed,
                age: 4,
                profileImageURL: nil,
                ownerName: "Alex Thompson",
                distance: 5.0,
                isOnline: true
            ),
            PetProfile(
                id: UUID(),
                name: "Bella",
                breed: breed,
                age: 2,
                profileImageURL: nil,
                ownerName: "Jessica Wilson",
                distance: 8.5,
                isOnline: false
            )
        ]
    }
    private func fetchCommunityPosts(userId: String) async throws -> [NetworkCommunityPost] {
        return generateMockCommunityPosts()
    }
    private func generateMockCommunityPosts() -> [NetworkCommunityPost] {
        return [
            NetworkCommunityPost(
                id: UUID().uuidString,
                authorId: "user1",
                authorName: "Sarah Johnson",
                content: "Beautiful day at the park with Luna! 🌞",
                createdAt: Date().addingTimeInterval(-7200),
                likesCount: 12,
                commentsCount: 3
            )
        ]
    }
    private func sendPlaydateInvitation(playdateId: String, petId: UUID) async throws {
        // Send playdate invitation notification
    }
}
// MARK: - Data Models
struct PetProfile: Identifiable {
    let id: UUID
    let name: String
    let breed: String
    let age: Int
    let profileImageURL: String?
    let ownerName: String
    let distance: Double // in kilometers
    let isOnline: Bool
}
struct FriendRequest: Identifiable {
    let id: UUID
    let fromPetId: UUID
    let toPetId: UUID
    let message: String
    let status: FriendRequestStatus
    let createdAt: Date
}
enum FriendRequestStatus: String, CaseIterable {
    case pending = "pending"
    case accepted = "accepted"
    case declined = "declined"
}
struct PetFriendship: Identifiable {
    let id: UUID
    let pet1Id: UUID
    let pet2Id: UUID
    let connectionType: NetworkConnectionType
    let establishedAt: Date
    var sharedMemories: [UUID]
}
// These types are now defined in SharedTypes.swift
struct NetworkMemorialTribute: Identifiable {
    let id: UUID
    let petId: UUID
    let authorId: String
    let message: String
    let isPublic: Bool
    var condolences: [NetworkCondolence]
    let createdAt: Date
}
struct NetworkCondolence: Identifiable {
    let id: UUID
    let tributeId: UUID
    let authorId: String
    let message: String
    let createdAt: Date
}
struct NetworkPostComment: Identifiable {
    let id: String
    let postId: String
    let authorId: String
    let content: String
    let createdAt: Date
}
