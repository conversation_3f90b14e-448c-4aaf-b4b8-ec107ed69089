//
//  PetPlannerService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//
import Foundation
import CoreLocation
import SwiftUI
// Using SharedWalkRecommendation directly to avoid redeclaration
@MainActor
class PetPlannerService: ObservableObject {
    static let shared = PetPlannerService()
    // Published properties for real-time updates
    @Published var currentWeather: WeatherData = WeatherData.sample
    @Published var airQuality: AirQualityData = AirQualityData.sample
    @Published var walkRecommendation: SharedWalkRecommendation = SharedWalkRecommendation(
        id: UUID().uuidString,
        petId: "",
        recommendedTime: Date(),
        duration: 1800, // 30 minutes
        route: "Local Park",
        score: 8.2,
        reasoning: "Weather conditions are favorable for outdoor activities."
    )
    @Published var hourlyForecast: [HourlyForecast] = HourlyForecast.sampleData
    @Published var weeklyForecast: [WeeklyForecast] = WeeklyForecast.sampleData
    @Published var nearbyLocations: [PetFriendlyLocation] = []
    @Published var environmentalAlerts: [EnvironmentalAlertUI] = []
    @Published var walkMemories: [WalkMemory] = []
    @Published var communityEvents: [CommunityEvent] = []
    // Loading states
    @Published var isLoadingWeather = false
    @Published var isLoadingLocations = false
    @Published var isLoadingAlerts = false
    // Services - Apple-first approach
    private let mapService = AppleMapService.shared
    private let appleWeatherService = AppleWeatherService.shared
    private let locationManager = CLLocationManager()
    private var locationDelegate: LocationDelegate?
    // Current location
    @Published var currentLocation: CLLocationCoordinate2D?
    private init() {
        setupLocationManager()
        loadInitialData()
    }
    // MARK: - Location Management
    private func setupLocationManager() {
        locationDelegate = LocationDelegate(service: self)
        locationManager.delegate = locationDelegate
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        requestLocationPermission()
    }
    func requestLocationPermission() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            // Use default location (San Francisco)
            currentLocation = CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)
            loadAllData()
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.requestLocation()
        @unknown default:
            break
        }
    }
    func updateLocation(_ location: CLLocationCoordinate2D) {
        currentLocation = location
        loadAllData()
    }
    // MARK: - Data Loading
    private func loadInitialData() {
        // Load sample data initially
        nearbyLocations = PetFriendlyLocation.sampleData
        environmentalAlerts = []
        // If we have location, load real data
        if currentLocation != nil {
            loadAllData()
        }
    }
    func loadAllData() {
        guard let location = currentLocation else { return }
        Task {
            await loadWeatherData(for: location)
            await loadNearbyLocations(for: location)
            await loadEnvironmentalAlerts(for: location)
        }
    }
    func refreshAllData() {
        guard currentLocation != nil else { return }
        loadAllData()
    }
    // MARK: - Weather Data
    private func loadWeatherData(for location: CLLocationCoordinate2D) async {
        isLoadingWeather = true
        do {
            // Load current weather using Apple WeatherKit
            let weather = try await appleWeatherService.getCurrentWeather(for: location)
            let airQualityData = try await appleWeatherService.getAirQuality(for: location)
            let hourlyData = try await appleWeatherService.getHourlyForecast(for: location)
            // Load pollen data from Google (not available in Apple frameworks)
            let pollenData = try await appleWeatherService.getPollenData(for: location)
            // Generate enhanced walk recommendation with pollen data
            let plannerRecommendation = appleWeatherService.generateWalkRecommendation(
                weather: weather,
                airQuality: airQualityData,
                pollen: pollenData
            )
            // Convert to SharedWalkRecommendation
            let recommendation = SharedWalkRecommendation(
                id: UUID().uuidString,
                petId: "",
                recommendedTime: Date(),
                duration: TimeInterval(plannerRecommendation.duration),
                route: plannerRecommendation.route,
                score: plannerRecommendation.overallScore,
                reasoning: plannerRecommendation.reasoning
            )
            // Update UI on main thread
            await MainActor.run {
                self.currentWeather = weather
                self.airQuality = airQualityData
                self.walkRecommendation = recommendation
                self.hourlyForecast = hourlyData
                self.isLoadingWeather = false
            }
            print("✅ Weather data loaded successfully")
        } catch {
            print("❌ Failed to load weather data: \(error)")
            await MainActor.run {
                self.isLoadingWeather = false
            }
        }
    }
    // MARK: - Location Data
    private func loadNearbyLocations(for location: CLLocationCoordinate2D) async {
        isLoadingLocations = true
        do {
            let locations = try await mapService.searchPetFriendlyPlaces(near: location)
            await MainActor.run {
                self.nearbyLocations = locations.isEmpty ? PetFriendlyLocation.sampleData : locations
                self.isLoadingLocations = false
            }
            print("✅ Loaded \(locations.count) nearby locations")
        } catch {
            print("❌ Failed to load nearby locations: \(error)")
            await MainActor.run {
                self.nearbyLocations = PetFriendlyLocation.sampleData
                self.isLoadingLocations = false
            }
        }
    }
    // MARK: - Environmental Alerts
    private func loadEnvironmentalAlerts(for location: CLLocationCoordinate2D) async {
        isLoadingAlerts = true
        // Generate alerts based on current conditions
        var alerts: [EnvironmentalAlertUI] = []
        // Check air quality
        if airQuality.index > 100 {
            alerts.append(EnvironmentalAlertUI(
                title: "Poor Air Quality",
                message: "Air quality is unhealthy. Consider limiting outdoor activities for sensitive pets.",
                severity: .high,
                icon: "wind.circle.fill",
                timeAgo: "Now"
            ))
        } else if airQuality.index > 50 {
            alerts.append(EnvironmentalAlertUI(
                title: "Moderate Air Quality",
                message: "Air quality is moderate. Sensitive pets should limit prolonged outdoor activities.",
                severity: .moderate,
                icon: "wind.circle.fill",
                timeAgo: "Now"
            ))
        }
        // Check temperature
        if currentWeather.temperature > 85 {
            alerts.append(EnvironmentalAlertUI(
                title: "High Temperature Warning",
                message: "Temperature is high. Ensure your pet has access to water and shade.",
                severity: .warning,
                icon: "thermometer.sun.fill",
                timeAgo: "Now"
            ))
        } else if currentWeather.temperature < 32 {
            alerts.append(EnvironmentalAlertUI(
                title: "Cold Temperature Alert",
                message: "Temperature is very cold. Consider shorter walks and protective gear.",
                severity: .warning,
                icon: "thermometer.snowflake",
                timeAgo: "Now"
            ))
        }
        // Check humidity
        if currentWeather.humidity > 80 {
            alerts.append(EnvironmentalAlertUI(
                title: "High Humidity",
                message: "Humidity levels are high. Watch for signs of overheating in your pet.",
                severity: .moderate,
                icon: "humidity.fill",
                timeAgo: "Now"
            ))
        }
        await MainActor.run {
            self.environmentalAlerts = alerts
            self.isLoadingAlerts = false
        }
        print("✅ Generated \(alerts.count) environmental alerts")
    }
    // MARK: - Walk Memory Management
    func addWalkMemory(_ memory: WalkMemory) {
        walkMemories
        saveWalkMemories()
    }
    func toggleFavorite(for memoryId: UUID) {
        print("Toggle favorite for memory: \(memoryId)")
    }
    private func saveWalkMemories() {
        // Save to UserDefaults or Core Data
        // For now, just keep in memory
    }
    // MARK: - Community Events Management
    func joinEvent(_ eventId: UUID) {
        print("Join event: \(eventId)")
    }
    func leaveEvent(_ eventId: UUID) {
        print("Leave event: \(eventId)")
    }
}
// MARK: - Location Delegate
class LocationDelegate: NSObject, CLLocationManagerDelegate {
    weak var service: PetPlannerService?
    init(service: PetPlannerService) {
        self.service = service
    }
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        Task { @MainActor in
            service?.updateLocation(location.coordinate)
        }
    }
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("❌ Location error: \(error)")
        Task { @MainActor in
            // Use default location (San Francisco)
            service?.updateLocation(CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194))
        }
    }
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        Task { @MainActor in
            service?.requestLocationPermission()
        }
    }
}
