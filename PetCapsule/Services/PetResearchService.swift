//
//  PetResearchService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import SwiftUI
import Combine

// ResearchKit is not available in iOS Simulator
// This is a forward-looking implementation for when ResearchKit becomes available
#if canImport(ResearchKit)
import ResearchKit
#endif

@MainActor
class PetResearchService: ObservableObject {
    static let shared = PetResearchService()
    
    @Published var availableStudies: [PetResearchStudy] = []
    @Published var participatingStudies: [PetResearchStudy] = []
    @Published var completedSurveys: [SurveyResult] = []
    @Published var isLoading = false
    
    private init() {
        loadAvailableStudies()
    }
    
    // MARK: - Study Management
    
    func loadAvailableStudies() {
        availableStudies = [
            PetResearchStudy(
                id: UUID(),
                title: "Pet Nutrition and Longevity Study",
                description: "Help researchers understand the relationship between diet and pet lifespan",
                institution: "Veterinary Research Institute",
                duration: "6 months",
                requirements: ["Dog or cat", "Age 1-15 years", "Regular vet checkups"],
                benefits: ["Free nutritional analysis", "Health insights", "Contribution to science"],
                isActive: true,
                participantCount: 2847
            ),
            PetResearchStudy(
                id: UUID(),
                title: "Canine Behavior and Exercise Study",
                description: "Research the impact of exercise on dog behavior and mental health",
                institution: "Animal Behavior Research Center",
                duration: "3 months",
                requirements: ["Dogs only", "Age 6 months - 10 years", "Daily activity tracking"],
                benefits: ["Personalized exercise plan", "Behavior insights", "$50 gift card"],
                isActive: true,
                participantCount: 1523
            ),
            PetResearchStudy(
                id: UUID(),
                title: "Feline Health Monitoring Study",
                description: "Study early detection methods for common feline health issues",
                institution: "Feline Health Foundation",
                duration: "12 months",
                requirements: ["Cats only", "Age 2+ years", "Monthly health photos"],
                benefits: ["Early health alerts", "Vet consultation credits", "Research updates"],
                isActive: true,
                participantCount: 892
            )
        ]
    }
    
    func joinStudy(_ study: PetResearchStudy) {
        participatingStudies.append(study)
        // Remove from available studies
        availableStudies.removeAll { $0.id == study.id }
    }
    
    func leaveStudy(_ study: PetResearchStudy) {
        participatingStudies.removeAll { $0.id == study.id }
        // Add back to available studies
        availableStudies.append(study)
    }
    
    // MARK: - Survey Creation

    #if canImport(ResearchKit)
    func createConsentTask(for study: PetResearchStudy) -> ORKOrderedTask {
        // Consent sections
        let consentSections = [
            ORKConsentSection(type: .overview),
            ORKConsentSection(type: .dataGathering),
            ORKConsentSection(type: .privacy),
            ORKConsentSection(type: .dataUse),
            ORKConsentSection(type: .timeCommitment),
            ORKConsentSection(type: .studyTasks),
            ORKConsentSection(type: .withdrawing)
        ]
        
        // Configure consent sections
        consentSections[0].title = "Welcome to \(study.title)"
        consentSections[0].content = study.description
        
        consentSections[1].title = "Data Collection"
        consentSections[1].content = "We will collect information about your pet's health, behavior, and activities through the PetCapsule app."
        
        consentSections[2].title = "Privacy Protection"
        consentSections[2].content = "Your pet's data will be anonymized and securely stored. Personal information will never be shared."
        
        consentSections[3].title = "How Data is Used"
        consentSections[3].content = "Data will be used solely for research purposes to improve pet health and care."
        
        consentSections[4].title = "Time Commitment"
        consentSections[4].content = "This study requires approximately 5-10 minutes per week for \(study.duration)."
        
        consentSections[5].title = "Study Activities"
        consentSections[5].content = "You'll complete brief surveys and share anonymized pet health data."
        
        consentSections[6].title = "Withdrawal"
        consentSections[6].content = "You can withdraw from the study at any time without penalty."
        
        // Create consent document
        let consentDocument = ORKConsentDocument()
        consentDocument.title = "Research Study Consent"
        consentDocument.sections = consentSections
        
        // Consent signature
        consentDocument.addSignature(ORKConsentSignature(
            forPersonWithTitle: "Participant",
            dateFormatString: nil,
            identifier: "participant"
        ))
        
        // Create consent steps
        let visualConsentStep = ORKVisualConsentStep(
            identifier: "visualConsent",
            document: consentDocument
        )
        
        let consentReviewStep = ORKConsentReviewStep(
            identifier: "consentReview",
            signature: consentDocument.signatures?.first,
            in: consentDocument
        )
        consentReviewStep.text = "Review and sign the consent form"
        consentReviewStep.reasonForConsent = "Consent to participate in \(study.title)"
        
        return ORKOrderedTask(
            identifier: "consentTask",
            steps: [visualConsentStep, consentReviewStep]
        )
    }
    
    func createPetProfileSurvey() -> ORKOrderedTask {
        var steps: [ORKStep] = []
        
        // Pet basic info
        let petNameStep = ORKQuestionStep(
            identifier: "petName",
            title: "Pet Information",
            question: "What is your pet's name?",
            answer: ORKTextAnswerFormat(maximumLength: 50)
        )
        steps.append(petNameStep)
        
        // Pet species
        let speciesChoices = [
            ORKTextChoice(text: "Dog", value: "dog" as NSString),
            ORKTextChoice(text: "Cat", value: "cat" as NSString),
            ORKTextChoice(text: "Other", value: "other" as NSString)
        ]
        let speciesStep = ORKQuestionStep(
            identifier: "petSpecies",
            title: "Pet Species",
            question: "What type of pet do you have?",
            answer: ORKTextChoiceAnswerFormat(style: .singleChoice, textChoices: speciesChoices)
        )
        steps.append(speciesStep)
        
        // Pet age
        let ageStep = ORKQuestionStep(
            identifier: "petAge",
            title: "Pet Age",
            question: "How old is your pet?",
            answer: ORKNumericAnswerFormat(style: .integer, unit: "years", minimum: 0, maximum: 30)
        )
        steps.append(ageStep)
        
        // Pet weight
        let weightStep = ORKQuestionStep(
            identifier: "petWeight",
            title: "Pet Weight",
            question: "What is your pet's weight?",
            answer: ORKNumericAnswerFormat(style: .decimal, unit: "lbs", minimum: 1, maximum: 200)
        )
        steps.append(weightStep)
        
        // Activity level
        let activityChoices = [
            ORKTextChoice(text: "Low - Mostly indoor, minimal exercise", value: "low" as NSString),
            ORKTextChoice(text: "Moderate - Regular walks, some playtime", value: "moderate" as NSString),
            ORKTextChoice(text: "High - Daily exercise, very active", value: "high" as NSString)
        ]
        let activityStep = ORKQuestionStep(
            identifier: "activityLevel",
            title: "Activity Level",
            question: "How would you describe your pet's activity level?",
            answer: ORKTextChoiceAnswerFormat(style: .singleChoice, textChoices: activityChoices)
        )
        steps.append(activityStep)
        
        // Health conditions
        let healthStep = ORKQuestionStep(
            identifier: "healthConditions",
            title: "Health Information",
            question: "Does your pet have any ongoing health conditions?",
            answer: ORKTextAnswerFormat(maximumLength: 500)
        )
        healthStep.isOptional = true
        steps.append(healthStep)
        
        return ORKOrderedTask(identifier: "petProfileSurvey", steps: steps)
    }
    
    func createWeeklyHealthSurvey() -> ORKOrderedTask {
        var steps: [ORKStep] = []
        
        // Overall health
        let healthScale = ORKScaleAnswerFormat(
            maximumValue: 10,
            minimumValue: 1,
            defaultValue: 5,
            step: 1,
            vertical: false,
            maximumValueDescription: "Excellent",
            minimumValueDescription: "Poor"
        )
        let healthStep = ORKQuestionStep(
            identifier: "overallHealth",
            title: "Weekly Health Check",
            question: "How would you rate your pet's overall health this week?",
            answer: healthScale
        )
        steps.append(healthStep)
        
        // Appetite
        let appetiteChoices = [
            ORKTextChoice(text: "Much less than usual", value: "much_less" as NSString),
            ORKTextChoice(text: "Slightly less than usual", value: "less" as NSString),
            ORKTextChoice(text: "Normal", value: "normal" as NSString),
            ORKTextChoice(text: "More than usual", value: "more" as NSString)
        ]
        let appetiteStep = ORKQuestionStep(
            identifier: "appetite",
            title: "Appetite",
            question: "How has your pet's appetite been this week?",
            answer: ORKTextChoiceAnswerFormat(style: .singleChoice, textChoices: appetiteChoices)
        )
        steps.append(appetiteStep)
        
        // Energy level
        let energyScale = ORKScaleAnswerFormat(
            maximumValue: 10,
            minimumValue: 1,
            defaultValue: 5,
            step: 1,
            vertical: false,
            maximumValueDescription: "Very energetic",
            minimumValueDescription: "Very lethargic"
        )
        let energyStep = ORKQuestionStep(
            identifier: "energyLevel",
            title: "Energy Level",
            question: "How energetic has your pet been this week?",
            answer: energyScale
        )
        steps.append(energyStep)
        
        // Behavioral changes
        let behaviorStep = ORKQuestionStep(
            identifier: "behaviorChanges",
            title: "Behavior",
            question: "Have you noticed any behavioral changes this week?",
            answer: ORKTextAnswerFormat(maximumLength: 300)
        )
        behaviorStep.isOptional = true
        steps.append(behaviorStep)
        
        return ORKOrderedTask(identifier: "weeklyHealthSurvey", steps: steps)
    }
    
    // MARK: - Survey Results
    
    func processSurveyResult(_ result: ORKTaskResult, for study: PetResearchStudy) {
        let surveyResult = SurveyResult(
            id: UUID(),
            studyId: study.id,
            surveyType: result.identifier,
            completedDate: Date(),
            responses: extractResponses(from: result)
        )
        
        completedSurveys.append(surveyResult)
        
        // Send to research backend (simulated)
        submitToResearchBackend(surveyResult)
    }
    
    private func extractResponses(from result: ORKTaskResult) -> [String: Any] {
        var responses: [String: Any] = [:]
        
        for stepResult in result.results ?? [] {
            if let questionResult = stepResult as? ORKQuestionResult,
               let answer = questionResult.answer {
                responses[stepResult.identifier] = answer
            }
        }
        
        return responses
    }
    
    private func submitToResearchBackend(_ result: SurveyResult) {
        // Simulate API call to research backend
        print("Submitting survey result to research backend: \(result.id)")
    }
    #else
    // Fallback implementations when ResearchKit is not available
    func createConsentTask(for study: PetResearchStudy) -> MockOrderedTask {
        return MockOrderedTask(identifier: "consentTask")
    }

    func createPetProfileSurvey() -> MockOrderedTask {
        return MockOrderedTask(identifier: "petProfileSurvey")
    }

    func createWeeklyHealthSurvey() -> MockOrderedTask {
        return MockOrderedTask(identifier: "weeklyHealthSurvey")
    }

    func processSurveyResult(_ result: MockTaskResult, for study: PetResearchStudy) {
        let surveyResult = SurveyResult(
            id: UUID(),
            studyId: study.id,
            surveyType: result.identifier,
            completedDate: Date(),
            responses: [:]
        )

        completedSurveys.append(surveyResult)
        submitToResearchBackend(surveyResult)
    }

    private func submitToResearchBackend(_ result: SurveyResult) {
        print("Submitting survey result to research backend: \(result.id)")
    }
    #endif
}

// MARK: - Mock Types for when ResearchKit is not available
#if !canImport(ResearchKit)
struct MockOrderedTask {
    let identifier: String
}

struct MockTaskResult {
    let identifier: String
}
#endif

// MARK: - Data Models

struct PetResearchStudy: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let institution: String
    let duration: String
    let requirements: [String]
    let benefits: [String]
    let isActive: Bool
    let participantCount: Int
}

struct SurveyResult: Identifiable, Codable {
    let id: UUID
    let studyId: UUID
    let surveyType: String
    let completedDate: Date
    let responses: [String: Any]
    
    enum CodingKeys: String, CodingKey {
        case id, studyId, surveyType, completedDate
    }
    
    init(id: UUID, studyId: UUID, surveyType: String, completedDate: Date, responses: [String: Any]) {
        self.id = id
        self.studyId = studyId
        self.surveyType = surveyType
        self.completedDate = completedDate
        self.responses = responses
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        studyId = try container.decode(UUID.self, forKey: .studyId)
        surveyType = try container.decode(String.self, forKey: .surveyType)
        completedDate = try container.decode(Date.self, forKey: .completedDate)
        responses = [:] // Simplified for Codable compliance
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(studyId, forKey: .studyId)
        try container.encode(surveyType, forKey: .surveyType)
        try container.encode(completedDate, forKey: .completedDate)
    }
}
