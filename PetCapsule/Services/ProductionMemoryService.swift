//
//  ProductionMemoryService.swift
//  PetCapsule
//
//  Production-ready Memory Service with real functionality
//
import Foundation
import SwiftUI
import SwiftData
import PhotosUI
import CoreML
import Vision
import NaturalLanguage
import Combine
import CloudKit
// MARK: - Production Environment Check
@available(iOS 18.0, *)
private var isProduction: Bool {
    #if DEBUG
    return false
    #else
    return true
    #endif
}
// MARK: - Apple Intelligence Integration
@available(iOS 18.0, *)
// MARK: - Memory Service Error
enum MemoryServiceError: LocalizedError, Equatable {
    case loadingFailed(String)
    case savingFailed(String)
    case uploadFailed(String)
    case processingFailed(String)
    case networkError(String)
    case invalidURL
    case thumbnailGenerationFailed
    case mediaProcessingFailed(String)
    var errorDescription: String? {
        switch self {
        case .loadingFailed(let message):
            return "Failed to load memories: \(message)"
        case .savingFailed(let message):
            return "Failed to save memory: \(message)"
        case .uploadFailed(let message):
            return "Failed to upload media: \(message)"
        case .processingFailed(let message):
            return "Failed to process memory: \(message)"
        case .networkError(let message):
            return "Network error: \(message)"
        case .invalidURL:
            return "Invalid media URL"
        case .thumbnailGenerationFailed:
            return "Failed to generate thumbnail"
        case .mediaProcessingFailed(let message):
            return "Media processing failed: \(message)"
        }
    }
}
import AVFoundation
import Photos
@available(iOS 18.0, *)
@MainActor
class ProductionMemoryService: ObservableObject {
    // MARK: - Singleton
    static let shared = ProductionMemoryService()
    // MARK: - Properties
    @Published var memories: [Memory] = []
    @Published var isLoading = false
    @Published var error: MemoryServiceError?
    // Services
    private let storageService = AppleNativeStorageService.shared
    // Removed AppleIntelligenceService to avoid actor isolation issues
    private var realDataService: RealDataService?
    // MARK: - Initialization
    init() {
        // Don't auto-load on init to prevent infinite loops
        // loadMemories() will be called when needed
    }
    // MARK: - Configuration
    func setRealDataService(_ service: RealDataService) {
        self.realDataService = service
        print("🔗 ProductionMemoryService connected to RealDataService")
        // Sync memories immediately if RealDataService has data
        if !service.memories.isEmpty {
            self.memories = service.memories
            print("✅ Synced \(self.memories.count) memories from RealDataService")
        } else {
            print("📝 RealDataService has no memories, will load when needed")
        }
    }
    // MARK: - Memory Loading
    func loadMemories() {
        // Prevent multiple simultaneous loads
        guard !isLoading else {
            print("⚠️ Already loading memories, skipping...")
            return
        }
        // Check if realDataService is available
        guard let realDataService = realDataService else {
            print("⚠️ RealDataService not set, skipping memory load")
            return
        }
        // Use existing data from realDataService if available and not empty
        if !realDataService.memories.isEmpty {
            self.memories = realDataService.memories
            print("✅ Using existing \(self.memories.count) memories from cache")
            return
        }
        // Don't load if RealDataService is already loading
        if realDataService.isLoading {
            print("⚠️ RealDataService is already loading, skipping memory load")
            return
        }
        isLoading = true
        error = nil
        print("🔄 Loading memories...")
        Task {
            guard let userId = realDataService.getCurrentUserId() else {
                print("❌ No authenticated user found")
                await MainActor.run {
                    self.isLoading = false
                    self.error = .loadingFailed("Please sign in to access your memories")
                }
                return
            }
            print("👤 Loading memories for user: \(userId)")
            await realDataService.loadUserMemories()
            await MainActor.run {
                self.memories = realDataService.memories
                self.isLoading = false
                print("✅ Successfully loaded \(self.memories.count) memories")
                // Ensure RealDataService and ProductionMemoryService stay in sync
                self.syncWithRealDataService()
            }
        }
    }
    func loadMoreMemories() async {
        guard !isLoading else {
            print("⚠️ Already loading memories, skipping...")
            return
        }
        guard realDataService != nil else {
            print("⚠️ RealDataService not set, skipping memory refresh")
            return
        }
        await MainActor.run {
            isLoading = true
        }
        // For now, just reload all memories (pagination will be implemented later)
        guard realDataService!.getCurrentUserId() != nil else {
            print("❌ No authenticated user found for refresh")
            await MainActor.run {
                self.isLoading = false
            }
            return
        }
        print("🔄 Refreshing memories...")
        await realDataService!.loadUserMemories()
        await MainActor.run {
            self.memories = realDataService!.memories
            self.isLoading = false
            print("✅ Refreshed \(self.memories.count) memories")
        }
    }
    // MARK: - Memory Creation
    func createMemory(
        title: String,
        content: String,
        type: MemoryType,
        petId: UUID,
        mediaItems: [PhotosPickerItem] = [],
        audioURL: URL? = nil
    ) async throws -> Memory {
        // isUploading = true // This property was removed
        // uploadProgress = 0.0 // This property was removed
        // processingStatus = "Creating memory..." // This property was removed
        // defer { // This block was removed
        //     Task { @MainActor in
        //         isUploading = false
        //         uploadProgress = 0.0
        //         processingStatus = ""
        //     }
        // }
        // Step 1: Create base memory
        let memory = Memory(
            title: title,
            content: content,
            type: type,
            isPublic: false
        )
        // Step 2: Process media if provided
        if !mediaItems.isEmpty {
            await updateProgress(0.2, "Processing media...")
            try await processMediaItems(mediaItems, for: memory)
        }
        if let audioURL = audioURL {
            await updateProgress(0.4, "Processing audio...")
            try await processAudioFile(audioURL, for: memory)
        }
        // Step 3: AI Analysis
        await updateProgress(0.6, "Analyzing with AI...")
        try await performAIAnalysis(for: memory)
        // Step 4: Save to database
        await updateProgress(0.8, "Saving to database...")
        guard realDataService != nil else {
            print("❌ RealDataService not set for memory creation")
            throw MemoryServiceError.savingFailed("Data service not available")
        }
        guard realDataService!.getCurrentUserId() != nil else {
            print("❌ No authenticated user found for memory creation")
            throw MemoryServiceError.savingFailed("No authenticated user found")
        }
        let success = await realDataService!.addMemory(memory)
        guard success else {
            throw MemoryServiceError.savingFailed("Failed to save memory to database")
        }
        // Step 5: Update local state
        await updateProgress(1.0, "Complete!")
        await MainActor.run {
            self.memories.insert(memory, at: 0)
            print("✅ Added memory to ProductionMemoryService: \(self.memories.count) total")
            // Also update RealDataService to keep them in sync
            if let realDataService = self.realDataService {
                realDataService.memories.insert(memory, at: 0)
                print("✅ Added memory to RealDataService: \(realDataService.memories.count) total")
            }
        }
        // Track analytics
        // TODO: Implement analytics tracking when AnalyticsService is properly configured
        print("Analytics: Memory created with type: \(type.rawValue)")
        return memory
    }
    // MARK: - Memory Management
    func saveMemory(_ memory: Memory) async throws {
        guard let realDataService = realDataService else {
            throw MemoryServiceError.savingFailed("Data service not available")
        }
        guard realDataService.getCurrentUserId() != nil else {
            throw MemoryServiceError.savingFailed("No authenticated user found")
        }
        // For now, we'll treat this as an update operation
        // In a real implementation, this would update the memory in the database
        await MainActor.run {
            if let index = self.memories.firstIndex(where: { $0.id == memory.id }) {
                self.memories[index] = memory
            } else {
                self.memories.insert(memory, at: 0)
            }
        }
        print("✅ Memory saved: \(memory.title)")
    }
    // MARK: - Media Processing
    private func processMediaItems(_ items: [PhotosPickerItem], for memory: Memory) async throws {
        for (index, item) in items.enumerated() {
            let progress = 0.2 + (Double(index) / Double(items.count)) * 0.2
            await updateProgress(progress, "Processing media \(index + 1)/\(items.count)...")
            if let data = try await item.loadTransferable(type: Data.self) {
                // Compress and optimize media
                let optimizedData = try await optimizeMediaData(data, for: item)
                let fileName = "\(memory.id)_\(index).\(item.supportedContentTypes.first?.preferredFilenameExtension ?? "jpg")"
                let uploadedURL = try await uploadMediaToStorage(data: optimizedData, fileName: fileName)
                // Set as primary media URL if first item
                if index == 0 {
                    memory.mediaURL = uploadedURL
                    // Generate thumbnail for videos
                    if item.supportedContentTypes.contains(.movie) {
                        let thumbnailURL = try await generateVideoThumbnail(from: uploadedURL)
                        memory.thumbnailURL = thumbnailURL
                    } else if item.supportedContentTypes.contains(.image) {
                        // Generate thumbnail for images
                        let thumbnailURL = try await generateImageThumbnail(from: optimizedData, fileName: "thumb_\(fileName)")
                        memory.thumbnailURL = thumbnailURL
                    }
                }
            }
        }
    }
    private func optimizeMediaData(_ data: Data, for item: PhotosPickerItem) async throws -> Data {
        if item.supportedContentTypes.contains(.image) {
            // Optimize image
            guard let image = UIImage(data: data) else { return data }
            // Resize if too large
            let maxSize: CGFloat = 1920
            let resizedImage = resizeImage(image, maxSize: maxSize)
            // Compress with quality
            return resizedImage.jpegData(compressionQuality: 0.8) ?? data
        } else if item.supportedContentTypes.contains(.movie) {
            // For videos, we'll implement compression later
            // For now, return original data
            return data
        }
        return data
    }
    private func resizeImage(_ image: UIImage, maxSize: CGFloat) -> UIImage {
        let size = image.size
        let ratio = min(maxSize / size.width, maxSize / size.height)
        if ratio >= 1 { return image }
        let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
        UIGraphicsEndImageContext()
        return resizedImage
    }
    private func generateImageThumbnail(from data: Data, fileName: String) async throws -> String {
        guard let image = UIImage(data: data) else {
            throw MemoryServiceError.thumbnailGenerationFailed
        }
        // Create thumbnail (300x300 max)
        let thumbnailImage = resizeImage(image, maxSize: 300)
        guard let thumbnailData = thumbnailImage.jpegData(compressionQuality: 0.7) else {
            throw MemoryServiceError.thumbnailGenerationFailed
        }
        return try await uploadMediaToStorage(data: thumbnailData, fileName: fileName)
    }
    private func processAudioFile(_ audioURL: URL, for memory: Memory) async throws {
        // Get audio data
        let audioData = try Data(contentsOf: audioURL)
        // Upload to storage
        let fileName = "\(memory.id)_audio.m4a"
        let uploadedURL = try await uploadMediaToStorage(data: audioData, fileName: fileName)
        memory.mediaURL = uploadedURL
        memory.type = .audio
        // Get duration
        let asset = AVAsset(url: audioURL)
        let duration = try await asset.load(.duration)
        memory.duration = CMTimeGetSeconds(duration)
    }
    // MARK: - AI Analysis
    private func performAIAnalysis(for memory: Memory) async throws {
        guard #available(iOS 18.0, *) else { return }
        let analysis = try await AIService.shared.analyzeMemory(
            title: memory.title,
            content: memory.content,
            imageData: nil as Data? // TODO: Add image data for visual analysis
        )
        memory.milestone = analysis.milestone
        memory.sentiment = analysis.sentiment
        memory.tags = analysis.tags
        // Update title if AI suggests improvement
        if analysis.suggestedTitle != memory.title && !analysis.suggestedTitle.isEmpty {
            memory.title = analysis.suggestedTitle
        }
    }
    // MARK: - Storage Operations
    private func uploadMediaToStorage(data: Data, fileName: String) async throws -> String {
        // Simple local storage implementation
        // In a real app, this would save to the local file system
        print("📁 Uploading media file: \(fileName) (\(data.count) bytes)")
        // Return a mock local file path for now
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let fileURL = documentsPath.appendingPathComponent("memories").appendingPathComponent(fileName)
        do {
            try FileManager.default.createDirectory(at: fileURL.deletingLastPathComponent(), withIntermediateDirectories: true)
            try data.write(to: fileURL)
            return fileURL.absoluteString
        } catch {
            throw MemoryServiceError.savingFailed("Failed to save media file: \(error.localizedDescription)")
        }
    }
    private func getContentType(for fileName: String) -> String {
        let fileExtension = (fileName as NSString).pathExtension.lowercased()
        switch fileExtension {
        case "jpg", "jpeg":
            return "image/jpeg"
        case "png":
            return "image/png"
        case "mp4":
            return "video/mp4"
        case "mov":
            return "video/quicktime"
        case "m4a":
            return "audio/mp4"
        case "mp3":
            return "audio/mpeg"
        default:
            return "application/octet-stream"
        }
    }
    private func generateVideoThumbnail(from videoURL: String) async throws -> String {
        guard let url = URL(string: videoURL) else {
            throw MemoryServiceError.invalidURL
        }
        let asset = AVAsset(url: url)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        let time = CMTime(seconds: 1.0, preferredTimescale: 600)
        let cgImage = try await imageGenerator.image(at: time).image
        let uiImage = UIImage(cgImage: cgImage)
        guard let thumbnailData = uiImage.jpegData(compressionQuality: 0.8) else {
            throw MemoryServiceError.thumbnailGenerationFailed
        }
        let fileName = "thumbnail_\(UUID().uuidString).jpg"
        return try await uploadMediaToStorage(data: thumbnailData, fileName: fileName)
    }
    // MARK: - Helper Methods
    private func updateProgress(_ progress: Double, _ status: String) async {
        await MainActor.run {
            // self.uploadProgress = progress // This property was removed
            // self.processingStatus = status // This property was removed
        }
    }
    private func syncWithRealDataService() {
        guard realDataService != nil else { return }
        // Ensure both services have the same memories
        if realDataService!.memories.count != self.memories.count {
            self.memories = realDataService!.memories
            print("🔄 Synced memories: ProductionMemoryService now has \(self.memories.count) memories")
        }
    }
}
