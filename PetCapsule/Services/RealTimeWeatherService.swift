//
//  RealTimeWeatherService.swift
//  PetCapsule
//
//  Enhanced real-time weather service with push notifications and environmental alerts
//

import Foundation
import CoreLocation
import UserNotifications
import WeatherKit
import Combine
import SwiftUI
import UIKit

@MainActor
class RealTimeWeatherService: ObservableObject {
    static let shared = RealTimeWeatherService()
    
    // Published properties for real-time updates
    @Published var currentWeather: WeatherData?
    @Published var airQuality: AirQualityData?
    @Published var pollenData: PollenData?
    @Published var weatherAlerts: [WeatherAlert] = []
    @Published var isMonitoring = false
    @Published var lastUpdateTime: Date?
    
    // Services
    private let appleWeatherService = AppleWeatherService.shared
    private let locationManager = CLLocationManager()
    @Published var currentLocation: CLLocation?
    private let notificationService = EnvironmentalNotificationService.shared
    
    // Real-time monitoring
    private var weatherUpdateTimer: Timer?
    private var locationSubscription: AnyCancellable?
    private var backgroundTaskIdentifier: UIBackgroundTaskIdentifier = .invalid
    
    // Configuration
    private let updateInterval: TimeInterval = 300 // 5 minutes
    private let significantChangeThreshold: Double = 5.0 // Temperature change threshold
    private let maxBackgroundTime: TimeInterval = 25.0 // Max background time (iOS limit is 30s)
    
    private init() {
        setupLocationManager()
        setupLocationSubscription()
        requestNotificationPermissions()
    }

    private func setupLocationManager() {
        locationManager.requestWhenInUseAuthorization()
        locationManager.startUpdatingLocation()
    }
    
    // MARK: - Real-time Monitoring
    
    func startRealTimeMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        startWeatherUpdateTimer()
        
        print("✅ Started real-time weather monitoring")
    }
    
    func stopRealTimeMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        stopWeatherUpdateTimer()
        endBackgroundTask()
        
        print("🛑 Stopped real-time weather monitoring")
    }
    
    private func startWeatherUpdateTimer() {
        weatherUpdateTimer = Timer.scheduledTimer(withTimeInterval: updateInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.updateWeatherData()
            }
        }
    }
    
    private func stopWeatherUpdateTimer() {
        weatherUpdateTimer?.invalidate()
        weatherUpdateTimer = nil
    }
    
    // MARK: - Weather Data Updates
    
    func updateWeatherData() async {
        guard let currentLocation = currentLocation else {
            print("❌ No current location available for weather update")
            return
        }
        
        // Start background task only if app is in background
        let appState = UIApplication.shared.applicationState
        let needsBackgroundTask = appState == .background || appState == .inactive
        
        if needsBackgroundTask {
            startBackgroundTask()
        }
        
        do {
            // Fetch all weather data concurrently with timeout
            let weatherTask = Task {
                try await appleWeatherService.getCurrentWeather(for: currentLocation.coordinate)
            }
            let airQualityTask = Task {
                try await appleWeatherService.getAirQuality(for: currentLocation.coordinate)
            }
            let pollenTask = Task {
                try await appleWeatherService.getPollenData(for: currentLocation.coordinate)
            }
            let alertsTask = Task {
                try await fetchWeatherAlerts(for: currentLocation.coordinate)
            }
            
            // Wait for all data with 20 second timeout
            async let weather = weatherTask.value
            async let airQualityData = airQualityTask.value
            async let pollen = pollenTask.value
            async let alerts = alertsTask.value
            
            let newWeather = try await weather
            let newAirQuality = try await airQualityData
            let newPollen = try await pollen
            let newAlerts = try await alerts
            
            // Check for significant changes
            let hasSignificantChange = checkForSignificantChanges(
                oldWeather: currentWeather,
                newWeather: newWeather,
                oldAirQuality: airQuality,
                newAirQuality: newAirQuality
            )
            
            // Update published properties
            currentWeather = newWeather
            airQuality = newAirQuality
            pollenData = newPollen
            weatherAlerts = newAlerts
            lastUpdateTime = Date()
            
            // Send notifications for significant changes
            if hasSignificantChange {
                await sendWeatherChangeNotification(weather: newWeather, airQuality: newAirQuality)
            }
            
            // Check for environmental alerts
            await checkEnvironmentalAlerts(weather: newWeather, airQuality: newAirQuality, pollen: newPollen)
            
            print("✅ Weather data updated successfully")
            
        } catch {
            print("❌ Failed to update weather data: \(error)")
        }
        
        // Always end background task when update completes
        if needsBackgroundTask {
            endBackgroundTask()
        }
    }
    
    private func fetchWeatherAlerts(for location: CLLocationCoordinate2D) async throws -> [WeatherAlert] {
        // In a real implementation, this would fetch weather alerts from Apple WeatherKit
        // For now, return sample alerts based on conditions
        var alerts: [WeatherAlert] = []
        
        if let weather = currentWeather {
            // Temperature alerts
            if weather.temperature > 85 {
                alerts.append(WeatherAlert(
                    id: UUID().uuidString,
                    type: .heatWarning,
                    title: "High Temperature Alert",
                    message: "Temperature is \(weather.temperature)°F. Consider shorter walks and provide extra water.",
                    severity: .moderate,
                    startTime: Date(),
                    endTime: Date().addingTimeInterval(3600 * 4) // 4 hours
                ))
            }
            
            if weather.temperature < 32 {
                alerts.append(WeatherAlert(
                    id: UUID().uuidString,
                    type: .coldWarning,
                    title: "Cold Temperature Alert",
                    message: "Temperature is \(weather.temperature)°F. Consider protective gear for your pet.",
                    severity: .moderate,
                    startTime: Date(),
                    endTime: Date().addingTimeInterval(3600 * 6) // 6 hours
                ))
            }
        }
        
        if let airQuality = airQuality, airQuality.index > 150 {
            alerts.append(WeatherAlert(
                id: UUID().uuidString,
                type: .airQuality,
                title: "Poor Air Quality",
                message: "Air quality index is \(airQuality.index). Limit outdoor activities.",
                severity: .high,
                startTime: Date(),
                endTime: Date().addingTimeInterval(3600 * 2) // 2 hours
            ))
        }
        
        return alerts
    }
    
    // MARK: - Change Detection
    
    private func checkForSignificantChanges(
        oldWeather: WeatherData?,
        newWeather: WeatherData,
        oldAirQuality: AirQualityData?,
        newAirQuality: AirQualityData
    ) -> Bool {
        
        // Temperature change
        if let oldWeather = oldWeather {
            let tempChange = abs(Double(newWeather.temperature - oldWeather.temperature))
            if tempChange >= significantChangeThreshold {
                return true
            }
        }
        
        // Air quality change
        if let oldAirQuality = oldAirQuality {
            let aqiChange = abs(newAirQuality.index - oldAirQuality.index)
            if aqiChange >= 25 { // Significant AQI change
                return true
            }
        }
        
        // Weather condition change
        if let oldWeather = oldWeather,
           oldWeather.condition != newWeather.condition {
            return true
        }
        
        return false
    }
    
    // MARK: - Notifications
    
    private func requestNotificationPermissions() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                print("✅ Notification permissions granted")
            } else if let error = error {
                print("❌ Notification permission error: \(error)")
            }
        }
    }
    

    
    private func sendWeatherChangeNotification(weather: WeatherData, airQuality: AirQualityData) async {
        let content = UNMutableNotificationContent()
        content.title = "Weather Update"
        content.body = "Temperature: \(weather.temperature)°F, \(weather.condition). Air Quality: \(airQuality.description)"
        content.sound = .default
        content.categoryIdentifier = "WEATHER_UPDATE"
        
        let request = UNNotificationRequest(
            identifier: "weather_change_\(Date().timeIntervalSince1970)",
            content: content,
            trigger: nil // Immediate delivery
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            print("✅ Weather change notification sent")
        } catch {
            print("❌ Failed to send weather notification: \(error)")
        }
    }
    
    private func checkEnvironmentalAlerts(weather: WeatherData, airQuality: AirQualityData, pollen: PollenData) async {
        var shouldAlert = false
        var alertMessage = ""
        
        // Check for extreme conditions
        if weather.temperature > 90 || weather.temperature < 25 {
            shouldAlert = true
            alertMessage += "Extreme temperature (\(weather.temperature)°F). "
        }
        
        if airQuality.index > 100 {
            shouldAlert = true
            alertMessage += "Poor air quality (AQI: \(airQuality.index)). "
        }
        
        let totalPollen = pollen.treeIndex + pollen.grassIndex + pollen.weedIndex
        if totalPollen > 20 {
            shouldAlert = true
            alertMessage += "High pollen levels. "
        }
        
        if shouldAlert {
            await sendEnvironmentalAlert(message: alertMessage)
        }
    }
    
    private func sendEnvironmentalAlert(message: String) async {
        let content = UNMutableNotificationContent()
        content.title = "Environmental Alert"
        content.body = message + "Consider adjusting your pet's outdoor activities."
        content.sound = .default
        content.categoryIdentifier = "ENVIRONMENTAL_ALERT"
        
        let request = UNNotificationRequest(
            identifier: "env_alert_\(Date().timeIntervalSince1970)",
            content: content,
            trigger: nil
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            print("✅ Environmental alert sent")
        } catch {
            print("❌ Failed to send environmental alert: \(error)")
        }
    }
    
    // MARK: - Background Tasks
    
    private func startBackgroundTask() {
        endBackgroundTask() // End any existing task first
        
        backgroundTaskIdentifier = UIApplication.shared.beginBackgroundTask(withName: "WeatherMonitoring") { [weak self] in
            print("⚠️ Weather monitoring background task about to expire - cleaning up")
            self?.endBackgroundTask()
        }
        
        // Auto-cleanup after maxBackgroundTime to prevent timeouts
        DispatchQueue.main.asyncAfter(deadline: .now() + maxBackgroundTime) { [weak self] in
            if self?.backgroundTaskIdentifier != .invalid {
                print("🕒 Auto-ending weather background task to prevent timeout")
                self?.endBackgroundTask()
            }
        }
    }
    
    private func endBackgroundTask() {
        if backgroundTaskIdentifier != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskIdentifier)
            backgroundTaskIdentifier = .invalid
            print("✅ Weather background task ended successfully")
        }
    }
    
    // MARK: - Location Subscription
    
    private func setupLocationSubscription() {
        locationSubscription = $currentLocation
            .compactMap { $0 }
            .removeDuplicates { old, new in
                // Only update if location changed significantly (>100 meters)
                old.distance(from: new) < 100
            }
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.updateWeatherData()
                }
            }
    }
    
    // MARK: - Public API
    
    func forceWeatherUpdate() async {
        await updateWeatherData()
    }
    
    func getWeatherSummary() -> String {
        guard let weather = currentWeather,
              let airQuality = airQuality else {
            return "Weather data unavailable"
        }
        
        return "\(weather.temperature)°F, \(weather.condition), Air Quality: \(airQuality.description)"
    }
    
    func isGoodWalkingWeather() -> Bool {
        guard let weather = currentWeather,
              let airQuality = airQuality else {
            return false
        }
        
        let tempOK = weather.temperature >= 45 && weather.temperature <= 85
        let aqOK = airQuality.index <= 100
        let conditionOK = !weather.condition.lowercased().contains("storm") &&
                         !weather.condition.lowercased().contains("severe")
        
        return tempOK && aqOK && conditionOK
    }
}

// MARK: - Weather Alert Model

struct WeatherAlert: Identifiable, Codable {
    let id: String
    let type: AlertType
    let title: String
    let message: String
    let severity: AlertSeverity
    let startTime: Date
    let endTime: Date
    
    var isActive: Bool {
        let now = Date()
        return now >= startTime && now <= endTime
    }
    
    enum AlertType: String, Codable, CaseIterable {
        case heatWarning = "heat_warning"
        case coldWarning = "cold_warning"
        case airQuality = "air_quality"
        case storm = "storm"
        case pollen = "pollen"
        case uv = "uv"
        
        var icon: String {
            switch self {
            case .heatWarning: return "thermometer.sun.fill"
            case .coldWarning: return "thermometer.snowflake"
            case .airQuality: return "aqi.medium"
            case .storm: return "cloud.bolt.fill"
            case .pollen: return "leaf.fill"
            case .uv: return "sun.max.fill"
            }
        }
    }
    
    enum AlertSeverity: String, Codable, CaseIterable {
        case low = "low"
        case moderate = "moderate"
        case high = "high"
        case severe = "severe"
        
        var color: Color {
            switch self {
            case .low: return .green
            case .moderate: return .yellow
            case .high: return .orange
            case .severe: return .red
            }
        }
    }
}

#if DEBUG
extension WeatherAlert {
    static let sampleData = [
        WeatherAlert(
            id: "1",
            type: .heatWarning,
            title: "High Temperature Alert",
            message: "Temperature is 87°F. Consider shorter walks and provide extra water.",
            severity: .moderate,
            startTime: Date(),
            endTime: Date().addingTimeInterval(3600 * 4)
        ),
        WeatherAlert(
            id: "2",
            type: .airQuality,
            title: "Poor Air Quality",
            message: "Air quality index is 125. Limit outdoor activities.",
            severity: .high,
            startTime: Date(),
            endTime: Date().addingTimeInterval(3600 * 2)
        )
    ]
}
#endif
