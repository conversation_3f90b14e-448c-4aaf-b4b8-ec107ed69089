//
//  SafeServiceManager.swift
//  PetCapsule
//
//  Safe service initialization and lifecycle management
//
import Foundation
import UIKit
import Combine
import OSLog
@MainActor
class SafeServiceManager: ObservableObject {
    static let shared = SafeServiceManager()
    // MARK: - Service Registry
    private var registeredServices: [String: AnyObject] = [:]
    private var serviceInitializationQueue = DispatchQueue(label: "service.initialization", qos: .userInitiated)
    private let logger = Logger(subsystem: "PetCapsule", category: "ServiceManager")
    private let performanceManager = PerformanceOptimizationManager.shared
    // Service health monitoring
    @Published var serviceHealth: [String: ServiceHealth] = [:]
    @Published var criticalServicesReady = false
    private init() {
        setupCriticalServices()
        setupServiceHealthMonitoring()
        setupLifecycleObservers()
    }
    // MARK: - Critical Services Setup
    private func setupCriticalServices() {
        Task {
            await initializeCriticalServices()
        }
    }
    private func initializeCriticalServices() async {
        logger.info("Initializing critical services")
        // Initialize services in dependency order with error handling
        let criticalServices: [(String, () async -> AnyObject?)] = [
            ("AuthenticationService", { AuthenticationService() }),
            ("AppleNativeDataService", { AppleNativeDataService.shared }),
            ("RealDataService", { RealDataService() }),
            ("ThemeManager", { ThemeManager.shared })
        ]
        for (serviceName, serviceFactory) in criticalServices {
            do {
                let service = await serviceFactory()
                if let service = service {
                    registeredServices[serviceName] = service
                    serviceHealth[serviceName] = ServiceHealth(
                        name: serviceName,
                        status: .healthy,
                        lastCheck: Date(),
                        errorCount: 0
                    )
                    logger.info("✅ Initialized \(serviceName)")
                } else {
                    throw ServiceError.initializationFailed(serviceName)
                }
            } catch {
                logger.error("❌ Failed to initialize \(serviceName): \(error)")
                serviceHealth[serviceName] = ServiceHealth(
                    name: serviceName,
                    status: .error,
                    lastCheck: Date(),
                    errorCount: 1,
                    lastError: error.localizedDescription
                )
            }
        }
        criticalServicesReady = true
        logger.info("Critical services initialization complete")
    }
    // MARK: - Service Registration
    func register<T: AnyObject>(_ service: T, with name: String) {
        registeredServices[name] = service
        serviceHealth[name] = ServiceHealth(
            name: name,
            status: .healthy,
            lastCheck: Date(),
            errorCount: 0
        )
        logger.info("Registered service: \(name)")
    }
    func getService<T>(_ type: T.Type, name: String) -> T? {
        guard let service = registeredServices[name] as? T else {
            logger.warning("Service '\(name)' not found or wrong type")
            return nil
        }
        return service
    }
    func unregister(_ name: String) {
        registeredServices.removeValue(forKey: name)
        serviceHealth.removeValue(forKey: name)
        logger.info("Unregistered service: \(name)")
    }
    // MARK: - Service Health Monitoring
    private func setupServiceHealthMonitoring() {
        let healthTimer = Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.checkServiceHealth()
            }
        }
        performanceManager.registerTimer(healthTimer)
    }
    private func checkServiceHealth() async {
        for (serviceName, _) in registeredServices {
            await checkIndividualServiceHealth(serviceName)
        }
    }
    private func checkIndividualServiceHealth(_ serviceName: String) async {
        guard var health = serviceHealth[serviceName] else { return }
        do {
            // Perform health check based on service type
            let isHealthy = try await performHealthCheck(for: serviceName)
            if isHealthy {
                health.status = .healthy
                health.errorCount = max(0, health.errorCount - 1)
            } else {
                health.status = .degraded
                health.errorCount += 1
            }
        } catch {
            health.status = .error
            health.errorCount += 1
            health.lastError = error.localizedDescription
            logger.error("Health check failed for \(serviceName): \(error)")
        }
        health.lastCheck = Date()
        serviceHealth[serviceName] = health
        // Take action for unhealthy services
        if health.errorCount > 5 {
            await handleUnhealthyService(serviceName, health: health)
        }
    }
    private func performHealthCheck(for serviceName: String) async throws -> Bool {
        // Implement specific health checks for different services
        switch serviceName {
        case "AuthenticationService":
            // Check if authentication service is responsive
            return registeredServices[serviceName] != nil
        case "AppleNativeDataService":
            return registeredServices[serviceName] != nil
        case "RealDataService":
            // Check data service status
            if let dataService = registeredServices[serviceName] as? RealDataService {
                return !dataService.isLoading
            }
            return false
        default:
            return registeredServices[serviceName] != nil
        }
    }
    private func handleUnhealthyService(_ serviceName: String, health: ServiceHealth) async {
        logger.error("Service \(serviceName) is unhealthy - attempting recovery")
        // Attempt service recovery
        do {
            try await restartService(serviceName)
            logger.info("Successfully restarted service: \(serviceName)")
        } catch {
            logger.error("Failed to restart service \(serviceName): \(error)")
            // Notify user about critical service failure
            if isCriticalService(serviceName) {
                NotificationCenter.default.post(
                    name: .criticalServiceFailure,
                    object: serviceName
                )
            }
        }
    }
    private func restartService(_ serviceName: String) async throws {
        // Remove the unhealthy service
        registeredServices.removeValue(forKey: serviceName)
        // Try to reinitialize
        switch serviceName {
        case "AuthenticationService":
            register(AuthenticationService(), with: serviceName)
        case "RealDataService":
            register(RealDataService(), with: serviceName)
        default:
            throw ServiceError.restartFailed(serviceName)
        }
    }
    private func isCriticalService(_ serviceName: String) -> Bool {
        let criticalServices = ["AuthenticationService", "AppleNativeDataService", "RealDataService"]
        return criticalServices.contains(serviceName)
    }
    // MARK: - Lifecycle Management
    private func setupLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillTerminate),
            name: UIApplication.willTerminateNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryPressure),
            name: .memoryPressureCritical,
            object: nil
        )
    }
    @objc private func appWillTerminate() {
        logger.info("App terminating - cleaning up services")
        Task {
            await MainActor.run {
                cleanupAllServices()
            }
        }
    }
    @objc private func handleMemoryPressure() {
        logger.warning("Memory pressure - cleaning up non-critical services")
        Task {
            await cleanupNonCriticalServices()
        }
    }
    private func cleanupAllServices() {
        for (serviceName, service) in registeredServices {
            cleanupService(service, name: serviceName)
        }
        registeredServices.removeAll()
        serviceHealth.removeAll()
    }
    private func cleanupNonCriticalServices() async {
        let nonCriticalServices = registeredServices.filter { serviceName, _ in
            !isCriticalService(serviceName)
        }
        for (serviceName, service) in nonCriticalServices {
            cleanupService(service, name: serviceName)
            registeredServices.removeValue(forKey: serviceName)
            serviceHealth.removeValue(forKey: serviceName)
        }
    }
    private func cleanupService(_ service: AnyObject, name: String) {
        // Perform cleanup based on service type
        if service is any ObservableObject {
            // Cancel any ongoing operations
            logger.info("Cleaning up observable service: \(name)")
        }
        // Additional cleanup for specific service types
        if name.contains("Timer") || name.contains("Monitoring") {
            logger.info("Cleaning up timer-based service: \(name)")
        }
    }
    deinit {
        // Cleanup services on MainActor
        Task { @MainActor in
            for (serviceName, service) in registeredServices {
                cleanupService(service, name: serviceName)
            }
            registeredServices.removeAll()
            serviceHealth.removeAll()
        }
        NotificationCenter.default.removeObserver(self)
    }
}
// MARK: - Supporting Types
struct ServiceHealth {
    let name: String
    var status: ServiceStatus
    var lastCheck: Date
    var errorCount: Int
    var lastError: String?
    var isHealthy: Bool {
        status == .healthy && errorCount < 3
    }
}
enum ServiceStatus {
    case healthy
    case degraded
    case error
    case initializing
    var displayName: String {
        switch self {
        case .healthy: return "Healthy"
        case .degraded: return "Degraded"
        case .error: return "Error"
        case .initializing: return "Initializing"
        }
    }
    var color: UIColor {
        switch self {
        case .healthy: return .systemGreen
        case .degraded: return .systemOrange
        case .error: return .systemRed
        case .initializing: return .systemBlue
        }
    }
}
enum ServiceError: Error {
    case initializationFailed(String)
    case restartFailed(String)
    case healthCheckFailed(String)
    var localizedDescription: String {
        switch self {
        case .initializationFailed(let service):
            return "Failed to initialize service: \(service)"
        case .restartFailed(let service):
            return "Failed to restart service: \(service)"
        case .healthCheckFailed(let service):
            return "Health check failed for service: \(service)"
        }
    }
}
// MARK: - Notifications
extension Notification.Name {
    static let criticalServiceFailure = Notification.Name("criticalServiceFailure")
    static let serviceHealthChanged = Notification.Name("serviceHealthChanged")
} 