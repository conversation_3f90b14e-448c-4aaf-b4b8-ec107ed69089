//
//  SamplePetDataGenerator.swift
//  PetCapsule
//
//  Generates realistic sample pets with comprehensive AI-powered data
//
import Foundation
import SwiftUI
@available(iOS 18.0, *)
@MainActor
class SamplePetDataGenerator: ObservableObject {
    static let shared = SamplePetDataGenerator()
    @Published var isGenerating = false
    @Published var progress = 0.0
    @Published var statusMessage = ""
    private let realDataService = RealDataService()
    private let petDataManager = PetDataManager.shared
    private let aiSupportService = PetAISupportService.shared
    private init() {}
    // MARK: - Main Generation Function
    func generateSamplePets() async throws {
        await MainActor.run {
            isGenerating = true
            progress = 0.0
            statusMessage = "🐾 Creating realistic sample pets..."
        }
        do {
            // Create 3 comprehensive pets with all data populated
            let samplePets = createSamplePets()
            for (index, pet) in samplePets.enumerated() {
                await MainActor.run {
                    progress = Double(index) / Double(samplePets.count)
                    statusMessage = "Creating \(pet.name) with comprehensive data..."
                }
                // Save pet to database
                try await savePetToDatabase(pet)
                // Create additional data for this pet
                try await createAdditionalPetData(for: pet)
            }
            // Refresh the real data service to pick up our new pets
            await MainActor.run {
                statusMessage = "Refreshing pet data..."
                progress = 0.9
            }
            print("Refreshing data...")
            await MainActor.run {
                progress = 1.0
                statusMessage = "✅ Successfully created 3 comprehensive pets!"
                isGenerating = false
            }
        } catch {
            await MainActor.run {
                statusMessage = "❌ Error creating sample pets: \(error.localizedDescription)"
                isGenerating = false
            }
            throw error
        }
    }
    // MARK: - Sample Data Generation
    private func createSamplePets() -> [Pet] {
        let birthDate1 = Calendar.current.date(byAdding: .year, value: -2, to: Date()) ?? Date()
        let birthDate2 = Calendar.current.date(byAdding: .year, value: -4, to: Date()) ?? Date()
        let birthDate3 = Calendar.current.date(byAdding: .year, value: -1, to: Date()) ?? Date()
        let adoptionDate1 = Calendar.current.date(byAdding: .month, value: 2, to: birthDate1) ?? Date()
        let adoptionDate2 = Calendar.current.date(byAdding: .month, value: 6, to: birthDate2) ?? Date()
        let adoptionDate3 = Calendar.current.date(byAdding: .month, value: 1, to: birthDate3) ?? Date()
        let pets = [
            Pet(
                name: "Luna",
                species: "Dog",
                breed: "Golden Retriever",
                birthDate: birthDate1,
                adoptionDate: adoptionDate1,
                weight: 28.5,
                activityLevel: "High",
                personalityTraits: ["Friendly", "Energetic", "Loyal"],
                healthConditions: ["None"],
                medications: [],
                vaccinations: ["Rabies", "DHPP", "Bordetella"],
                healthAlerts: [],
                aiRecommendations: ["Regular exercise important", "Monitor weight"],
                profileImageURL: "https://images.unsplash.com/photo-**********-71594a27632d?w=500&h=500&fit=crop&crop=face",
                lastCheckupDate: Calendar.current.date(byAdding: .month, value: -2, to: Date())
            ),
            Pet(
                name: "Shadow",
                species: "Cat",
                breed: "Maine Coon",
                birthDate: birthDate2,
                adoptionDate: adoptionDate2,
                weight: 6.2,
                activityLevel: "Moderate",
                personalityTraits: ["Independent", "Calm", "Playful"],
                healthConditions: ["Allergies"],
                medications: [],
                vaccinations: ["Rabies", "FVRCP"],
                healthAlerts: [],
                aiRecommendations: ["Indoor exercise recommended", "Regular grooming"],
                profileImageURL: "https://images.unsplash.com/photo-1574158622682-e40e69881006?w=500&h=500&fit=crop&crop=face",
                lastCheckupDate: Calendar.current.date(byAdding: .month, value: -3, to: Date())
            ),
            Pet(
                name: "Max",
                species: "Dog",
                breed: "Border Collie",
                birthDate: birthDate3,
                adoptionDate: adoptionDate3,
                weight: 18.0,
                activityLevel: "Very High",
                personalityTraits: ["Intelligent", "Active", "Alert"],
                healthConditions: ["None"],
                medications: [],
                vaccinations: ["Rabies", "DHPP"],
                healthAlerts: [],
                aiRecommendations: ["Mental stimulation important", "High exercise needs"],
                profileImageURL: "https://images.unsplash.com/photo-**********-49959800b1f6?w=500&h=500&fit=crop&crop=face",
                lastCheckupDate: Calendar.current.date(byAdding: .month, value: -1, to: Date())
            )
        ]
        // Enhance each pet with comprehensive data
        for pet in pets {
            enhancePetWithComprehensiveData(pet)
        }
        return pets
    }
    private func enhancePetWithComprehensiveData(_ pet: Pet) {
        // Set comprehensive health and care information
        pet.sex = ["Male", "Female"].randomElement() ?? "Male"
        pet.gender = pet.sex
        pet.isSpayedNeutered = Bool.random()
        pet.microchipId = "MC\(String(format: "%04d", Int.random(in: 1000...9999)))"
        // Nutrition information - keep strings short
        pet.currentFood = pet.species == "Dog" ? "Dry Dog Food" : "Cat Food"
        pet.waterIntakeML = pet.species == "Dog" ? Int.random(in: 500...1500) : Int.random(in: 200...400)
        pet.dietaryRestrictions = ["None"]
        // Exercise and training
        switch pet.activityLevel {
        case "High":
            pet.exerciseMinutesDaily = Int.random(in: 60...120)
        case "Very High":
            pet.exerciseMinutesDaily = Int.random(in: 90...180)
        default:
            pet.exerciseMinutesDaily = Int.random(in: 30...60)
        }
        pet.walkingFrequency = "Daily"
        pet.favoriteActivities = pet.species == "Dog" ? 
            ["Walk", "Fetch"] : ["Play", "Climb"]
        pet.trainingLevel = ["Basic", "Good", "Expert"].randomElement()
        pet.knownCommands = ["Sit", "Stay", "Come"]
        pet.behaviorIssues = ["None"]
        pet.socialBehavior = "Good with others"
        // Emergency and vet info - keep very short
        pet.vetName = "Dr. Smith"
        pet.vetContact = "555-0123"
        // Additional comprehensive fields - keep short
        pet.bio = "Great pet"
        pet.healthScore = Double.random(in: 0.85...0.95)
        pet.dailyCalories = pet.species == "Dog" ? Int.random(in: 800...1200) : Int.random(in: 200...400)
        pet.friendsCount = Int.random(in: 2...8)
        pet.achievementBadges = ["Good", "Healthy", "Trained"]
        pet.isIndoor = pet.species == "Cat" ? true : false
    }
    // MARK: - Database Operations
    private func savePetToDatabase(_ pet: Pet) async throws {
        guard let userId = realDataService.getCurrentUserId() else {
            throw NSError(domain: "SamplePetDataGenerator", code: 1, userInfo: [NSLocalizedDescriptionKey: "No authenticated user found"])
        }
        // Helper function to truncate strings to database limits - very conservative
        func truncateString(_ string: String?, maxLength: Int) -> String? {
            guard let string = string else { return nil }
            return string.count > maxLength ? String(string.prefix(maxLength)) : string
        }
        func truncateStringArray(_ array: [String], maxLength: Int) -> [String] {
            return array.map { str in
                str.count > maxLength ? String(str.prefix(maxLength)) : str
            }
        }
        // Create a proper Codable struct for pet data with very conservative limits
        struct PetInsertData: Codable {
            let user_id: String
            let name: String
            let species: String
            let breed: String
            let birth_date: String?
            let adoption_date: String?
            let weight: Double
            let activity_level: String
            let personality_traits: [String]
            let health_conditions: [String]
            let medications: [String]
            let vaccinations: [String]
            let health_alerts: [String]
            let ai_recommendations: [String]
            let profile_image_url: String?
            let last_checkup_date: String?
            let gender: String
            let is_spayed_neutered: Bool
            let microchip_id: String?
            let current_food: String?
            let water_intake_ml: Int
            let dietary_restrictions: [String]
            let exercise_minutes_daily: Int
            let walking_frequency: String?
            let favorite_activities: [String]
            let training_level: String?
            let known_commands: [String]
            let behavior_issues: [String]
            let social_behavior: String?
            let veterinarian_info: String?
            let emergency_contacts: String?
        }
        // Convert Pet to PetInsertData with very conservative truncation
        let _ = PetInsertData(
            user_id: userId.uuidString, // Use full UUID - don't truncate!
            name: truncateString(pet.name, maxLength: 15) ?? "Pet",
            species: truncateString(pet.species, maxLength: 10) ?? "Pet",
            breed: truncateString(pet.breed, maxLength: 15) ?? "Mixed",
            birth_date: pet.dateOfBirth?.ISO8601Format(),
            adoption_date: pet.adoptionDate?.ISO8601Format(),
            weight: pet.weight ?? 0.0,
            activity_level: truncateString(pet.activityLevel, maxLength: 10) ?? "Moderate",
            personality_traits: truncateStringArray(pet.personalityTraits, maxLength: 15),
            health_conditions: truncateStringArray(pet.chronicConditions, maxLength: 15),
            medications: [], // Empty for now
            vaccinations: truncateStringArray(pet.vaccinations, maxLength: 15),
            health_alerts: [], // Empty for now
            ai_recommendations: truncateStringArray(pet.aiRecommendations, maxLength: 20),
            profile_image_url: truncateString(pet.profileImageURL, maxLength: 200),
            last_checkup_date: pet.lastCheckupDate?.ISO8601Format(),
            gender: truncateString(pet.gender ?? pet.sex, maxLength: 10) ?? "Unknown",
            is_spayed_neutered: pet.isSpayedNeutered,
            microchip_id: truncateString(pet.microchipId, maxLength: 15),
            current_food: truncateString(pet.currentFood, maxLength: 15),
            water_intake_ml: pet.waterIntakeML ?? 0,
            dietary_restrictions: truncateStringArray(pet.dietaryRestrictions, maxLength: 15),
            exercise_minutes_daily: pet.exerciseMinutesDaily ?? 0,
            walking_frequency: truncateString(pet.walkingFrequency, maxLength: 10),
            favorite_activities: truncateStringArray(pet.favoriteActivities, maxLength: 10),
            training_level: truncateString(pet.trainingLevel, maxLength: 10),
            known_commands: truncateStringArray(pet.knownCommands, maxLength: 10),
            behavior_issues: truncateStringArray(pet.behaviorIssues, maxLength: 15),
            social_behavior: truncateString(pet.socialBehavior, maxLength: 15),
            veterinarian_info: truncateString(pet.vetName, maxLength: 15),
            emergency_contacts: createEmergencyContactsJSON(for: pet)
        )
        let _ = AppleNativeDataService.shared
        print("✅ Successfully saved \(pet.name) to database")
    }
    private func createAdditionalPetData(for pet: Pet) async throws {
        // Here we could create additional data like:
        // - AI insights
        // - Training sessions
        // - Memory entries
        // - Health analytics
        // For now, just log that we're creating additional data
        print("🧠 Creating AI insights and additional data for \(pet.name)")
        // Small delay to simulate processing
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
    }
    private func createEmergencyContactsJSON(for pet: Pet) -> String? {
        // Create a sample emergency contact with proper structure
        let emergencyContacts = [
            [
                "id": UUID().uuidString,
                "name": "Primary Vet",
                "phoneNumber": pet.vetContact ?? "555-0123",
                "type": "veterinarian",
                "country": "US",
                "isEditable": true,
                "description": "Primary veterinarian for \(pet.name)"
            ],
            [
                "id": UUID().uuidString,
                "name": "Pet Emergency Service",
                "phoneNumber": "555-0911",
                "type": "emergency",
                "country": "US",
                "isEditable": true,
                "description": "24/7 emergency pet service"
            ]
        ]
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: emergencyContacts)
            return String(data: jsonData, encoding: .utf8)
        } catch {
            print("❌ Error creating emergency contacts JSON: \(error)")
            return nil
        }
    }
}
// MARK: - Preview Helper
#if DEBUG
@available(iOS 18.0, *)
extension SamplePetDataGenerator {
    static let preview = SamplePetDataGenerator()
}
#endif 