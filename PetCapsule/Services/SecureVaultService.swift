//
//  SecureVaultService.swift
//  PetCapsule
//
//  Secure vault service using Apple native storage
//  Uses local encrypted storage
//

import Foundation
import SwiftUI
import SwiftData

@MainActor
class SecureVaultService: ObservableObject {
    static let shared = SecureVaultService()
    
    @Published var vaults: [Vault] = []
    @Published var isLoading = false
    @Published var error: String?
    
    private let dataService = AppleNativeDataService.shared
    
    private init() {
        loadVaults()
    }
    
    func loadVaults() {
        Task {
            await loadUserVaults()
        }
    }
    
    func loadUserVaults() async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // Load vaults from local storage
            let allVaults = await dataService.getAllVaults()
            await MainActor.run {
                self.vaults = allVaults
            }
        } catch {
            await MainActor.run {
                self.error = "Failed to load vaults: \(error.localizedDescription)"
            }
        }
    }
    
    func createVault(name: String, description: String, unlockDate: Date, petID: String?) async throws -> Vault {
        let vault = Vault(
            name: name,
            vaultDescription: description,
            unlockDate: unlockDate,
            petID: petID
        )
        
        // Save to local storage
        try await dataService.createVault(vault)
        
        await MainActor.run {
            self.vaults.append(vault)
        }
        
        return vault
    }
    
    func deleteVault(_ vault: Vault) async throws {
        // Delete from local storage
        try await dataService.deleteVault(vault)
        
        await MainActor.run {
            self.vaults.removeAll { $0.id == vault.id }
        }
    }
}
