//
//  SocialInteractionService.swift
//  PetCapsule
//
//  Enhanced social interaction tracking with playdates, community engagement, and social analytics
//
import Foundation
import CoreLocation
import Combine
import SwiftUI
// Using SharedTypes.swift definitions directly to avoid redeclaration conflicts
// Type aliases for clarity (only new ones not already defined)
typealias SharedSharedSocialConnection = SharedSocialConnection
typealias SharedPlaydateActivityType = PlaydateActivityType
typealias SharedSharedPlaydatePetInfo = SharedPlaydatePetInfo
typealias SharedPostComment = PostComment
@MainActor
class SocialInteractionService: ObservableObject {
    static let shared = SocialInteractionService()
    // Published properties
    @Published var socialProfile: SocialProfile?
    @Published var playdates: [SharedPlaydate] = []
    @Published var socialConnections: [SharedSharedSharedSocialConnection] = []
    @Published var communityPosts: [CommunityPost] = []
    @Published var socialActivities: [SocialActivity] = []
    @Published var socialMetrics: SocialMetrics?
    @Published var nearbyPets: [NearbyPet] = []
    @Published var socialRecommendations: [SocialRecommendation] = []
    @Published var isLoading = false
    @Published var lastUpdateTime: Date?
    // Services
    private let dataService = AppleNativeDataService.shared
    private let locationManager = CLLocationManager()
    @Published var currentLocation: CLLocation?
    private let petNetworkService = PetNetworkService.shared
    private let communityEventsService = CommunityEventsService.shared
    private init() {
        loadSocialData()
        setupRealtimeSubscriptions()
    }
    // MARK: - Playdate Management
    func createPlaydate(
        title: String,
        description: String,
        location: CLLocationCoordinate2D,
        locationName: String,
        dateTime: Date,
        duration: TimeInterval,
        maxParticipants: Int,
        petRequirements: [PetRequirement],
        activityType: SharedPlaydateActivityType,
        isPublic: Bool = true
    ) async throws -> SharedPlaydate {
        let playdate = SharedPlaydate(
            id: UUID().uuidString,
            organizerId: getCurrentUserId(),
            title: title,
            description: description,
            scheduledDate: dateTime,
            location: locationName,
            status: .pending
        )
        // Save to database
        try await savePlaydateToDatabase(playdate)
        // Add to local array
        playdates.append(playdate)
        // Create social activity record
        let activity = SocialActivity(
            id: UUID().uuidString,
            userId: getCurrentUserId(),
            activityType: .playdateCreated,
            relatedId: playdate.id,
            description: "Created playdate: \(title)",
            timestamp: Date()
        )
        socialActivities.append(activity)
        try await saveSocialActivityToDatabase(activity)
        // Update social metrics
        await updateSocialMetrics()
        print("✅ Playdate created: \(title)")
        return playdate
    }
    func joinPlaydate(_ playdateId: String, petInfo: SharedSharedPlaydatePetInfo) async throws {
        guard let index = playdates.firstIndex(where: { $0.id == playdateId }) else {
            throw SocialInteractionError.playdateNotFound
        }
        let playdate = playdates[index]
        // Simple check - in a real implementation, would check participant count from database
        // For now, just proceed with joining
        // No need to update the playdate since we're not modifying it
        // Save to database
        try await savePlaydateParticipantToDatabase(playdateId, petInfo: petInfo)
        // Create social activity record
        let activity = SocialActivity(
            id: UUID().uuidString,
            userId: getCurrentUserId(),
            activityType: .playdateJoined,
            relatedId: playdateId,
            description: "Joined playdate: \(playdate.title)",
            timestamp: Date()
        )
        socialActivities.append(activity)
        try await saveSocialActivityToDatabase(activity)
        // Update social metrics
        await updateSocialMetrics()
        print("✅ Joined playdate: \(playdate.title)")
    }
    func leavePlaydate(_ playdateId: String) async throws {
        guard let index = playdates.firstIndex(where: { $0.id == playdateId }) else {
            throw SocialInteractionError.playdateNotFound
        }
        // In a real implementation, would remove participant from database
        // SharedPlaydate doesn't track participant count directly
        // Remove from database
        try await removePlaydateParticipantFromDatabase(playdateId)
        print("✅ Left playdate: \(playdates[index].title)")
    }
    func cancelPlaydate(_ playdateId: String) async throws {
        guard let index = playdates.firstIndex(where: { $0.id == playdateId }) else {
            throw SocialInteractionError.playdateNotFound
        }
        playdates[index].status = .cancelled
        // Update in database
        try await savePlaydateToDatabase(playdates[index])
        print("✅ Cancelled playdate: \(playdates[index].title)")
    }
    func completePlaydate(_ playdateId: String) async throws {
        guard let index = playdates.firstIndex(where: { $0.id == playdateId }) else {
            throw SocialInteractionError.playdateNotFound
        }
        playdates[index].status = .completed
        // Update in database
        try await savePlaydateToDatabase(playdates[index])
        // Create social activity record
        let activity = SocialActivity(
            id: UUID().uuidString,
            userId: getCurrentUserId(),
            activityType: .playdateCompleted,
            relatedId: playdateId,
            description: "Completed playdate: \(playdates[index].title)",
            timestamp: Date()
        )
        socialActivities.append(activity)
        try await saveSocialActivityToDatabase(activity)
        print("✅ Completed playdate: \(playdates[index].title)")
    }
    // MARK: - Social Connections
    func sendFriendRequest(to userId: String, message: String? = nil) async throws {
        let connection = SharedSharedSharedSocialConnection(
            id: UUID().uuidString,
            userId: getCurrentUserId(),
            connectedUserId: userId,
            connectedUserName: "Unknown User", // Would get from user service
            connectionType: "friendRequest",
            status: .pending,
            createdAt: Date(),
            updatedAt: Date(),
            mutualConnections: 0,
            sharedInterests: []
        )
        // Save to database
        try await saveSharedSharedSharedSocialConnectionToDatabase(connection)
        // Add to local array
        socialConnections.append(connection)
        print("✅ Friend request sent to: \(userId)")
    }
    func acceptFriendRequest(_ connectionId: String) async throws {
        guard let index = socialConnections.firstIndex(where: { $0.id == connectionId }) else {
            throw SocialInteractionError.connectionNotFound
        }
        var connection = socialConnections[index]
        connection.status = .connected
        // connection.connectionType is a let constant, can't modify
        connection.updatedAt = Date()
        socialConnections[index] = connection
        // Update in database
        try await saveSharedSharedSharedSocialConnectionToDatabase(connection)
        // Create social activity record
        let activity = SocialActivity(
            id: UUID().uuidString,
            userId: getCurrentUserId(),
            activityType: .friendAdded,
            relatedId: connection.userId,
            description: "Became friends",
            timestamp: Date()
        )
        socialActivities.append(activity)
        try await saveSocialActivityToDatabase(activity)
        // Update social metrics
        await updateSocialMetrics()
        print("✅ Friend request accepted: \(connectionId)")
    }
    func declineFriendRequest(_ connectionId: String) async throws {
        guard let index = socialConnections.firstIndex(where: { $0.id == connectionId }) else {
            throw SocialInteractionError.connectionNotFound
        }
        var connection = socialConnections[index]
        connection.status = .declined
        connection.updatedAt = Date()
        socialConnections[index] = connection
        // Update in database
        try await saveSharedSharedSharedSocialConnectionToDatabase(connection)
        print("✅ Friend request declined: \(connectionId)")
    }
    // MARK: - Community Posts
    func createCommunityPost(
        content: String,
        mediaUrls: [String] = [],
        tags: [String] = [],
        visibility: PostVisibility = .public,
        relatedMemoryId: String? = nil
    ) async throws -> CommunityPost {
        let post = CommunityPost(
            id: UUID().uuidString,
            authorId: getCurrentUserId(),
            authorName: "Current User", // Would get from user service
            content: content,
            createdAt: Date(),
            likesCount: 0,
            commentsCount: 0
        )
        // Save to database
        try await saveCommunityPostToDatabase(post)
        // Add to local array
        communityPosts.insert(post, at: 0)
        // Create social activity record
        let activity = SocialActivity(
            id: UUID().uuidString,
            userId: getCurrentUserId(),
            activityType: .postCreated,
            relatedId: post.id,
            description: "Created a community post",
            timestamp: Date()
        )
        socialActivities.append(activity)
        try await saveSocialActivityToDatabase(activity)
        // Update social metrics
        await updateSocialMetrics()
        print("✅ Community post created: \(post.id)")
        return post
    }
    func likePost(_ postId: String) async throws {
        guard let index = communityPosts.firstIndex(where: { $0.id == postId }) else {
            throw SocialInteractionError.postNotFound
        }
        communityPosts[index].likesCount += 1
        // Update in database
        try await updatePostLikesInDatabase(postId, likes: communityPosts[index].likesCount)
        // Create social activity record
        let activity = SocialActivity(
            id: UUID().uuidString,
            userId: getCurrentUserId(),
            activityType: .postLiked,
            relatedId: postId,
            description: "Liked a post",
            timestamp: Date()
        )
        socialActivities.append(activity)
        try await saveSocialActivityToDatabase(activity)
        print("✅ Post liked: \(postId)")
    }
    func addComment(to postId: String, content: String) async throws {
        let comment = PostComment(
            id: UUID().uuidString,
            postId: postId,
            authorId: getCurrentUserId(),
            authorName: "Current User", // Would get from user service
            content: content,
            createdAt: Date(),
            likesCount: 0
        )
        // Add to post - Note: CommunityPost doesn't have comments array
        if let index = communityPosts.firstIndex(where: { $0.id == postId }) {
            communityPosts[index].commentsCount += 1
        }
        // Save to database
        try await saveCommentToDatabase(comment)
        // Create social activity record
        let activity = SocialActivity(
            id: UUID().uuidString,
            userId: getCurrentUserId(),
            activityType: .commentAdded,
            relatedId: postId,
            description: "Commented on a post",
            timestamp: Date()
        )
        socialActivities.append(activity)
        try await saveSocialActivityToDatabase(activity)
        print("✅ Comment added to post: \(postId)")
    }
    // MARK: - Social Discovery
    func discoverNearbyPets() async {
        guard let currentLocation = currentLocation else {
            print("❌ No current location for pet discovery")
            return
        }
        isLoading = true
        defer { isLoading = false }
        do {
            let nearbyPetsData: [NearbyPetResponse] = []
            print("Finding nearby pets with Apple native services near \(currentLocation.coordinate)")
            nearbyPets = nearbyPetsData.map { $0.toNearbyPet() }
            print("✅ Discovered \(nearbyPets.count) nearby pets")
        } catch {
            print("❌ Failed to discover nearby pets: \(error)")
        }
    }
    func generateSocialRecommendations() async {
        var recommendations: [SocialRecommendation] = []
        // Recommend playdates based on pet compatibility
        let compatiblePets = nearbyPets.filter { pet in
            // Simple compatibility logic - would be more sophisticated in real implementation
            return pet.age > 6 && pet.isVaccinated
        }
        if !compatiblePets.isEmpty {
            recommendations.append(SocialRecommendation(
                id: UUID(),
                type: .playdate,
                title: "Organize a Playdate",
                description: "Found \(compatiblePets.count) compatible pets nearby",
                priority: .medium,
                actionData: ["compatible_pets": compatiblePets.map { $0.id }]
            ))
        }
        // Recommend joining community events
        let upcomingEvents = communityEventsService.getUpcomingEvents(limit: 5)
        if !upcomingEvents.isEmpty {
            recommendations.append(SocialRecommendation(
                id: UUID(),
                type: .communityEvent,
                title: "Join Community Events",
                description: "\(upcomingEvents.count) events happening near you",
                priority: .high,
                actionData: ["event_count": upcomingEvents.count]
            ))
        }
        // Recommend connecting with similar pet owners
        if socialConnections.filter({ $0.connectionType == "friend" }).count < 5 {
            recommendations.append(SocialRecommendation(
                id: UUID(),
                type: .socialConnection,
                title: "Connect with Pet Owners",
                description: "Build your pet community network",
                priority: .low,
                actionData: [:]
            ))
        }
        socialRecommendations = recommendations
        print("✅ Generated \(recommendations.count) social recommendations")
    }
    // MARK: - Social Analytics
    private func updateSocialMetrics() async {
        let friendsCount = socialConnections.filter { $0.connectionType == "friend" && $0.status == .connected }.count
        let playdatesCount = playdates.filter { $0.status == .completed }.count
        let postsCount = communityPosts.count
        let totalLikes = communityPosts.reduce(0) { $0 + $1.likesCount }
        let totalComments = communityPosts.reduce(0) { $0 + $1.commentsCount }
        // Calculate engagement score
        let engagementScore = calculateEngagementScore(
            friends: friendsCount,
            playdates: playdatesCount,
            posts: postsCount,
            likes: totalLikes,
            comments: totalComments
        )
        socialMetrics = SocialMetrics(
            friendsCount: friendsCount,
            playdatesCount: playdatesCount,
            postsCount: postsCount,
            totalLikes: totalLikes,
            totalComments: totalComments,
            engagementScore: engagementScore,
            lastUpdated: Date()
        )
        // Update social profile
        if var profile = socialProfile {
            profile.socialScore = engagementScore
            profile.lastActive = Date()
            socialProfile = profile
            try? await saveSocialProfileToDatabase(profile)
        }
    }
    private func calculateEngagementScore(
        friends: Int,
        playdates: Int,
        posts: Int,
        likes: Int,
        comments: Int
    ) -> Double {
        let friendsScore = min(Double(friends) * 10, 50) // Max 50 points for friends
        let playdatesScore = min(Double(playdates) * 5, 30) // Max 30 points for playdates
        let postsScore = min(Double(posts) * 2, 20) // Max 20 points for posts
        let likesScore = min(Double(likes) * 0.5, 15) // Max 15 points for likes
        let commentsScore = min(Double(comments) * 1, 10) // Max 10 points for comments
        let totalScore = friendsScore + playdatesScore + postsScore + likesScore + commentsScore
        return min(totalScore / 125.0, 1.0) // Normalize to 0-1 scale
    }
    // MARK: - Data Loading and Persistence
    private func loadSocialData() {
        Task {
            isLoading = true
            defer { isLoading = false }
            do {
                async let profile = loadSocialProfile()
                async let playdatesData = loadPlaydates()
                async let connectionsData = loadSharedSharedSharedSocialConnections()
                async let postsData = loadCommunityPosts()
                async let activitiesData = loadSocialActivities()
                let (loadedProfile, loadedPlaydates, loadedConnections, loadedPosts, loadedActivities) = try await (profile, playdatesData, connectionsData, postsData, activitiesData)
                await MainActor.run {
                    socialProfile = loadedProfile
                    playdates = loadedPlaydates
                    socialConnections = loadedConnections
                    communityPosts = loadedPosts
                    socialActivities = loadedActivities
                    lastUpdateTime = Date()
                }
                await updateSocialMetrics()
                await generateSocialRecommendations()
                print("✅ Social data loaded successfully")
            } catch {
                print("❌ Failed to load social data: \(error)")
            }
        }
    }
    private func setupRealtimeSubscriptions() {
        // Setup real-time subscriptions for social updates
        print("📡 Setting up real-time social subscriptions")
    }
    // MARK: - Database Operations
    private func savePlaydateToDatabase(_ playdate: SharedPlaydate) async throws {
        print("💾 Saving playdate: \(playdate.id)")
    }
    private func savePlaydateParticipantToDatabase(_ playdateId: String, petInfo: SharedSharedPlaydatePetInfo) async throws {
        print("💾 Saving playdate participant: \(playdateId)")
    }
    private func removePlaydateParticipantFromDatabase(_ playdateId: String) async throws {
        print("💾 Removing playdate participant: \(playdateId)")
    }
    private func saveSharedSharedSharedSocialConnectionToDatabase(_ connection: SharedSharedSharedSocialConnection) async throws {
        print("💾 Saving social connection: \(connection.id)")
    }
    private func saveCommunityPostToDatabase(_ post: CommunityPost) async throws {
        print("💾 Saving community post: \(post.id)")
    }
    private func updatePostLikesInDatabase(_ postId: String, likes: Int) async throws {
        print("💾 Updating post likes: \(postId)")
    }
    private func saveCommentToDatabase(_ comment: PostComment) async throws {
        print("💾 Saving comment: \(comment.id)")
    }
    private func saveSocialActivityToDatabase(_ activity: SocialActivity) async throws {
        print("💾 Saving social activity: \(activity.id)")
    }
    private func saveSocialProfileToDatabase(_ profile: SocialProfile) async throws {
        print("💾 Saving social profile: \(profile.userId)")
    }
    // MARK: - Data Loading Methods
    private func loadSocialProfile() async throws -> SocialProfile? {
        return SocialProfile(
            userId: getCurrentUserId(),
            displayName: "Pet Owner",
            bio: nil,
            profileImageUrl: nil,
            socialScore: 0.0,
            isPublic: true,
            lastActive: Date(),
            createdAt: Date()
        )
    }
    private func loadPlaydates() async throws -> [SharedPlaydate] {
        return []
    }
    private func loadSharedSharedSharedSocialConnections() async throws -> [SharedSharedSharedSocialConnection] {
        return []
    }
    private func loadCommunityPosts() async throws -> [CommunityPost] {
        return []
    }
    private func loadSocialActivities() async throws -> [SocialActivity] {
        return []
    }
    private func getCurrentUserId() -> String {
        // Get current user ID from auth service
        return "current_user_id" // Placeholder
    }
    // MARK: - Public API
    func getUpcomingPlaydates() -> [SharedPlaydate] {
        return playdates.filter { $0.scheduledDate > Date() && $0.status == .confirmed }
    }
    func getFriends() -> [SharedSharedSharedSocialConnection] {
        return socialConnections.filter { $0.connectionType == "friend" && $0.status == .connected }
    }
    func getPendingFriendRequests() -> [SharedSharedSharedSocialConnection] {
        return socialConnections.filter { $0.connectionType == "friendRequest" && $0.status == .pending }
    }
    func getRecentSocialActivities(limit: Int = 10) -> [SocialActivity] {
        return Array(socialActivities.sorted { $0.timestamp > $1.timestamp }.prefix(limit))
    }
}
// MARK: - Data Models
struct SocialProfile: Identifiable, Codable {
    let id: String
    let userId: String
    var displayName: String
    var bio: String?
    var profileImageUrl: String?
    var socialScore: Double
    var isPublic: Bool
    var lastActive: Date
    let createdAt: Date
    init(userId: String, displayName: String, bio: String? = nil, profileImageUrl: String? = nil, socialScore: Double = 0.0, isPublic: Bool = true, lastActive: Date = Date(), createdAt: Date = Date()) {
        self.id = UUID().uuidString
        self.userId = userId
        self.displayName = displayName
        self.bio = bio
        self.profileImageUrl = profileImageUrl
        self.socialScore = socialScore
        self.isPublic = isPublic
        self.lastActive = lastActive
        self.createdAt = createdAt
    }
}
// Playdate is now defined in SharedTypes.swift
// SharedSharedSharedSocialConnection is now defined in SharedTypes.swift
// CommunityPost is now defined in SharedTypes.swift
// SharedPostComment is now defined in SharedTypes.swift
struct SocialActivity: Identifiable, Codable {
    let id: String
    let userId: String
    let activityType: SocialActivityType
    let relatedId: String
    var description: String
    let timestamp: Date
}
struct SocialMetrics: Codable {
    let friendsCount: Int
    let playdatesCount: Int
    let postsCount: Int
    let totalLikes: Int
    let totalComments: Int
    let engagementScore: Double
    let lastUpdated: Date
}
struct NearbyPet: Identifiable, Codable {
    let id: String
    let name: String
    let breed: String
    let age: Int
    let profileImageUrl: String?
    let distance: Double
    let isVaccinated: Bool
    let ownerName: String
    let lastSeen: Date
}
struct SocialRecommendation: Identifiable, Codable {
    let id: UUID
    let type: RecommendationType
    var title: String
    var description: String
    let priority: RecommendationPriority
    let actionData: [String: Any]
    enum CodingKeys: String, CodingKey {
        case id, type, title, description, priority
    }
    init(id: UUID, type: RecommendationType, title: String, description: String, priority: RecommendationPriority, actionData: [String: Any]) {
        self.id = id
        self.type = type
        self.title = title
        self.description = description
        self.priority = priority
        self.actionData = actionData
    }
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        type = try container.decode(RecommendationType.self, forKey: .type)
        title = try container.decode(String.self, forKey: .title)
        description = try container.decode(String.self, forKey: .description)
        priority = try container.decode(RecommendationPriority.self, forKey: .priority)
        actionData = [:]
    }
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(type, forKey: .type)
        try container.encode(title, forKey: .title)
        try container.encode(description, forKey: .description)
        try container.encode(priority, forKey: .priority)
    }
}
// MARK: - Enums
// PlaydateActivityType is now defined in SharedTypes.swift as SharedPlaydateActivityType
// PlaydateStatus is now defined in SharedTypes.swift
enum ConnectionType: String, CaseIterable, Codable {
    case friendRequest = "friend_request"
    case friend = "friend"
    case blocked = "blocked"
    case following = "following"
    case follower = "follower"
    var displayName: String {
        switch self {
        case .friendRequest: return "Friend Request"
        case .friend: return "Friend"
        case .blocked: return "Blocked"
        case .following: return "Following"
        case .follower: return "Follower"
        }
    }
}
// ConnectionStatus is now defined in SharedTypes.swift
// SharedPostVisibility is now defined in SharedTypes.swift
enum SocialActivityType: String, CaseIterable, Codable {
    case playdateCreated = "playdate_created"
    case playdateJoined = "playdate_joined"
    case playdateCompleted = "playdate_completed"
    case friendAdded = "friend_added"
    case postCreated = "post_created"
    case postLiked = "post_liked"
    case commentAdded = "comment_added"
    case eventJoined = "event_joined"
    case memoryShared = "memory_shared"
    var displayName: String {
        switch self {
        case .playdateCreated: return "Created Playdate"
        case .playdateJoined: return "Joined Playdate"
        case .playdateCompleted: return "Completed Playdate"
        case .friendAdded: return "Added Friend"
        case .postCreated: return "Created Post"
        case .postLiked: return "Liked Post"
        case .commentAdded: return "Added Comment"
        case .eventJoined: return "Joined Event"
        case .memoryShared: return "Shared Memory"
        }
    }
    var icon: String {
        switch self {
        case .playdateCreated: return "plus.circle"
        case .playdateJoined: return "person.badge.plus"
        case .playdateCompleted: return "checkmark.circle"
        case .friendAdded: return "person.2.fill"
        case .postCreated: return "square.and.pencil"
        case .postLiked: return "heart.fill"
        case .commentAdded: return "bubble.left"
        case .eventJoined: return "calendar.badge.plus"
        case .memoryShared: return "square.and.arrow.up"
        }
    }
}
// SharedRecommendationType is now defined in SharedTypes.swift
// SharedRecommendationPriority is now defined in SharedTypes.swift
struct NearbyPetResponse: Codable {
    let id: String
    let name: String
    let breed: String
    let age: Int
    let profile_image_url: String?
    let distance_meters: Double
    let is_vaccinated: Bool
    let owner_name: String
    let last_seen: String
    func toNearbyPet() -> NearbyPet {
        return NearbyPet(
            id: id,
            name: name,
            breed: breed,
            age: age,
            profileImageUrl: profile_image_url,
            distance: distance_meters / 1000.0, // Convert to kilometers
            isVaccinated: is_vaccinated,
            ownerName: owner_name,
            lastSeen: ISO8601DateFormatter().date(from: last_seen) ?? Date()
        )
    }
}
// MARK: - Error Types
enum SocialInteractionError: Error, LocalizedError {
    case playdateNotFound
    case playdateFull
    case connectionNotFound
    case postNotFound
    case invalidPermissions
    case networkError
    var errorDescription: String? {
        switch self {
        case .playdateNotFound:
            return "Playdate not found"
        case .playdateFull:
            return "Playdate is full"
        case .connectionNotFound:
            return "Social connection not found"
        case .postNotFound:
            return "Post not found"
        case .invalidPermissions:
            return "Invalid permissions for this action"
        case .networkError:
            return "Network error occurred"
        }
    }
}
