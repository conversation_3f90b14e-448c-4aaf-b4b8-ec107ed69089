//
//  SpotlightIndexingService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

import Foundation
import CoreSpotlight
import MobileCoreServices
import SwiftUI

@MainActor
class SpotlightIndexingService: ObservableObject {
    static let shared = SpotlightIndexingService()
    
    @Published var indexingProgress: Double = 0.0
    @Published var isIndexing = false
    @Published var indexedItemsCount = 0
    
    private let searchableIndex = CSSearchableIndex.default()
    
    private init() {
        setupSpotlightIntegration()
    }
    
    // MARK: - Setup
    
    func setupSpotlightIntegration() {
        // Set up search continuation
        searchableIndex.beginBatch()
        
        // Index existing data
        Task {
            await indexAllContent()
        }
    }
    
    // MARK: - Indexing Methods
    
    func indexAllContent() async {
        isIndexing = true
        indexingProgress = 0.0
        
        do {
            // Index pets
            await indexPets()
            indexingProgress = 0.2
            
            // Index memories
            await indexMemories()
            indexingProgress = 0.4
            
            // Index health records
            await indexHealthRecords()
            indexingProgress = 0.6
            
            // Index vaccination records
            await indexVaccinationRecords()
            indexingProgress = 0.8
            
            // Index AI conversations
            await indexAIConversations()
            indexingProgress = 1.0
            
            // Commit the batch
            try await searchableIndex.endBatch(withClientState: Data())
            
        } catch {
            print("Spotlight indexing error: \(error)")
        }
        
        isIndexing = false
    }
    
    // MARK: - Pet Indexing
    
    func indexPets() async {
        // Note: Using RealDataService instead of PetManager
        let pets: [Pet] = [] // TODO: Get pets from RealDataService
        var searchableItems: [CSSearchableItem] = []
        
        for pet in pets {
            let attributeSet = CSSearchableItemAttributeSet(contentType: .content)
            
            // Basic attributes
            attributeSet.title = pet.name
            attributeSet.contentDescription = "\(pet.species) • \(pet.breed ?? "Unknown breed") • \(pet.age) months old"
            attributeSet.keywords = [
                pet.name,
                pet.species,
                pet.breed ?? "Unknown",
                "pet",
                "animal",
                "\(pet.age) months",
                pet.gender ?? "Unknown"
            ]
            
            // Additional metadata
            attributeSet.displayName = pet.name
            attributeSet.alternateNames = [pet.name]
            attributeSet.contentCreationDate = pet.createdAt
            attributeSet.contentModificationDate = pet.updatedAt
            
            // Custom attributes
            attributeSet.setValue(pet.species as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "species")!)
            attributeSet.setValue((pet.breed ?? "Unknown") as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "breed")!)
            attributeSet.setValue(NSNumber(value: pet.age), forCustomKey: CSCustomAttributeKey(keyName: "age")!)
            if let weight = pet.weight {
                attributeSet.setValue(NSNumber(value: weight), forCustomKey: CSCustomAttributeKey(keyName: "weight")!)
            }
            
            // Thumbnail
            if let imageData = await loadPetImage(pet) {
                attributeSet.thumbnailData = imageData
            }
            
            let item = CSSearchableItem(
                uniqueIdentifier: "pet_\(pet.id)",
                domainIdentifier: "pets",
                attributeSet: attributeSet
            )
            
            searchableItems.append(item)
        }
        
        do {
            try await searchableIndex.indexSearchableItems(searchableItems)
            indexedItemsCount += searchableItems.count
        } catch {
            print("Failed to index pets: \(error)")
        }
    }
    
    // MARK: - Memory Indexing
    
    func indexMemories() async {
        // Note: Using RealDataService instead of MemoryManager
        let memories: [Memory] = [] // TODO: Get memories from RealDataService
        var searchableItems: [CSSearchableItem] = []
        
        for memory in memories {
            let attributeSet = CSSearchableItemAttributeSet(contentType: .content)
            
            // Basic attributes
            attributeSet.title = memory.title
            attributeSet.contentDescription = memory.content
            attributeSet.keywords = [
                memory.title,
                memory.content,
                memory.type.rawValue,
                "memory",
                "photo",
                "video"
            ] + memory.tags
            
            // Memory-specific attributes
            attributeSet.displayName = memory.title
            attributeSet.contentCreationDate = memory.createdAt
            attributeSet.contentModificationDate = memory.updatedAt
            
            // Media attributes
            if memory.type == .photo || memory.type == .video {
                attributeSet.contentType = memory.type == .photo ? UTType.image.identifier : UTType.movie.identifier
                
                if let mediaData = await loadMemoryMedia(memory) {
                    attributeSet.thumbnailData = mediaData
                }
            }
            
            // Custom attributes
            attributeSet.setValue(memory.type.rawValue as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "memoryType")!)
            attributeSet.setValue(NSNumber(value: memory.isFavorite), forCustomKey: CSCustomAttributeKey(keyName: "isFavorite")!)
            if let sentiment = memory.sentiment {
                attributeSet.setValue(sentiment as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "sentiment")!)
            }
            
            if let milestone = memory.milestone {
                attributeSet.setValue(milestone as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "milestone")!)
            }
            
            let item = CSSearchableItem(
                uniqueIdentifier: "memory_\(memory.id.uuidString)",
                domainIdentifier: "memories",
                attributeSet: attributeSet
            )
            
            searchableItems.append(item)
        }
        
        do {
            try await searchableIndex.indexSearchableItems(searchableItems)
            indexedItemsCount += searchableItems.count
        } catch {
            print("Failed to index memories: \(error)")
        }
    }
    
    // MARK: - Health Records Indexing
    
    func indexHealthRecords() async {
        // Note: Using RealDataService instead of HealthManager
        let healthRecords: [HealthRecord] = [] // TODO: Get health records from RealDataService
        var searchableItems: [CSSearchableItem] = []
        
        for record in healthRecords {
            let attributeSet = CSSearchableItemAttributeSet(contentType: .content)
            
            // Basic attributes
            attributeSet.title = "Health Record - \(record.type.displayName)"
            attributeSet.contentDescription = record.notes ?? "Health record for \(record.petId)"
            attributeSet.keywords = [
                "health",
                "medical",
                "record",
                record.type.displayName,
                record.notes ?? ""
            ]
            
            // Health-specific attributes
            attributeSet.displayName = record.type.displayName
            attributeSet.contentCreationDate = record.date
            
            // Custom attributes
            attributeSet.setValue(record.type.rawValue as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "healthType")!)
            attributeSet.setValue(NSNumber(value: record.value), forCustomKey: CSCustomAttributeKey(keyName: "healthValue")!)
            attributeSet.setValue(record.unit as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "healthUnit")!)
            
            let item = CSSearchableItem(
                uniqueIdentifier: "health_\(record.id.uuidString)",
                domainIdentifier: "health",
                attributeSet: attributeSet
            )
            
            searchableItems.append(item)
        }
        
        do {
            try await searchableIndex.indexSearchableItems(searchableItems)
            indexedItemsCount += searchableItems.count
        } catch {
            print("Failed to index health records: \(error)")
        }
    }
    
    // MARK: - Vaccination Records Indexing
    
    func indexVaccinationRecords() async {
        // Note: Using RealDataService instead of VaccinationManager
        let vaccinationRecords: [VaccinationRecord] = [] // TODO: Get vaccination records from RealDataService
        var searchableItems: [CSSearchableItem] = []
        
        for record in vaccinationRecords {
            let attributeSet = CSSearchableItemAttributeSet(contentType: .content)
            
            // Basic attributes
            attributeSet.title = "Vaccination - \(record.vaccineName)"
            attributeSet.contentDescription = "Vaccination record for \(record.vaccineName)"
            attributeSet.keywords = [
                "vaccination",
                "vaccine",
                "immunization",
                record.vaccineName,
                record.veterinarian
            ]
            
            // Vaccination-specific attributes
            attributeSet.displayName = record.vaccineName
            attributeSet.contentCreationDate = record.dateAdministered
            
            // Custom attributes
            attributeSet.setValue(record.vaccineName as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "vaccineName")!)
            attributeSet.setValue(record.veterinarian as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "veterinarian")!)

            // For upcoming vaccinations, use nextDueDate
            if let nextDueDate = record.nextDueDate {
                attributeSet.setValue(nextDueDate as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "nextDueDate")!)
            }

            let item = CSSearchableItem(
                uniqueIdentifier: "vaccination_\(record.id)",
                domainIdentifier: "vaccinations",
                attributeSet: attributeSet
            )
            
            searchableItems.append(item)
        }
        
        do {
            try await searchableIndex.indexSearchableItems(searchableItems)
            indexedItemsCount += searchableItems.count
        } catch {
            print("Failed to index vaccination records: \(error)")
        }
    }
    
    // MARK: - AI Conversations Indexing
    
    func indexAIConversations() async {
        // Note: Using RealDataService instead of AIConversationManager
        let conversations: [AIConversation] = [] // TODO: Get conversations from RealDataService
        var searchableItems: [CSSearchableItem] = []
        
        for conversation in conversations {
            let attributeSet = CSSearchableItemAttributeSet(contentType: .content)
            
            // Basic attributes
            attributeSet.title = "AI Conversation - \(conversation.agentName)"
            attributeSet.contentDescription = conversation.title
            attributeSet.keywords = [
                "ai",
                "chat",
                "conversation",
                conversation.agentName,
                conversation.agentSpecialization
            ]

            // Conversation-specific attributes
            attributeSet.displayName = conversation.agentName
            attributeSet.contentCreationDate = conversation.createdAt
            attributeSet.contentModificationDate = conversation.updatedAt
            
            // Custom attributes
            attributeSet.setValue(conversation.agentSpecialization as NSSecureCoding, forCustomKey: CSCustomAttributeKey(keyName: "agentType")!)
            attributeSet.setValue(NSNumber(value: conversation.messageCount), forCustomKey: CSCustomAttributeKey(keyName: "messageCount")!)
            
            let item = CSSearchableItem(
                uniqueIdentifier: "conversation_\(conversation.id)",
                domainIdentifier: "conversations",
                attributeSet: attributeSet
            )
            
            searchableItems.append(item)
        }
        
        do {
            try await searchableIndex.indexSearchableItems(searchableItems)
            indexedItemsCount += searchableItems.count
        } catch {
            print("Failed to index AI conversations: \(error)")
        }
    }
    
    // MARK: - Individual Item Updates
    
    func indexPet(_ pet: Pet) async {
        let attributeSet = CSSearchableItemAttributeSet(contentType: .content)
        
        attributeSet.title = pet.name
        attributeSet.contentDescription = "\(pet.species) • \(pet.breed ?? "Unknown breed") • \(pet.age) months old"
        attributeSet.keywords = [pet.name, pet.species, pet.breed ?? "Unknown", "pet"]
        
        let item = CSSearchableItem(
            uniqueIdentifier: "pet_\(pet.id)",
            domainIdentifier: "pets",
            attributeSet: attributeSet
        )
        
        do {
            try await searchableIndex.indexSearchableItems([item])
        } catch {
            print("Failed to index pet: \(error)")
        }
    }
    
    func indexMemory(_ memory: Memory) async {
        let attributeSet = CSSearchableItemAttributeSet(contentType: .content)
        
        attributeSet.title = memory.title
        attributeSet.contentDescription = memory.content
        attributeSet.keywords = [memory.title, memory.content, "memory"] + memory.tags
        
        let item = CSSearchableItem(
            uniqueIdentifier: "memory_\(memory.id)",
            domainIdentifier: "memories",
            attributeSet: attributeSet
        )
        
        do {
            try await searchableIndex.indexSearchableItems([item])
        } catch {
            print("Failed to index memory: \(error)")
        }
    }
    
    // MARK: - Deletion
    
    func deleteFromIndex(identifier: String) async {
        do {
            try await searchableIndex.deleteSearchableItems(withIdentifiers: [identifier])
        } catch {
            print("Failed to delete from index: \(error)")
        }
    }
    
    func deleteAllFromDomain(_ domain: String) async {
        do {
            try await searchableIndex.deleteSearchableItems(withDomainIdentifiers: [domain])
        } catch {
            print("Failed to delete domain from index: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func loadPetImage(_ pet: Pet) async -> Data? {
        // Load pet image data for thumbnail
        // This would typically load from your image storage
        return nil
    }
    
    private func loadMemoryMedia(_ memory: Memory) async -> Data? {
        // Load memory media data for thumbnail
        // This would typically load from your media storage
        return nil
    }
    
    // MARK: - Search Handling
    
    func handleSpotlightSearch(userActivity: NSUserActivity) -> Bool {
        guard userActivity.activityType == CSSearchableItemActionType,
              let identifier = userActivity.userInfo?[CSSearchableItemActivityIdentifier] as? String else {
            return false
        }
        
        // Parse identifier and navigate to appropriate content
        let components = identifier.split(separator: "_")
        guard components.count == 2 else { return false }
        
        let type = String(components[0])
        let id = String(components[1])
        
        switch type {
        case "pet":
            navigateToPet(id: id)
        case "memory":
            navigateToMemory(id: id)
        case "health":
            navigateToHealthRecord(id: id)
        case "vaccination":
            navigateToVaccinationRecord(id: id)
        case "conversation":
            navigateToConversation(id: id)
        default:
            return false
        }
        
        return true
    }
    
    private func navigateToPet(id: String) {
        NotificationCenter.default.post(
            name: .navigateToSpotlightResult,
            object: SpotlightResult(type: .pet, id: id)
        )
    }
    
    private func navigateToMemory(id: String) {
        NotificationCenter.default.post(
            name: .navigateToSpotlightResult,
            object: SpotlightResult(type: .memory, id: id)
        )
    }
    
    private func navigateToHealthRecord(id: String) {
        NotificationCenter.default.post(
            name: .navigateToSpotlightResult,
            object: SpotlightResult(type: .health, id: id)
        )
    }
    
    private func navigateToVaccinationRecord(id: String) {
        NotificationCenter.default.post(
            name: .navigateToSpotlightResult,
            object: SpotlightResult(type: .vaccination, id: id)
        )
    }
    
    private func navigateToConversation(id: String) {
        NotificationCenter.default.post(
            name: .navigateToSpotlightResult,
            object: SpotlightResult(type: .conversation, id: id)
        )
    }
}

// MARK: - Supporting Types

struct SpotlightResult {
    let type: SpotlightResultType
    let id: String
}

enum SpotlightResultType {
    case pet
    case memory
    case health
    case vaccination
    case conversation
}

extension Notification.Name {
    static let navigateToSpotlightResult = Notification.Name("navigateToSpotlightResult")
}
