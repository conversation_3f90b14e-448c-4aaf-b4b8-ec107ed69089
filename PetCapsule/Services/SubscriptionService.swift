//
//  SubscriptionService.swift
//  PetCapsule
//
//  Premium subscription management for $2M/month revenue
//

import Foundation
import StoreKit
import SwiftUI

@MainActor
class SubscriptionService: ObservableObject {
    static let shared = SubscriptionService()

    @Published var subscriptionStatus: SubscriptionStatus = .pawStarter
    @Published var currentPlan: SubscriptionPlan?
    @Published var availablePlans: [SubscriptionPlan] = []
    @Published var isLoading = false
    @Published var revenue: RevenueMetrics = RevenueMetrics()

    private var updateListenerTask: Task<Void, Error>?

    init() {
        setupPlans()
        startListeningForTransactions()
    }

    deinit {
        updateListenerTask?.cancel()
    }

    // MARK: - Subscription Plans

    private func setupPlans() {
        availablePlans = [
            // 🐾 Paw Starter - Free Plan (1 pet, limited features, no AI)
            SubscriptionPlan(
                id: "paw_starter",
                name: "Paw Starter",
                price: 0,
                duration: .lifetime,
                features: [
                    "1 pet profile",
                    "5 memories per month",
                    "Basic photo storage (100MB)",
                    "Standard timeline view",
                    "Community access",
                    "Basic health tracking",
                    "Simple walk logging"
                ],
                limitations: [
                    "No AI features",
                    "No specialized AI agents",
                    "No video montages",
                    "No emergency protocols",
                    "No advanced analytics",
                    "No family sharing",
                    "Limited storage"
                ],
                maxPets: 1,
                subtitle: "Perfect for getting started with your first furry friend"
            ),

            // 🎯 Growing Bond - Premium Plan (2 pets, $9.99, core AI features)
            SubscriptionPlan(
                id: "growing_bond",
                name: "Growing Bond",
                price: 9.99,
                duration: .monthly,
                features: [
                    "Up to 2 pet profiles",
                    "👑 AI Pet Master agent",
                    "🏥 Health Guardian specialist",
                    "🥗 Dr. Nutrition specialist",
                    "📸 Enhanced memory storage (2GB)",
                    "🚨 Emergency protocols & first aid",
                    "📊 Health analytics",
                    "🎯 Smart walk planner",
                    "💬 Priority customer support",
                    "📱 Apple Watch integration",
                    "🌍 Environmental monitoring"
                ],
                aiFeatures: [
                    "Smart memory suggestions",
                    "Health pattern analysis",
                    "Nutrition optimization",
                    "Emergency guidance"
                ],
                maxPets: 2,
                popularBadge: true,
                subtitle: "Essential AI-powered care for growing pet families"
            ),

            // 👨‍👩‍👧‍👦 Family Circle - Family Plan (5 pets, $14.99, full family features)
            SubscriptionPlan(
                id: "family_circle",
                name: "Family Circle",
                price: 14.99,
                duration: .monthly,
                features: [
                    "Everything in Growing Bond",
                    "Up to 5 pet profiles",
                    "🎾 Trainer Pro specialist",
                    "✂️ Style Guru specialist",
                    "🛍️ Shopping Assistant specialist",
                    "🛡️ Insurance Advisor specialist",
                    "🏠 Wellness Coach specialist",
                    "👥 Family sharing & multi-user access",
                    "📱 Family dashboard & coordination",
                    "📈 Family pet health reporting",
                    "🎨 Premium memorial themes",
                    "📍 Location sharing for emergencies",
                    "🌡️ Environmental scoring & alerts",
                    "💾 10GB premium storage",
                    "🎯 Collaborative care planning"
                ],
                aiFeatures: [
                    "All Growing Bond AI features",
                    "Multi-pet behavior correlation",
                    "Family activity coordination",
                    "Cross-pet health insights"
                ],
                maxPets: 5,
                familyFeatures: true,
                subtitle: "Complete AI ecosystem for multi-pet households"
            ),

            // 🏆 Premium Pro - Large Family Plan (10 pets, $19.99, ultimate features)
            SubscriptionPlan(
                id: "premium_pro",
                name: "Premium Pro",
                price: 19.99,
                duration: .monthly,
                features: [
                    "Everything in Family Circle",
                    "Up to 10 pet profiles",
                    "🏆 All 8 specialized AI agents",
                    "🌍 Social Dashboard & community features",
                    "📅 Advanced planning & resource hub",
                    "🎪 Community events & coordination",
                    "📊 Advanced analytics dashboard",
                    "🔄 Real-time health monitoring",
                    "🎯 Personalized breed recommendations",
                    "💎 Custom branding options",
                    "📡 API access for integrations",
                    "💾 50GB ultimate storage",
                    "🎖️ Exclusive Premium Pro badge",
                    "🚀 Early access to new features"
                ],
                aiFeatures: [
                    "All previous AI features",
                    "Large pack management",
                    "Advanced social coordination",
                    "Predictive health modeling"
                ],
                maxPets: 10,
                familyFeatures: true,
                isUltimate: true,
                subtitle: "Ultimate AI-powered solution for large pet families"
            )


        ]
    }

    // MARK: - Purchase Management

    func purchase(_ plan: SubscriptionPlan) async throws {
        isLoading = true
        defer { isLoading = false }

        // Simulate StoreKit purchase
        try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

        // Update subscription status
        subscriptionStatus = .growingBond
        currentPlan = plan

        // Track revenue
        await trackRevenue(plan: plan, type: .subscription)

        // Unlock features
        await unlockPremiumFeatures()

        // Send to analytics
        await AnalyticsService.shared.trackPurchase(plan: plan)
    }

    func restorePurchases() async throws {
        isLoading = true
        defer { isLoading = false }

        // Simulate restore
        try await Task.sleep(nanoseconds: 1_000_000_000)

        // Check for existing purchases
        if let existingPlan = availablePlans.first(where: { $0.id == "premium_monthly" }) {
            subscriptionStatus = .growingBond
            currentPlan = existingPlan
            await unlockPremiumFeatures()
        }
    }

    // MARK: - Revenue Tracking

    private func trackRevenue(plan: SubscriptionPlan, type: RevenueType) async {
        let revenueEvent = RevenueEvent(
            amount: plan.price,
            plan: plan,
            type: type,
            timestamp: Date()
        )

        revenue.addEvent(revenueEvent)

        // Send to backend analytics
        await sendRevenueToBackend(revenueEvent)
    }

    private func sendRevenueToBackend(_ event: RevenueEvent) async {
        // Integration with analytics platforms
        // Mixpanel, Amplitude, custom backend
    }

    // MARK: - Feature Management

    private func unlockPremiumFeatures() async {
        // Unlock AI features
        // AIService.shared.unlockPremiumFeatures()

        // Unlock video features
        // VideoMontageService.shared.unlockPremiumFeatures()

        // Unlock storage
        // StorageService.shared.upgradeToPremium()

        // Unlock themes
        // ThemeService.shared.unlockPremiumThemes()
    }

    func hasFeature(_ feature: PremiumFeature) -> Bool {
        // ✅ ALL FEATURES UNLOCKED - Building app first, then deciding on premium
        return true
    }
    
    // ✅ Override: All subscription checks return unlocked
    var isSubscriptionActive: Bool { return true }
    var isPremiumUser: Bool { return true }
    var hasUnlimitedAccess: Bool { return true }

    // MARK: - Transaction Listening

    private func startListeningForTransactions() {
        updateListenerTask = Task.detached {
            for await result in Transaction.updates {
                do {
                    let transaction = try await self.checkVerified(result)
                    await self.updateSubscriptionStatus(transaction)
                    await transaction.finish()
                } catch {
                    print("Transaction verification failed: \(error)")
                }
            }
        }
    }

    private func checkVerified<T>(_ result: VerificationResult<T>) async throws -> T {
        switch result {
        case .unverified:
            throw SubscriptionError.failedVerification
        case .verified(let safe):
            return safe
        }
    }

    private func updateSubscriptionStatus(_ transaction: StoreKit.Transaction) async {
        // Update subscription based on transaction
        if let plan = availablePlans.first(where: { $0.id == transaction.productID }) {
            subscriptionStatus = .growingBond
            currentPlan = plan
            await unlockPremiumFeatures()
        }
    }
}

// MARK: - Data Models

struct SubscriptionPlan: Identifiable, Codable {
    let id: String
    let name: String
    let price: Double
    let duration: SubscriptionDuration
    let features: [String]
    var limitations: [String] = []
    var aiFeatures: [String] = []
    var maxPets: Int = 3
    var familyFeatures: Bool = false
    var businessFeatures: Bool = false
    var isProfessional: Bool = false
    var isLifetime: Bool = false
    var popularBadge: Bool = false
    var isUltimate: Bool = false
    var subtitle: String? = nil

    var formattedPrice: String {
        if price == 0 {
            return "Free"
        } else if isLifetime {
            return "$\(Int(price)) once"
        } else {
            return String(format: "$%.2f/month", price)
        }
    }

    var monthlyRevenue: Double {
        switch duration {
        case .monthly:
            return price
        case .yearly:
            return price / 12
        case .lifetime:
            return price / 60 // Amortized over 5 years
        }
    }
}

enum SubscriptionDuration: String, Codable, CaseIterable {
    case monthly = "monthly"
    case yearly = "yearly"
    case lifetime = "lifetime"
}

enum SubscriptionStatus: String, Codable {
    case pawStarter = "paw_starter"
    case growingBond = "growing_bond"
    case familyCircle = "family_circle"
    case premiumPro = "premium_pro"
    case expired = "expired"
}

enum PremiumFeature: String, CaseIterable {
    case aiCuration = "ai_curation"
    case videoMontages = "video_montages"
    case premiumThemes = "premium_themes"
    case unlimitedStorage = "unlimited_storage"
    case familySharing = "family_sharing"
    case businessTools = "business_tools"
    case apiAccess = "api_access"
}

enum RevenueType: String, Codable {
    case subscription = "subscription"
    case oneTime = "one_time"
    case addon = "addon"
    case marketplace = "marketplace"
}

struct RevenueEvent: Identifiable, Codable {
    var id = UUID()
    let amount: Double
    let plan: SubscriptionPlan
    let type: RevenueType
    let timestamp: Date
}

@MainActor
class RevenueMetrics: ObservableObject {
    @Published var monthlyRevenue: Double = 0
    @Published var totalRevenue: Double = 0
    @Published var activeSubscribers: Int = 0
    @Published var churnRate: Double = 0
    @Published var averageRevenuePerUser: Double = 0
    @Published var revenueGrowth: Double = 0

    private var events: [RevenueEvent] = []

    func addEvent(_ event: RevenueEvent) {
        events.append(event)
        calculateMetrics()
    }

    private func calculateMetrics() {
        let currentMonth = Calendar.current.component(.month, from: Date())
        let currentYear = Calendar.current.component(.year, from: Date())

        // Monthly revenue
        monthlyRevenue = events
            .filter {
                Calendar.current.component(.month, from: $0.timestamp) == currentMonth &&
                Calendar.current.component(.year, from: $0.timestamp) == currentYear
            }
            .reduce(0) { $0 + $1.amount }

        // Total revenue
        totalRevenue = events.reduce(0) { $0 + $1.amount }

        // Active subscribers (mock)
        activeSubscribers = Int(monthlyRevenue / 20) // Assuming $20 average

        // ARPU
        if activeSubscribers > 0 {
            averageRevenuePerUser = monthlyRevenue / Double(activeSubscribers)
        }
    }
}

enum SubscriptionError: Error {
    case failedVerification
    case purchaseFailed
    case restoreFailed
    case networkError
}
