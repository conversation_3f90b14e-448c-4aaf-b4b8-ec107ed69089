//
//  SwiftDataConfiguration.swift
//  PetCapsule
//
//  🍎 SwiftData Configuration with CloudKit Integration
//

import Foundation
import SwiftData
import CloudKit

struct SwiftDataConfiguration {
    static let shared = SwiftDataConfiguration()
    
    let cloudKitEnabled = true
    let cloudKitContainerIdentifier = "iCloud.com.petcapsule.PetCapsule"
    
    var modelContainer: ModelContainer {
        // Use basic schema to avoid conflicts
        let schema = Schema([])
        
        let modelConfiguration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            cloudKitDatabase: .none
        )
        
        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            print("❌ Failed to create ModelContainer: \(error)")
            // Return simple fallback
            let fallbackSchema = Schema([])
            let fallbackConfiguration = ModelConfiguration(
                schema: fallbackSchema,
                isStoredInMemoryOnly: true
            )
            do {
                return try ModelContainer(for: fallbackSchema, configurations: [fallbackConfiguration])
            } catch {
                fatalError("Failed to create fallback ModelContainer: \(error)")
            }
        }
    }
} 