//
//  ThemeManager.swift
//  PetCapsule
//
//  Enhanced with Glassmorphism Design System
//

import SwiftUI
import Combine

struct GlassConfig {
    let blurRadius: CGFloat
    let opacity: Double
    let borderOpacity: Double
    let shadowRadius: CGFloat
    let cornerRadius: CGFloat
    
    init(
        blurRadius: CGFloat = 10,
        opacity: Double = 0.25,
        borderOpacity: Double = 0.5,
        shadowRadius: CGFloat = 10,
        cornerRadius: CGFloat = 16
    ) {
        self.blurRadius = blurRadius
        self.opacity = opacity
        self.borderOpacity = borderOpacity
        self.shadowRadius = shadowRadius
        self.cornerRadius = cornerRadius
    }
}

enum AppTheme: String, CaseIterable {
    case system = "System"
    case light = "Light"
    case dark = "Dark"
    
    var colorScheme: ColorScheme? {
        switch self {
        case .system:
            return nil
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
    
    var icon: String {
        switch self {
        case .system:
            return "circle.lefthalf.filled"
        case .light:
            return "sun.max.fill"
        case .dark:
            return "moon.fill"
        }
    }
}

enum GlassDesignTheme: String, CaseIterable {
    case aurora = "Aurora"
    case nordic = "Nordic"
    case mediterranean = "Mediterranean"
    case prismatic = "Prismatic"
    
    var icon: String {
        switch self {
        case .aurora:
            return "sparkles"
        case .nordic:
            return "snowflake"
        case .mediterranean:
            return "sun.max"
        case .prismatic:
            return "rainbow"
        }
    }
    
    var description: String {
        switch self {
        case .aurora:
            return "Northern lights inspired with green, blue, and purple tones"
        case .nordic:
            return "Clean Scandinavian design with ice blue and frost white"
        case .mediterranean:
            return "Warm coastal colors with turquoise, coral, and gold"
        case .prismatic:
            return "Rainbow spectrum with dynamic color transitions"
        }
    }
    
    var glassThemeConfig: GlassConfig {
        switch self {
        case .aurora:
            return GlassConfig(blurRadius: 15, opacity: 0.25, borderOpacity: 0.4, shadowRadius: 15, cornerRadius: 20)
        case .nordic:
            return GlassConfig(blurRadius: 8, opacity: 0.2, borderOpacity: 0.3, shadowRadius: 8, cornerRadius: 12)
        case .mediterranean:
            return GlassConfig(blurRadius: 12, opacity: 0.3, borderOpacity: 0.5, shadowRadius: 12, cornerRadius: 16)
        case .prismatic:
            return GlassConfig(
                blurRadius: 12,
                opacity: 0.3,
                borderOpacity: 0.6,
                shadowRadius: 12,
                cornerRadius: 18
            )
        }
    }
}

@MainActor
class ThemeManager: ObservableObject {
    static let shared = ThemeManager()
    
    @Published var currentTheme: AppTheme {
        didSet {
            UserDefaults.standard.set(currentTheme.rawValue, forKey: "selectedTheme")
            applyTheme()
        }
    }
    
    @Published var currentGlassTheme: GlassDesignTheme {
        didSet {
            UserDefaults.standard.set(currentGlassTheme.rawValue, forKey: "selectedGlassTheme")
        }
    }
    
    @Published var isGlassmorphismEnabled: Bool {
        didSet {
            UserDefaults.standard.set(isGlassmorphismEnabled, forKey: "glassmorphismEnabled")
        }
    }
    
    @Published var glassIntensity: Double {
        didSet {
            UserDefaults.standard.set(glassIntensity, forKey: "glassIntensity")
        }
    }
    
    @Published var glassAnimationsEnabled: Bool {
        didSet {
            UserDefaults.standard.set(glassAnimationsEnabled, forKey: "glassAnimationsEnabled")
        }
    }
    
    @Published var isDarkMode: Bool = false
    
    private init() {
        // Load saved theme or default to system
        let savedTheme = UserDefaults.standard.string(forKey: "selectedTheme") ?? AppTheme.system.rawValue
        self.currentTheme = AppTheme(rawValue: savedTheme) ?? .system
        
        // Load glass theme settings
        let savedGlassTheme = UserDefaults.standard.string(forKey: "selectedGlassTheme") ?? GlassDesignTheme.aurora.rawValue
        self.currentGlassTheme = GlassDesignTheme(rawValue: savedGlassTheme) ?? .aurora
        
        // Check if keys exist, if not use defaults
        if UserDefaults.standard.object(forKey: "glassmorphismEnabled") != nil {
            self.isGlassmorphismEnabled = UserDefaults.standard.bool(forKey: "glassmorphismEnabled")
        } else {
            self.isGlassmorphismEnabled = true
        }
        
        let savedIntensity = UserDefaults.standard.double(forKey: "glassIntensity")
        self.glassIntensity = savedIntensity != 0 ? savedIntensity : 1.0
        
        if UserDefaults.standard.object(forKey: "glassAnimationsEnabled") != nil {
            self.glassAnimationsEnabled = UserDefaults.standard.bool(forKey: "glassAnimationsEnabled")
        } else {
            self.glassAnimationsEnabled = true
        }
        
        // Set initial dark mode state
        updateDarkModeState()
        applyTheme()
    }
    
    func setTheme(_ theme: AppTheme) {
        currentTheme = theme
    }
    
    func setGlassTheme(_ glassTheme: GlassDesignTheme) {
        currentGlassTheme = glassTheme
    }
    
    func toggleGlassmorphism() {
        isGlassmorphismEnabled.toggle()
    }
    
    func setGlassIntensity(_ intensity: Double) {
        glassIntensity = max(0.1, min(2.0, intensity))
    }
    
    func toggleGlassAnimations() {
        glassAnimationsEnabled.toggle()
    }
    
    private func applyTheme() {
        updateDarkModeState()
        
        // Apply to all windows
        for scene in UIApplication.shared.connectedScenes {
            if let windowScene = scene as? UIWindowScene {
                for window in windowScene.windows {
                    window.overrideUserInterfaceStyle = currentTheme.colorScheme?.uiUserInterfaceStyle ?? .unspecified
                }
            }
        }
    }
    
    private func updateDarkModeState() {
        switch currentTheme {
        case .system:
            isDarkMode = UITraitCollection.current.userInterfaceStyle == .dark
        case .light:
            isDarkMode = false
        case .dark:
            isDarkMode = true
        }
    }
    
    // Computed properties for easy access
    var currentGlassConfig: GlassConfig {
        return currentGlassTheme.glassThemeConfig
    }
    
    var effectiveGlassIntensity: Double {
        return isGlassmorphismEnabled ? glassIntensity : 0.1
    }
}

// MARK: - ColorScheme Extension
extension ColorScheme {
    var uiUserInterfaceStyle: UIUserInterfaceStyle {
        switch self {
        case .light:
            return .light
        case .dark:
            return .dark
        @unknown default:
            return .unspecified
        }
    }
}

// MARK: - Enhanced Theme-Aware Colors
extension Color {
    static var themeBackground: Color {
        Color(UIColor.systemBackground)
    }
    
    static var themeSecondaryBackground: Color {
        Color(UIColor.secondarySystemBackground)
    }
    
    static var themeTertiaryBackground: Color {
        Color(UIColor.tertiarySystemBackground)
    }
    
    static var themePrimary: Color {
        Color(UIColor.label)
    }
    
    static var themeSecondary: Color {
        Color(UIColor.secondaryLabel)
    }
    
    static var themeTertiary: Color {
        Color(UIColor.tertiaryLabel)
    }
    
    static var themeAccent: Color {
        Color.purple
    }
    
    static var themeBorder: Color {
        Color(UIColor.separator)
    }
    
    static var themeCardBackground: Color {
        Color(UIColor.systemBackground)
    }
    
    static var themeCardShadow: Color {
        Color.black.opacity(0.1)
    }
    
    // Glass-aware colors removed - using direct theme properties in view modifiers
}

// MARK: - Enhanced Theme-Aware Gradients
extension LinearGradient {
    static var themeCard: LinearGradient {
        LinearGradient(
            colors: [
                Color.themeCardBackground,
                Color.themeSecondaryBackground.opacity(0.5)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    static var themeAccentGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color.purple.opacity(0.8),
                Color.blue.opacity(0.6)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // Glass-aware gradients removed - using direct theme properties in view modifiers
}

// MARK: - Enhanced View Modifiers
struct AdaptiveGlassCard: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager
    let fallbackStyle: Bool
    
    init(fallbackStyle: Bool = true) {
        self.fallbackStyle = fallbackStyle
    }
    
    func body(content: Content) -> some View {
        if themeManager.isGlassmorphismEnabled {
            content
                .glassCard(intensity: themeManager.effectiveGlassIntensity)
        } else if fallbackStyle {
            content
                .background(Color.themeCardBackground)
                .cornerRadius(16)
                .shadow(
                    color: Color.themeCardShadow,
                    radius: themeManager.isDarkMode ? 0 : 8,
                    x: 0,
                    y: themeManager.isDarkMode ? 0 : 2
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.themeBorder.opacity(themeManager.isDarkMode ? 0.3 : 0.1), lineWidth: 1)
                )
        } else {
            content
        }
    }
}

struct AdaptiveGlassButton: ViewModifier {
    let style: GlassButtonType
    let fallbackStyle: ButtonStyleType
    @EnvironmentObject private var themeManager: ThemeManager
    
    init(style: GlassButtonType = .primary, fallbackStyle: ButtonStyleType = .primary) {
        self.style = style
        self.fallbackStyle = fallbackStyle
    }
    
    func body(content: Content) -> some View {
        if themeManager.isGlassmorphismEnabled {
            content
                .glassButton(style)
        } else {
            content
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(fallbackStyle.backgroundColor)
                .foregroundColor(fallbackStyle.foregroundColor)
                .cornerRadius(12)
                .shadow(color: fallbackStyle.shadowColor, radius: 2, x: 0, y: 1)
        }
    }
}

struct AdaptiveGlassTextField: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager
    
    func body(content: Content) -> some View {
        if themeManager.isGlassmorphismEnabled {
            content
                .glassTextField()
        } else {
            content
                .padding(16)
                .background(Color.themeSecondaryBackground)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.themeTertiary.opacity(0.3), lineWidth: 1)
                )
        }
    }
}

struct AdaptiveGlassBackground: ViewModifier {
    @EnvironmentObject private var themeManager: ThemeManager
    
    func body(content: Content) -> some View {
        if themeManager.isGlassmorphismEnabled && themeManager.glassAnimationsEnabled {
            content
                .glassBackground(animated: true)
        } else if themeManager.isGlassmorphismEnabled {
            content
                .background(Color.themeBackground.ignoresSafeArea())
        } else {
            content
                .background(Color.themeBackground.ignoresSafeArea())
        }
    }
}

// MARK: - ButtonStyleType Extension (Legacy Support)
enum ButtonStyleType {
    case primary
    case secondary
    case tertiary
    case destructive

    var backgroundColor: some View {
        switch self {
        case .primary:
            return AnyView(LinearGradient.themeAccentGradient)
        case .secondary:
            return AnyView(Color.themeSecondaryBackground)
        case .tertiary:
            return AnyView(Color.clear)
        case .destructive:
            return AnyView(Color.red)
        }
    }

    var foregroundColor: Color {
        switch self {
        case .primary, .destructive:
            return .white
        case .secondary, .tertiary:
            return .themePrimary
        }
    }

    var shadowColor: Color {
        switch self {
        case .primary:
            return Color.themeAccent.opacity(0.3)
        case .destructive:
            return Color.red.opacity(0.3)
        default:
            return Color.black.opacity(0.1)
        }
    }
    
    func toGlassButtonType() -> GlassButtonType {
        switch self {
        case .primary:
            return .primary
        case .secondary:
            return .secondary
        case .tertiary:
            return .ghost
        case .destructive:
            return .destructive
        }
    }
}

// MARK: - Enhanced View Extensions
extension View {
    // Legacy support with automatic glass upgrade
    func themeCard() -> some View {
        modifier(AdaptiveGlassCard())
    }
    
    func themeButton(_ style: ButtonStyleType = .primary) -> some View {
        modifier(AdaptiveGlassButton(
            style: style.toGlassButtonType(),
            fallbackStyle: style
        ))
    }
    
    func themeTextField() -> some View {
        modifier(AdaptiveGlassTextField())
    }
    
    func themeBackground() -> some View {
        modifier(AdaptiveGlassBackground())
    }
    
    func preferredColorScheme(_ theme: AppTheme) -> some View {
        self.preferredColorScheme(theme.colorScheme)
    }
    
    // Glass theme configuration
    func adaptiveGlassTheme() -> some View {
        self.environmentObject(ThemeManager.shared)
    }
}
