//
//  TrainingPlannerService.swift
//  PetCapsule
//
//  Comprehensive training planning and progress tracking service
//
import Foundation
import SwiftUI
@MainActor
class TrainingPlannerService: ObservableObject {
    static let shared = TrainingPlannerService()
    @Published var trainingPlans: [TrainingPlan] = []
    @Published var trainingPrograms: [TrainingProgram] = []
    @Published var trainingSessions: [TrainingSession] = []
    @Published var skillAssessments: [SkillAssessment] = []
    @Published var isLoading = false
    private let petDataManager = PetDataManager.shared
    private init() {
        loadSampleData()
    }
    // MARK: - Training Plan Generation
    func generateTrainingPlan(for pet: Pet, goals: Set<TrainingGoal>, experience: ExperienceLevel) async -> TrainingPlan {
        isLoading = true
        defer { isLoading = false }
        // Use comprehensive pet data to determine actual training level and needs
        let actualExperience = determineActualExperienceLevel(for: pet, suggested: experience)
        let enhancedGoals = enhanceGoalsBasedOnPetData(for: pet, requestedGoals: goals)
        print("🎾 Generating training plan for \(pet.name)")
        print("   Known commands: \(pet.knownCommands.count)")
        print("   Behavior issues: \(pet.behaviorIssues.count)")
        print("   Training level: \(pet.trainingLevel ?? "Not set")")
        print("   Training status: \(petDataManager.evaluateTrainingStatus(for: pet).rawValue)")
        // Generate customized training modules based on comprehensive pet characteristics
        let modules = generateComprehensiveTrainingModules(for: pet, goals: enhancedGoals, experience: actualExperience)
        // Create personalized training schedule
        let schedule = generatePersonalizedTrainingSchedule(for: pet, modules: modules, experience: actualExperience)
        // Set realistic milestones based on current progress
        let milestones = generateRealisticMilestones(for: pet, goals: enhancedGoals, modules: modules)
        let plan = TrainingPlan(
            id: UUID().uuidString,
            petId: pet.id,
            petName: pet.name,
            goals: Array(enhancedGoals),
            experienceLevel: actualExperience,
            modules: modules,
            schedule: schedule,
            milestones: milestones,
            estimatedDuration: Int(calculateRealisticDuration(for: pet, modules: modules)),
            createdAt: Date(),
            lastUpdated: Date()
        )
        trainingPlans.append(plan)
        return plan
    }
    // MARK: - Comprehensive Training Assessment
    private func determineActualExperienceLevel(for pet: Pet, suggested: ExperienceLevel) -> ExperienceLevel {
        let knownCommandsCount = pet.knownCommands.count
        let _ = pet.behaviorIssues.count // Unused but may be needed for future logic
        let trainingStatus = petDataManager.evaluateTrainingStatus(for: pet)
        // Override suggested experience based on actual pet data
        switch trainingStatus {
        case .notStarted:
            return .beginner
        case .inProgress:
            if knownCommandsCount >= 10 {
                return .advanced
            } else if knownCommandsCount >= 5 {
                return .intermediate
            } else {
                return .beginner
            }
        case .advanced:
            return knownCommandsCount >= 15 ? .advanced : .intermediate
        }
    }
    private func enhanceGoalsBasedOnPetData(for pet: Pet, requestedGoals: Set<TrainingGoal>) -> Set<TrainingGoal> {
        var enhancedGoals = requestedGoals
        // Add goals based on behavior issues
        for issue in pet.behaviorIssues {
            switch issue.lowercased() {
            case let i where i.contains("aggress"):
                enhancedGoals
            case let i where i.contains("bark"):
                enhancedGoals
            case let i where i.contains("pull"):
                enhancedGoals
            case let i where i.contains("house"):
                enhancedGoals
            case let i where i.contains("social"):
                enhancedGoals
            default:
                break
            }
        }
        // Add basic obedience if no commands known
        if pet.knownCommands.isEmpty {
            enhancedGoals
        }
        // Add advanced tricks if highly trained
        if pet.knownCommands.count >= 10 {
            enhancedGoals
        }
        return enhancedGoals
    }
    private func generateComprehensiveTrainingModules(for pet: Pet, goals: Set<TrainingGoal>, experience: ExperienceLevel) -> [TrainingModule] {
        var modules: [TrainingModule] = []
        // Basic obedience (customize based on known commands)
        if experience == .beginner || goals.contains(.basicObedience) {
            modules.append(createPersonalizedBasicObedienceModule(for: pet))
        }
        // Goal-specific modules with personalization
        for goal in goals {
            switch goal {
            case .basicObedience:
                break // Already handled above
            case .houseTraining:
                modules.append(createPersonalizedHouseTrainingModule(for: pet))
            case .leashTraining:
                modules.append(createPersonalizedLeashTrainingModule(for: pet))
            case .socialSkills:
                modules.append(createPersonalizedSocializationModule(for: pet))
            case .advancedTricks:
                modules.append(createPersonalizedAdvancedTricksModule(for: pet))
            case .behaviorCorrection:
                modules.append(createPersonalizedBehaviorCorrectionModule(for: pet))
            case .agility:
                modules.append(createPersonalizedAgilityModule(for: pet))
            case .therapy:
                modules.append(createPersonalizedTherapyTrainingModule(for: pet))
            }
        }
        return modules
    }
    private func createPersonalizedBasicObedienceModule(for pet: Pet) -> TrainingModule {
        let knownCommands = Set(pet.knownCommands.map { $0.lowercased() })
        var exercises: [TrainingExercise] = []
        // Only include exercises for commands the pet doesn't know
        if !knownCommands.contains("sit") {
            exercises.append(createSitCommandExercise(for: pet))
        }
        if !knownCommands.contains("stay") {
            exercises.append(createStayCommandExercise(for: pet))
        }
        if !knownCommands.contains("come") {
            exercises.append(createComeCommandExercise(for: pet))
        }
        if !knownCommands.contains("down") {
            exercises.append(createDownCommandExercise(for: pet))
        }
        // If pet knows all basic commands, add intermediate ones
        if exercises.isEmpty {
            exercises.append(createAdvancedObedienceExercise(for: pet))
        }
        let estimatedWeeks = Double(max(2, exercises.count * 1)) // At least 2 weeks, 1 week per new command
        return TrainingModule(
            id: UUID().uuidString,
            name: "Personalized Basic Obedience",
            description: "Customized foundation commands based on \(pet.name)'s current knowledge",
            category: .obedience,
            difficulty: knownCommands.count >= 3 ? .intermediate : .beginner,
            estimatedWeeks: estimatedWeeks,
            exercises: exercises,
            prerequisites: [],
            learningObjectives: exercises.map { "Master \($0.name) command" }
        )
    }
    private func createPersonalizedBehaviorCorrectionModule(for pet: Pet) -> TrainingModule {
        var exercises: [TrainingExercise] = []
        // Create targeted exercises for specific behavior issues
        for issue in pet.behaviorIssues {
            switch issue.lowercased() {
            case let i where i.contains("bark"):
                exercises.append(createBarkingCorrectionExercise(for: pet))
            case let i where i.contains("jump"):
                exercises.append(createJumpingCorrectionExercise(for: pet))
            case let i where i.contains("pull"):
                exercises.append(createPullingCorrectionExercise(for: pet))
            case let i where i.contains("chew"):
                exercises.append(createChewingCorrectionExercise(for: pet))
            default:
                exercises.append(createGeneralBehaviorExercise(for: pet, issue: issue))
            }
        }
        if exercises.isEmpty {
            exercises.append(createPreventiveBehaviorExercise(for: pet))
        }
        return TrainingModule(
            id: UUID().uuidString,
            name: "Targeted Behavior Correction",
            description: "Address specific behavioral concerns: \(pet.behaviorIssues.joined(separator: ", "))",
            category: .obedience, // Use existing category
            difficulty: .intermediate,
            estimatedWeeks: Double(exercises.count * 2), // 2 weeks per behavior issue
            exercises: exercises,
            prerequisites: ["Basic obedience commands"],
            learningObjectives: pet.behaviorIssues.map { "Reduce \($0.lowercased()) behavior" }
        )
    }
    private func createBasicObedienceModule(for pet: Pet) -> TrainingModule {
        let exercises = [
            TrainingExercise(
                id: UUID().uuidString,
                name: "Sit Command",
                description: "Teach your pet to sit on command",
                category: .obedience,
                difficulty: .beginner,
                steps: [
                    "Hold a treat close to your pet's nose",
                    "Slowly move the treat up, allowing their head to follow",
                    "As their head moves up, their bottom should naturally lower",
                    "Once sitting, say 'Sit' and give the treat",
                    "Repeat 5-10 times per session"
                ],
                videoURL: nil,
                requiredEquipment: ["Treats", "Quiet space"],
                tips: [
                    "Keep sessions short (5-15 minutes)",
                    "Use high-value treats",
                    "Practice before meal times when your pet is hungry",
                    "Be patient and consistent"
                ]
            ),
            TrainingExercise(
                id: UUID().uuidString,
                name: "Stay Command",
                description: "Teach your pet to stay in position",
                category: .obedience,
                difficulty: .intermediate,
                steps: [
                    "Start with your pet in a sit position",
                    "Hold your hand up in a 'stop' gesture",
                    "Say 'Stay' and take one step back",
                    "Wait 2-3 seconds, then return and reward",
                    "Gradually increase distance and duration"
                ],
                videoURL: nil,
                requiredEquipment: ["Treats", "Open space"],
                tips: [
                    "Start with very short durations",
                    "Don't call your pet to you - always return to them",
                    "Release with a specific word like 'Okay' or 'Free'"
                ]
            )
        ]
        return TrainingModule(
            id: UUID().uuidString,
            name: "Basic Obedience",
            description: "Foundation commands every pet should know",
            category: .obedience,
            difficulty: .beginner,
            estimatedWeeks: 4,
            exercises: exercises,
            prerequisites: [],
            learningObjectives: [
                "Pet responds to 'Sit' command 80% of the time",
                "Pet holds 'Stay' for 30 seconds at 6 feet distance",
                "Pet comes when called in low-distraction environment"
            ]
        )
    }
    private func createHouseTrainingModule(for pet: Pet) -> TrainingModule {
        let exercises = [
            TrainingExercise(
                id: UUID().uuidString,
                name: "Crate Training",
                description: "Establish a safe space and routine",
                category: .houseTraining,
                difficulty: .beginner,
                steps: [
                    "Make the crate comfortable with bedding",
                    "Feed meals in the crate",
                    "Start with short periods with door open",
                    "Gradually increase time with door closed",
                    "Never use crate as punishment"
                ],
                videoURL: nil,
                requiredEquipment: ["Appropriate-sized crate", "Comfortable bedding", "Water bowl"],
                tips: [
                    "Size crate appropriately - room to stand and turn",
                    "Place in quiet but not isolated area",
                    "Ignore whining if you know they don't need to go out"
                ]
            )
        ]
        return TrainingModule(
            id: UUID().uuidString,
            name: "House Training",
            description: "Establish proper bathroom habits",
            category: .houseTraining,
            difficulty: .beginner,
            estimatedWeeks: 8,
            exercises: exercises,
            prerequisites: [],
            learningObjectives: [
                "Pet signals when they need to go outside",
                "Accidents reduced to less than once per week",
                "Pet is comfortable in crate for 4+ hours"
            ]
        )
    }
    private func createLeashTrainingModule(for pet: Pet) -> TrainingModule {
        // Implementation for leash training
        return TrainingModule(
            id: UUID().uuidString,
            name: "Leash Training",
            description: "Walk politely on leash without pulling",
            category: .leashTraining,
            difficulty: .intermediate,
            estimatedWeeks: 6,
            exercises: [],
            prerequisites: ["Basic Obedience"],
            learningObjectives: [
                "Pet walks beside you without pulling",
                "Pet stops when you stop",
                "Pet responds to direction changes"
            ]
        )
    }
    private func createSocializationModule(for pet: Pet) -> TrainingModule {
        // Implementation for socialization
        return TrainingModule(
            id: UUID().uuidString,
            name: "Socialization",
            description: "Positive interactions with people, pets, and environments",
            category: .socialization,
            difficulty: .intermediate,
            estimatedWeeks: 12,
            exercises: [],
            prerequisites: [],
            learningObjectives: [
                "Pet remains calm around strangers",
                "Pet plays appropriately with other pets",
                "Pet adapts to new environments confidently"
            ]
        )
    }
    private func createAdvancedTricksModule(for pet: Pet) -> TrainingModule {
        // Implementation for advanced tricks
        return TrainingModule(
            id: UUID().uuidString,
            name: "Advanced Tricks",
            description: "Fun and impressive tricks to show off",
            category: .tricks,
            difficulty: .advanced,
            estimatedWeeks: 8,
            exercises: [],
            prerequisites: ["Basic Obedience"],
            learningObjectives: [
                "Pet performs 5+ complex tricks on command",
                "Pet chains multiple behaviors together",
                "Pet performs tricks in various environments"
            ]
        )
    }
    private func createBehaviorCorrectionModule(for pet: Pet) -> TrainingModule {
        // Implementation for behavior correction
        return TrainingModule(
            id: UUID().uuidString,
            name: "Behavior Correction",
            description: "Address problematic behaviors",
            category: .behaviorModification,
            difficulty: .advanced,
            estimatedWeeks: 10,
            exercises: [],
            prerequisites: ["Basic Obedience"],
            learningObjectives: [
                "Reduce unwanted behaviors by 80%",
                "Pet responds to redirection commands",
                "Pet demonstrates improved impulse control"
            ]
        )
    }
    private func createAgilityModule(for pet: Pet) -> TrainingModule {
        // Implementation for agility training
        return TrainingModule(
            id: UUID().uuidString,
            name: "Agility Training",
            description: "Navigate obstacles with speed and precision",
            category: .agility,
            difficulty: .advanced,
            estimatedWeeks: 16,
            exercises: [],
            prerequisites: ["Basic Obedience", "Leash Training"],
            learningObjectives: [
                "Pet navigates basic agility course",
                "Pet responds to directional commands",
                "Pet demonstrates confidence on obstacles"
            ]
        )
    }
    private func createTherapyTrainingModule(for pet: Pet) -> TrainingModule {
        // Implementation for therapy training
        return TrainingModule(
            id: UUID().uuidString,
            name: "Therapy Training",
            description: "Prepare for therapy or service work",
            category: .therapy,
            difficulty: .expert,
            estimatedWeeks: 24,
            exercises: [],
            prerequisites: ["Basic Obedience", "Socialization", "Behavior Correction"],
            learningObjectives: [
                "Pet remains calm in medical environments",
                "Pet provides comfort on command",
                "Pet ignores distractions during work"
            ]
        )
    }
    private func generateTrainingSchedule(modules: [TrainingModule], experience: ExperienceLevel) -> TrainingSchedule {
        let sessionsPerWeek = experience == .beginner ? 3 : (experience == .intermediate ? 4 : 5)
        let sessionDuration = experience == .beginner ? 15 : (experience == .intermediate ? 20 : 30)
        return TrainingSchedule(
            sessionsPerWeek: sessionsPerWeek,
            sessionDuration: TimeInterval(sessionDuration * 60), // Convert to seconds
            recommendedDays: [.monday, .wednesday, .friday],
            preferredTimeOfDay: .morning
        )
    }
    private func generateMilestones(for goals: Set<TrainingGoal>, modules: [TrainingModule]) -> [TrainingMilestone] {
        var milestones: [TrainingMilestone] = []
        for (_, module) in modules.enumerated() {
            let milestone = TrainingMilestone(
                id: UUID().uuidString,
                name: "Complete \(module.name)",
                description: "Successfully complete all exercises in \(module.name) module",
                requiredSkills: module.learningObjectives,
                badgeURL: nil
            )
            milestones.append(milestone)
        }
        return milestones
    }
    private func calculateEstimatedDuration(modules: [TrainingModule]) -> Int {
        return modules.reduce(0) { $0 + Int($1.estimatedWeeks) }
    }
    // MARK: - Session Management
    func createTrainingSession(planId: String, moduleId: String, exerciseId: String) -> TrainingSession {
        let session = TrainingSession(
            id: UUID().uuidString,
            petId: "current_pet_id", // Would get from context
            exerciseId: exerciseId,
            date: Date(),
            duration: 0,
            performance: .good,
            notes: nil,
            mediaAttachments: [],
            trainerFeedback: nil
        )
        trainingSessions.append(session)
        return session
    }
    func completeTrainingSession(_ sessionId: String, duration: Int, rating: Int, notes: String) {
        if let index = trainingSessions.firstIndex(where: { $0.id == sessionId }) {
            // Create a new session with updated values since TrainingSession properties are let constants
            let session = trainingSessions[index]
            let updatedSession = TrainingSession(
                id: session.id,
                petId: session.petId,
                exerciseId: session.exerciseId,
                date: session.date,
                duration: TimeInterval(duration * 60), // Convert minutes to seconds
                performance: PerformanceRating.allCases[min(rating - 1, 3)], // Map 1-4 to enum
                notes: notes.isEmpty ? nil : notes,
                mediaAttachments: session.mediaAttachments,
                trainerFeedback: nil
            )
            trainingSessions[index] = updatedSession
        }
    }
    // MARK: - Progress Tracking
    func getTrainingProgress(for petId: String) -> TrainingProgress {
        let activePlans = trainingPlans.filter { $0.petId == petId }
        let completedSessions = trainingSessions.filter { session in
            activePlans.contains { $0.petId == session.petId } && session.duration > 0
        }
        let totalSessions = trainingSessions.filter { session in
            activePlans.contains { $0.petId == session.petId }
        }
        let completionRate = totalSessions.count > 0 ? Double(completedSessions.count) / Double(totalSessions.count) : 0
        return TrainingProgress(
            petId: petId,
            overallCompletion: completionRate,
            skillsMastered: ["Basic Commands"], // Would calculate from actual data
            timeSpent: TimeInterval(completedSessions.count * 30 * 60), // Estimate
            successRate: calculateAverageRating(sessions: completedSessions) / 4.0,
            lastTrained: completedSessions.last?.date ?? Date(),
            nextRecommendation: "Continue practicing basic commands",
            weeklySummary: WeeklyTrainingSummary(
                weekOf: Date(),
                sessionsCompleted: completedSessions.count,
                totalDuration: TimeInterval(completedSessions.count * 30 * 60),
                topPerformingSkill: "Sit",
                skillToImprove: "Stay"
            )
        )
    }
    private func calculateTrainingStreak(for petId: String) -> Int {
        // Calculate consecutive days of training
        return 3 // Placeholder
    }
    private func calculateAverageRating(sessions: [TrainingSession]) -> Double {
        guard !sessions.isEmpty else { return 0 }
        let totalRating = sessions.reduce(0) { $0 + $1.performance.value }
        return Double(totalRating) / Double(sessions.count)
    }
    private func calculateSkillsLearned(for petId: String) -> Int {
        // Count completed exercises/skills
        return 8 // Placeholder
    }
    // MARK: - Sample Data
    private func loadSampleData() {
        // Load sample training programs, exercises, etc.
        // Simplified sample data to avoid compilation errors
    }
    // MARK: - Missing Helper Methods (Stubs for compilation)
    private func generatePersonalizedTrainingSchedule(for pet: Pet, modules: [TrainingModule], experience: ExperienceLevel) -> TrainingSchedule {
        // Simplified implementation - return existing method result
        return generateTrainingSchedule(modules: modules, experience: experience)
    }
    private func generateRealisticMilestones(for pet: Pet, goals: Set<TrainingGoal>, modules: [TrainingModule]) -> [TrainingMilestone] {
        // Simplified implementation - return existing method result
        return generateMilestones(for: goals, modules: modules)
    }
    private func calculateRealisticDuration(for pet: Pet, modules: [TrainingModule]) -> Double {
        // Simplified implementation - return existing method result
        return Double(calculateEstimatedDuration(modules: modules))
    }
    // Stub training module methods
    private func createPersonalizedHouseTrainingModule(for pet: Pet) -> TrainingModule {
        return createHouseTrainingModule(for: pet)
    }
    private func createPersonalizedLeashTrainingModule(for pet: Pet) -> TrainingModule {
        return createLeashTrainingModule(for: pet)
    }
    private func createPersonalizedSocializationModule(for pet: Pet) -> TrainingModule {
        return createSocializationModule(for: pet)
    }
    private func createPersonalizedAdvancedTricksModule(for pet: Pet) -> TrainingModule {
        return createAdvancedTricksModule(for: pet)
    }
    private func createPersonalizedAgilityModule(for pet: Pet) -> TrainingModule {
        return createAgilityModule(for: pet)
    }
    private func createPersonalizedTherapyTrainingModule(for pet: Pet) -> TrainingModule {
        return createTherapyTrainingModule(for: pet)
    }
    // Stub exercise creation methods
    private func createSitCommandExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Sit Command",
            description: "Teach \(pet.name) to sit on command",
            category: .obedience,
            difficulty: .beginner,
            steps: ["Hold treat close to nose", "Move treat up", "Say 'Sit' when sitting", "Give treat"],
            videoURL: nil,
            requiredEquipment: ["Treats"],
            tips: ["Be patient", "Use high-value treats"]
        )
    }
    private func createStayCommandExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Stay Command",
            description: "Teach \(pet.name) to stay in position",
            category: .obedience,
            difficulty: .intermediate,
            steps: ["Start with sit", "Hold hand up", "Say 'Stay'", "Step back", "Return and reward"],
            videoURL: nil,
            requiredEquipment: ["Treats"],
            tips: ["Start with short durations", "Always return to pet"]
        )
    }
    private func createComeCommandExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Come Command",
            description: "Teach \(pet.name) to come when called",
            category: .obedience,
            difficulty: .intermediate,
            steps: ["Start close", "Say 'Come'", "Reward when they approach", "Gradually increase distance"],
            videoURL: nil,
            requiredEquipment: ["Treats", "Long leash"],
            tips: ["Never call to scold", "Make it rewarding"]
        )
    }
    private func createDownCommandExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Down Command",
            description: "Teach \(pet.name) to lie down on command",
            category: .obedience,
            difficulty: .beginner,
            steps: ["Start with sit", "Hold treat to floor", "Say 'Down'", "Reward when lying down"],
            videoURL: nil,
            requiredEquipment: ["Treats"],
            tips: ["Be patient", "Don't push down"]
        )
    }
    private func createAdvancedObedienceExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Advanced Heel",
            description: "Perfect \(pet.name)'s heel command",
            category: .obedience,
            difficulty: .advanced,
            steps: ["Start in heel position", "Walk slowly", "Reward for staying in position", "Add turns"],
            videoURL: nil,
            requiredEquipment: ["Leash", "Treats"],
            tips: ["Keep sessions short", "Practice in different environments"]
        )
    }
    // Behavior correction exercises
    private func createBarkingCorrectionExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Quiet Command",
            description: "Reduce excessive barking in \(pet.name)",
            category: .obedience,
            difficulty: .intermediate,
            steps: ["Allow some barks", "Say 'Quiet'", "Wait for silence", "Reward quiet behavior"],
            videoURL: nil,
            requiredEquipment: ["Treats"],
            tips: ["Don't reward barking", "Be consistent"]
        )
    }
    private func createJumpingCorrectionExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "No Jumping",
            description: "Stop \(pet.name) from jumping on people",
            category: .obedience,
            difficulty: .intermediate,
            steps: ["Turn away when jumping", "Ignore until calm", "Reward calm greetings", "Practice with visitors"],
            videoURL: nil,
            requiredEquipment: ["Treats"],
            tips: ["Be consistent", "Everyone must follow rules"]
        )
    }
    private func createPullingCorrectionExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Loose Leash Walking",
            description: "Stop \(pet.name) from pulling on leash",
            category: .obedience,
            difficulty: .intermediate,
            steps: ["Stop when pulling", "Change direction", "Reward loose leash", "Practice regularly"],
            videoURL: nil,
            requiredEquipment: ["Leash", "Treats"],
            tips: ["Be patient", "Consistency is key"]
        )
    }
    private func createChewingCorrectionExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Appropriate Chewing",
            description: "Redirect \(pet.name)'s chewing to appropriate items",
            category: .obedience,
            difficulty: .beginner,
            steps: ["Provide appropriate chew toys", "Redirect when chewing wrong items", "Reward good chewing", "Remove temptations"],
            videoURL: nil,
            requiredEquipment: ["Chew toys", "Treats"],
            tips: ["Provide variety", "Supervise initially"]
        )
    }
    private func createGeneralBehaviorExercise(for pet: Pet, issue: String) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Address \(issue)",
            description: "Work on \(issue.lowercased()) behavior with \(pet.name)",
            category: .obedience,
            difficulty: .intermediate,
            steps: ["Identify triggers", "Practice alternative behaviors", "Reward good choices", "Be consistent"],
            videoURL: nil,
            requiredEquipment: ["Treats"],
            tips: ["Patience required", "Consider professional help"]
        )
    }
    private func createPreventiveBehaviorExercise(for pet: Pet) -> TrainingExercise {
        return TrainingExercise(
            id: UUID().uuidString,
            name: "Good Manners",
            description: "Maintain \(pet.name)'s excellent behavior",
            category: .obedience,
            difficulty: .beginner,
            steps: ["Regular practice sessions", "Reinforce known commands", "Socialize appropriately", "Keep training fun"],
            videoURL: nil,
            requiredEquipment: ["Treats"],
            tips: ["Keep it positive", "Short regular sessions"]
        )
    }
}
