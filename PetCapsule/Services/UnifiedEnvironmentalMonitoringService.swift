//
//  UnifiedEnvironmentalMonitoringService.swift
//  PetCapsule
//
//  Unified environmental monitoring service that coordinates all environmental data sources
//
import Foundation
import CoreLocation
import Combine
import UserNotifications
import SwiftUI
import UIKit
@MainActor
class UnifiedEnvironmentalMonitoringService: ObservableObject {
    static let shared = UnifiedEnvironmentalMonitoringService()
    // Published properties
    @Published var isMonitoring = false
    @Published var currentEnvironmentalState: EnvironmentalState?
    @Published var monitoringHistory: [EnvironmentalSnapshot] = []
    @Published var activeAlerts: [EnvironmentalAlert] = []
    @Published var monitoringSettings: MonitoringSettings = MonitoringSettings.default
    @Published var lastUpdateTime: Date?
    @Published var connectionStatus: ConnectionStatus = .pending
    // Services
    private let weatherService = AppleWeatherService.shared
    private let airQualityService = AppleAirQualityService.shared
    private let alertsService = EnhancedEnvironmentalAlertsService.shared
    private let scoringService = EnvironmentalScoringService.shared
    private let notificationService = EnvironmentalNotificationService.shared
    private let locationManager = CLLocationManager()
    @Published var currentLocation: CLLocation?
    private let dataService = AppleNativeDataService.shared
    // Monitoring state
    private var monitoringTimer: Timer?
    private var backgroundTask: UIBackgroundTaskIdentifier = .invalid
    private var cancellables = Set<AnyCancellable>()
    // Data caching
    private var dataCache: [String: CachedEnvironmentalData] = [:]
    private let cacheExpirationTime: TimeInterval = 300 // 5 minutes
    private init() {
        setupLocationSubscription()
        loadMonitoringSettings()
    }
    // MARK: - Monitoring Control
    func startMonitoring() async {
        guard !isMonitoring else { return }
        isMonitoring = true
        connectionStatus = .connecting
        // Start background task for continuous monitoring
        startBackgroundTask()
        // Start periodic monitoring
        startMonitoringTimer()
        // Start real-time services
        alertsService.startEnvironmentalMonitoring()
        // Perform initial environmental check
        await performEnvironmentalCheck()
        connectionStatus = .connected
        print("✅ Unified environmental monitoring started")
    }
    func stopMonitoring() {
        guard isMonitoring else { return }
        isMonitoring = false
        connectionStatus = .declined
        // Stop monitoring timer
        stopMonitoringTimer()
        // Stop services
        alertsService.stopEnvironmentalMonitoring()
        // End background task
        endBackgroundTask()
        print("🛑 Unified environmental monitoring stopped")
    }
    func pauseMonitoring() {
        guard isMonitoring else { return }
        stopMonitoringTimer()
        connectionStatus = .pending
        print("⏸️ Environmental monitoring paused")
    }
    func resumeMonitoring() async {
        guard isMonitoring else { return }
        connectionStatus = .connecting
        startMonitoringTimer()
        await performEnvironmentalCheck()
        connectionStatus = .connected
        print("▶️ Environmental monitoring resumed")
    }
    // MARK: - Environmental Data Collection
    func performEnvironmentalCheck() async {
        guard let currentLocation = currentLocation else {
            print("❌ No current location for environmental check")
            return
        }
        let locationKey = "\(currentLocation.coordinate.latitude),\(currentLocation.coordinate.longitude)"
        // Check cache first
        if let cachedData = dataCache[locationKey],
           Date().timeIntervalSince(cachedData.timestamp) < cacheExpirationTime {
            await processEnvironmentalData(cachedData.data, location: currentLocation.coordinate)
            return
        }
        do {
            // Gather all environmental data concurrently
            async let weather = weatherService.getCurrentWeather(for: currentLocation.coordinate)
            async let airQuality = weatherService.getAirQuality(for: currentLocation.coordinate)
            async let pollen = airQualityService.getAirQualityData(for: currentLocation.coordinate)
            async let environmentalScore = scoringService.calculateEnvironmentalScore(for: currentLocation.coordinate)
            let weatherData = try await weather
            let airQualityData = try await airQuality
            let pollenData = try await pollen
            let scoreData = try await environmentalScore
            let environmentalData = ComprehensiveEnvironmentalData(
                weather: weatherData,
                airQuality: airQualityData,
                pollen: pollenData,
                environmentalScore: scoreData,
                location: currentLocation.coordinate,
                timestamp: Date()
            )
            // Cache the data
            dataCache[locationKey] = CachedEnvironmentalData(
                data: environmentalData,
                timestamp: Date()
            )
            // Process the data
            await processEnvironmentalData(environmentalData, location: currentLocation.coordinate)
            lastUpdateTime = Date()
        } catch {
            print("❌ Failed to perform environmental check: \(error)")
            connectionStatus = .declined
        }
    }
    private func processEnvironmentalData(_ data: ComprehensiveEnvironmentalData, location: CLLocationCoordinate2D) async {
        // Update current environmental state
        currentEnvironmentalState = EnvironmentalState(
            location: location,
            weather: data.weather,
            airQuality: data.airQuality,
            pollen: data.pollen,
            environmentalScore: data.environmentalScore,
            timestamp: data.timestamp,
            riskLevel: calculateRiskLevel(data),
            recommendations: generateRecommendations(data)
        )
        // Add to monitoring history
        let snapshot = EnvironmentalSnapshot(
            id: UUID().uuidString,
            location: location,
            data: data,
            riskLevel: calculateRiskLevel(data),
            timestamp: data.timestamp
        )
        monitoringHistory
        // Limit history size
        if monitoringHistory.count > 100 {
            monitoringHistory = Array(monitoringHistory.prefix(100))
        }
        // Check for alerts
        await checkEnvironmentalAlerts(data)
        // Save to database
        await saveEnvironmentalSnapshot(snapshot)
        // Update scoring service
        scoringService.currentEnvironmentalScore = data.environmentalScore
    }
    // MARK: - Alert Processing
    private func checkEnvironmentalAlerts(_ data: ComprehensiveEnvironmentalData) async {
        var newAlerts: [EnvironmentalAlert] = []
        // Temperature alerts
        if Double(data.weather.temperature) > monitoringSettings.temperatureThresholds.high {
            newAlerts.append(createAlert(
                type: .temperature,
                severity: .high,
                message: "High temperature alert: \(data.weather.temperature)°F",
                value: Double(data.weather.temperature),
                threshold: monitoringSettings.temperatureThresholds.high
            ))
        } else if Double(data.weather.temperature) < monitoringSettings.temperatureThresholds.low {
            newAlerts.append(createAlert(
                type: .temperature,
                severity: .high,
                message: "Low temperature alert: \(data.weather.temperature)°F",
                value: Double(data.weather.temperature),
                threshold: monitoringSettings.temperatureThresholds.low
            ))
        }
        // Air quality alerts
        if data.airQuality.index > monitoringSettings.airQualityThreshold {
            let severity: AlertSeverity = data.airQuality.index > 150 ? .critical : .high
            newAlerts.append(createAlert(
                type: .airQuality,
                severity: severity,
                message: "Poor air quality: AQI \(data.airQuality.index)",
                value: Double(data.airQuality.index),
                threshold: Double(monitoringSettings.airQualityThreshold)
            ))
        }
        // Pollen alerts
        let totalPollen = data.pollen.treeIndex + data.pollen.grassIndex + data.pollen.weedIndex
        if totalPollen > monitoringSettings.pollenThreshold {
            newAlerts.append(createAlert(
                type: .pollen,
                severity: .high,
                message: "High pollen count: \(totalPollen)",
                value: Double(totalPollen),
                threshold: Double(monitoringSettings.pollenThreshold)
            ))
        }
        // Update active alerts
        for alert in newAlerts {
            if !activeAlerts.contains(where: { $0.alertType == alert.alertType && $0.severity == alert.severity }) {
                activeAlerts.append(alert)
                await saveAlert(alert)
                await sendAlertNotification(alert)
            }
        }
    }
    private func createAlert(type: EnvironmentalAlertType, severity: AlertSeverity, message: String, value: Double, threshold: Double) -> EnvironmentalAlert {
        return EnvironmentalAlert(
            id: UUID().uuidString,
            alertType: type,
            severity: severity,
            title: "\(severity.rawValue.capitalized) \(type.rawValue.capitalized) Alert",
            message: message,
            location: nil,
            latitude: currentLocation?.coordinate.latitude,
            longitude: currentLocation?.coordinate.longitude,
            triggeredAt: Date(),
            expiresAt: Date().addingTimeInterval(3600), // Alert valid for 1 hour
            isAcknowledged: false,
            petIds: [],
            actionRequired: true,
            recommendations: []
        )
    }
    private func calculateRiskLevel(_ data: ComprehensiveEnvironmentalData) -> RiskLevel {
        if data.environmentalScore.overallScore > 0.8 {
            return .low
        } else if data.environmentalScore.overallScore > 0.6 {
            return .medium
        } else if data.environmentalScore.overallScore > 0.4 {
            return .high
        } else {
            return .critical
        }
    }
    private func generateRecommendations(_ data: ComprehensiveEnvironmentalData) -> [String] {
        var recommendations: [String] = []
        if data.environmentalScore.overallScore < 0.7 {
            recommendations.append("Consider limiting outdoor activities")
        }
        if data.airQuality.index > 100 {
            recommendations.append("Air quality is poor - avoid outdoor exercise")
        }
        if data.pollen.treeIndex + data.pollen.grassIndex + data.pollen.weedIndex > 50 {
            recommendations.append("High pollen levels - take allergy precautions")
        }
        return recommendations
    }
    // MARK: - Location Services
    private func setupLocationSubscription() {
        // Since CLLocationManager doesn't have a published currentLocation,
        // we'll use a simple approach for now
        Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            Task { @MainActor in
                guard self.isMonitoring else { return }
                await self.performEnvironmentalCheck()
            }
        }
    }
    // MARK: - Background Task Management
    private func startBackgroundTask() {
        backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.endBackgroundTask()
        }
    }
    private func endBackgroundTask() {
        UIApplication.shared.endBackgroundTask(backgroundTask)
        backgroundTask = .invalid
    }
    // MARK: - Monitoring Timer
    private func startMonitoringTimer() {
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: monitoringSettings.updateInterval, repeats: true) { [weak self] _ in
            Task {
                await self?.performEnvironmentalCheck()
            }
        }
    }
    private func stopMonitoringTimer() {
        monitoringTimer?.invalidate()
        monitoringTimer = nil
    }
    // MARK: - Settings Management
    func updateMonitoringSettings(_ settings: MonitoringSettings) async {
        self.monitoringSettings = settings
        do {
            try await saveMonitoringSettingsToDatabase(settings)
        } catch {
            print("❌ Failed to save monitoring settings: \(error)")
        }
    }
    private func loadMonitoringSettings() {
        // In a real app, this would load from UserDefaults or a database
        // For now, using default settings
    }
    // MARK: - Data Persistence
    private func saveEnvironmentalSnapshot(_ snapshot: EnvironmentalSnapshot) async {
        print("💾 Saving environmental snapshot: \(snapshot.id)")
    }
    private func saveAlert(_ alert: EnvironmentalAlert) async {
        print("💾 Saving environmental alert: \(alert.id)")
    }
    private func saveMonitoringSettingsToDatabase(_ settings: MonitoringSettings) async throws {
        print("💾 Saving monitoring settings")
    }
    private func loadMonitoringSettingsFromDatabase() async throws -> MonitoringSettings {
        return MonitoringSettings.default
    }
    private func sendAlertNotification(_ alert: EnvironmentalAlert) async {
        await notificationService.sendEnvironmentalAlert(
            alert: alert,
            currentValue: 0,
            message: alert.message
        )
    }
    // MARK: - Public API
    func dismissAlert(_ alertId: String) {
        if let index = activeAlerts.firstIndex(where: { $0.id == alertId }) {
            activeAlerts.remove(at: index)
        }
    }
    func getHistoryForTimeRange(_ timeRange: TimeInterval) -> [EnvironmentalSnapshot] {
        let cutoffDate = Date().addingTimeInterval(-timeRange)
        return monitoringHistory.filter { $0.timestamp >= cutoffDate }
    }
    func getCurrentRiskLevel() -> RiskLevel {
        return currentEnvironmentalState?.riskLevel ?? .unknown
    }
    func getActiveAlertsCount() -> Int {
        return activeAlerts.count
    }
}
// MARK: - Data Models
struct EnvironmentalState {
    let location: CLLocationCoordinate2D
    let weather: WeatherData
    let airQuality: AirQualityData
    let pollen: PollenData
    let environmentalScore: EnvironmentalScore
    let timestamp: Date
    let riskLevel: RiskLevel
    let recommendations: [String]
}
struct EnvironmentalSnapshot: Identifiable {
    let id: String
    let location: CLLocationCoordinate2D
    let data: ComprehensiveEnvironmentalData
    let riskLevel: RiskLevel
    let timestamp: Date
}
struct ComprehensiveEnvironmentalData {
    let weather: WeatherData
    let airQuality: AirQualityData
    let pollen: PollenData
    let environmentalScore: EnvironmentalScore
    let location: CLLocationCoordinate2D
    let timestamp: Date
}
struct CachedEnvironmentalData {
    let data: ComprehensiveEnvironmentalData
    let timestamp: Date
}
struct MonitoringSettings {
    var updateInterval: TimeInterval
    var notificationsEnabled: Bool
    var temperatureThresholds: TemperatureThresholds
    var airQualityThreshold: Int
    var pollenThreshold: Int
    var environmentalScoreThreshold: Double
    var backgroundMonitoringEnabled: Bool
    var locationBasedMonitoring: Bool
    static let `default` = MonitoringSettings(
        updateInterval: 1800, // 30 minutes
        notificationsEnabled: true,
        temperatureThresholds: TemperatureThresholds(low: 32, high: 85),
        airQualityThreshold: 100,
        pollenThreshold: 15,
        environmentalScoreThreshold: 0.5,
        backgroundMonitoringEnabled: true,
        locationBasedMonitoring: true
    )
}
struct TemperatureThresholds {
    let low: Double
    let high: Double
}
enum RiskLevel: String, CaseIterable {
    case unknown = "unknown"
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    var displayName: String {
        rawValue.capitalized
    }
    var color: Color {
        switch self {
        case .unknown: return .gray
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
    var icon: String {
        switch self {
        case .unknown: return "questionmark.circle"
        case .low: return "checkmark.circle.fill"
        case .medium: return "exclamationmark.triangle.fill"
        case .high: return "exclamationmark.octagon.fill"
        case .critical: return "xmark.octagon.fill"
        }
    }
}
