//
//  UnifiedPlannerService.swift
//  PetCapsule
//
//  Unified service for managing all planning activities
//

import Foundation
import Combine
import SwiftUI
import CoreLocation

@MainActor
class UnifiedPlannerService: ObservableObject {
    static let shared = UnifiedPlannerService()
    
    // Published properties
    @Published var upcomingEvents: [PlannerEvent] = []
    @Published var overdueEvents: [PlannerEvent] = []
    @Published var todaysEvents: [PlannerEvent] = []
    @Published var completionStats: CompletionStats = CompletionStats()
    @Published var isLoading = false
    @Published var todaysPlan: DailyPlan?
    @Published var activeReminders: [PlannerReminder] = []
    @Published var lastSyncTime: Date?
    @Published var plannerInsights: [PlannerInsight] = []
    @Published var currentWalkPath: [CLLocationCoordinate2D] = []
    @Published var isTrackingWalk: Bool = false
    @Published var walkMemories: [WalkMemory] = []
    @Published var communityEvents: [CommunityEvent] = []
    
    // Service dependencies
    private let petDataManager = PetDataManager.shared
    private let walkPlannerService = PetPlannerService.shared
    private let nutritionPlannerService = NutritionPlannerService.shared
    private let trainingPlannerService = TrainingPlannerService.shared
    private let healthMonitoringService = ComprehensiveHealthMonitoringService.shared
    private let vaccinationPlannerService = VaccinationPlannerService.shared
    private let emergencyPlanningService = EmergencyPlanningService.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupSubscriptions()
    }
    
    // MARK: - Public Methods
    
    func loadPlannerData(for petId: String? = nil) async {
        isLoading = true
        defer { isLoading = false }
        
        // Use current pet from PetDataManager if no specific petId provided
        let selectedPet = petId != nil ? petDataManager.getPetById(petId!) : petDataManager.currentPet
        
        await loadUpcomingEvents(for: selectedPet)
        await loadOverdueEvents(for: selectedPet)
        await loadTodaysEvents(for: selectedPet)
        updateCompletionStats()
        generateComprehensivePlannerInsights()
    }
    
    func refreshAllData(for petId: String? = nil) async {
        // First refresh the comprehensive pet data
        await petDataManager.refreshData()
        // Then update planner data with fresh comprehensive information
        await loadPlannerData(for: petId)
    }
    
    func refreshAllPlanners() async {
        await refreshAllData()
    }
    
    func dismissReminder(_ reminder: PlannerReminder) {
        activeReminders.removeAll { $0.id == reminder.id }
    }
    
    func startWalkTracking() {
        isTrackingWalk = true
        currentWalkPath = []
    }
    
    func stopWalkTracking() {
        isTrackingWalk = false
        currentWalkPath = []
    }
    
    // MARK: - Private Methods
    
    private func loadUpcomingEvents(for pet: Pet?) async {
        guard let pet = pet else { 
            upcomingEvents = []
            return 
        }
        
        // Generate comprehensive events using pet data
        let healthTasks = getHealthTasks(for: pet)
        let nutritionEvents = generateNutritionEvents(for: pet)
        let trainingEvents = generateTrainingEvents(for: pet)
        let vaccinationEvents = generateVaccinationEvents(for: pet)
        
        // Stub implementation for now - create empty PlannerData
        let plannerData = PlannerData(
            walkPlans: [],
            nutritionPlans: [],
            trainingPlans: [],
            healthTasks: [],
            vaccinationReminders: [],
            emergencyPlans: []
        )

        // Convert to PlannerEvent format with comprehensive data
        var events = convertToEvents(plannerData)
        events.append(contentsOf: nutritionEvents)
        events.append(contentsOf: trainingEvents)
        events.append(contentsOf: vaccinationEvents)
        
        upcomingEvents = events.sorted { $0.scheduledDate < $1.scheduledDate }
    }
    
    private func loadOverdueEvents(for pet: Pet?) async {
        guard let pet = pet else { 
            overdueEvents = []
            return 
        }
        
        // Check for overdue items based on comprehensive pet data
        var overdue: [PlannerEvent] = []
        
        // Check overdue vaccinations
        let currentDate = Date()
        for vaccination in pet.vaccinationRecords {
            if let nextDue = vaccination.nextDueDate, nextDue < currentDate {
                overdue.append(PlannerEvent(
                    id: vaccination.id,
                    type: .health,
                    title: "Overdue: \(vaccination.vaccineName) Vaccination",
                    description: "This vaccination is past due. Please schedule with your veterinarian.",
                    scheduledDate: nextDue,
                    isCompleted: false,
                    priority: .high,
                    category: "Vaccination"
                ))
            }
        }
        
        // Check overdue medications
        for medication in pet.medications.filter({ $0.isActive }) {
            for reminderTime in medication.reminderTimes {
                if reminderTime < currentDate && Calendar.current.isDate(reminderTime, inSameDayAs: currentDate) {
                    overdue.append(PlannerEvent(
                        id: medication.id,
                        type: .health,
                        title: "Overdue: \(medication.name)",
                        description: "Medication dose was scheduled for \(reminderTime.formatted(date: .omitted, time: .shortened))",
                        scheduledDate: reminderTime,
                        isCompleted: false,
                        priority: .high,
                        category: "Medication"
                    ))
                }
            }
        }
        
        overdueEvents = overdue
    }
    
    private func loadTodaysEvents(for pet: Pet?) async {
        let today = Calendar.current.startOfDay(for: Date())
        todaysEvents = upcomingEvents.filter { event in
            Calendar.current.isDate(event.scheduledDate, inSameDayAs: today)
        }
        
        // Create daily plan based on comprehensive pet data
        if let pet = pet {
            todaysPlan = generateDailyPlan(for: pet)
        }
    }
    
    private func convertToEvents(_ data: PlannerData) -> [PlannerEvent] {
        var events: [PlannerEvent] = []
        
        // Convert health tasks
        for task in data.healthTasks {
            events.append(PlannerEvent(
                id: task.id,
                type: .health,
                title: task.title,
                description: task.description,
                scheduledDate: task.dueDate,
                isCompleted: task.isCompleted,
                priority: task.priority,
                category: "Health"
            ))
        }
        
        return events.sorted { $0.scheduledDate < $1.scheduledDate }
    }
    
    // MARK: - Service Method Implementations
    
    private func getTrainingPlans() -> [TrainingPlan] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    private func getHealthTasks(for pet: Pet) -> [HealthTask] {
        var tasks: [HealthTask] = []
        
        // Generate health tasks based on comprehensive pet data
        _ = petDataManager.getHealthScore(for: pet)
        
        // Health checkup recommendations
        if let lastCheckup = pet.lastCheckupDate {
            let monthsSinceCheckup = Calendar.current.dateComponents([.month], from: lastCheckup, to: Date()).month ?? 0
            if monthsSinceCheckup >= 12 {
                tasks.append(HealthTask(
                    id: UUID().uuidString,
                    petId: pet.id,
                    title: "Annual Health Checkup",
                    description: "Time for \(pet.name)'s annual veterinary examination",
                    taskType: "Health Checkup",
                    priority: .high,
                    dueDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date(),
                    isCompleted: false,
                    completedDate: nil,
                    reminderTime: Calendar.current.date(byAdding: .day, value: 5, to: Date()),
                    notes: "Annual comprehensive health examination",
                    createdAt: Date()
                ))
            }
        }
        
        // Medication management
        for medication in pet.medications.filter({ $0.isActive }) {
            if medication.reminderTimes.contains(where: { Calendar.current.isDateInToday($0) }) {
                tasks.append(HealthTask(
                    id: medication.id,
                    petId: pet.id,
                    title: "Give \(medication.name)",
                    description: "Dosage: \(medication.dosage)\nInstructions: \(medication.instructions ?? "")",
                    taskType: "Medication",
                    priority: .medium,
                    dueDate: medication.reminderTimes.first(where: { Calendar.current.isDateInToday($0) }) ?? Date(),
                    isCompleted: false,
                    completedDate: nil,
                    reminderTime: medication.reminderTimes.first,
                    notes: "Daily medication administration",
                    createdAt: Date()
                ))
            }
        }
        
        return tasks
    }
    
    private func getVaccinationRecords() -> [SharedVaccinationReminder] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    private func getEmergencyPlans() -> [String] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    private func getEmergencyContacts() -> [SharedEmergencyContact] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    private func getUpcomingEvents() -> [String] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    private func getNutritionPlans() -> [NutritionPlan] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    private func getUpcomingHealthTasks() -> [SharedHealthTask] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    private func getOverdueHealthTasks() -> [SharedHealthTask] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    private func getOverdueWalks() -> [WalkPlan] {
        // Stub implementation - replace with actual service call when available
        return []
    }
    
    // MARK: - Comprehensive Event Generation
    
    private func generateNutritionEvents(for pet: Pet) -> [PlannerEvent] {
        var events: [PlannerEvent] = []
        
        // Daily feeding schedule events
        for feedingTime in pet.feedingSchedule {
            if let mealTime = parseTimeString(formatTimeFromDate(feedingTime.time)) {
                let today = Calendar.current.startOfDay(for: Date())
                let feedingDate = Calendar.current.date(byAdding: .hour, value: mealTime.hour, to: today) ?? Date()
                
                events.append(PlannerEvent(
                    id: feedingTime.id,
                    type: .nutrition,
                    title: "Feeding Time: \(feedingTime.foodType)",
                    description: "Amount: \(feedingTime.amount)\nNotes: \(feedingTime.notes ?? "")",
                    scheduledDate: feedingDate,
                    isCompleted: false,
                    priority: .medium,
                    category: "Nutrition"
                ))
            }
        }
        
        // Water intake monitoring
        if let waterIntake = pet.waterIntakeML, waterIntake > 0 {
            events.append(PlannerEvent(
                id: UUID().uuidString,
                type: .health,
                title: "Monitor Water Intake",
                description: "Target: \(waterIntake)ml daily\nCurrent Status: \(petDataManager.evaluateNutritionStatus(for: pet).rawValue)",
                scheduledDate: Date(),
                isCompleted: false,
                priority: .low,
                category: "Hydration"
            ))
        }
        
        return events
    }
    
    private func generateTrainingEvents(for pet: Pet) -> [PlannerEvent] {
        var events: [PlannerEvent] = []
        
        // Daily training recommendations based on current level
        let trainingStatus = petDataManager.evaluateTrainingStatus(for: pet)
        let trainingLevel = pet.trainingLevel ?? "Beginner"
        
        switch trainingStatus {
        case .notStarted:
            events.append(PlannerEvent(
                id: UUID().uuidString,
                type: .training,
                title: "Start Basic Training",
                description: "Begin with basic commands like 'sit' and 'stay'\nCurrent Level: \(trainingLevel)",
                scheduledDate: Date(),
                isCompleted: false,
                priority: .medium,
                category: "Training"
            ))
        case .inProgress:
            events.append(PlannerEvent(
                id: UUID().uuidString,
                type: .training,
                title: "Continue Training Session",
                description: "Practice known commands: \(pet.knownCommands.joined(separator: ", "))\nWork on: \(pet.behaviorIssues.joined(separator: ", "))",
                scheduledDate: Date(),
                isCompleted: false,
                priority: .medium,
                category: "Training"
            ))
        case .advanced:
            events.append(PlannerEvent(
                id: UUID().uuidString,
                type: .training,
                title: "Advanced Training Challenge",
                description: "Try new tricks or refine existing skills\nKnown Commands: \(pet.knownCommands.count) commands",
                scheduledDate: Date(),
                isCompleted: false,
                priority: .low,
                category: "Training"
            ))
        }
        
        return events
    }
    
    private func generateVaccinationEvents(for pet: Pet) -> [PlannerEvent] {
        var events: [PlannerEvent] = []
        
        for vaccination in pet.vaccinationRecords {
            if let nextDue = vaccination.nextDueDate {
                let daysUntilDue = Calendar.current.dateComponents([.day], from: Date(), to: nextDue).day ?? 0
                
                // Alert 30 days before due date
                if daysUntilDue <= 30 && daysUntilDue > 0 {
                    events.append(PlannerEvent(
                        id: vaccination.id,
                        type: .health,
                        title: "Upcoming: \(vaccination.vaccineName) Vaccination",
                        description: "Due: \(nextDue.formatted(date: .abbreviated, time: .omitted))\nDays remaining: \(daysUntilDue)",
                        scheduledDate: nextDue,
                        isCompleted: false,
                        priority: daysUntilDue <= 7 ? .high : .medium,
                        category: "Vaccination"
                    ))
                }
            }
        }
        
        return events
    }
    
    private func generateDailyPlan(for pet: Pet) -> DailyPlan {
        _ = petDataManager.getHealthScore(for: pet)
        _ = petDataManager.evaluateNutritionStatus(for: pet)
        _ = petDataManager.evaluateExerciseStatus(for: pet)
        _ = petDataManager.evaluateTrainingStatus(for: pet)
        
        let allEvents = generateNutritionEvents(for: pet) + generateTrainingEvents(for: pet) + generateVaccinationEvents(for: pet)
        let completionRate = allEvents.isEmpty ? 0.0 : Double(allEvents.filter { $0.isCompleted }.count) / Double(allEvents.count)
        
        return DailyPlan(
            date: Date(),
            events: allEvents,
            completionRate: completionRate
        )
    }
    
    private func generateComprehensivePlannerInsights() {
        var insights: [PlannerInsight] = []
        
        for pet in petDataManager.pets {
            let healthScore = petDataManager.getHealthScore(for: pet)
            
            // Health insights
            if healthScore < 0.7 {
                insights.append(PlannerInsight(
                    title: "\(pet.name)'s Health Needs Attention",
                    description: "Health score: \(Int(healthScore * 100))%. Consider scheduling a veterinary checkup.",
                    category: "Health",
                    priority: .high
                ))
            }
            
            // Nutrition insights
            let nutritionStatus = petDataManager.evaluateNutritionStatus(for: pet)
            if nutritionStatus == .needsImprovement {
                insights.append(PlannerInsight(
                    title: "\(pet.name)'s Nutrition Can Be Improved",
                    description: "Consider adjusting feeding schedule or food type. Current status: \(nutritionStatus.rawValue)",
                    category: "Nutrition",
                    priority: .medium
                ))
            }
            
            // Training insights
            let trainingStatus = petDataManager.evaluateTrainingStatus(for: pet)
            if trainingStatus == .notStarted {
                insights.append(PlannerInsight(
                    title: "\(pet.name) Ready for Training",
                    description: "Start with basic commands to improve behavior and bonding.",
                    category: "Training",
                    priority: .medium
                ))
            }
        }
        
        plannerInsights = insights
    }
    
    private func parseTimeString(_ timeString: String) -> (hour: Int, minute: Int)? {
        let components = timeString.split(separator: ":")
        guard components.count == 2,
              let hour = Int(components[0]),
              let minute = Int(components[1]) else {
            return nil
        }
        return (hour: hour, minute: minute)
    }
    
    // MARK: - Statistics and Analytics
    
    private func updateCompletionStats() {
        let _: [WalkPlan] = [] // Placeholder for future implementation
        let _: [NutritionPlan] = [] // Placeholder for future implementation
        let _: [TrainingPlan] = [] // Placeholder for future implementation
        
        // Simplified completion calculation without using unavailable properties
        let completedTasks = upcomingEvents.filter { $0.isCompleted }.count
        let totalTasks = upcomingEvents.count
        
        completionStats = CompletionStats(
            totalTasks: totalTasks,
            completedTasks: completedTasks,
            overdueTasks: overdueEvents.count,
            completionRate: totalTasks > 0 ? Double(completedTasks) / Double(totalTasks) : 0.0
        )
    }
    
    // MARK: - Event Management
    
    func markEventCompleted(_ eventId: String) async {
        guard let eventIndex = upcomingEvents.firstIndex(where: { $0.id == eventId }),
              let event = upcomingEvents.first(where: { $0.id == eventId }) else { return }
        
        // Update local state
        upcomingEvents[eventIndex].isCompleted = true
        
        // Call appropriate service method based on event type
        switch event.type {
        case .walk:
            // Stub - implement when service method is available
            break
        case .nutrition:
            // Stub - implement when service method is available
            break
        case .training:
            // Stub - implement when service method is available
            break
        case .health:
            // Stub - implement when service method is available
            break
        case .vaccination:
            // Stub - implement when service method is available
            break
        case .emergency:
            // Stub - implement when service method is available
            break
        }
        
        updateCompletionStats()
    }
    
    // MARK: - Subscription Setup
    
    private func setupSubscriptions() {
        // Simplified subscription setup without using unavailable publishers
        // These would be implemented when the service publishers are available
    }
    
    // MARK: - Analytics Methods
    
    func getCompletionAnalytics() -> CompletionStats {
        let _: [WalkPlan] = [] // Placeholder for future implementation
        let _: [NutritionPlan] = [] // Placeholder for future implementation
        let _: [TrainingPlan] = [] // Placeholder for future implementation
        
        // Simplified analytics calculation
        let completedWalks = 0 // Placeholder - would count completed walk plans
        let completedNutrition = 0 // Placeholder - would count completed nutrition plans
        let completedTraining = 0 // Placeholder - would count completed training plans
        
        let totalCompleted = completedWalks + completedNutrition + completedTraining
        let totalPlanned = 0 // Placeholder - would count total planned activities
        
        return CompletionStats(
            totalTasks: totalPlanned,
            completedTasks: totalCompleted,
            overdueTasks: overdueEvents.count,
            completionRate: totalPlanned > 0 ? Double(totalCompleted) / Double(totalPlanned) : 0.0
        )
    }
    
    func getTrendData() -> [CompletionTrend] {
        // Stub implementation for trend data
        return []
    }
}

// MARK: - Supporting Types

struct CompletionStats {
    let totalTasks: Int
    let completedTasks: Int
    let overdueTasks: Int
    let completionRate: Double
    
    init(totalTasks: Int = 0, completedTasks: Int = 0, overdueTasks: Int = 0, completionRate: Double = 0.0) {
        self.totalTasks = totalTasks
        self.completedTasks = completedTasks
        self.overdueTasks = overdueTasks
        self.completionRate = completionRate
    }
}

struct CompletionTrend: Identifiable {
    let id = UUID()
    let date: Date
    let completionRate: Double
}

// MARK: - Supporting Types and Models

struct PlannerEvent: Identifiable {
    let id: String
    let type: PlannerEventType
    let title: String
    let description: String
    let scheduledDate: Date
    var isCompleted: Bool
    let priority: RecommendationPriority
    let category: String
    var scheduledTime: Date { scheduledDate }
    var isOverdue: Bool { scheduledDate < Date() && !isCompleted }
}

enum PlannerEventType: String, CaseIterable {
    case walk = "walk"
    case nutrition = "nutrition"
    case training = "training"
    case health = "health"
    case vaccination = "vaccination"
    case emergency = "emergency"
    
    var icon: String {
        switch self {
        case .walk: return "figure.walk"
        case .nutrition: return "fork.knife"
        case .training: return "brain.head.profile"
        case .health: return "heart.fill"
        case .vaccination: return "syringe"
        case .emergency: return "exclamationmark.triangle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .walk: return .blue
        case .nutrition: return .green
        case .training: return .purple
        case .health: return .red
        case .vaccination: return .orange
        case .emergency: return .red
        }
    }
}

struct PlannerData {
    let walkPlans: [WalkPlan]
    let nutritionPlans: [NutritionPlan]
    let trainingPlans: [TrainingPlan]
    let healthTasks: [HealthTask]
    let vaccinationReminders: [SharedVaccinationReminder]
    let emergencyPlans: [String]
}

struct PlannerInsight: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let category: String
    let priority: RecommendationPriority
    let type: RecommendationType
    var message: String { description }
    var actionable: Bool { priority != .low }
    
    init(title: String, description: String, category: String, priority: RecommendationPriority = .medium) {
        self.title = title
        self.description = description
        self.category = category
        self.priority = priority
        self.type = .activity
    }
}

struct DailyPlan: Identifiable {
    let id = UUID()
    let date: Date
    let events: [PlannerEvent]
    let completionRate: Double
    let completionScore: Double
    var totalTasks: Int { events.count }
    var completedTasks: Int { events.filter { $0.isCompleted }.count }
    var pendingTasks: Int { events.filter { !$0.isCompleted }.count }
    var environmentalRecommendations: [EnvironmentalRecommendation] { [] }
    
    init(date: Date = Date(), events: [PlannerEvent] = [], completionRate: Double = 0.0) {
        self.date = date
        self.events = events
        self.completionRate = completionRate
        self.completionScore = completionRate
    }
}

struct EnvironmentalRecommendation: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let severity: AlertSeverity
    let actionRequired: Bool
    let priority: RecommendationPriority
    var message: String { description }
    
    init(title: String, description: String, severity: AlertSeverity = .moderate, actionRequired: Bool = false) {
        self.title = title
        self.description = description
        self.severity = severity
        self.actionRequired = actionRequired
        self.priority = .medium
    }
}

struct PlannerReminder: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let dueDate: Date
    let isUrgent: Bool
    let type: RecommendationType
    let priority: RecommendationPriority
    var message: String { description }
    
    init(title: String, description: String, dueDate: Date, isUrgent: Bool = false) {
        self.title = title
        self.description = description
        self.dueDate = dueDate
        self.isUrgent = isUrgent
        self.type = .activity
        self.priority = isUrgent ? .high : .medium
    }
}

// MARK: - Type Aliases for Missing Types

typealias Event = String // Simplified for now

// MARK: - Helper Methods

private func formatTimeFromDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.timeStyle = .short
    return formatter.string(from: date)
}

private func parseTimeString(_ timeString: String) -> DateComponents? {
    let formatter = DateFormatter()
    formatter.dateFormat = "h:mm a"
    
    if let time = formatter.date(from: timeString) {
        return Calendar.current.dateComponents([.hour, .minute], from: time)
    }
    
    return nil
}
