//
//  VaccinationPlannerService.swift
//  PetCapsule
//
//  Vaccination scheduling and tracking service
//

import Foundation
import SwiftUI

@MainActor
class VaccinationPlannerService: ObservableObject {
    static let shared = VaccinationPlannerService()
    
    @Published var vaccinationSchedules: [VaccinationSchedule] = []
    @Published var vaccinationRecords: [VaccinationRecord] = []
    @Published var upcomingVaccinations: [UpcomingVaccination] = []
    @Published var isLoading = false
    
    private init() {
        loadSampleData()
    }
    
    // MARK: - Schedule Generation
    
    func generateVaccinationSchedule(for pet: Pet) async -> VaccinationSchedule {
        isLoading = true
        defer { isLoading = false }
        
        let coreVaccines = getCoreVaccines(for: pet.species)
        let nonCoreVaccines = getNonCoreVaccines(for: pet.species)
        let boosters = generateBoosterSchedule(for: pet)
        
        let schedule = VaccinationSchedule(
            id: UUID().uuidString,
            petId: pet.id,
            petName: pet.name,
            species: pet.species,
            coreVaccines: coreVaccines,
            nonCoreVaccines: nonCoreVaccines,
            boosters: boosters,
            createdAt: Date(),
            lastUpdated: Date()
        )
        
        vaccinationSchedules.append(schedule)
        generateUpcomingVaccinations(from: schedule)
        
        return schedule
    }
    
    private func getCoreVaccines(for species: String) -> [VaccineInfo] {
        switch species.lowercased() {
        case "dog":
            return [
                VaccineInfo(
                    id: "dhpp",
                    name: "DHPP",
                    fullName: "Distemper, Hepatitis, Parvovirus, Parainfluenza",
                    description: "Core vaccine protecting against four serious diseases",
                    isCore: true,
                    firstDoseAge: 6, // weeks
                    boosterInterval: 52, // weeks (1 year)
                    series: [6, 9, 12, 16], // weeks
                    sideEffects: ["Mild lethargy", "Soreness at injection site"],
                    contraindications: ["Illness", "Pregnancy"]
                ),
                VaccineInfo(
                    id: "rabies",
                    name: "Rabies",
                    fullName: "Rabies Vaccine",
                    description: "Required by law in most areas",
                    isCore: true,
                    firstDoseAge: 12,
                    boosterInterval: 156, // 3 years after first adult dose
                    series: [12, 64], // 12 weeks, then 1 year later
                    sideEffects: ["Mild fever", "Decreased appetite"],
                    contraindications: ["Severe illness", "Previous allergic reaction"]
                )
            ]
        case "cat":
            return [
                VaccineInfo(
                    id: "fvrcp",
                    name: "FVRCP",
                    fullName: "Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia",
                    description: "Core vaccine for cats",
                    isCore: true,
                    firstDoseAge: 6,
                    boosterInterval: 52,
                    series: [6, 9, 12, 16],
                    sideEffects: ["Mild lethargy", "Decreased appetite"],
                    contraindications: ["Illness", "Pregnancy"]
                ),
                VaccineInfo(
                    id: "rabies-cat",
                    name: "Rabies",
                    fullName: "Rabies Vaccine",
                    description: "Required by law in most areas",
                    isCore: true,
                    firstDoseAge: 12,
                    boosterInterval: 52,
                    series: [12, 64],
                    sideEffects: ["Mild fever", "Injection site swelling"],
                    contraindications: ["Severe illness", "Previous allergic reaction"]
                )
            ]
        default:
            return []
        }
    }
    
    private func getNonCoreVaccines(for species: String) -> [VaccineInfo] {
        switch species.lowercased() {
        case "dog":
            return [
                VaccineInfo(
                    id: "bordetella",
                    name: "Bordetella",
                    fullName: "Kennel Cough",
                    description: "Recommended for dogs in social settings",
                    isCore: false,
                    firstDoseAge: 8,
                    boosterInterval: 52,
                    series: [8, 12],
                    sideEffects: ["Mild cough", "Nasal discharge"],
                    contraindications: ["Respiratory illness"]
                ),
                VaccineInfo(
                    id: "lyme",
                    name: "Lyme",
                    fullName: "Lyme Disease",
                    description: "For dogs in tick-endemic areas",
                    isCore: false,
                    firstDoseAge: 12,
                    boosterInterval: 52,
                    series: [12, 16],
                    sideEffects: ["Mild lethargy", "Joint soreness"],
                    contraindications: ["Previous Lyme infection"]
                )
            ]
        case "cat":
            return [
                VaccineInfo(
                    id: "felv",
                    name: "FeLV",
                    fullName: "Feline Leukemia Virus",
                    description: "For cats with outdoor access",
                    isCore: false,
                    firstDoseAge: 8,
                    boosterInterval: 52,
                    series: [8, 12],
                    sideEffects: ["Mild fever", "Decreased appetite"],
                    contraindications: ["FeLV positive cats"]
                )
            ]
        default:
            return []
        }
    }
    
    private func generateBoosterSchedule(for pet: Pet) -> [BoosterInfo] {
        let coreVaccines = getCoreVaccines(for: pet.species)
        var boosters: [BoosterInfo] = []
        
        for vaccine in coreVaccines {
            let nextBoosterDate = Calendar.current.date(
                byAdding: .weekOfYear,
                value: vaccine.boosterInterval,
                to: Date()
            ) ?? Date()
            
            let booster = BoosterInfo(
                id: UUID().uuidString,
                vaccineId: vaccine.id,
                vaccineName: vaccine.name,
                dueDate: nextBoosterDate,
                isOverdue: false,
                reminderSent: false
            )
            
            boosters.append(booster)
        }
        
        return boosters
    }
    
    private func generateUpcomingVaccinations(from schedule: VaccinationSchedule) {
        var upcoming: [UpcomingVaccination] = []
        
        // Generate upcoming vaccinations based on schedule
        for vaccine in schedule.coreVaccines + schedule.nonCoreVaccines {
            for ageWeeks in vaccine.series {
                let dueDate = Calendar.current.date(
                    byAdding: .weekOfYear,
                    value: ageWeeks,
                    to: Date()
                ) ?? Date()
                
                let upcomingVaccination = UpcomingVaccination(
                    id: UUID().uuidString,
                    petId: schedule.petId,
                    vaccineId: vaccine.id,
                    vaccineName: vaccine.name,
                    dueDate: dueDate,
                    isCore: vaccine.isCore,
                    priority: vaccine.isCore ? .high : .medium,
                    status: .scheduled,
                    reminderSent: false,
                    notes: ""
                )
                
                upcoming.append(upcomingVaccination)
            }
        }
        
        upcomingVaccinations.append(contentsOf: upcoming)
    }
    
    // MARK: - Record Management
    
    func addVaccinationRecord(
        petId: String,
        vaccineId: String,
        vaccineName: String,
        administeredDate: Date,
        veterinarian: String,
        clinic: String,
        lotNumber: String,
        nextDueDate: Date?,
        notes: String
    ) {
        let record = VaccinationRecord(
            id: UUID().uuidString,
            vaccineName: vaccineName,
            dateAdministered: administeredDate,
            nextDueDate: nextDueDate,
            veterinarian: veterinarian,
            clinic: clinic,
            lotNumber: lotNumber,
            manufacturer: nil,
            notes: notes,
            isCore: true
        )
        
        vaccinationRecords.append(record)
        
        // Update upcoming vaccinations
        if let index = upcomingVaccinations.firstIndex(where: { 
            $0.petId == petId && $0.vaccineId == vaccineId 
        }) {
            upcomingVaccinations[index].status = .completed
        }
    }
    
    // MARK: - Reminder Management
    
    func getOverdueVaccinations(for petId: String) -> [UpcomingVaccination] {
        return upcomingVaccinations.filter { vaccination in
            vaccination.petId == petId &&
            vaccination.status == .scheduled &&
            vaccination.dueDate < Date()
        }
    }
    
    func getUpcomingVaccinations(for petId: String, within days: Int = 30) -> [UpcomingVaccination] {
        let futureDate = Calendar.current.date(byAdding: .day, value: days, to: Date()) ?? Date()
        
        return upcomingVaccinations.filter { vaccination in
            vaccination.petId == petId &&
            vaccination.status == .scheduled &&
            vaccination.dueDate >= Date() &&
            vaccination.dueDate <= futureDate
        }.sorted { $0.dueDate < $1.dueDate }
    }
    
    func sendReminder(for vaccinationId: String) {
        if let index = upcomingVaccinations.firstIndex(where: { $0.id == vaccinationId }) {
            upcomingVaccinations[index].reminderSent = true
            // Here you would integrate with notification system
        }
    }
    
    // MARK: - Analytics
    
    func getVaccinationCompliance(for petId: String) -> VaccinationCompliance {
        let totalRequired = upcomingVaccinations.filter { $0.petId == petId && $0.isCore }.count
        let completed = upcomingVaccinations.filter { 
            $0.petId == petId && $0.isCore && $0.status == .completed 
        }.count
        
        let overdue = getOverdueVaccinations(for: petId).count
        let upcoming = getUpcomingVaccinations(for: petId).count
        
        return VaccinationCompliance(
            totalRequired: totalRequired,
            completed: completed,
            overdue: overdue,
            upcoming: upcoming,
            complianceRate: totalRequired > 0 ? Double(completed) / Double(totalRequired) : 0
        )
    }
    
    // MARK: - Sample Data
    
    private func loadSampleData() {
        // Load sample vaccination data
        // Simplified sample data to avoid compilation errors
    }
}

// MARK: - Data Models

struct VaccinationSchedule: Identifiable, Codable {
    let id: String
    let petId: String
    let petName: String
    let species: String
    let coreVaccines: [VaccineInfo]
    let nonCoreVaccines: [VaccineInfo]
    let boosters: [BoosterInfo]
    let createdAt: Date
    let lastUpdated: Date
}

struct VaccineInfo: Identifiable, Codable {
    let id: String
    let name: String
    let fullName: String
    let description: String
    let isCore: Bool
    let firstDoseAge: Int // weeks
    let boosterInterval: Int // weeks
    let series: [Int] // ages in weeks for series
    let sideEffects: [String]
    let contraindications: [String]
}

struct BoosterInfo: Identifiable, Codable {
    let id: String
    let vaccineId: String
    let vaccineName: String
    let dueDate: Date
    var isOverdue: Bool
    var reminderSent: Bool
}

struct UpcomingVaccination: Identifiable, Codable {
    let id: String
    let petId: String
    let vaccineId: String
    let vaccineName: String
    let dueDate: Date
    let isCore: Bool
    let priority: VaccinationPriority
    var status: VaccinationStatus
    var reminderSent: Bool
    var notes: String
    
    var isOverdue: Bool {
        status == .scheduled && dueDate < Date()
    }
    
    var daysUntilDue: Int {
        Calendar.current.dateComponents([.day], from: Date(), to: dueDate).day ?? 0
    }
}

enum VaccinationPriority: String, CaseIterable, Codable, Sendable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var color: Color {
        switch self {
        case .low: return .gray
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
    
    var displayName: String {
        rawValue.capitalized
    }
}

enum VaccinationStatus: String, CaseIterable, Codable, Sendable {
    case scheduled = "scheduled"
    case completed = "completed"
    case overdue = "overdue"
    case cancelled = "cancelled"
    
    var color: Color {
        switch self {
        case .scheduled: return .blue
        case .completed: return .green
        case .overdue: return .red
        case .cancelled: return .gray
        }
    }
    
    var displayName: String {
        switch self {
        case .scheduled: return "Scheduled"
        case .completed: return "Completed"
        case .overdue: return "Overdue"
        case .cancelled: return "Cancelled"
        }
    }
}

// VaccinationRecord is defined in Pet.swift

struct VaccinationCompliance: Codable {
    let totalRequired: Int
    let completed: Int
    let overdue: Int
    let upcoming: Int
    let complianceRate: Double
    
    var complianceText: String {
        "\(completed)/\(totalRequired) core vaccines completed"
    }
    
    var statusText: String {
        if overdue > 0 {
            return "\(overdue) overdue"
        } else if upcoming > 0 {
            return "\(upcoming) upcoming"
        } else {
            return "Up to date"
        }
    }
}
