//
//  VetFinderService.swift
//  PetCapsule
//
//  Apple Maps SDK integration for finding nearby veterinarians
//

import Foundation
import MapKit
import CoreLocation
import Combine

@MainActor
class VetFinderService: NSObject, ObservableObject {
    @Published var nearbyVets: [VeterinaryLocation] = []
    @Published var emergencyVets: [VeterinaryLocation] = []
    @Published var isSearching = false
    @Published var searchError: String?
    @Published var userLocation: CLLocation?
    @Published var showVetMapPopup = false
    @Published var selectedVets: [VeterinaryLocation] = []
    
    private let locationManager = CLLocationManager()
    private let searchRadius: CLLocationDistance = 25000 // 25km
    private var locationCompletion: ((CLLocation?) -> Void)?
    private var isWaitingForLocation = false
    
    override init() {
        super.init()
        setupLocationManager()
    }
    
    // MARK: - Location Setup
    
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        // Don't automatically request permission - let the user trigger it
    }
    
    func getCurrentLocationAndSearchVets() async {
        print("🔍 Starting location request for vet search...")
        
        // Check current authorization status
        let authStatus = locationManager.authorizationStatus
        print("📍 Current authorization status: \(authStatus)")
        
        switch authStatus {
        case .notDetermined:
            print("📱 Requesting location permission...")
            searchError = "Requesting location permission..."
            
            // Request permission and wait for response
            await requestLocationPermission()
            
            // After permission request, check again
            if locationManager.authorizationStatus == .authorizedWhenInUse || 
               locationManager.authorizationStatus == .authorizedAlways {
                await performLocationSearch()
            } else {
                searchError = "Location permission denied. Please enable location access in Settings to find nearby vets."
                print("❌ Location permission denied")
            }
            
        case .denied, .restricted:
            searchError = "Location access denied. Please enable location access in Settings to find nearby vets."
            print("❌ Location access denied or restricted")
            
        case .authorizedWhenInUse, .authorizedAlways:
            print("✅ Location permission granted, performing search...")
            await performLocationSearch()
            
        @unknown default:
            searchError = "Unknown location authorization status"
        }
    }
    
    private func requestLocationPermission() async {
        locationManager.requestWhenInUseAuthorization()
        
        // Wait a short time for the permission dialog to be processed
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
    }
    
    private func performLocationSearch() async {
        isSearching = true
        searchError = "Getting your location..."
        
        print("📍 Requesting current location...")
        
        // Prevent multiple concurrent location requests
        guard !isWaitingForLocation else {
            print("⚠️ Already waiting for location, skipping request")
            return
        }
        
        isWaitingForLocation = true
        defer { isWaitingForLocation = false }
        
        // Request current location
        await withCheckedContinuation { continuation in
            locationCompletion = { location in
                continuation.resume()
            }
            locationManager.requestLocation()
        }
        
        guard let currentLocation = userLocation else {
            searchError = "Unable to determine your current location. Please try again."
            isSearching = false
            print("❌ Failed to get current location")
            return
        }
        
        print("🗺️ Got location: \(currentLocation.coordinate.latitude), \(currentLocation.coordinate.longitude)")
        searchError = "Searching for nearby vets..."
        
        await searchNearbyVets(location: currentLocation)
    }
    
    func requestLocationAndSearchVets() {
        Task {
            await getCurrentLocationAndSearchVets()
        }
    }
    
    // MARK: - Vet Search
    
    func searchNearbyVets(location: CLLocation? = nil) async {
        let searchLocation = location ?? userLocation
        guard let searchLocation = searchLocation else {
            searchError = "Location not available"
            return
        }
        
        isSearching = true
        searchError = nil
        
        await withTaskGroup(of: Void.self) { group in
            group.addTask {
                await self.searchVeterinarians(near: searchLocation)
            }
            group.addTask {
                await self.searchEmergencyVets(near: searchLocation)
            }
        }
        
        isSearching = false
    }
    
    private func searchVeterinarians(near location: CLLocation) async {
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = "veterinarian animal hospital pet clinic"
        request.region = MKCoordinateRegion(
            center: location.coordinate,
            latitudinalMeters: searchRadius,
            longitudinalMeters: searchRadius
        )
        
        do {
            let search = MKLocalSearch(request: request)
            let response = try await search.start()
            
            let vets = response.mapItems.compactMap { mapItem -> VeterinaryLocation? in
                return createVeterinaryLocation(from: mapItem, location: location)
            }
            
            nearbyVets = vets.sorted { $0.distance < $1.distance }
        } catch {
            print("Error searching for veterinarians: \(error)")
            searchError = "Failed to find nearby veterinarians"
        }
    }
    
    private func searchEmergencyVets(near location: CLLocation) async {
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = "emergency veterinarian 24 hour animal hospital emergency pet clinic"
        request.region = MKCoordinateRegion(
            center: location.coordinate,
            latitudinalMeters: searchRadius * 2,
            longitudinalMeters: searchRadius * 2
        )
        
        do {
            let search = MKLocalSearch(request: request)
            let response = try await search.start()
            
            let emergencyVetLocations = response.mapItems.compactMap { mapItem -> VeterinaryLocation? in
                var vet = createVeterinaryLocation(from: mapItem, location: location)
                vet?.isEmergency = true
                return vet
            }
            
            emergencyVets = emergencyVetLocations.sorted { $0.distance < $1.distance }
        } catch {
            print("Error searching for emergency veterinarians: \(error)")
        }
    }
    
    private func createVeterinaryLocation(from mapItem: MKMapItem, location: CLLocation) -> VeterinaryLocation? {
        guard let coordinate = mapItem.placemark.location?.coordinate else { return nil }
        
        let vetLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        let distance = location.distance(from: vetLocation)
        
        guard distance <= searchRadius * 2 else { return nil }
        
        return VeterinaryLocation(
            id: UUID().uuidString,
            name: mapItem.name ?? "Unknown Veterinarian",
            address: formatAddress(from: mapItem.placemark),
            coordinate: coordinate,
            distance: distance,
            phoneNumber: mapItem.phoneNumber,
            website: mapItem.url?.absoluteString,
            rating: nil,
            isEmergency: false,
            hours: nil,
            mapItem: mapItem
        )
    }
    
    private func formatAddress(from placemark: CLPlacemark) -> String {
        var addressComponents: [String] = []
        
        if let streetNumber = placemark.subThoroughfare {
            addressComponents.append(streetNumber)
        }
        
        if let street = placemark.thoroughfare {
            addressComponents.append(street)
        }
        
        if let city = placemark.locality {
            addressComponents.append(city)
        }
        
        if let state = placemark.administrativeArea {
            addressComponents.append(state)
        }
        
        if let zipCode = placemark.postalCode {
            addressComponents.append(zipCode)
        }
        
        return addressComponents.joined(separator: ", ")
    }
    
    // MARK: - Quick Actions
    
    func showEmergencyVetOptions() async {
        await searchNearbyVets()
        
        guard let nearestVet = findNearestEmergencyVet() else {
            searchError = "No emergency veterinarians found nearby"
            return
        }
        
        let alert = UIAlertController(
            title: "Emergency Veterinarian",
            message: "\(nearestVet.name)\n\(nearestVet.address)\n\(String(format: "%.1f km away", nearestVet.distance / 1000))",
            preferredStyle: .actionSheet
        )
        
        if nearestVet.phoneNumber != nil {
            alert.addAction(UIAlertAction(title: "Call Now", style: .default) { _ in
                self.callVet(nearestVet)
            })
        }
        
        alert.addAction(UIAlertAction(title: "Get Directions", style: .default) { _ in
            self.openInMaps(nearestVet)
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(alert, animated: true)
        }
    }
    
    func openInMaps(_ vet: VeterinaryLocation) {
        vet.mapItem.openInMaps(launchOptions: [
            MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving
        ])
    }
    
    func callVet(_ vet: VeterinaryLocation) {
        guard let phoneNumber = vet.phoneNumber else {
            searchError = "Phone number not available"
            return
        }
        
        let cleanNumber = phoneNumber.replacingOccurrences(of: "[^0-9+]", with: "", options: .regularExpression)
        
        guard let url = URL(string: "tel:\(cleanNumber)"),
              UIApplication.shared.canOpenURL(url) else {
            searchError = "Cannot make phone calls on this device"
            return
        }
        
        UIApplication.shared.open(url)
    }
    
    func findNearestEmergencyVet() -> VeterinaryLocation? {
        return emergencyVets.first ?? nearbyVets.first { vet in
            vet.name.lowercased().contains("emergency") ||
            vet.name.lowercased().contains("24") ||
            vet.name.lowercased().contains("urgent")
        }
    }
    
    // MARK: - Enhanced Vet Search with Map Popup
    
    func showNearbyVetsMapPopup() async {
        await getCurrentLocationAndSearchVets()
        
        // Get top 5 nearest vets (both regular and emergency)
        var allVets = nearbyVets + emergencyVets
        allVets.sort { $0.distance < $1.distance }
        selectedVets = Array(allVets.prefix(5))
        
        if selectedVets.isEmpty {
            searchError = "No veterinarians found within 25km of your location"
        } else {
            showVetMapPopup = true
            searchError = nil
        }
    }
}

// MARK: - CLLocationManagerDelegate

extension VetFinderService: @preconcurrency CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        print("📍 Location updated: \(location.coordinate.latitude), \(location.coordinate.longitude)")
        userLocation = location
        
        // Only call completion if we're actively waiting for it
        if let completion = locationCompletion {
            completion(location)
            locationCompletion = nil
        }
        
        // Also auto-search if this is a regular location update (not from a continuation wait)
        else {
            Task {
                await searchNearbyVets(location: location)
            }
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("❌ Location error: \(error)")
        searchError = "Unable to get current location: \(error.localizedDescription)"
        isSearching = false
        
        // Only call completion if we're actively waiting for it
        if let completion = locationCompletion {
            completion(nil)
            locationCompletion = nil
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        print("🔐 Location authorization changed: \(status)")
        
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            print("✅ Location permission granted")
            // Don't call completion here - wait for actual location result
        case .denied, .restricted:
            print("❌ Location permission denied or restricted")
            searchError = "Location access denied. Please enable location services in Settings to find nearby vets."
            isSearching = false
            // Don't call completion here - this will be handled by requestLocation failure
        case .notDetermined:
            print("📱 Location permission not determined")
            // Don't auto-request, let the user-initiated flow handle this
            break
        @unknown default:
            print("❓ Unknown location authorization status")
            break
        }
    }
}

// MARK: - Veterinary Location Model

struct VeterinaryLocation: Identifiable, Equatable {
    let id: String
    let name: String
    let address: String
    let coordinate: CLLocationCoordinate2D
    let distance: CLLocationDistance
    let phoneNumber: String?
    let website: String?
    let rating: Double?
    var isEmergency: Bool
    let hours: String?
    let mapItem: MKMapItem
    
    var distanceString: String {
        if distance < 1000 {
            return "\(Int(distance)) m"
        } else {
            return String(format: "%.1f km", distance / 1000)
        }
    }
    
    var emergencyStatus: String? {
        if isEmergency {
            return "24/7 Emergency"
        }
        return nil
    }
    
    static func == (lhs: VeterinaryLocation, rhs: VeterinaryLocation) -> Bool {
        return lhs.id == rhs.id
    }
} 