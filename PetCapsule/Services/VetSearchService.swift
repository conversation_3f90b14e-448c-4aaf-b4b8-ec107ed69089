//
//  VetSearchService.swift
//  PetCapsule
//
//  Service for searching and managing veterinarian information
//

import Foundation
import SwiftUI
import CoreLocation
import MapKit

@MainActor
class VetSearchService: ObservableObject {
    static let shared = VetSearchService()
    
    @Published var nearbyVets: [VeterinaryClinic] = []
    @Published var isSearching = false
    @Published var searchError: String?
    
    private init() {}
    
    func findNearbyVets(
        location: CLLocationCoordinate2D,
        radius: Double = 25000, // 25km default
        emergencyOnly: Bool = false
    ) async throws -> [VeterinaryClinic] {
        
        await MainActor.run {
            isSearching = true
            searchError = nil
        }
        
        defer {
            Task { @MainActor in
                isSearching = false
            }
        }
        
        // Search for veterinary clinics using MapKit
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = emergencyOnly ? "emergency veterinary clinic" : "veterinary clinic"
        request.region = MKCoordinateRegion(
            center: location,
            latitudinalMeters: radius,
            longitudinalMeters: radius
        )
        
        let search = MKLocalSearch(request: request)
        
        do {
            let response = try await search.start()
            
            var vets: [VeterinaryClinic] = []
            
            for item in response.mapItems {
                let distance = CLLocation(latitude: location.latitude, longitude: location.longitude)
                    .distance(from: CLLocation(latitude: item.placemark.coordinate.latitude, longitude: item.placemark.coordinate.longitude))
                
                let vet = VeterinaryClinic(
                    name: item.name ?? "Unknown Veterinary Clinic",
                    address: formatAddress(from: item.placemark),
                    phoneNumber: item.phoneNumber ?? "No phone number",
                    is24Hour: isLikely24Hour(item.name ?? ""),
                    distance: distance / 1609.34, // Convert to miles
                    rating: generateRating(), // Placeholder rating
                    specialties: generateSpecialties(for: item.name ?? "")
                )
                
                vets.append(vet)
            }
            
            // Sort by distance
            vets.sort { $0.distance < $1.distance }
            
            // Add some sample emergency vets if none found
            if vets.isEmpty {
                vets = generateSampleVets(near: location)
            }
            
            await MainActor.run {
                self.nearbyVets = vets
            }
            
            return vets
            
        } catch {
            await MainActor.run {
                self.searchError = "Failed to find nearby veterinarians: \(error.localizedDescription)"
            }
            
            // Return sample vets as fallback
            let sampleVets = generateSampleVets(near: location)
            await MainActor.run {
                self.nearbyVets = sampleVets
            }
            return sampleVets
        }
    }
    
    private func formatAddress(from placemark: CLPlacemark) -> String {
        var addressComponents: [String] = []
        
        if let streetNumber = placemark.subThoroughfare {
            addressComponents.append(streetNumber)
        }
        
        if let street = placemark.thoroughfare {
            addressComponents.append(street)
        }
        
        if let city = placemark.locality {
            addressComponents.append(city)
        }
        
        if let state = placemark.administrativeArea {
            addressComponents.append(state)
        }
        
        return addressComponents.joined(separator: " ")
    }
    
    private func isLikely24Hour(_ name: String) -> Bool {
        let emergencyKeywords = ["emergency", "24", "urgent", "after hours", "24/7", "24-7"]
        let lowercaseName = name.lowercased()
        return emergencyKeywords.contains { lowercaseName.contains($0) }
    }
    
    private func generateRating() -> Double {
        // Generate a realistic rating between 3.5 and 5.0
        return Double.random(in: 3.5...5.0)
    }
    
    private func generateSpecialties(for name: String) -> [String] {
        let allSpecialties = [
            "Emergency Care",
            "Surgery",
            "Internal Medicine",
            "Cardiology",
            "Orthopedics",
            "Dermatology",
            "Oncology",
            "Dentistry",
            "Exotic Animals",
            "Small Animals",
            "Large Animals"
        ]
        
        let lowercaseName = name.lowercased()
        var specialties: [String] = []
        
        if lowercaseName.contains("emergency") {
            specialties.append("Emergency Care")
        }
        
        if lowercaseName.contains("animal hospital") {
            specialties.append("Surgery")
            specialties.append("Internal Medicine")
        }
        
        if lowercaseName.contains("specialty") {
            specialties.append("Cardiology")
            specialties.append("Oncology")
        }
        
        // Add 1-3 random specialties if none found
        if specialties.isEmpty {
            let randomCount = Int.random(in: 1...3)
            specialties = Array(allSpecialties.shuffled().prefix(randomCount))
        }
        
        return specialties
    }
    
    private func generateSampleVets(near location: CLLocationCoordinate2D) -> [VeterinaryClinic] {
        let sampleVets = [
            VeterinaryClinic(
                name: "Emergency Animal Hospital",
                address: "123 Main St, Your City, State",
                phoneNumber: "(*************",
                is24Hour: true,
                distance: 2.3,
                rating: 4.8,
                specialties: ["Emergency Care", "Surgery", "Critical Care"]
            ),
            VeterinaryClinic(
                name: "City Veterinary Clinic",
                address: "456 Oak Ave, Your City, State",
                phoneNumber: "(*************",
                is24Hour: false,
                distance: 3.1,
                rating: 4.5,
                specialties: ["General Practice", "Preventive Care"]
            ),
            VeterinaryClinic(
                name: "After Hours Animal Care",
                address: "789 Pine St, Your City, State",
                phoneNumber: "(*************",
                is24Hour: true,
                distance: 4.7,
                rating: 4.6,
                specialties: ["Emergency Care", "Urgent Care"]
            ),
            VeterinaryClinic(
                name: "Companion Animal Hospital",
                address: "321 Elm Dr, Your City, State",
                phoneNumber: "(*************",
                is24Hour: false,
                distance: 5.2,
                rating: 4.7,
                specialties: ["Small Animals", "Internal Medicine", "Dentistry"]
            ),
            VeterinaryClinic(
                name: "Metro Emergency Vet",
                address: "654 Maple Blvd, Your City, State",
                phoneNumber: "(*************",
                is24Hour: true,
                distance: 6.8,
                rating: 4.4,
                specialties: ["Emergency Care", "Trauma", "Surgery"]
            )
        ]
        
        return sampleVets
    }
    
    func searchByName(_ query: String) async -> [VeterinaryClinic] {
        return nearbyVets.filter { vet in
            vet.name.localizedCaseInsensitiveContains(query) ||
            vet.specialties.contains { $0.localizedCaseInsensitiveContains(query) }
        }
    }
    
    func getEmergencyVets() -> [VeterinaryClinic] {
        return nearbyVets.filter { $0.is24Hour }
    }
    
    func getVetsWithSpecialty(_ specialty: String) -> [VeterinaryClinic] {
        return nearbyVets.filter { vet in
            vet.specialties.contains { $0.localizedCaseInsensitiveContains(specialty) }
        }
    }
}

// EmergencyCallService is defined in EmergencyCallService.swift

// MARK: - Vet Detail View

struct VetDetailView: View {
    let vet: VeterinaryClinic
    @EnvironmentObject var vetService: VetSearchService
    @Environment(\.dismiss) private var dismiss
    @State private var isSaved = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(vet.name)
                                    .font(.title2)
                                    .fontWeight(.bold)
                                
                                Text(vet.address)
                                    .font(.headline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing, spacing: 4) {
                                HStack {
                                    Image(systemName: "star.fill")
                                        .foregroundColor(.yellow)
                                    Text(String(format: "%.1f", vet.rating))
                                        .fontWeight(.medium)
                                }
                                
                                Text("\(vet.specialties.count) specialties")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        // Status and Distance
                        HStack {
                            HStack {
                                Circle()
                                    .fill(vet.is24Hour ? Color.green : Color.red)
                                    .frame(width: 8, height: 8)
                                Text(vet.is24Hour ? "Open 24/7" : "Closed")
                                    .font(.subheadline)
                                    .foregroundColor(vet.is24Hour ? .green : .red)
                            }
                            
                            Text("•")
                                .foregroundColor(.secondary)
                            
                            Text("\(String(format: "%.1f", vet.distance)) miles away")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Contact Actions
                    HStack(spacing: 12) {
                        Button(action: callVet) {
                            HStack {
                                Image(systemName: "phone.fill")
                                Text("Call")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                        
                        Button(action: openDirections) {
                            HStack {
                                Image(systemName: "location.fill")
                                Text("Directions")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                    }
                    
                    // Specialties
                    if !vet.specialties.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Specialties")
                                .font(.headline)
                                .fontWeight(.bold)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                                ForEach(vet.specialties, id: \.self) { specialty in
                                    Text(specialty)
                                        .font(.subheadline)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(Color.green.opacity(0.1))
                                        .foregroundColor(.green)
                                        .cornerRadius(8)
                                }
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Veterinarian Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isSaved ? "Saved" : "Save") {
                        if isSaved {
                            // Remove vet from saved list
                        } else {
                            // Add vet to saved list
                        }
                        isSaved.toggle()
                    }
                    .foregroundColor(isSaved ? .green : .blue)
                }
            }
            .onAppear {
                isSaved = vetService.nearbyVets.contains { $0.id == vet.id }
            }
        }
    }
    
    private func callVet() {
        if let url = URL(string: "tel:\(vet.phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openDirections() {
        let mapItem = MKMapItem(placemark: MKPlacemark(coordinate: vet.coordinate))
        mapItem.name = vet.name
        mapItem.openInMaps(launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving])
    }
}
