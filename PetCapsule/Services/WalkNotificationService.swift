//
//  WalkNotificationService.swift
//  PetCapsule
//
//  Smart notification service for optimal walk timing
//

import Foundation
import UserNotifications
import CoreLocation
import SwiftUI

@MainActor
class WalkNotificationService: ObservableObject {
    static let shared = WalkNotificationService()
    
    @Published var isEnabled = false
    @Published var nextAlertTime = "No alerts scheduled"
    @Published var preferredWalkTimes: [WalkTimePreference] = []
    
    private let notificationCenter = UNUserNotificationCenter.current()
    private let weatherService = AppleWeatherService.shared
    private let plannerService = PetPlannerService.shared
    
    // Notification settings
    @AppStorage("walk_notifications_enabled") private var notificationsEnabled = false
    @AppStorage("morning_walk_time") private var morningWalkTime = "08:00"
    @AppStorage("evening_walk_time") private var eveningWalkTime = "18:00"
    @AppStorage("notification_lead_time") private var notificationLeadTime = 30 // minutes
    @AppStorage("minimum_walk_score") private var minimumWalkScore = 6.0
    
    private init() {
        isEnabled = notificationsEnabled
        setupPreferredTimes()
        checkNotificationPermissions()
    }
    
    // MARK: - Public Methods
    
    func setupWalkNotifications() {
        Task {
            await requestNotificationPermission()
            if isEnabled {
                await scheduleIntelligentNotifications()
            }
        }
    }
    
    func toggleNotifications(_ enabled: Bool) {
        isEnabled = enabled
        notificationsEnabled = enabled
        
        if enabled {
            Task {
                await requestNotificationPermission()
                await scheduleIntelligentNotifications()
            }
        } else {
            cancelAllNotifications()
        }
    }
    
    func scheduleNextWalkNotification() {
        guard isEnabled else { return }
        
        Task {
            await scheduleIntelligentNotifications()
        }
    }
    
    func updatePreferences(
        morningTime: String? = nil,
        eveningTime: String? = nil,
        leadTime: Int? = nil,
        minimumScore: Double? = nil
    ) {
        if let morningTime = morningTime {
            self.morningWalkTime = morningTime
        }
        if let eveningTime = eveningTime {
            self.eveningWalkTime = eveningTime
        }
        if let leadTime = leadTime {
            self.notificationLeadTime = leadTime
        }
        if let minimumScore = minimumScore {
            self.minimumWalkScore = minimumScore
        }
        
        setupPreferredTimes()
        
        if isEnabled {
            Task {
                await scheduleIntelligentNotifications()
            }
        }
    }
    
    // MARK: - Permission Management
    
    private func checkNotificationPermissions() {
        notificationCenter.getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.isEnabled = settings.authorizationStatus == .authorized && self.notificationsEnabled
            }
        }
    }
    
    private func requestNotificationPermission() async {
        do {
            let granted = try await notificationCenter.requestAuthorization(
                options: [.alert, .badge, .sound, .provisional]
            )
            
            await MainActor.run {
                self.isEnabled = granted && self.notificationsEnabled
            }
            
            if granted {
                print("✅ Notification permission granted")
            } else {
                print("❌ Notification permission denied")
            }
        } catch {
            print("❌ Failed to request notification permission: \(error)")
        }
    }
    
    // MARK: - Intelligent Scheduling
    
    private func scheduleIntelligentNotifications() async {
        guard let currentLocation = plannerService.currentLocation else {
            print("❌ No current location for weather forecasting")
            return
        }
        
        // Cancel existing notifications
        cancelAllNotifications()
        
        // Get weather forecast for next 24 hours
        do {
            let hourlyForecast = try await weatherService.getHourlyForecast(for: currentLocation)
            let optimalTimes = findOptimalWalkTimes(from: hourlyForecast)
            
            await scheduleNotifications(for: optimalTimes)
            await updateNextAlertTime()
            
            print("✅ Scheduled \(optimalTimes.count) intelligent walk notifications")
            
        } catch {
            print("❌ Failed to schedule intelligent notifications: \(error)")
            // Fallback to basic time-based notifications
            await scheduleBasicTimeNotifications()
        }
    }
    
    private func findOptimalWalkTimes(from forecast: [HourlyForecast]) -> [OptimalWalkTime] {
        var optimalTimes: [OptimalWalkTime] = []
        let now = Date()
        
        // Group by preferred time windows
        let morningWindow = getTimeWindow(for: morningWalkTime, duration: 3) // 3-hour window
        let eveningWindow = getTimeWindow(for: eveningWalkTime, duration: 3)
        
        // Find best times in each window
        for window in [morningWindow, eveningWindow] {
            let windowForecasts = forecast.filter { forecast in
                let forecastHour = hourFromString(forecast.hour)
                return window.contains(forecastHour)
            }
            
            // Find the best forecast in this window
            if let bestForecast = windowForecasts.max(by: { walkQualityScore($0.walkQuality) < walkQualityScore($1.walkQuality) }),
               walkQualityScore(bestForecast.walkQuality) >= minimumWalkScore {
                
                let walkTime = dateFromHourString(bestForecast.hour)
                let notificationTime = walkTime.addingTimeInterval(-Double(notificationLeadTime * 60))
                
                if notificationTime > now {
                    optimalTimes.append(OptimalWalkTime(
                        notificationTime: notificationTime,
                        walkTime: walkTime,
                        score: walkQualityScore(bestForecast.walkQuality),
                        weather: bestForecast,
                        reason: generateWalkReason(for: bestForecast)
                    ))
                }
            }
        }
        
        // Add emergency good weather notifications (unexpected great conditions)
        let emergencyTimes = findEmergencyGoodWeather(from: forecast, excluding: optimalTimes)
        optimalTimes.append(contentsOf: emergencyTimes)
        
        return optimalTimes.sorted { $0.notificationTime < $1.notificationTime }
    }
    
    private func findEmergencyGoodWeather(from forecast: [HourlyForecast], excluding existing: [OptimalWalkTime]) -> [OptimalWalkTime] {
        let now = Date()
        let existingTimes = Set(existing.map { Calendar.current.component(.hour, from: $0.walkTime) })
        
        return forecast.compactMap { forecast in
            let forecastHour = hourFromString(forecast.hour)
            
            // Only consider if not already scheduled and score is excellent
            guard !existingTimes.contains(forecastHour),
                  walkQualityScore(forecast.walkQuality) >= 8.5 else { // Excellent conditions
                return nil
            }
            
            let walkTime = dateFromHourString(forecast.hour)
            let notificationTime = walkTime.addingTimeInterval(-Double(notificationLeadTime * 60))
            
            guard notificationTime > now else { return nil }
            
            return OptimalWalkTime(
                notificationTime: notificationTime,
                walkTime: walkTime,
                score: walkQualityScore(forecast.walkQuality),
                weather: forecast,
                reason: "Perfect conditions detected! 🌟"
            )
        }
    }
    
    private func scheduleNotifications(for optimalTimes: [OptimalWalkTime]) async {
        for (index, optimalTime) in optimalTimes.enumerated() {
            let content = UNMutableNotificationContent()
            content.title = "Perfect Walk Weather! 🐾"
            content.body = generateNotificationBody(for: optimalTime)
            content.sound = .default
            content.badge = 1
            
            // Add rich notification data
            content.userInfo = [
                "walkTime": optimalTime.walkTime.timeIntervalSince1970,
                "score": optimalTime.score,
                "temperature": optimalTime.weather?.temperature ?? 70,
                "type": "walk_notification"
            ]
            
            // Create trigger
            let trigger = UNTimeIntervalNotificationTrigger(
                timeInterval: optimalTime.notificationTime.timeIntervalSinceNow,
                repeats: false
            )
            
            let identifier = "walk_notification_\(index)_\(UUID().uuidString)"
            let request = UNNotificationRequest(
                identifier: identifier,
                content: content,
                trigger: trigger
            )
            
            do {
                try await notificationCenter.add(request)
                print("✅ Scheduled notification for \(optimalTime.walkTime.formatted(date: .omitted, time: .shortened))")
            } catch {
                print("❌ Failed to schedule notification: \(error)")
            }
        }
    }
    
    private func scheduleBasicTimeNotifications() async {
        // Fallback: Schedule basic time-based notifications
        let times = [morningWalkTime, eveningWalkTime]
        
        for (index, timeString) in times.enumerated() {
            let content = UNMutableNotificationContent()
            content.title = "Walk Reminder 🐾"
            content.body = "It's time for your pet's walk! Check the weather conditions."
            content.sound = .default
            
            // Create daily trigger
            let dateComponents = timeStringToDateComponents(timeString)
            let trigger = UNCalendarNotificationTrigger(
                dateMatching: dateComponents,
                repeats: true
            )
            
            let identifier = "basic_walk_\(index)"
            let request = UNNotificationRequest(
                identifier: identifier,
                content: content,
                trigger: trigger
            )
            
            do {
                try await notificationCenter.add(request)
                print("✅ Scheduled basic notification for \(timeString)")
            } catch {
                print("❌ Failed to schedule basic notification: \(error)")
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func setupPreferredTimes() {
        preferredWalkTimes = [
            WalkTimePreference(
                id: "morning",
                name: "Morning Walk",
                time: morningWalkTime,
                isEnabled: true
            ),
            WalkTimePreference(
                id: "evening",
                name: "Evening Walk",
                time: eveningWalkTime,
                isEnabled: true
            )
        ]
    }
    
    private func getTimeWindow(for timeString: String, duration: Int) -> ClosedRange<Int> {
        let hour = timeStringToHour(timeString)
        let startHour = max(0, hour - duration/2)
        let endHour = min(23, hour + duration/2)
        return startHour...endHour
    }
    
    private func timeStringToHour(_ timeString: String) -> Int {
        let components = timeString.split(separator: ":")
        return Int(components[0]) ?? 8
    }
    
    private func timeStringToDateComponents(_ timeString: String) -> DateComponents {
        let components = timeString.split(separator: ":")
        let hour = Int(components[0]) ?? 8
        let minute = Int(components[1]) ?? 0
        
        var dateComponents = DateComponents()
        dateComponents.hour = hour
        dateComponents.minute = minute
        return dateComponents
    }
    
    private func generateWalkReason(for forecast: HourlyForecast) -> String {
        let score = walkQualityScore(forecast.walkQuality)
        let temp = forecast.temperature
        
        if score >= 9 {
            return "Perfect weather - ideal for a long walk!"
        } else if score >= 7 {
            return "Great conditions for outdoor exercise"
        } else if temp < 45 {
            return "Good time for a quick walk before it gets colder"
        } else if temp > 85 {
            return "Cooler conditions coming up - good for sensitive pets"
        } else {
            return "Good walking weather ahead"
        }
    }
    
    private func generateNotificationBody(for optimalTime: OptimalWalkTime) -> String {
        let timeString = optimalTime.walkTime.formatted(date: .omitted, time: .shortened)
        let scoreText = String(format: "%.1f", optimalTime.score)
        
        return "\(optimalTime.reason) Perfect time: \(timeString) (Score: \(scoreText)/10)"
    }
    
    private func updateNextAlertTime() async {
        let pendingRequests = await notificationCenter.pendingNotificationRequests()
        let walkNotifications = pendingRequests.filter { $0.content.userInfo["type"] as? String == "walk_notification" }
        
        if let nextNotification = walkNotifications.first {
            if let trigger = nextNotification.trigger as? UNTimeIntervalNotificationTrigger {
                let nextTime = Date().addingTimeInterval(trigger.timeInterval)
                await MainActor.run {
                    self.nextAlertTime = nextTime.formatted(date: .omitted, time: .shortened)
                }
            }
        } else {
            await MainActor.run {
                self.nextAlertTime = "No alerts scheduled"
            }
        }
    }
    
    private func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
        nextAlertTime = "No alerts scheduled"
        print("✅ Cancelled all walk notifications")
    }
    
    // MARK: - Additional Helper Functions
    
    private func hourFromString(_ hourString: String) -> Int {
        let components = hourString.replacingOccurrences(of: " AM", with: "").replacingOccurrences(of: " PM", with: "")
        if let hour = Int(components) {
            let isAM = hourString.contains("AM")
            if isAM {
                return hour == 12 ? 0 : hour
            } else {
                return hour == 12 ? 12 : hour + 12
            }
        }
        return 8 // Default to 8 AM
    }
    
    private func dateFromHourString(_ hourString: String) -> Date {
        let hour = hourFromString(hourString)
        let calendar = Calendar.current
        var components = calendar.dateComponents([.year, .month, .day], from: Date())
        components.hour = hour
        components.minute = 0
        return calendar.date(from: components) ?? Date()
    }
    
    private func walkQualityScore(_ quality: WalkQuality) -> Double {
        switch quality {
        case .excellent: return 10.0
        case .good: return 7.5
        case .fair: return 5.0
        case .poor: return 2.5
        }
    }
}

// MARK: - Supporting Types

// Note: OptimalWalkTime is defined in PetPlannerView.swift

struct WalkTimePreference: Identifiable {
    let id: String
    let name: String
    let time: String
    let isEnabled: Bool
}

// MARK: - Notification Extensions

extension WalkNotificationService {
    func handleNotificationResponse(_ response: UNNotificationResponse) {
        guard let userInfo = response.notification.request.content.userInfo as? [String: Any],
              userInfo["type"] as? String == "walk_notification" else {
            return
        }
        
        switch response.actionIdentifier {
        case "start_walk_action":
            // Open app to start walk tracker
            DispatchQueue.main.async {
                // Navigate to walk tracker
            }
        case "snooze_action":
            // Snooze for 30 minutes
            scheduleSnoozeNotification()
        case UNNotificationDefaultActionIdentifier:
            // User tapped notification - open app
            break
        default:
            break
        }
    }
    
    private func scheduleSnoozeNotification() {
        let content = UNMutableNotificationContent()
        content.title = "Walk Reminder 🐾"
        content.body = "Don't forget about your pet's walk!"
        content.sound = .default
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 30 * 60, repeats: false) // 30 minutes
        let request = UNNotificationRequest(
            identifier: "walk_snooze_\(UUID().uuidString)",
            content: content,
            trigger: trigger
        )
        
        notificationCenter.add(request) { error in
            if let error = error {
                print("❌ Failed to schedule snooze notification: \(error)")
            } else {
                print("✅ Scheduled snooze notification")
            }
        }
    }
} 