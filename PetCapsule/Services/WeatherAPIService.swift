//
//  WeatherAPIService.swift
//  PetCapsule
//
//  Legacy weather service - now redirects to AppleWeatherService
//  Maintained for backward compatibility
//

import Foundation
import CoreLocation
import SwiftUI

// Legacy weather service that redirects to the new Apple WeatherKit service
@MainActor
class WeatherAPIService: ObservableObject {
    static let shared = WeatherAPIService()

    // Redirect to the new Apple WeatherKit service
    private let appleWeatherService = AppleWeatherService.shared

    private init() {}

    // MARK: - Legacy API Compatibility

    func getCurrentWeather(for location: CLLocationCoordinate2D) async throws -> WeatherData {
        return try await appleWeatherService.getCurrentWeather(for: location)
    }

    func getAirQuality(for location: CLLocationCoordinate2D) async throws -> AirQualityData {
        return try await appleWeatherService.getAirQuality(for: location)
    }

    func getHourlyForecast(for location: CLLocationCoordinate2D) async throws -> [HourlyForecast] {
        return try await appleWeatherService.getHourlyForecast(for: location)
    }

    func generateSharedWalkRecommendation(weather: WeatherData, airQuality: AirQualityData) -> WalkRecommendation {
        return appleWeatherService.generateWalkRecommendation(weather: weather, airQuality: airQuality, pollen: PollenData.sample)
    }

}
