//
//  WebSearchService.swift
//  PetCapsule
//
//  Service for performing internet searches to supplement AI agent knowledge
//

import Foundation

@MainActor
class WebSearchService: ObservableObject {
    static let shared = WebSearchService()
    
    @Published var isSearching = false
    @Published var lastError: String?
    
    private let searchAPIKey = "YOUR_SEARCH_API_KEY" // Configure with your search API key
    private let searchEngineID = "YOUR_SEARCH_ENGINE_ID" // Configure with your search engine ID
    
    private init() {}
    
    // MARK: - Public Methods
    
    func searchWeb(query: String, maxResults: Int = 5) async -> [WebSearchResult] {
        isSearching = true
        defer { isSearching = false }

        // Use real web search implementation
        return await performRealSearch(query: query, maxResults: maxResults)
    }
    
    func searchPetSpecificInfo(query: String, petType: String? = nil) async -> [WebSearchResult] {
        var enhancedQuery = query
        
        if let petType = petType {
            enhancedQuery = "\(query) \(petType) pet care"
        } else {
            enhancedQuery = "\(query) pet care veterinary"
        }
        
        return await searchWeb(query: enhancedQuery)
    }
    
    // MARK: - Private Methods
    
    private func performMockSearch(query: String, maxResults: Int) async -> [WebSearchResult] {
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Return mock search results for development
        return [
            WebSearchResult(
                title: "Latest Pet Care Guidelines - \(query)",
                url: "https://example-vet.com/guidelines",
                snippet: "Comprehensive guide covering the latest veterinary recommendations for \(query). Updated with current best practices and expert advice.",
                source: "Veterinary Medical Association"
            ),
            WebSearchResult(
                title: "Expert Tips: \(query) for Pet Owners",
                url: "https://petcare-experts.com/tips",
                snippet: "Professional veterinarians share their top recommendations for \(query). Evidence-based advice for optimal pet health and wellness.",
                source: "Pet Care Experts"
            ),
            WebSearchResult(
                title: "Research Study: \(query) in Companion Animals",
                url: "https://research.vet/studies",
                snippet: "Recent scientific research findings on \(query) in dogs and cats. Peer-reviewed study with practical implications for pet owners.",
                source: "Veterinary Research Journal"
            )
        ].prefix(maxResults).map { $0 }
    }
    
    private func performRealWebSearch(query: String, maxResults: Int) async -> [WebSearchResult] {
        // For now, return mock results since we don't have direct web search access
        // In a real implementation, this would use Google Custom Search API or similar
        return await performMockSearch(query: query, maxResults: maxResults)
    }

    private func performRealSearch(query: String, maxResults: Int) async -> [WebSearchResult] {
        // This would implement real Google Custom Search API integration
        guard !searchAPIKey.isEmpty && searchAPIKey != "YOUR_SEARCH_API_KEY" else {
            // Return empty results if no API key is configured
            return []
        }

        let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let urlString = "https://www.googleapis.com/customsearch/v1?key=\(searchAPIKey)&cx=\(searchEngineID)&q=\(encodedQuery)&num=\(maxResults)"

        guard let url = URL(string: urlString) else {
            lastError = "Invalid search URL"
            return []
        }

        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            let searchResponse = try JSONDecoder().decode(GoogleSearchResponse.self, from: data)

            return searchResponse.items?.map { item in
                WebSearchResult(
                    title: item.title,
                    url: item.link,
                    snippet: item.snippet,
                    source: extractDomain(from: item.link)
                )
            } ?? []

        } catch {
            lastError = "Search failed: \(error.localizedDescription)"
            return []
        }
    }
    
    private func extractDomain(from url: String) -> String {
        guard let urlComponents = URLComponents(string: url),
              let host = urlComponents.host else {
            return "Unknown Source"
        }
        return host.replacingOccurrences(of: "www.", with: "")
    }
}

// MARK: - Data Models

struct WebSearchResult: Identifiable, Codable {
    let id: UUID
    let title: String
    let url: String
    let snippet: String
    let source: String

    init(title: String, url: String, snippet: String, source: String) {
        self.id = UUID()
        self.title = title
        self.url = url
        self.snippet = snippet
        self.source = source
    }
    
    var displayTitle: String {
        title.count > 60 ? String(title.prefix(60)) + "..." : title
    }
    
    var displaySnippet: String {
        snippet.count > 150 ? String(snippet.prefix(150)) + "..." : snippet
    }
}

// MARK: - Google Search API Models

struct GoogleSearchResponse: Codable {
    let items: [GoogleSearchItem]?
}

struct GoogleSearchItem: Codable {
    let title: String
    let link: String
    let snippet: String
}

// MARK: - Search Categories

enum SearchCategory: String, CaseIterable {
    case health = "health"
    case nutrition = "nutrition"
    case training = "training"
    case grooming = "grooming"
    case behavior = "behavior"
    case emergency = "emergency"
    case general = "general"
    
    var displayName: String {
        switch self {
        case .health: return "Health & Medical"
        case .nutrition: return "Nutrition & Diet"
        case .training: return "Training & Behavior"
        case .grooming: return "Grooming & Care"
        case .behavior: return "Behavior & Psychology"
        case .emergency: return "Emergency Care"
        case .general: return "General Pet Care"
        }
    }
    
    var searchTerms: [String] {
        switch self {
        case .health:
            return ["veterinary", "health", "medical", "symptoms", "diagnosis"]
        case .nutrition:
            return ["nutrition", "diet", "food", "feeding", "supplements"]
        case .training:
            return ["training", "obedience", "commands", "behavior modification"]
        case .grooming:
            return ["grooming", "hygiene", "coat care", "nail trimming"]
        case .behavior:
            return ["behavior", "psychology", "anxiety", "stress", "socialization"]
        case .emergency:
            return ["emergency", "first aid", "urgent care", "crisis"]
        case .general:
            return ["pet care", "companion animal", "veterinary advice"]
        }
    }
}
