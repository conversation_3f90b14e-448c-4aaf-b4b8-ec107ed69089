//
//  KeyboardHandler.swift
//  PetTime Capsule
//
//  Keyboard handling utility to prevent constraint conflicts
//

import SwiftUI
import Combine

@MainActor
class KeyboardHandler: ObservableObject {
    @Published var keyboardHeight: CGFloat = 0
    @Published var isKeyboardVisible = false
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupKeyboardObservers()
    }
    
    private func setupKeyboardObservers() {
        NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)
            .compactMap { notification in
                notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect
            }
            .map { rect in
                rect.height
            }
            .sink { [weak self] height in
                withAnimation(.easeInOut(duration: 0.3)) {
                    self?.keyboardHeight = height
                    self?.isKeyboardVisible = true
                }
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)
            .sink { [weak self] _ in
                withAnimation(.easeInOut(duration: 0.3)) {
                    self?.keyboardHeight = 0
                    self?.isKeyboardVisible = false
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - SwiftUI View Modifier

struct KeyboardAdaptive: ViewModifier {
    @StateObject private var keyboardHandler = KeyboardHandler()
    
    func body(content: Content) -> some View {
        content
            .padding(.bottom, keyboardHandler.keyboardHeight)
            .animation(.easeInOut(duration: 0.3), value: keyboardHandler.keyboardHeight)
            .environmentObject(keyboardHandler)
    }
}

extension View {
    func keyboardAdaptive() -> some View {
        modifier(KeyboardAdaptive())
    }
}

// MARK: - Keyboard Dismissal

extension View {
    func dismissKeyboardOnTap() -> some View {
        onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
    }
}

// MARK: - Safe Area Keyboard Handling

struct KeyboardSafeArea: ViewModifier {
    @StateObject private var keyboardHandler = KeyboardHandler()
    
    func body(content: Content) -> some View {
        content
            .safeAreaInset(edge: .bottom) {
                Rectangle()
                    .fill(Color.clear)
                    .frame(height: keyboardHandler.isKeyboardVisible ? keyboardHandler.keyboardHeight : 0)
                    .animation(.easeInOut(duration: 0.3), value: keyboardHandler.keyboardHeight)
            }
    }
}

extension View {
    func keyboardSafeArea() -> some View {
        modifier(KeyboardSafeArea())
    }
}
