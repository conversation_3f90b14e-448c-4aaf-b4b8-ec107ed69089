//
//  AIHealthCenterView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

@available(iOS 18.0, *)
struct AIHealthCenterView: View {
    @EnvironmentObject var realDataService: RealDataService
    @EnvironmentObject var aiSupportService: PetAISupportService
    @State private var selectedPet: Pet?
    @State private var showAnalysisResults = false
    @State private var analysisResults: PetHealthAnalysisResult?
    @State private var animateCards = false
    @State private var selectedTab = 0

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Pet Selector
                petSelectorSection

                // Tab Selector
                tabSelectorSection

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    healthOverviewTab
                        .tag(0)

                    nutritionTab
                        .tag(1)

                    behaviorTab
                        .tag(2)

                    recommendationsTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Health Guardian")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Analyze") {
                        performAIAnalysis()
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple)
                    )
                    .disabled(selectedPet == nil || aiSupportService.isAnalyzing)
                }
            }
            .sheet(isPresented: $showAnalysisResults) {
                if let results = analysisResults {
                    AIAnalysisResultsView(results: results)
                }
            }
            .onAppear {
                if selectedPet == nil && !realDataService.pets.isEmpty {
                    selectedPet = realDataService.pets.first
                }
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
            }
        }
    }

    // MARK: - Pet Selector Section

    private var petSelectorSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Select Pet for Analysis")
                    .font(.petTitle3)
                    .fontWeight(.semibold)

                Spacer()
            }

            if realDataService.pets.isEmpty {
                noPetsView
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(realDataService.pets, id: \.id) { pet in
                            petSelectorCard(pet: pet)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private func petSelectorCard(pet: Pet) -> some View {
        VStack(spacing: 8) {
            // Pet Image
            AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                ZStack {
                    Circle()
                        .fill(Color.purple.opacity(0.2))

                    Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                        .font(.title2)
                }
            }
            .frame(width: 60, height: 60)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(selectedPet?.id == pet.id ? Color.purple : Color.clear, lineWidth: 3)
            )

            Text(pet.name)
                .font(.petCaption)
                .fontWeight(.semibold)
                .foregroundColor(selectedPet?.id == pet.id ? .purple : .primary)

            // Health indicator
            Circle()
                .fill(pet.healthScore > 0.8 ? .green : pet.healthScore > 0.6 ? .orange : .red)
                .frame(width: 8, height: 8)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedPet?.id == pet.id ? Color.purple.opacity(0.1) : Color(.systemGray6))
        )
        .onTapGesture {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                selectedPet = pet
            }
        }
    }

    private var noPetsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "pawprint.circle")
                .font(.system(size: 40))
                .foregroundColor(.purple.opacity(0.6))

            Text("No pets added yet")
                .font(.petSubheadline)
                .foregroundColor(.secondary)

            Text("Add a pet to start using AI health analysis")
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }

    // MARK: - Tab Selector Section

    private var tabSelectorSection: some View {
        HStack(spacing: 0) {
            tabButton(title: "Health", icon: "heart.fill", index: 0)
            tabButton(title: "Nutrition", icon: "leaf.fill", index: 1)
            tabButton(title: "Behavior", icon: "brain.head.profile", index: 2)
            tabButton(title: "AI Tips", icon: "lightbulb.fill", index: 3)
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    private func tabButton(title: String, icon: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)

                Text(title)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == index ? Color.purple.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Health Overview Tab

    private var healthOverviewTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                if let pet = selectedPet {
                    // Overall Health Score
                    healthScoreCard(pet: pet)

                    // Health Metrics
                    healthMetricsGrid(pet: pet)

                    // Recent Health Alerts
                    healthAlertsSection(pet: pet)

                    // Vaccination Status
                    vaccinationStatusSection(pet: pet)
                } else {
                    selectPetPrompt
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private func healthScoreCard(pet: Pet) -> some View {
        VStack(spacing: 16) {
            HStack {
                Text("Overall Health Score")
                    .font(.petTitle3)
                    .fontWeight(.bold)

                Spacer()

                Text("Last updated: \(formatDate(pet.lastAIAnalysis ?? Date()))")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            ZStack {
                Circle()
                    .stroke(Color(.systemGray5), lineWidth: 12)
                    .frame(width: 120, height: 120)

                Circle()
                    .trim(from: 0, to: pet.healthScore)
                    .stroke(
                        pet.healthScore > 0.8 ? Color.green :
                        pet.healthScore > 0.6 ? Color.orange : Color.red,
                        style: StrokeStyle(lineWidth: 12, lineCap: .round)
                    )
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: animateCards)

                VStack {
                    Text("\(Int(pet.healthScore * 100))")
                        .font(.petLargeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("Health Score")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
            }

            Text(pet.healthScore > 0.8 ? "Excellent Health" :
                 pet.healthScore > 0.6 ? "Good Health" : "Needs Attention")
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(pet.healthScore > 0.8 ? .green :
                                pet.healthScore > 0.6 ? .orange : .red)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }

    private func healthMetricsGrid(pet: Pet) -> some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            healthMetricCard(
                title: "Weight",
                value: pet.weight != nil ? String(format: "%.1f kg", pet.weight!) : "Not set",
                icon: "scalemass.fill",
                color: .blue
            )

            healthMetricCard(
                title: "Activity Level",
                value: pet.activityLevel.capitalized,
                icon: "figure.run",
                color: .green
            )

            healthMetricCard(
                title: "Last Checkup",
                value: pet.lastCheckupDate != nil ? formatDate(pet.lastCheckupDate!) : "Not set",
                icon: "stethoscope",
                color: .orange
            )

            healthMetricCard(
                title: "Medications",
                value: "\(pet.medications.count) active",
                icon: "pills.fill",
                color: .red
            )
        }
    }

    private func healthMetricCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Spacer()
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(title)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private func healthAlertsSection(pet: Pet) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Health Alerts")
                .font(.petTitle3)
                .fontWeight(.bold)

            if pet.healthAlerts.isEmpty {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)

                    Text("No health alerts")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)

                    Spacer()
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green.opacity(0.1))
                )
            } else {
                ForEach(pet.healthAlerts, id: \.self) { alert in
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)

                        Text(alert.title)
                            .font(.petSubheadline)
                            .foregroundColor(.primary)

                        Spacer()
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.orange.opacity(0.1))
                    )
                }
            }
        }
    }

    private func vaccinationStatusSection(pet: Pet) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Vaccination Status")
                .font(.petTitle3)
                .fontWeight(.bold)

            if pet.vaccinations.isEmpty {
                Text("No vaccination records")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(pet.vaccinations, id: \.self) { vaccination in
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)

                            Text(vaccination)
                                .font(.petCaption)
                                .foregroundColor(.primary)

                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.green.opacity(0.1))
                        )
                    }
                }
            }
        }
    }

    // MARK: - Helper Methods

    private var selectPetPrompt: some View {
        VStack(spacing: 16) {
            Image(systemName: "arrow.up.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.purple.opacity(0.6))

            Text("Select a pet above to view AI health analysis")
                .font(.petTitle3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }

    private func performAIAnalysis() {
        guard let pet = selectedPet else { return }

        Task {
            let results = await aiSupportService.analyzeCompletePetHealth(for: pet)
            await MainActor.run {
                analysisResults = results
                showAnalysisResults = true
            }
        }
    }

    // MARK: - Nutrition Tab

    private var nutritionTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                if let pet = selectedPet {
                    Text("Nutrition analysis for \(pet.name)")
                        .font(.petTitle3)
                        .foregroundColor(.secondary)
                        .padding()

                    // Placeholder for nutrition content
                    Text("Nutrition recommendations will be displayed here")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                } else {
                    selectPetPrompt
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    // MARK: - Behavior Tab

    private var behaviorTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                if let pet = selectedPet {
                    Text("Behavior analysis for \(pet.name)")
                        .font(.petTitle3)
                        .foregroundColor(.secondary)
                        .padding()

                    // Placeholder for behavior content
                    Text("Behavior insights will be shown here")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                } else {
                    selectPetPrompt
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    // MARK: - Recommendations Tab

    private var recommendationsTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                if let pet = selectedPet {
                    Text("AI recommendations for \(pet.name)")
                        .font(.petTitle3)
                        .foregroundColor(.secondary)
                        .padding()

                    // AI Recommendations
                    if !pet.aiRecommendations.isEmpty {
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Current Recommendations")
                                .font(.petTitle3)
                                .fontWeight(.bold)

                            ForEach(pet.aiRecommendations, id: \.self) { recommendation in
                                HStack {
                                    Image(systemName: "brain.head.profile")
                                        .foregroundColor(.purple)

                                    Text(recommendation)
                                        .font(.petSubheadline)
                                        .foregroundColor(.primary)

                                    Spacer()
                                }
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.purple.opacity(0.1))
                                )
                            }
                        }
                        .padding()
                    } else {
                        Text("No AI recommendations available yet")
                            .font(.petSubheadline)
                            .foregroundColor(.secondary)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.systemGray6))
                            )
                    }
                } else {
                    selectPetPrompt
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }
}
