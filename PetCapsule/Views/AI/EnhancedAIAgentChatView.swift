//
//  EnhancedAIAgentChatView.swift
//  PetTime Capsule
//
//  Created by AI Assistant on 2024-12-23.
//  Copyright © 2024 PetTime Capsule. All rights reserved.
//

import SwiftUI
import AppIntents

/// Enhanced AI agent chat view with tool calling capabilities and contextual conversations
@available(iOS 18.0, *)
struct EnhancedAIAgentChatView: View {
    let agent: AIAgent
    let selectedPet: Pet?
    
    @StateObject private var chatManager = AIAgentChatManager()
    @StateObject private var toolService = AIAgentToolService.shared
    @State private var messageText = ""
    @State private var isLoading = false
    @State private var showingToolSuggestions = false
    @State private var availableTools: [AIToolDescription] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Chat Header
                chatHeaderView
                
                // Messages List
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(chatManager.messages) { message in
                                AIChatMessageView(
                                    message: message,
                                    onToolResultTap: { toolResult in
                                        handleToolResultTap(toolResult)
                                    }
                                )
                                .id(message.id)
                            }
                            
                            if isLoading {
                                LoadingMessageView()
                            }
                        }
                        .padding()
                    }
                    .onChange(of: chatManager.messages.count) { _ in
                        if let lastMessage = chatManager.messages.last {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                proxy.scrollTo(lastMessage.id, anchor: .bottom)
                            }
                        }
                    }
                }
                
                // Tool Suggestions (if available)
                if showingToolSuggestions && !availableTools.isEmpty {
                    toolSuggestionsView
                }
                
                // Input Area
                chatInputView
            }
            .navigationTitle(agent.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("View Available Tools") {
                            showingToolSuggestions.toggle()
                        }
                        
                        Button("Clear Conversation") {
                            chatManager.clearConversation()
                        }
                        
                        if let pet = selectedPet {
                            Button("Switch Pet") {
                                // Handle pet switching
                            }
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
        }
        .onAppear {
            setupChat()
        }
    }
    
    // MARK: - Chat Header
    
    private var chatHeaderView: some View {
        VStack(spacing: 8) {
            HStack {
                // Agent Avatar
                Circle()
                    .fill(LinearGradient(
                        colors: agent.gradientColors.map { Color(hex: $0) },
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(systemName: agent.iconName)
                            .foregroundColor(.white)
                            .font(.system(size: 18, weight: .semibold))
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(agent.name)
                        .font(.headline)
                    
                    Text(agent.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Selected Pet Info
                if let pet = selectedPet {
                    HStack(spacing: 8) {
                        AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                                .overlay(
                                    Image(systemName: "pawprint.fill")
                                        .foregroundColor(.gray)
                                        .font(.caption)
                                )
                        }
                        .frame(width: 30, height: 30)
                        .clipShape(Circle())
                        
                        Text(pet.name)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
            }
            
            // Context Indicator
            if chatManager.hasActiveContext {
                HStack {
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.blue)
                        .font(.caption)
                    
                    Text("AI has access to \(selectedPet?.name ?? "pet")'s data")
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    // MARK: - Tool Suggestions
    
    private var toolSuggestionsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(availableTools.prefix(5), id: \.name) { tool in
                    Button(action: {
                        suggestToolUsage(tool)
                    }) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(tool.name.replacingOccurrences(of: "get", with: "").replacingOccurrences(of: "Pet", with: ""))
                                .font(.caption)
                                .fontWeight(.medium)
                            
                            Text(tool.description)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color(.systemGray5))
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    // MARK: - Chat Input
    
    private var chatInputView: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 12) {
                TextField("Ask about \(selectedPet?.name ?? "your pet")...", text: $messageText, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1...4)
                
                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(messageText.isEmpty ? .gray : .blue)
                }
                .disabled(messageText.isEmpty || isLoading)
            }
            .padding()
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - Setup and Actions
    
    private func setupChat() {
        guard let pet = selectedPet else { return }
        
        let context = AIAgentContext(
            agentType: AIAgentType(rawValue: agent.name.lowercased()) ?? .healthAndEmergency,
            selectedPetId: UUID(uuidString: pet.id) ?? UUID(),
            conversationHistory: [],
            userPermissions: Set(["health", "appointments", "memories", "behavior", "care"])
        )
        
        chatManager.setupContext(context)
        loadAvailableTools()
        
        // Send welcome message
        let welcomeMessage = generateWelcomeMessage(for: pet)
        chatManager.addMessage(AIChatMessage(
            id: UUID(),
            content: welcomeMessage,
            isFromUser: false,
            timestamp: Date(),
            toolResults: nil
        ))
    }
    
    private func loadAvailableTools() {
        guard let pet = selectedPet else { return }
        
        let context = AIAgentContext(
            agentType: AIAgentType(rawValue: agent.name.lowercased()) ?? .healthAndEmergency,
            selectedPetId: UUID(uuidString: pet.id) ?? UUID()
        )
        
        availableTools = toolService.getAvailableTools(for: context)
    }
    
    private func sendMessage() {
        guard !messageText.isEmpty, let pet = selectedPet else { return }
        
        let userMessage = AIChatMessage(
            id: UUID(),
            content: messageText,
            isFromUser: true,
            timestamp: Date()
        )
        
        chatManager.addMessage(userMessage)
        
        let userQuery = messageText
        messageText = ""
        isLoading = true
        
        Task {
            await processUserMessage(userQuery, for: pet)
        }
    }
    
    private func processUserMessage(_ query: String, for pet: Pet) async {
        // Suggest relevant tools based on the query
        let suggestedTools = toolService.suggestTools(for: query, context: chatManager.context)
        
        var toolResults: [AIToolResult] = []
        var responseText = ""
        
        // Execute suggested tools
        for toolName in suggestedTools.prefix(3) { // Limit to 3 tools per query
            do {
                let parameters = ["petId": pet.id]
                let result = try await toolService.executeTool(name: toolName, parameters: parameters)
                toolResults.append(result)
            } catch {
                print("Tool execution failed for \(toolName): \(error)")
            }
        }
        
        // Generate contextual response based on tool results
        responseText = await generateContextualResponse(query: query, toolResults: toolResults, pet: pet)
        
        await MainActor.run {
            let aiMessage = AIChatMessage(
                id: UUID(),
                content: responseText,
                isFromUser: false,
                timestamp: Date(),
                toolResults: toolResults.isEmpty ? nil : toolResults
            )
            
            chatManager.addMessage(aiMessage)
            isLoading = false
        }
    }
    
    private func generateContextualResponse(query: String, toolResults: [AIToolResult], pet: Pet) async -> String {
        // This would integrate with Apple's Foundation Model for natural language generation
        // For now, we'll create a contextual response based on the tool results
        
        var response = "Based on \(pet.name)'s data, "
        
        for result in toolResults {
            if let metadata = result.metadata,
               let summary = metadata["summary"] as? String {
                response += summary + " "
            }
        }
        
        // Add personalized recommendations
        if query.lowercased().contains("health") {
            response += "Would you like me to check for any upcoming appointments or health alerts?"
        } else if query.lowercased().contains("appointment") {
            response += "I can help you schedule new appointments or remind you about existing ones."
        } else if query.lowercased().contains("behavior") {
            response += "I can analyze behavior patterns and suggest training improvements."
        }
        
        return response.isEmpty ? "I'm here to help with any questions about \(pet.name)!" : response
    }
    
    private func generateWelcomeMessage(for pet: Pet) -> String {
        return "Hello! I'm \(agent.name), your AI assistant for \(pet.name). I have access to \(pet.name)'s health records, appointments, behavior notes, and memories. How can I help you today?"
    }
    
    private func suggestToolUsage(_ tool: AIToolDescription) {
        messageText = "Tell me about \(tool.name.replacingOccurrences(of: "get", with: "").replacingOccurrences(of: "Pet", with: "").lowercased())"
    }
    
    private func handleToolResultTap(_ toolResult: AIToolResult) {
        // Handle when user taps on a tool result to get more details
        if let data = toolResult.data {
            // Show detailed view or ask follow-up questions
            messageText = "Tell me more about this data"
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct AIChatMessageView: View {
    let message: AIChatMessage
    let onToolResultTap: (AIToolResult) -> Void
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
            }
            
            VStack(alignment: message.isFromUser ? .trailing : .leading, spacing: 8) {
                Text(message.content)
                    .padding()
                    .background(message.isFromUser ? Color.blue : Color(.systemGray5))
                    .foregroundColor(message.isFromUser ? .white : .primary)
                    .cornerRadius(16)
                
                // Tool Results
                if let toolResults = message.toolResults {
                    ForEach(Array(toolResults.enumerated()), id: \.offset) { index, result in
                        ToolResultView(result: result) {
                            onToolResultTap(result)
                        }
                    }
                }
                
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if !message.isFromUser {
                Spacer()
            }
        }
    }
}

@available(iOS 18.0, *)
struct ToolResultView: View {
    let result: AIToolResult
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: "wrench.and.screwdriver")
                        .foregroundColor(.blue)
                        .font(.caption)
                    
                    Text("Data Retrieved")
                        .font(.caption)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                if let metadata = result.metadata,
                   let summary = metadata["summary"] as? String {
                    Text(summary)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            }
            .padding(8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

@available(iOS 18.0, *)
struct LoadingMessageView: View {
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                HStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(Color.gray)
                            .frame(width: 8, height: 8)
                            .scaleEffect(1.0)
                            .animation(
                                Animation.easeInOut(duration: 0.6)
                                    .repeatForever()
                                    .delay(Double(index) * 0.2),
                                value: true
                            )
                    }
                }
                .padding()
                .background(Color(.systemGray5))
                .cornerRadius(16)
            }
            
            Spacer()
        }
    }
}

// MARK: - Supporting Models

struct AIChatMessage: Identifiable {
    let id: UUID
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let toolResults: [AIToolResult]?

    init(id: UUID = UUID(), content: String, isFromUser: Bool, timestamp: Date = Date(), toolResults: [AIToolResult]? = nil) {
        self.id = id
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = timestamp
        self.toolResults = toolResults
    }
}

@available(iOS 18.0, *)
class AIAgentChatManager: ObservableObject {
    @Published var messages: [AIChatMessage] = []
    @Published var hasActiveContext = false
    
    var context: AIAgentContext = AIAgentContext(agentType: .healthAndEmergency)
    
    func setupContext(_ context: AIAgentContext) {
        self.context = context
        self.hasActiveContext = context.selectedPetId != nil
    }
    
    func addMessage(_ message: AIChatMessage) {
        messages.append(message)
    }
    
    func clearConversation() {
        messages.removeAll()
    }
}

// AIAgentType is defined in AIModels.swift

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
