//
//  EnvironmentalAlertsView.swift
//  PetCapsule
//
//  User interface for managing environmental alerts and viewing active alerts
//

import SwiftUI

// Type aliases removed to avoid redeclaration - using SharedTypes.swift definitions directly

// MARK: - Supporting Components

struct EnvironmentalTabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isSelected ? .blue : .secondary)
                
                Rectangle()
                    .frame(height: 2)
                    .foregroundColor(isSelected ? .blue : .clear)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
    }
}

struct EnvironmentalAlertsView: View {
    @StateObject private var alertsService = EnhancedEnvironmentalAlertsService.shared
    @State private var selectedTab = 0
    @State private var showingNewAlert = false
    @State private var showingAlertSettings = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Navigation
                tabNavigation
                
                // Content
                TabView(selection: $selectedTab) {
                    // Active Alerts Tab
                    activeAlertsTab
                        .tag(0)
                    
                    // Alert Settings Tab
                    alertSettingsTab
                        .tag(1)
                    
                    // Alert History Tab
                    alertHistoryTab
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Safe Adventures")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("New Alert Setting") {
                            showingNewAlert = true
                        }
                        
                        Button("Alert Preferences") {
                            showingAlertSettings = true
                        }
                        
                        if alertsService.isMonitoring {
                            Button("Stop Monitoring") {
                                alertsService.stopEnvironmentalMonitoring()
                            }
                        } else {
                            Button("Start Monitoring") {
                                alertsService.startEnvironmentalMonitoring()
                            }
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .sheet(isPresented: $showingNewAlert) {
                NewAlertSettingView()
            }
            .sheet(isPresented: $showingAlertSettings) {
                AlertPreferencesView()
            }
            .onAppear {
                if !alertsService.isMonitoring {
                    alertsService.startEnvironmentalMonitoring()
                }
            }
        }
    }
    
    // MARK: - Tab Navigation
    
    private var tabNavigation: some View {
        HStack(spacing: 0) {
                            EnvironmentalTabButton(title: "Active", isSelected: selectedTab == 0) {
                    selectedTab = 0
                }
                
                EnvironmentalTabButton(title: "Settings", isSelected: selectedTab == 1) {
                    selectedTab = 1
                }
                
                EnvironmentalTabButton(title: "History", isSelected: selectedTab == 2) {
                    selectedTab = 2
                }
        }
        .background(Color(.systemBackground))
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color(.systemGray4)),
            alignment: .bottom
        )
    }
    
    // MARK: - Active Alerts Tab
    
    private var activeAlertsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Monitoring Status
                monitoringStatusCard
                
                // Active Alerts
                if alertsService.activeAlerts.isEmpty {
                    emptyActiveAlertsView
                } else {
                    ForEach(alertsService.activeAlerts) { alert in
                        ActiveAlertCard(alert: alert)
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Alert Settings Tab
    
    private var alertSettingsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(alertsService.userAlertSettings) { setting in
                    AlertSettingCard(setting: setting)
                }
                
                // Add New Alert Button
                Button(action: { showingNewAlert = true }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                        
                        Text("Add New Alert")
                            .font(.headline)
                            .foregroundColor(.blue)
                        
                        Spacer()
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.blue.opacity(0.3), style: StrokeStyle(lineWidth: 2, dash: [5]))
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding()
        }
    }
    
    // MARK: - Alert History Tab
    
    private var alertHistoryTab: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                if alertsService.alertHistory.isEmpty {
                    emptyHistoryView
                } else {
                    ForEach(alertsService.alertHistory) { historyItem in
                        AlertHistoryCard(historyItem: historyItem)
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Supporting Views
    
    private var monitoringStatusCard: some View {
        HStack(spacing: 16) {
            // Status Indicator
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(alertsService.isMonitoring ? Color.green.opacity(0.2) : Color.gray.opacity(0.2))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: alertsService.isMonitoring ? "checkmark.circle.fill" : "pause.circle.fill")
                        .font(.title2)
                        .foregroundColor(alertsService.isMonitoring ? .green : .gray)
                }
                
                Text(alertsService.isMonitoring ? "Monitoring" : "Paused")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(alertsService.isMonitoring ? .green : .gray)
            }
            
            // Status Details
            VStack(alignment: .leading, spacing: 4) {
                Text("Environmental Monitoring")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                if let lastCheck = alertsService.lastCheckTime {
                    Text("Last check: \(lastCheck.formatted(.relative(presentation: .named)))")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                } else {
                    Text("No checks performed yet")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Text("\(alertsService.userAlertSettings.filter { $0.isEnabled }.count) active alert settings")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var emptyActiveAlertsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.shield.fill")
                .font(.system(size: 48))
                .foregroundColor(.green)
            
            Text("All Clear!")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("No environmental alerts at this time. Your alert settings are monitoring conditions in the background.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 40)
        .frame(maxWidth: .infinity)
    }
    
    private var emptyHistoryView: some View {
        VStack(spacing: 16) {
            Image(systemName: "clock.badge.checkmark")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("No Alert History")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("When environmental alerts are triggered, they'll appear here for your review.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 40)
        .frame(maxWidth: .infinity)
    }
    
    private func getUnit(for alertType: EnvironmentalAlertType) -> String {
        switch alertType {
        case .temperature:
            return "°F"
        case .airQuality:
            return " AQI"
        case .pollen:
            return " count"
        case .weatherCondition:
            return ""
        case .humidity:
            return "%"
        case .windSpeed:
            return " mph"
        }
    }
}

// MARK: - Active Alert Card

struct ActiveAlertCard: View {
    let alert: ActiveEnvironmentalAlert
    @StateObject private var alertsService = EnhancedEnvironmentalAlertsService.shared
    
    private var alertCardBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(alert.severity.color.opacity(0.05))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(alert.severity.color.opacity(0.3), lineWidth: 1)
            )
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Image(systemName: alert.alertType.icon)
                    .font(.title2)
                    .foregroundColor(alert.severity.color)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(alert.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(alert.alertType.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                SeverityBadge(severity: alert.severity)
            }
            
            // Message
            Text(alert.message)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            // Current Value vs Threshold
            if let currentValue = alert.currentValue,
               let threshold = alert.threshold {
                HStack {
                    Text("Current: \(String(format: "%.1f", currentValue))\(getUnit(for: alert.alertType))")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(alert.severity.color)
                    
                    Spacer()
                    
                    Text("Threshold: \(String(format: "%.1f", threshold))\(getUnit(for: alert.alertType))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Time and Actions
            HStack {
                Text(alert.triggeredAt.formatted(.relative(presentation: .named)))
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Button("Acknowledge") {
                    alertsService.acknowledgeAlert(alert.id)
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
        .padding()
        .background(alertCardBackground)
    }
    
    private func getUnit(for alertType: EnvironmentalAlertType) -> String {
        switch alertType {
        case .temperature:
            return "°F"
        case .airQuality:
            return " AQI"
        case .pollen:
            return " count"
        case .weatherCondition:
            return ""
        case .humidity:
            return "%"
        case .windSpeed:
            return " mph"
        }
    }
}

// MARK: - Alert Setting Card

struct AlertSettingCard: View {
    let setting: SharedEnvironmentalAlertSetting
    @StateObject private var alertsService = EnhancedEnvironmentalAlertsService.shared
    @State private var showingEditSheet = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: setting.alertType.icon)
                    .font(.title2)
                    .foregroundColor(setting.isEnabled ? .blue : .gray)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(setting.alertType.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(setting.isEnabled ? .primary : .secondary)
                    
                    Text(thresholdText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: .constant(setting.isEnabled))
                    .disabled(true) // Visual only, edit through sheet
            }
            
            // Notification preferences
            HStack(spacing: 16) {
                if setting.notificationEnabled {
                    NotificationBadge(type: "Push", color: .blue)
                }
                
                // Note: emailNotifications and inAppNotifications don't exist on SharedEnvironmentalAlertSetting
                // Would need to be added to the model or removed from UI
                
                Spacer()
                
                Button("Edit") {
                    showingEditSheet = true
                }
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.blue.opacity(0.1))
                .foregroundColor(.blue)
                .cornerRadius(6)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .sheet(isPresented: $showingEditSheet) {
            EditAlertSettingView(setting: setting)
        }
    }
    
    private var thresholdText: String {
        var parts: [String] = []
        
        if let min = setting.minThreshold {
            parts.append("Min: \(String(format: "%.0f", min))")
        }
        
        if let max = setting.maxThreshold {
            parts.append("Max: \(String(format: "%.0f", max))")
        }
        
        if let conditions = setting.conditionValues, !conditions.isEmpty {
            parts.append("Conditions: \(conditions.joined(separator: ", "))")
        }
        
        return parts.isEmpty ? "No thresholds set" : parts.joined(separator: " • ")
    }
}

// MARK: - Supporting Components

struct SeverityBadge: View {
    let severity: SharedAlertSeverity
    
    var body: some View {
        Text(severity.displayName)
            .font(.caption2)
            .fontWeight(.bold)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(severity.color)
            .foregroundColor(.white)
            .cornerRadius(8)
    }
}

struct NotificationBadge: View {
    let type: String
    let color: Color
    
    var body: some View {
        Text(type)
            .font(.caption2)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(color.opacity(0.2))
            .foregroundColor(color)
            .cornerRadius(4)
    }
}

struct AlertHistoryCard: View {
    let historyItem: AlertHistoryItem
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: historyItem.alertType.icon)
                .font(.title3)
                .foregroundColor(historyItem.severity.color)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(historyItem.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(historyItem.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                Text(historyItem.triggeredAt.formatted(.relative(presentation: .named)))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if historyItem.acknowledged {
                Image(systemName: "checkmark.circle.fill")
                    .font(.caption)
                    .foregroundColor(.green)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Placeholder Views

struct NewAlertSettingView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("New Alert Setting")
                    .font(.headline)
                
                Text("Alert setting creation interface would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("New Alert")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct EditAlertSettingView: View {
    let setting: SharedEnvironmentalAlertSetting
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Edit Alert Setting")
                    .font(.headline)
                
                Text("Alert setting editing interface would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Edit Alert")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct AlertPreferencesView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Alert Preferences")
                    .font(.headline)
                
                Text("Global alert preferences would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Preferences")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    EnvironmentalAlertsView()
}
