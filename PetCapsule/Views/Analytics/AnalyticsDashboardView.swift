//
//  AnalyticsDashboardView.swift
//  PetCapsule
//
//  Analytics dashboard for pet health and behavior insights
//

import SwiftUI

// Temporarily commented out to avoid compilation errors
/*
struct AnalyticsDashboardView: View {
    @StateObject private var analyticsService = PetHealthAnalyticsService.shared
    @StateObject private var healthService = ComprehensiveHealthMonitoringService.shared
    @EnvironmentObject var petService: RealDataService
    @State private var selectedPet: Pet?
    @State private var selectedTimeframe: AnalyticsTimeframe = .month
    @State private var showingDetailedAnalytics = false
    @State private var animateCharts = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Pet Selector
                    if !petService.pets.isEmpty {
                        petSelectorSection
                    }
                    
                    // Overview Cards
                    overviewCardsSection
                    
                    // Health Trends
                    healthTrendsSection
                    
                    // Activity Insights
                    activityInsightsSection
                    
                    // Predictive Analytics
                    predictiveAnalyticsSection
                }
                .padding()
            }
            .navigationTitle("Health Analytics")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                withAnimation(.easeInOut(duration: 1.0)) {
                    animateCharts = true
                }
                
                if selectedPet == nil && !petService.pets.isEmpty {
                    selectedPet = petService.pets.first
                }
            }
        }
    }
    
    // MARK: - Pet Selector Section
    
    private var petSelectorSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Pet")
                .font(.headline)
                .fontWeight(.semibold)
            
            petScrollView
        }
    }
    
    private var petScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(petService.pets, id: \.id) { pet in
                    petSelectionButton(for: pet)
                }
            }
            .padding(.horizontal)
        }
    }
    
    private func petSelectionButton(for pet: Pet) -> some View {
        Button(action: {
            selectedPet = pet
            Task {
                await analyticsService.generateComprehensiveAnalytics(for: pet.id)
            }
        }) {
            petButtonContent(for: pet)
        }
    }
    
    private func petButtonContent(for pet: Pet) -> some View {
        VStack(spacing: 8) {
            petCircleIcon(for: pet)
            
            Text(pet.name)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .lineLimit(1)
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedPet?.id == pet.id ? Color.blue.opacity(0.1) : Color.clear)
        )
    }
    
    private func petCircleIcon(for pet: Pet) -> some View {
        Circle()
            .fill(selectedPet?.id == pet.id ? Color.blue : Color.gray)
            .frame(width: 60, height: 60)
            .overlay(
                Text(pet.name.prefix(1).uppercased())
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            )
    }
    
    // MARK: - Overview Cards Section
    
    private var overviewCardsSection: some View {
        VStack(spacing: 16) {
            // Wellness Score
            WellnessScoreCard(
                score: analyticsService.dashboardMetrics?.overallWellnessScore ?? 0.0,
                color: getWellnessColor(analyticsService.dashboardMetrics?.overallWellnessScore ?? 0.0)
            )
            
            // Quick Stats
            HStack(spacing: 16) {
                QuickStatCard(
                    title: "Insights",
                    value: "\(analyticsService.dashboardMetrics?.insightsCount ?? 0)",
                    icon: "lightbulb.fill",
                    color: .yellow
                )
                
                QuickStatCard(
                    title: "Trends",
                    value: "\(analyticsService.dashboardMetrics?.trendsCount ?? 0)",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .blue
                )
                
                QuickStatCard(
                    title: "Last Updated",
                    value: analyticsService.dashboardMetrics?.lastUpdated.formatted(.relative(presentation: .named)) ?? "N/A",
                    icon: "clock.fill",
                    color: .gray
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Health Trends Section
    
    private var healthTrendsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Health Trends")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Picker("Timeframe", selection: $selectedTimeframe) {
                    ForEach(AnalyticsTimeframe.allCases, id: \.self) { timeframe in
                        Text(timeframe.displayName).tag(timeframe)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 200)
            }
            
            if analyticsService.healthTrends.isEmpty {
                Text("No trends available")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            } else {
                ForEach(Array(analyticsService.healthTrends.prefix(3)), id: \.id) { trend in
                    HealthTrendCard(trend: trend)
                }
            }
        }
    }
    
    // MARK: - Activity Insights Section
    
    private var activityInsightsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Activity Insights")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if !analyticsService.petInsights.isEmpty {
                    Text("\(analyticsService.petInsights.count) total")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            if analyticsService.petInsights.isEmpty {
                EmptyInsightsView()
            } else {
                // High Priority Insights
                let highPriorityInsights = analyticsService.getHighPriorityInsights()
                if !highPriorityInsights.isEmpty {
                    ForEach(highPriorityInsights.prefix(3)) { insight in
                        AnalyticsInsightCard(insight: insight) {}
                    }
                }
                
                // Recent Insights
                ForEach(analyticsService.petInsights.prefix(2)) { insight in
                    AnalyticsInsightCard(insight: insight) {}
                }
            }
        }
    }
    
    // MARK: - Predictive Analytics Section
    
    private var predictiveAnalyticsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Predictive Insights")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(analyticsService.predictiveInsights.prefix(3)) { insight in
                PredictiveInsightCard(insight: insight)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func getWellnessColor(_ score: Double) -> Color {
        switch score {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .blue
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
}
*/

// Simple placeholder view
struct AnalyticsDashboardView: View {
    @EnvironmentObject var petService: RealDataService
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Analytics Dashboard")
                    .font(.title)
                    .padding()
                
                if !petService.pets.isEmpty {
                    Text("Found \(petService.pets.count) pets")
                        .foregroundColor(.green)
                } else {
                    Text("No pets found")
                        .foregroundColor(.red)
                }
                
                Spacer()
            }
            .navigationTitle("Analytics")
        }
    }
}

// MARK: - Supporting Views

struct ScoreBreakdownRow: View {
    let title: String
    let score: Double
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 70, alignment: .leading)
            
            ProgressView(value: score)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
                .frame(width: 60)
            
            Text("\(Int(score * 100))%")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)
                .frame(width: 30, alignment: .trailing)
        }
    }
}

struct QuickStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
        )
    }
}

struct AnalyticsInsightCard: View {
    let insight: PetInsight
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: insight.priority.icon)
                    .font(.title2)
                    .foregroundColor(insight.priority.color)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(insight.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Text(insight.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Image(systemName: insight.category.icon)
                        .font(.caption)
                        .foregroundColor(insight.category.color)
                    
                    Text(insight.category.displayName)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(insight.priority.color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(insight.priority.color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AnalyticsCategoryCard: View {
    let category: InsightCategory
    let insightCount: Int
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: category.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : category.color)
                
                Text(category.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
                
                Text("\(insightCount) insights")
                    .font(.caption)
                    .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? category.color : category.color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct HealthTrendCard: View {
    let trend: AnalyticsHealthTrend
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: trend.metric.icon)
                .font(.title2)
                .foregroundColor(trend.metric.color)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(trend.metric.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(trend.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                HStack(spacing: 4) {
                    Image(systemName: trend.trend.icon)
                        .font(.caption)
                        .foregroundColor(trend.trend.color)
                    
                    Text("\(Int(abs(trend.changePercentage * 100)))%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(trend.trend.color)
                }
                
                Text(trend.timeframe.capitalized)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct PredictiveInsightCard: View {
    let insight: PredictiveInsight
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: insight.type.icon)
                .font(.title2)
                .foregroundColor(insight.type.color)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(insight.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(insight.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("\(Int(insight.confidence * 100))%")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(insight.type.color)
                
                Text("confidence")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(insight.type.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(insight.type.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct EmptyInsightsView: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "lightbulb")
                .font(.system(size: 32))
                .foregroundColor(.gray)
            
            Text("No Insights Available")
                .font(.subheadline)
                .fontWeight(.medium)
            
            Text("Analytics are being generated. Check back soon for personalized insights about your pet.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 20)
    }
}

enum AnalyticsTimeframe: String, CaseIterable {
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    
    var displayName: String {
        switch self {
        case .week: return "Week"
        case .month: return "Month"
        case .quarter: return "Quarter"
        }
    }
}

// MARK: - Placeholder Detail Views

struct InsightDetailsView: View {
    let insight: PetInsight
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Insight Details")
                    .font(.headline)
                
                Text("Detailed insight information would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle(insight.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

@available(iOS 18.0, *)
#Preview {
    AnalyticsDashboardView()
}
