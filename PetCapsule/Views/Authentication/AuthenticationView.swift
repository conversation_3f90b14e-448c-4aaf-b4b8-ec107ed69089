//
//  AuthenticationView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import AuthenticationServices

struct AuthenticationView: View {
    @EnvironmentObject var authService: AuthenticationService
    @StateObject private var biometricService = BiometricAuthenticationService()

    @State private var showPrivacyPolicy = false
    @State private var showTermsOfService = false

    var body: some View {
        NavigationView {
            ZStack {
                // Modern Magic UI Background with animated gradient
                MagicGradientBackground()
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 40) {
                        // Modern Logo Section with Magic UI
                        modernLogoSection

                        // Modern Authentication Section with Magic UI - Apple ID Only
                        modernAuthenticationSection

                        // Modern Privacy Section
                        modernPrivacySection

                        // Development Skip Option
                        if Config.Features.skipAuthentication {
                            modernDevelopmentSkipSection
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 40)
                }
            }
            .navigationBarHidden(true)
            .alert("Authentication Error", isPresented: .constant(authService.errorMessage != nil)) {
                Button("OK") {
                    authService.errorMessage = nil
                }
            } message: {
                Text(authService.errorMessage ?? "")
            }
            .sheet(isPresented: $showPrivacyPolicy) {
                SimplePrivacyPolicyView()
            }
            .sheet(isPresented: $showTermsOfService) {
                SimpleTermsOfServiceView()
            }
        }
    }

    // MARK: - Modern Magic UI Logo Section

    private var modernLogoSection: some View {
        VStack(spacing: 24) {
            // Modern App Icon with Magic UI Effects
            ZStack {
                // Outer glow effect
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.purple.opacity(0.3),
                                Color.blue.opacity(0.2),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 50,
                            endRadius: 120
                        )
                    )
                    .frame(width: 140, height: 140)
                    .blur(radius: 10)

                // Main icon container with glassmorphism
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.25),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .overlay(
                        Circle()
                            .stroke(
                                LinearGradient(
                                    colors: [Color.white.opacity(0.6), Color.white.opacity(0.2)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)

                // Paw print icon with gradient
                Image(systemName: "pawprint.fill")
                    .font(.system(size: 48, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.purple, Color.blue, Color.cyan],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }

            // Modern title with enhanced typography
            VStack(spacing: 12) {
                Text("PetTime Capsule")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.primary, Color.primary.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                Text("Preserve precious moments with your beloved pets using AI-powered memory curation")
                    .font(.system(size: 16, weight: .medium, design: .rounded))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.top, 20)
    }

    // MARK: - Modern Biometric Section

    private var modernBiometricSection: some View {
        MagicCard {
            VStack(spacing: 20) {
                // Biometric icon with animation
                ZStack {
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [Color.blue.opacity(0.3), Color.clear],
                                center: .center,
                                startRadius: 20,
                                endRadius: 60
                            )
                        )
                        .frame(width: 80, height: 80)

                    Image(systemName: biometricService.biometricType.icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [Color.blue, Color.purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                }

                VStack(spacing: 8) {
                    Text("Secure Authentication")
                        .font(.system(size: 20, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Text("Use \(biometricService.biometricType.displayName) to securely access your pet memories")
                        .font(.system(size: 14, weight: .medium, design: .rounded))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                MagicButton(
                    title: "Authenticate with \(biometricService.biometricType.displayName)",
                    icon: biometricService.biometricType.icon,
                    style: .secondary,
                    isLoading: authService.isLoading,
                    action: authenticateWithBiometrics
                )

                Button("Use different account") {
                    authService.authenticationState = .unauthenticated
                }
                .font(.system(size: 14, weight: .medium, design: .rounded))
                .foregroundColor(.purple)
            }
        }
    }

    // MARK: - Biometric Authentication Section (Legacy)

    private var biometricAuthenticationSection: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Image(systemName: biometricService.biometricType.icon)
                    .font(.system(size: 60))
                    .foregroundColor(.blue)

                Text("Welcome Back!")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Use \(biometricService.biometricType.displayName) to securely access your pet data")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }

            VStack(spacing: 16) {
                // Biometric Authentication Button
                Button(action: authenticateWithBiometrics) {
                    HStack {
                        if authService.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        } else {
                            Image(systemName: biometricService.biometricType.icon)
                                .font(.title3)
                        }
                        Text("Authenticate with \(biometricService.biometricType.displayName)")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                    .shadow(color: .blue.opacity(0.3), radius: 5, x: 0, y: 2)
                }
                .disabled(authService.isLoading)

                // Alternative Sign In Button
                Button("Sign in with different account") {
                    authService.authenticationState = .unauthenticated
                }
                .foregroundColor(.blue)
                .font(.body)
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Modern Authentication Section

    private var modernAuthenticationSection: some View {
        VStack(spacing: 32) {
            // Welcome text with better font visibility
            VStack(spacing: 12) {
                Text("Welcome to PetTime Capsule")
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.white, .white.opacity(0.9)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .multilineTextAlignment(.center)
                
                Text("Capture and preserve your pet's precious moments with Apple's secure authentication")
                    .font(.system(size: 16, weight: .medium, design: .rounded))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            
            // Apple Sign In - The only authentication method
            VStack(spacing: 16) {
                Button(action: {
                    Task {
                        do {
                            try await authService.signInWithApple()
                        } catch {
                            print("Apple Sign-In error: \(error)")
                        }
                    }
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "applelogo")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                        
                        Text("Continue with Apple")
                            .font(.system(size: 18, weight: .semibold, design: .rounded))
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 56)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.8)],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
                    )
                }
                .disabled(authService.isLoading)
                .opacity(authService.isLoading ? 0.7 : 1.0)
                
                if authService.isLoading {
                    HStack(spacing: 8) {
                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)
                        
                        Text("Signing you in securely...")
                            .font(.system(size: 14, weight: .medium, design: .rounded))
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .padding(.top, 8)
                }
            }
        }
        .padding(.horizontal, 24)
    }

    // MARK: - Legacy Authentication Section Removed
    // Apple-only authentication is now handled in modernAuthenticationSection

    // MARK: - Modern Privacy Section

    private var modernPrivacySection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 20) {
                Button("Privacy Policy") {
                    showPrivacyPolicy = true
                }
                .font(.system(size: 14, weight: .medium, design: .rounded))
                .foregroundColor(.white.opacity(0.8))

                Text("•")
                    .foregroundColor(.white.opacity(0.6))

                Button("Terms of Service") {
                    showTermsOfService = true
                }
                .font(.system(size: 14, weight: .medium, design: .rounded))
                .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 20)
    }

    private var modernDevelopmentSkipSection: some View {
        MagicButton(
            title: "Skip for Development",
            icon: "hammer.fill",
            style: .tertiary,
            action: skipAuthentication
        )
        .padding(.horizontal, 20)
    }

    // MARK: - Privacy Section (Legacy)

    private var privacySection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 16) {
                Button("Privacy Policy") {
                    showPrivacyPolicy = true
                }
                .font(.caption)
                .foregroundColor(.blue)

                Text("•")
                    .foregroundColor(.secondary)

                Button("Terms of Service") {
                    showTermsOfService = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            Text("By continuing, you agree to our Terms of Service and Privacy Policy")
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
    }

    // MARK: - Development Skip Section

    private var developmentSkipSection: some View {
        VStack(spacing: 8) {
            Divider()
                .padding(.horizontal, 40)

            Button("Skip Authentication (Development Mode)") {
                // Skip authentication for development
                skipAuthentication()
            }
            .font(.caption)
            .foregroundColor(.orange)
            .padding(.bottom, 20)
        }
    }

    // MARK: - Computed Properties

    // Removed isFormValid - no longer needed for Apple-only authentication

    // MARK: - Actions

    // MARK: - Authentication Methods
    private func authenticateWithBiometrics() {
        Task {
            do {
                try await authService.signInWithApple()
            } catch {
                print("Apple Sign-In error: \(error)")
            }
        }
    }

    private func performEmailAuthentication() {
        Task {
            do {
                // Apple-native migration: Use Apple ID sign-in instead of email/password
                try await authService.signInWithApple()
            } catch {
                print("Apple Sign-In error: \(error)")
            }
        }
    }

    private func skipAuthentication() {
        print("🔧 Skip authentication button tapped")

        // In production, this should be removed or disabled
        #if DEBUG
        // Enable development mode with unique user
        Task { @MainActor in
            print("🔧 Development mode: Using Apple ID sign-in instead of mock user")
            do {
                try await authService.signInWithApple()
                print("🔧 Apple ID authentication initiated")
            } catch {
                print("🔧 Apple ID authentication error: \(error)")
            }
        }
        #else
        print("⚠️ Skip authentication is disabled in production builds")
        #endif
    }
}

// MARK: - Custom Text Field Components

struct CustomTextField: View {
    let title: String
    @Binding var text: String
    let icon: String
    var keyboardType: UIKeyboardType = .default

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .frame(width: 20)

            TextField(title, text: $text)
                .keyboardType(keyboardType)
                .autocapitalization(keyboardType == .emailAddress ? .none : .words)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct CustomSecureField: View {
    let title: String
    @Binding var text: String
    let icon: String
    @State private var isSecure = true

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .frame(width: 20)

            if isSecure {
                SecureField(title, text: $text)
            } else {
                TextField(title, text: $text)
            }

            Button(action: { isSecure.toggle() }) {
                Image(systemName: isSecure ? "eye.slash" : "eye")
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Supporting Views

struct ForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authService: AuthenticationService
    @State private var email = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("Reset Password")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Enter your email address and we'll send you a link to reset your password.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                CustomTextField(
                    title: "Email",
                    text: $email,
                    icon: "envelope.fill",
                    keyboardType: .emailAddress
                )

                Button("Send Reset Link") {
                    Task {
                        do {
                            try await authService.forgotPassword(email: email)
                            dismiss()
                        } catch {
                            print("Password reset error: \(error)")
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(email.contains("@") ? Color.blue : Color.gray.opacity(0.3))
                .foregroundColor(.white)
                .cornerRadius(12)
                .disabled(!email.contains("@"))

                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SimplePrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Privacy Policy")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("Your privacy is important to us. This privacy policy explains how PetTime Capsule collects, uses, and protects your information.")
                        .font(.body)

                    // Add more privacy policy content here

                    Spacer()
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SimpleTermsOfServiceView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Terms of Service")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("By using PetTime Capsule, you agree to these terms and conditions.")
                        .font(.body)

                    // Add more terms of service content here

                    Spacer()
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Social Authentication Button

struct SocialAuthButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(color)
                
                Text(title)
                    .font(.system(size: 16, weight: .semibold, design: .rounded))
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                ZStack {
                    // Glassmorphism background
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.15),
                                    Color.white.opacity(0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    // Border with brand color
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                    
                    // Subtle glow effect
                    RoundedRectangle(cornerRadius: 16)
                        .fill(color.opacity(0.05))
                }
            )
            .shadow(color: color.opacity(0.2), radius: 10, x: 0, y: 5)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    AuthenticationView()
}

