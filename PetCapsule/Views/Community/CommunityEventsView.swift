//
//  CommunityEventsView.swift
//  PetCapsule
//
//  Community events interface with creation, discovery, and RSVP management
//

import SwiftUI
import MapKit

struct CommunityEventsView: View {
    @StateObject private var eventsService = CommunityEventsService.shared
    @State private var selectedTab = 0
    @State private var showingCreateEvent = false
    @State private var showingEventDetails = false
    @State private var selectedEvent: CommunityEvent?
    @State private var searchText = ""
    @State private var selectedEventType: CommunityEventType?
    @State private var showingFilters = false
    
    var body: some View {
        // Clean build-time check - no runtime complexity
        if BuildTargets.hasCommunityEvents {
            communityEventsContent
        } else {
            featureNotIncludedView
        }
    }
    
    // MARK: - Main Content
    
    private var communityEventsContent: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search and Filter Bar
                searchAndFilterBar
                
                // Tab Navigation
                tabNavigation
                
                // Content
                TabView(selection: $selectedTab) {
                    // Discover Tab
                    discoverTab
                        .tag(0)
                    
                    // My Events Tab
                    myEventsTab
                        .tag(1)
                    
                    // RSVP'd Tab
                    rsvpedTab
                        .tag(2)
                    
                    // Map Tab
                    mapTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Pet Parent Meetups")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingCreateEvent = true }) {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingCreateEvent) {
                CreateEventView()
            }
            .sheet(isPresented: $showingEventDetails) {
                if let event = selectedEvent {
                    EventDetailsView(event: event)
                }
            }
            .sheet(isPresented: $showingFilters) {
                EventFiltersView(
                    selectedEventType: $selectedEventType
                )
            }
            .onAppear {
                Task {
                    await eventsService.loadNearbyEvents()
                }
            }
        }
    }
    
    // MARK: - Feature Not Included View
    
    private var featureNotIncludedView: some View {
        VStack(spacing: 20) {
            Image(systemName: "building.2")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("Community Events")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("This feature is not included in this build")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Text("Available in: \(BuildTargets.availableFeatures.joined(separator: ", "))")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .navigationTitle("Community Events")
    }
    
    // MARK: - Search and Filter Bar
    
    private var searchAndFilterBar: some View {
        HStack(spacing: 12) {
            // Search Field
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search events...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            
            // Filter Button
            Button(action: { showingFilters = true }) {
                Image(systemName: "line.3.horizontal.decrease.circle")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Tab Navigation
    
    private var tabNavigation: some View {
        HStack(spacing: 0) {
            Button("Discover") {
                selectedTab = 0
            }
            .foregroundColor(selectedTab == 0 ? .blue : .primary)
            .frame(maxWidth: .infinity)
            
            Button("My Events") {
                selectedTab = 1
            }
            .foregroundColor(selectedTab == 1 ? .blue : .primary)
            .frame(maxWidth: .infinity)
            
            Button("RSVP'd") {
                selectedTab = 2
            }
            .foregroundColor(selectedTab == 2 ? .blue : .primary)
            .frame(maxWidth: .infinity)
            
            Button("Map") {
                selectedTab = 3
            }
            .foregroundColor(selectedTab == 3 ? .blue : .primary)
            .frame(maxWidth: .infinity)
        }
        .background(Color(.systemBackground))
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color(.systemGray4)),
            alignment: .bottom
        )
    }
    
    // MARK: - Discover Tab
    
    private var discoverTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Featured Events
                if !featuredEvents.isEmpty {
                    featuredEventsSection
                }
                
                // Upcoming Events
                upcomingEventsSection
                
                // Nearby Events
                nearbyEventsSection
            }
            .padding()
        }
        .refreshable {
            await eventsService.loadNearbyEvents()
        }
    }
    
    // MARK: - My Events Tab
    
    private var myEventsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if eventsService.myEvents.isEmpty {
                    emptyMyEventsView
                } else {
                    ForEach(eventsService.myEvents) { event in
                        EventCard(event: event, showManageButton: true) {
                            selectedEvent = event
                            showingEventDetails = true
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - RSVP'd Tab
    
    private var rsvpedTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if eventsService.rsvpedEvents.isEmpty {
                    emptyRSVPedEventsView
                } else {
                    ForEach(eventsService.rsvpedEvents) { event in
                        EventCard(event: event, showRSVPStatus: true) {
                            selectedEvent = event
                            showingEventDetails = true
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Map Tab
    
    private var mapTab: some View {
        EventsMapView(events: filteredEvents) { event in
            selectedEvent = event
            showingEventDetails = true
        }
    }
    
    // MARK: - Featured Events Section
    
    private var featuredEventsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Featured Events")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(featuredEvents) { event in
                        FeaturedEventCard(event: event) {
                            selectedEvent = event
                            showingEventDetails = true
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Upcoming Events Section
    
    private var upcomingEventsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Upcoming Events")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(upcomingEvents.prefix(5)) { event in
                EventCard(event: event) {
                    selectedEvent = event
                    showingEventDetails = true
                }
            }
            
            if upcomingEvents.count > 5 {
                Button("View All Upcoming Events") {
                    // Navigate to full list
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
    }
    
    // MARK: - Nearby Events Section
    
    private var nearbyEventsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Nearby Events")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(eventsService.nearbyEvents.prefix(3)) { event in
                EventCard(event: event, showDistance: true) {
                    selectedEvent = event
                    showingEventDetails = true
                }
            }
            
            if eventsService.nearbyEvents.count > 3 {
                Button("View All Nearby Events") {
                    // Navigate to full list
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
    }
    
    // MARK: - Empty States
    
    private var emptyMyEventsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "calendar.badge.plus")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("No Events Created")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Create your first community event to bring pet owners together!")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Create Event") {
                showingCreateEvent = true
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .padding(.vertical, 40)
    }
    
    private var emptyRSVPedEventsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "calendar.badge.checkmark")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("No RSVP'd Events")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Browse and RSVP to community events to see them here.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Discover Events") {
                selectedTab = 0
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .padding(.vertical, 40)
    }
    
    // MARK: - Computed Properties
    
    private var featuredEvents: [CommunityEvent] {
        Array(eventsService.events.filter { event in
            event.date > Date()
        }.prefix(3)) // Take first 3 as featured
    }
    
    private var upcomingEvents: [CommunityEvent] {
        eventsService.getUpcomingEvents(limit: 20)
    }
    
    private var filteredEvents: [CommunityEvent] {
        var events = eventsService.events
        
        // Apply search filter
        if !searchText.isEmpty {
            events = events.filter { event in
                event.title.localizedCaseInsensitiveContains(searchText) ||
                (event.description?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                event.location.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Apply event type filter - removed since category doesn't exist in the model
        // if let eventType = selectedEventType {
        //     events = events.filter { $0.category == eventType.rawValue }
        // }
        
        return events
    }
}

// MARK: - Event Card

struct EventCard: View {
    let event: CommunityEvent
    var showDistance: Bool = false
    var showRSVPStatus: Bool = false
    var showManageButton: Bool = false
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(event.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                        
                        HStack(spacing: 8) {
                            EventTypeBadge(type: .meetup)
                            
                            if showDistance {
                                DistanceBadge(distance: 2.5) // Placeholder distance
                            }
                            
                            if showRSVPStatus {
                                RSVPStatusBadge(status: .attending) // Placeholder status
                            }
                        }
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text(event.date.formatted(.dateTime.weekday(.wide).month(.abbreviated).day()))
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text(event.date.formatted(.dateTime.hour().minute()))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Location and Participants
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: "location")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(event.location)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Image(systemName: "person.2")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(event.currentParticipants)\(event.maxAttendees.map { "/\($0)" } ?? "")")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Description
                if let description = event.description, !description.isEmpty {
                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                // Environmental Quality Check
                if event.minAirQualityScore < 70 {
                    EnvironmentalWarningBadge(score: Double(event.minAirQualityScore) / 100.0)
                }
                
                // Action Buttons
                if showManageButton {
                    HStack {
                        Button("Manage") {
                            // Manage event action
                        }
                        .font(.caption)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                        
                        Spacer()
                        
                        Text(event.status.capitalized)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.2))
                            .foregroundColor(.blue)
                            .cornerRadius(6)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Featured Event Card

struct FeaturedEventCard: View {
    let event: CommunityEvent
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Featured Badge
                HStack {
                    Text("FEATURED")
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.orange)
                        .cornerRadius(6)
                    
                    Spacer()
                    
                    EventTypeBadge(type: .meetup)
                }
                
                // Title and Date
                VStack(alignment: .leading, spacing: 4) {
                    Text(event.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Text(event.date.formatted(.dateTime.weekday(.wide).month(.wide).day().hour().minute()))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // Location
                HStack(spacing: 4) {
                    Image(systemName: "location")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(event.location)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                // Participants
                HStack(spacing: 4) {
                    Image(systemName: "person.2")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(event.attendeeIDs.count) going")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .frame(width: 200)
            .background(
                LinearGradient(
                    colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Views

struct EventTypeBadge: View {
    let type: CommunityEventType
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: type.icon)
                .font(.caption2)
            
            Text(type.displayName)
                .font(.caption2)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(type.color.opacity(0.2))
        .foregroundColor(type.color)
        .cornerRadius(6)
    }
}

struct DistanceBadge: View {
    let distance: Double
    
    var body: some View {
        Text("\(String(format: "%.1f", distance)) mi")
            .font(.caption2)
            .padding(.horizontal, 6)
            .padding(.vertical, 3)
            .background(Color.gray.opacity(0.2))
            .foregroundColor(.gray)
            .cornerRadius(6)
    }
}

struct RSVPStatusBadge: View {
    let status: RSVPStatus
    
    var body: some View {
        HStack(spacing: 2) {
            Image(systemName: status.icon)
                .font(.caption2)
            
            Text(status.displayName)
                .font(.caption2)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(status.color.opacity(0.2))
        .foregroundColor(status.color)
        .cornerRadius(6)
    }
}

struct EnvironmentalWarningBadge: View {
    let score: Double
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.caption2)
                .foregroundColor(.orange)
            
            Text("Check weather conditions")
                .font(.caption2)
                .foregroundColor(.orange)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - Placeholder Views

struct EventsMapView: View {
    let events: [CommunityEvent]
    let onEventTap: (CommunityEvent) -> Void
    
    var body: some View {
        VStack {
            Text("Events Map")
                .font(.headline)
            
            Text("Interactive map with event markers would be displayed here")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
}

struct CreateEventView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Create Event")
                    .font(.headline)
                
                Text("Event creation interface would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("New Event")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct EventDetailsView: View {
    let event: CommunityEvent
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Event Details")
                    .font(.headline)
                
                Text("Detailed event view would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle(event.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct EventFiltersView: View {
    @Binding var selectedEventType: CommunityEventType?
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Event Filters")
                    .font(.headline)
                
                Text("Filter options would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    CommunityEventsView()
}
