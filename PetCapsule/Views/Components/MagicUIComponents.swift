//
//  MagicUIComponents.swift
//  PetCapsule
//
//  Modern Magic UI components inspired by 21st dev MCP
//

import SwiftUI

// MARK: - Magic Gradient Background

struct MagicGradientBackground: View {
    @State private var animateGradient = false
    
    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.2),
                    Color(red: 0.2, green: 0.1, blue: 0.3),
                    Color(red: 0.1, green: 0.2, blue: 0.4)
                ],
                startPoint: animateGradient ? .topLeading : .bottomTrailing,
                endPoint: animateGradient ? .bottomTrailing : .topLeading
            )
            .animation(.easeInOut(duration: 8).repeatForever(autoreverses: true), value: animateGradient)
            
            // Overlay gradient for depth
            RadialGradient(
                colors: [
                    Color.purple.opacity(0.3),
                    Color.blue.opacity(0.2),
                    Color.clear
                ],
                center: .center,
                startRadius: 100,
                endRadius: 400
            )
            .blur(radius: 20)
            
            // Animated particles with safe positioning
            GeometryReader { geometry in
                ForEach(0..<20, id: \.self) { index in
                    Circle()
                        .fill(Color.white.opacity(0.1))
                        .frame(width: 4)
                        .position(
                            x: safePosition(
                                value: geometry.size.width * CGFloat(index % 5) / 5.0,
                                fallback: 50.0
                            ),
                            y: safePosition(
                                value: geometry.size.height * CGFloat(index / 5) / 4.0,
                                fallback: 50.0
                            )
                        )
                        .animation(
                            .linear(duration: 15)
                            .repeatForever(autoreverses: false),
                            value: animateGradient
                        )
                }
            }
        }
        .onAppear {
            animateGradient = true
        }
    }

    // Helper function to ensure safe positioning values
    private func safePosition(value: CGFloat, fallback: CGFloat) -> CGFloat {
        if value.isNaN || value.isInfinite || value < 0 {
            return fallback
        }
        return value
    }
}

// MARK: - Magic Button

struct MagicButton: View {
    let title: String
    let icon: String?
    let style: MagicButtonStyle
    let isLoading: Bool
    let isEnabled: Bool
    let action: () -> Void
    
    init(
        title: String,
        icon: String? = nil,
        style: MagicButtonStyle = .primary,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(style.foregroundColor)
                } else if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .semibold))
                }
                
                Text(title)
                    .font(.system(size: 16, weight: .semibold, design: .rounded))
            }
            .foregroundColor(style.foregroundColor)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                ZStack {
                    // Glassmorphism background
                    RoundedRectangle(cornerRadius: 16)
                        .fill(style.backgroundColor)
                    
                    // Border
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(style.borderColor, lineWidth: 1)
                    
                    // Shine effect
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.2),
                                    Color.clear,
                                    Color.clear,
                                    Color.white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                }
            )
            .shadow(
                color: style.shadowColor,
                radius: isEnabled ? 20 : 5,
                x: 0,
                y: isEnabled ? 10 : 2
            )
            .scaleEffect(isEnabled ? 1.0 : 0.95)
            .opacity(isEnabled ? 1.0 : 0.6)
        }
        .disabled(!isEnabled || isLoading)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isEnabled)
    }
}

enum MagicButtonStyle {
    case primary
    case secondary
    case tertiary
    
    var backgroundColor: LinearGradient {
        switch self {
        case .primary:
            return LinearGradient(
                colors: [
                    Color.white.opacity(0.25),
                    Color.white.opacity(0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .secondary:
            return LinearGradient(
                colors: [
                    Color.purple.opacity(0.8),
                    Color.blue.opacity(0.6)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .tertiary:
            return LinearGradient(
                colors: [
                    Color.clear,
                    Color.clear
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    var foregroundColor: Color {
        switch self {
        case .primary:
            return .primary
        case .secondary:
            return .white
        case .tertiary:
            return .purple
        }
    }
    
    var borderColor: Color {
        switch self {
        case .primary:
            return Color.white.opacity(0.3)
        case .secondary:
            return Color.white.opacity(0.2)
        case .tertiary:
            return Color.purple.opacity(0.3)
        }
    }
    
    var shadowColor: Color {
        switch self {
        case .primary:
            return Color.black.opacity(0.1)
        case .secondary:
            return Color.purple.opacity(0.3)
        case .tertiary:
            return Color.clear
        }
    }
}

// MARK: - Magic Text Field

struct MagicTextField: View {
    let placeholder: String
    @Binding var text: String
    let icon: String?
    let keyboardType: UIKeyboardType
    let isSecure: Bool
    
    @State private var isFocused = false
    
    init(
        placeholder: String,
        text: Binding<String>,
        icon: String? = nil,
        keyboardType: UIKeyboardType = .default,
        isSecure: Bool = false
    ) {
        self.placeholder = placeholder
        self._text = text
        self.icon = icon
        self.keyboardType = keyboardType
        self.isSecure = isSecure
    }
    
    var body: some View {
        HStack(spacing: 16) {
            if let icon = icon {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isFocused ? .purple : .secondary)
                    .frame(width: 20)
            }
            
            if isSecure {
                SecureField(placeholder, text: $text)
                    .font(.system(size: 16, weight: .medium, design: .rounded))
                    .keyboardType(keyboardType)
            } else {
                TextField(placeholder, text: $text)
                    .font(.system(size: 16, weight: .medium, design: .rounded))
                    .keyboardType(keyboardType)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            ZStack {
                // Glassmorphism background
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.15),
                                Color.white.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                // Border
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        isFocused ? Color.purple.opacity(0.6) : Color.white.opacity(0.2),
                        lineWidth: isFocused ? 2 : 1
                    )
            }
        )
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isFocused)
    }
}

// MARK: - Magic Card

struct MagicCard<Content: View>: View {
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        content
            .padding(24)
            .background(
                ZStack {
                    // Glassmorphism background
                    RoundedRectangle(cornerRadius: 20)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.25),
                                    Color.white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    // Border
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [Color.white.opacity(0.6), Color.white.opacity(0.2)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                }
            )
            .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
    }
}
