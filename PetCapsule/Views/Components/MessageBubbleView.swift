import SwiftUI

struct MessageBubbleView: View {
    let message: ChatMessage
    let isFromUser: Bool
    let agent: AIAgent
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        HStack {
            if isFromUser {
                Spacer(minLength: 50)
            }
            
            VStack(alignment: isFromUser ? .trailing : .leading, spacing: 4) {
                if !isFromUser {
                    HStack(spacing: 8) {
                        Circle()
                            .fill(LinearGradient(
                                colors: [Color.blue, Color.purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 24, height: 24)
                            .overlay(
                                Image(systemName: agent.iconName)
                                    .font(.caption)
                                    .foregroundColor(.white)
                            )
                        
                        Text(agent.name)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                    }
                }
                
                Text(message.content)
                    .font(.body)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 20, style: .continuous)
                            .fill(isFromUser ? Color.blue : (colorScheme == .dark ? Color(.systemGray5) : Color(.systemGray6)))
                    )
                    .foregroundColor(isFromUser ? .white : .primary)
                
                Text(formatTime(message.timestamp))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if !isFromUser {
                Spacer(minLength: 50)
            }
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
} 