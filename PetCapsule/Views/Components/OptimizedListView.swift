//
//  OptimizedListView.swift
//  PetCapsule
//
//  High-performance list view with view recycling and lazy loading
//
import SwiftUI
struct OptimizedListView<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let content: (Item) -> Content
    let onAppear: ((Item) -> Void)?
    let onDisappear: ((Item) -> Void)?
    @State private var visibleItems: Set<Item.ID> = []
    @StateObject private var performanceMonitor = PerformanceMonitoringService.shared
    // Performance optimization settings
    private let batchSize = 20
    private let preloadBuffer = 5
    init(
        items: [Item],
        onAppear: ((Item) -> Void)? = nil,
        onDisappear: ((Item) -> Void)? = nil,
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = items
        self.content = content
        self.onAppear = onAppear
        self.onDisappear = onDisappear
    }
    var body: some View {
        ScrollView {
            LazyVStack(spacing: optimizedSpacing) {
                ForEach(items) { item in
                    OptimizedListCell(
                        item: item,
                        content: content,
                        onAppear: { handleItemAppear(item) },
                        onDisappear: { handleItemDisappear(item) }
                    )
                    .id(item.id)
                }
            }
            .padding(.horizontal, 16)
        }
        .scrollIndicators(.hidden)
        .onAppear {
            optimizeForPerformance()
        }
    }
    // MARK: - Performance Optimizations
    private var optimizedSpacing: CGFloat {
        // Reduce spacing on lower performance devices
        performanceMonitor.currentFPS > 55 ? 12 : 8
    }
    private func optimizeForPerformance() {
        // Preload first batch of items
        let initialBatch = Array(items.prefix(batchSize))
        for item in initialBatch {
            visibleItems
        }
    }
    private func handleItemAppear(_ item: Item) {
        visibleItems
        onAppear?(item)
        // Preload nearby items for smooth scrolling
        preloadNearbyItems(for: item)
    }
    private func handleItemDisappear(_ item: Item) {
        visibleItems.remove(item.id)
        onDisappear?(item)
        // Clean up resources for items that are far from view
        cleanupDistantItems(from: item)
    }
    private func preloadNearbyItems(for item: Item) {
        guard let currentIndex = items.firstIndex(where: { $0.id == item.id }) else { return }
        let startIndex = max(0, currentIndex - preloadBuffer)
        let endIndex = min(items.count - 1, currentIndex + preloadBuffer)
        for index in startIndex...endIndex {
            let nearbyItem = items[index]
            if !visibleItems.contains(nearbyItem.id) {
                visibleItems
            }
        }
    }
    private func cleanupDistantItems(from item: Item) {
        guard let currentIndex = items.firstIndex(where: { $0.id == item.id }) else { return }
        // Remove items that are more than 2 screens away
        let cleanupDistance = batchSize * 2
        for (index, distantItem) in items.enumerated() {
            if abs(index - currentIndex) > cleanupDistance {
                visibleItems.remove(distantItem.id)
            }
        }
    }
}
// MARK: - Optimized List Cell
struct OptimizedListCell<Item: Identifiable, Content: View>: View {
    let item: Item
    let content: (Item) -> Content
    let onAppear: () -> Void
    let onDisappear: () -> Void
    @State private var isVisible = false
    @StateObject private var performanceMonitor = PerformanceMonitoringService.shared
    var body: some View {
        Group {
            if isVisible || performanceMonitor.currentFPS > 55 {
                content(item)
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            } else {
                // Placeholder for better performance
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
                    .frame(height: 80)
                    .redacted(reason: .placeholder)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.3)) {
                isVisible = true
            }
            onAppear()
        }
        .onDisappear {
            isVisible = false
            onDisappear()
        }
    }
}
// MARK: - Memory-Optimized Grid View
struct OptimizedGridView<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let columns: [GridItem]
    let content: (Item) -> Content
    @StateObject private var performanceMonitor = PerformanceMonitoringService.shared
    init(
        items: [Item],
        columns: [GridItem],
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = items
        self.columns = columns
        self.content = content
    }
    var body: some View {
        ScrollView {
            LazyVGrid(columns: adaptiveColumns, spacing: optimizedSpacing) {
                ForEach(items) { item in
                    content(item)
                        .transition(.asymmetric(
                            insertion: .scale.combined(with: .opacity),
                            removal: .opacity
                        ))
                }
            }
            .padding(.horizontal, 16)
        }
        .animation(.easeInOut(duration: 0.3), value: items.count)
    }
    private var adaptiveColumns: [GridItem] {
        // Reduce columns on lower performance devices
        if performanceMonitor.currentFPS < 55 {
            return [GridItem(.adaptive(minimum: 180), spacing: 8)]
        }
        return columns
    }
    private var optimizedSpacing: CGFloat {
        performanceMonitor.currentFPS > 55 ? 12 : 8
    }
}
// MARK: - Performance-Aware Animation Modifier
struct PerformanceAwareAnimation: ViewModifier {
    @StateObject private var performanceMonitor = PerformanceMonitoringService.shared
    let animation: Animation
    let fallbackAnimation: Animation
    init(
        animation: Animation = .easeInOut(duration: 0.3),
        fallback: Animation = .easeInOut(duration: 0.1)
    ) {
        self.animation = animation
        self.fallbackAnimation = fallback
    }
    func body(content: Content) -> some View {
        content
            .animation(
                performanceMonitor.currentFPS > 55 ? animation : fallbackAnimation,
                value: performanceMonitor.currentFPS
            )
    }
}
extension View {
    func performanceAwareAnimation(
        _ animation: Animation = .easeInOut(duration: 0.3),
        fallback: Animation = .easeInOut(duration: 0.1)
    ) -> some View {
        self.modifier(PerformanceAwareAnimation(animation: animation, fallback: fallback))
    }
}
// MARK: - Optimized Image Loading
struct OptimizedAsyncImage: View {
    let url: URL?
    let placeholder: Image
    @State private var loadedImage: UIImage?
    @State private var isLoading = false
    @StateObject private var performanceMonitor = PerformanceMonitoringService.shared
    init(url: URL?, placeholder: Image = Image(systemName: "photo")) {
        self.url = url
        self.placeholder = placeholder
    }
    var body: some View {
        Group {
            if let loadedImage = loadedImage {
                Image(uiImage: loadedImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else if isLoading {
                ProgressView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                placeholder
                    .foregroundColor(.gray)
            }
        }
        .onAppear {
            loadImageIfNeeded()
        }
    }
    private func loadImageIfNeeded() {
        guard let url = url, loadedImage == nil, !isLoading else { return }
        // Skip loading on very low performance
        guard performanceMonitor.currentFPS > 30 else { return }
        isLoading = true
        Task {
            do {
                let (data, _) = try await URLSession.shared.data(from: url)
                if let image = UIImage(data: data) {
                    await MainActor.run {
                        self.loadedImage = image
                        self.isLoading = false
                    }
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }
    }
}
