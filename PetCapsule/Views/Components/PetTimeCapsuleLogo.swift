//
//  PetTimeCapsuleLogo.swift
//  PetTime Capsule
//
//  App Logo Component
//

import SwiftUI

struct PetTimeCapsuleLogo: View {
    let size: CGFloat
    let showText: Bool
    let style: LogoStyle
    
    init(size: CGFloat = 100, showText: Bool = true, style: LogoStyle = .full) {
        self.size = size
        self.showText = showText
        self.style = style
    }
    
    var body: some View {
        VStack(spacing: size * 0.1) {
            // Logo Icon
            logoIcon
            
            // App Name Text
            if showText {
                logoText
            }
        }
    }
    
    private var logoIcon: some View {
        ZStack {
            // Background circle with gradient
            Circle()
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 0.4, green: 0.49, blue: 0.92),
                            Color(red: 0.46, green: 0.29, blue: 0.64)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: size, height: size)
                .shadow(color: .black.opacity(0.2), radius: size * 0.05, x: 2, y: 4)
            
            // Time capsule
            VStack(spacing: 0) {
                // Capsule lid
                Ellipse()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: size * 0.8, height: size * 0.08)
                    .offset(y: size * 0.02)
                
                Ellipse()
                    .fill(Color.white.opacity(0.9))
                    .frame(width: size * 0.8, height: size * 0.08)
                
                // Capsule body
                RoundedRectangle(cornerRadius: size * 0.3)
                    .fill(Color.white.opacity(0.9))
                    .frame(width: size * 0.8, height: size * 0.6)
                    .overlay(
                        // Paw print inside
                        pawPrint
                    )
            }
            .offset(y: -size * 0.05)
            
            // Floating heart
            heart
                .offset(x: size * 0.2, y: -size * 0.5)
            
            // Sparkles
            sparkles
            
            // Clock element
            clockElement
                .offset(x: size * 0.4, y: size * 0.4)
        }
    }
    
    private var pawPrint: some View {
        ZStack {
            // Main pad
            Ellipse()
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 0.94, green: 0.58, blue: 0.98),
                            Color(red: 0.96, green: 0.34, blue: 0.42)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: size * 0.24, height: size * 0.2)
            
            // Toes
            VStack(spacing: size * 0.02) {
                HStack(spacing: size * 0.08) {
                    Ellipse()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(red: 0.94, green: 0.58, blue: 0.98),
                                    Color(red: 0.96, green: 0.34, blue: 0.42)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: size * 0.08, height: size * 0.12)
                    
                    Ellipse()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(red: 0.94, green: 0.58, blue: 0.98),
                                    Color(red: 0.96, green: 0.34, blue: 0.42)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: size * 0.08, height: size * 0.12)
                    
                    Ellipse()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color(red: 0.94, green: 0.58, blue: 0.98),
                                    Color(red: 0.96, green: 0.34, blue: 0.42)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: size * 0.08, height: size * 0.12)
                }
                .offset(y: -size * 0.14)
                
                Spacer()
            }
        }
    }
    
    private var heart: some View {
        Image(systemName: "heart.fill")
            .font(.system(size: size * 0.1))
            .foregroundStyle(
                LinearGradient(
                    colors: [
                        Color(red: 1.0, green: 0.6, blue: 0.62),
                        Color(red: 0.99, green: 0.81, blue: 0.94)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
    }
    
    private var sparkles: some View {
        ZStack {
            ForEach(0..<6, id: \.self) { index in
                Circle()
                    .fill(Color.white.opacity(0.8))
                    .frame(width: size * 0.02, height: size * 0.02)
                    .offset(
                        x: cos(Double(index) * .pi / 3) * size * 0.45,
                        y: sin(Double(index) * .pi / 3) * size * 0.45
                    )
            }
        }
    }
    
    private var clockElement: some View {
        ZStack {
            Circle()
                .stroke(Color.white.opacity(0.7), lineWidth: size * 0.01)
                .frame(width: size * 0.15, height: size * 0.15)
            
            // Clock hands
            VStack {
                Rectangle()
                    .fill(Color.white.opacity(0.7))
                    .frame(width: size * 0.005, height: size * 0.04)
                    .offset(y: -size * 0.02)
                
                Spacer()
            }
            
            HStack {
                Spacer()
                
                Rectangle()
                    .fill(Color.white.opacity(0.7))
                    .frame(width: size * 0.03, height: size * 0.005)
                    .offset(x: size * 0.015)
            }
        }
    }
    
    private var logoText: some View {
        VStack(spacing: size * 0.02) {
            Text("PetTime")
                .font(.system(size: size * 0.2, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [
                            Color(red: 0.4, green: 0.49, blue: 0.92),
                            Color(red: 0.46, green: 0.29, blue: 0.64)
                        ],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
            
            Text("Capsule")
                .font(.system(size: size * 0.15, weight: .medium, design: .rounded))
                .foregroundColor(.secondary)
        }
    }
}

enum LogoStyle {
    case full
    case iconOnly
    case textOnly
    case compact
}

// MARK: - Preview
#Preview {
    VStack(spacing: 40) {
        PetTimeCapsuleLogo(size: 120, showText: true, style: .full)
        
        HStack(spacing: 30) {
            PetTimeCapsuleLogo(size: 80, showText: false, style: .iconOnly)
            PetTimeCapsuleLogo(size: 60, showText: false, style: .iconOnly)
            PetTimeCapsuleLogo(size: 40, showText: false, style: .iconOnly)
        }
    }
    .padding()
    .background(
        LinearGradient(
            colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
}
