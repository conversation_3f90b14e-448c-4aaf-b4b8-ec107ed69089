//
//  UnifiedVetComponents.swift
//  PetCapsule
//
//  Unified vet finder components for consistent design across the app
//

import SwiftUI
import CoreLocation

// MARK: - Unified Vet Card Component

struct UnifiedVetCard: View {
    let vet: VeterinaryClinic
    let onTap: () -> Void
    let onCall: (() -> Void)?
    let style: VetCardStyle
    
    init(vet: VeterinaryClinic, style: VetCardStyle = .standard, onTap: @escaping () -> Void, onCall: (() -> Void)? = nil) {
        self.vet = vet
        self.style = style
        self.onTap = onTap
        self.onCall = onCall
    }
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with name and badges
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(vet.name)
                            .font(style == .compact ? .subheadline : .headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(2)
                        
                        Text(vet.address)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(style == .compact ? 1 : 2)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("\(String(format: "%.1f", vet.distance)) mi")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                        
                        if vet.is24Hour {
                            Text("24/7")
                                .font(.caption2)
                                .fontWeight(.medium)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.green.opacity(0.2))
                                .foregroundColor(.green)
                                .cornerRadius(4)
                        }
                        
                        if vet.rating > 0 {
                            HStack(spacing: 2) {
                                Image(systemName: "star.fill")
                                    .font(.caption2)
                                    .foregroundColor(.yellow)
                                Text(String(format: "%.1f", vet.rating))
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                // Specialties (if any)
                if !vet.specialties.isEmpty && style != .compact {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 6) {
                            ForEach(vet.specialties.prefix(3), id: \.self) { specialty in
                                Text(specialty)
                                    .font(.caption2)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.purple.opacity(0.1))
                                    .foregroundColor(.purple)
                                    .cornerRadius(4)
                            }
                        }
                    }
                }
                
                // Contact section
                HStack {
                    Text(vet.phoneNumber)
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    if let onCall = onCall, style == .emergency {
                        Button("Call Now") {
                            onCall()
                        }
                        .font(.caption)
                        .fontWeight(.semibold)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
            }
            .padding(style == .compact ? 12 : 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(style.backgroundColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(style.borderColor, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Vet Card Styles

enum VetCardStyle {
    case standard
    case compact
    case emergency
    
    var backgroundColor: Color {
        switch self {
        case .standard, .compact:
            return Color(.systemBackground)
        case .emergency:
            return Color.red.opacity(0.05)
        }
    }
    
    var borderColor: Color {
        switch self {
        case .standard, .compact:
            return Color.blue.opacity(0.3)
        case .emergency:
            return Color.red.opacity(0.3)
        }
    }
}

// MARK: - Unified Vet Search Results Component

struct UnifiedVetSearchResults: View {
    let vets: [VeterinaryClinic]
    let isLoading: Bool
    let style: VetCardStyle
    let title: String
    let searchRadius: String
    let onVetTap: (VeterinaryClinic) -> Void
    let onVetCall: ((VeterinaryClinic) -> Void)?
    let onViewAll: (() -> Void)?
    let limit: Int?
    
    init(
        vets: [VeterinaryClinic],
        isLoading: Bool,
        style: VetCardStyle = .standard,
        title: String,
        searchRadius: String = "5 mile radius",
        limit: Int? = nil,
        onVetTap: @escaping (VeterinaryClinic) -> Void,
        onVetCall: ((VeterinaryClinic) -> Void)? = nil,
        onViewAll: (() -> Void)? = nil
    ) {
        self.vets = vets
        self.isLoading = isLoading
        self.style = style
        self.title = title
        self.searchRadius = searchRadius
        self.onVetTap = onVetTap
        self.onVetCall = onVetCall
        self.onViewAll = onViewAll
        self.limit = limit
    }
    
    var displayVets: [VeterinaryClinic] {
        if let limit = limit {
            return Array(vets.prefix(limit))
        }
        return vets
    }
    
    var body: some View {
        VStack(spacing: 12) {
            if isLoading {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Finding \(title.lowercased()) within \(searchRadius)...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
            } else if !vets.isEmpty {
                VStack(spacing: 12) {
                    // Header
                    HStack {
                        Text("\(title) (\(searchRadius))")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        if let onViewAll = onViewAll, let limit = limit, vets.count > limit {
                            Button("View All") {
                                onViewAll()
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                    }
                    
                    // Vet Cards
                    ForEach(displayVets, id: \.id) { vet in
                        UnifiedVetCard(
                            vet: vet,
                            style: style,
                            onTap: { onVetTap(vet) },
                            onCall: onVetCall != nil ? { onVetCall?(vet) } : nil
                        )
                    }
                    
                    // Show more button if there are additional vets
                    if let limit = limit, vets.count > limit, onViewAll != nil {
                        Button("View \(vets.count - limit) More Vets") {
                            onViewAll?()
                        }
                        .font(.subheadline)
                        .foregroundColor(.blue)
                        .padding(.top, 8)
                    }
                }
            } else if !isLoading {
                UnifiedVetEmptyState(
                    title: "No \(title.lowercased()) found nearby",
                    subtitle: "Try expanding search or adjusting filters",
                    onExpandSearch: { /* Handle expand search */ }
                )
            }
        }
    }
}

// MARK: - Unified Empty State Component

struct UnifiedVetEmptyState: View {
    let title: String
    let subtitle: String
    let onExpandSearch: (() -> Void)?
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "location.magnifyingglass")
                    .font(.title2)
                    .foregroundColor(.orange)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding()
            .background(Color.orange.opacity(0.1))
            .cornerRadius(12)
            
            if let onExpandSearch = onExpandSearch {
                Button("Search Wider Area") {
                    onExpandSearch()
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
    }
}

// MARK: - Quick Stats Component

struct VetQuickStats: View {
    let vets: [VeterinaryClinic]
    
    var emergencyCount: Int {
        vets.filter { $0.is24Hour }.count
    }
    
    var nearbyCount: Int {
        vets.filter { $0.distance <= 2.0 }.count
    }
    
    var specialtyCount: Int {
        vets.filter { !$0.specialties.isEmpty }.count
    }
    
    var body: some View {
        HStack(spacing: 16) {
            if emergencyCount > 0 {
                Label("\(emergencyCount) Emergency", systemImage: "cross.circle.fill")
                    .font(.caption)
                    .foregroundColor(.red)
            }
            
            if nearbyCount > 0 {
                Label("\(nearbyCount) Within 2mi", systemImage: "location.circle.fill")
                    .font(.caption)
                    .foregroundColor(.green)
            }
            
            if specialtyCount > 0 {
                Label("\(specialtyCount) Specialty", systemImage: "stethoscope")
                    .font(.caption)
                    .foregroundColor(.purple)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    VStack(spacing: 20) {
        UnifiedVetCard(
            vet: VeterinaryClinic.sampleVet,
            style: .emergency,
            onTap: {},
            onCall: {}
        )
        
        UnifiedVetSearchResults(
            vets: [VeterinaryClinic.sampleVet, VeterinaryClinic.sampleVet],
            isLoading: false,
            title: "Emergency Vets",
            limit: 3,
            onVetTap: { _ in },
            onVetCall: { _ in },
            onViewAll: {}
        )
    }
    .padding()
}

// MARK: - Sample Data Extension

extension VeterinaryClinic {
    static let sampleVet = VeterinaryClinic(
        name: "24/7 Emergency Animal Hospital",
        address: "123 Emergency Ave, Your City",
        phoneNumber: "(555) 911-PETS",
        is24Hour: true,
        distance: 2.3,
        rating: 4.8,
        specialties: ["Emergency", "Surgery", "Critical Care"]
    )
} 