//
//  GenZDashboardView.swift
//  PetCapsule
//
//  Created by AI Assistant on 6/6/25.
//

import SwiftUI

// Temporarily commented out to avoid compilation errors
/*
@available(iOS 18.0, *)
struct GenZDashboardView: View {
    @EnvironmentObject var realDataService: RealDataService
    @StateObject private var aiSupportService = EnhancedAIAgentService.shared
    @StateObject private var performanceService = PerformanceMonitoringService.shared
    @State private var animateCards = false
    @State private var rotationAngle: Double = 0
    @State private var showAddPet = false
    @State private var showMemories = false
    @State private var showPetSupport = false
    @State private var showNotifications = false
    @State private var showSettings = false
    @State private var currentGradientIndex = 0
    @State private var glowAnimation = false
    @State private var selectedPet: Pet?
    @State private var showPetDetail = false
    
    // Tab selection binding for navigation
    @Binding var tabSelection: Int
    
    // Glass morphism colors for Gen Z
    private var glassColors: [Color] {
        [
            Color.pink.opacity(0.3),
            Color.purple.opacity(0.3),
            Color.blue.opacity(0.3),
            Color.cyan.opacity(0.3),
            Color.mint.opacity(0.3),
            Color.green.opacity(0.3),
            Color.yellow.opacity(0.3),
            Color.orange.opacity(0.3),
            Color.red.opacity(0.3)
        ]
    }

    private var neonGradients: [LinearGradient] {
        [
            LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing),
            LinearGradient(colors: [.purple, .blue], startPoint: .topLeading, endPoint: .bottomTrailing),
            LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing),
            LinearGradient(colors: [.cyan, .mint], startPoint: .topLeading, endPoint: .bottomTrailing),
            LinearGradient(colors: [.mint, .green], startPoint: .topLeading, endPoint: .bottomTrailing),
            LinearGradient(colors: [.green, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing),
            LinearGradient(colors: [.yellow, .orange], startPoint: .topLeading, endPoint: .bottomTrailing),
            LinearGradient(colors: [.orange, .red], startPoint: .topLeading, endPoint: .bottomTrailing),
            LinearGradient(colors: [.red, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        ]
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Animated background
                animatedBackground
                
                ScrollView {
                    LazyVStack(spacing: 20) {
                        // Welcome header with glass effect
                        welcomeGlassHeader
                        
                        // Glass tile grid
                        glassTileGrid
                        
                        // Pets section with glass cards
                        petsGlassSection
                        
                        // Quick actions with neon effects
                        quickActionsGlass
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .onAppear {
                withAnimation(.easeInOut(duration: 1.0)) {
                    animateCards = true
                }
                startAnimations()
            }
        }
        .sheet(isPresented: $showAddPet) {
            AddPetView()
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showMemories) {
            if #available(iOS 18.0, *) {
                ComprehensiveMemoryView()
                    .environmentObject(realDataService)
            } else {
                Text("Memories require iOS 18.0+")
            }
        }
        .sheet(isPresented: $showPetSupport) {
            if #available(iOS 18.0, *) {
                PetSupportView()
                    .environmentObject(realDataService)
            } else {
                Text("AI Support requires iOS 18.0+")
            }
        }
        .sheet(isPresented: $showPetDetail) {
            if let pet = selectedPet {
                PetDetailView(pet: pet)
            }
        }
    }
    
    // MARK: - Animated Background
    private var animatedBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color.black,
                    Color.purple.opacity(0.3),
                    Color.blue.opacity(0.2),
                    Color.black
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // Floating orbs
            ForEach(0..<6, id: \.self) { index in
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                glassColors[index % glassColors.count],
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 0,
                            endRadius: 100
                        )
                    )
                    .frame(width: 200, height: 200)
                    .offset(
                        x: CGFloat.random(in: -200...200),
                        y: CGFloat.random(in: -300...300)
                    )
                    .scaleEffect(glowAnimation ? 1.2 : 0.8)
                    .animation(
                        .easeInOut(duration: Double.random(in: 3...6))
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.5),
                        value: glowAnimation
                    )
            }
        }
    }
    
    // MARK: - Welcome Glass Header
    private var welcomeGlassHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Hey bestie! 💖")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.pink, .purple, .blue],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                    
                    Text("Your pets are living their best life ✨")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                // Rotating emoji
                Text("🌈")
                    .font(.system(size: 40))
                    .rotationEffect(.degrees(rotationAngle))
                    .animation(.linear(duration: 10).repeatForever(autoreverses: false), value: rotationAngle)
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(
                            LinearGradient(
                                colors: [.pink.opacity(0.5), .purple.opacity(0.5), .blue.opacity(0.5)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .shadow(color: .pink.opacity(0.3), radius: 20, x: 0, y: 10)
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.1), value: animateCards)
    }
    
    // MARK: - Glass Tile Grid
    private var glassTileGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 16) {
            glassTile(
                icon: "pawprint.fill",
                title: "My Pets",
                subtitle: "\(realDataService.pets.count) furry friends",
                gradient: neonGradients[0],
                action: { tabSelection = 1 } // Navigate to My Pets tab
            )

            glassTile(
                icon: "photo.stack.fill",
                title: "Memories",
                subtitle: "\(realDataService.pets.reduce(0) { $0 + $1.memoryIDs.count }) precious moments",
                gradient: neonGradients[1],
                action: { showMemories = true }
            )

            glassTile(
                icon: "brain.head.profile.fill",
                title: "AI Support",
                subtitle: "Get smart advice",
                gradient: neonGradients[2],
                action: { showPetSupport = true }
            )

            glassTile(
                icon: "plus.circle.fill",
                title: "Add Pet",
                subtitle: "New family member",
                gradient: neonGradients[3],
                action: { showAddPet = true }
            )
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.3), value: animateCards)
    }

    private func glassTile(
        icon: String,
        title: String,
        subtitle: String,
        gradient: LinearGradient,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            VStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(gradient)
                        .frame(width: 60, height: 60)
                        .shadow(color: .white.opacity(0.3), radius: 10, x: 0, y: 5)

                    Image(systemName: icon)
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.white)
                }

                VStack(spacing: 4) {
                    Text(title)
                        .font(.system(size: 18, weight: .bold, design: .rounded))
                        .foregroundColor(.white)

                    Text(subtitle)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
            }
            .frame(height: 140)
            .frame(maxWidth: .infinity)
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(gradient, lineWidth: 2)
                    )
            )
            .shadow(color: .pink.opacity(0.4), radius: 15, x: 0, y: 8)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(glowAnimation ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: glowAnimation)
    }

    // MARK: - Pets Glass Section
    private var petsGlassSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Your Squad 🐾")
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.cyan, .blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Spacer()

                Button("See All") {
                    tabSelection = 1 // Navigate to My Pets tab
                }
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.white.opacity(0.8))
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(.ultraThinMaterial)
                        .overlay(
                            Capsule()
                                .stroke(.white.opacity(0.3), lineWidth: 1)
                        )
                )
            }

            if realDataService.pets.isEmpty {
                emptyPetsGlassView
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(Array(realDataService.pets.enumerated()), id: \.element.id) { index, pet in
                            petGlassCard(pet: pet, index: index)
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.5), value: animateCards)
    }

    private var emptyPetsGlassView: some View {
        VStack(spacing: 20) {
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [.pink.opacity(0.3), .purple.opacity(0.1)],
                            center: .center,
                            startRadius: 0,
                            endRadius: 50
                        )
                    )
                    .frame(width: 100, height: 100)

                Text("🐕‍🦺")
                    .font(.system(size: 50))
                    .scaleEffect(glowAnimation ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: glowAnimation)
            }

            VStack(spacing: 8) {
                Text("No pets yet! 😢")
                    .font(.system(size: 20, weight: .bold, design: .rounded))
                    .foregroundColor(.white)

                Text("Add your first furry friend to get started!")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }

            Button("Add Your First Pet 🎉") {
                showAddPet = true
            }
            .font(.system(size: 16, weight: .bold, design: .rounded))
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                Capsule()
                    .fill(
                        LinearGradient(
                            colors: [.pink, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .shadow(color: .pink.opacity(0.5), radius: 10, x: 0, y: 5)
            )
        }
        .frame(maxWidth: .infinity)
        .padding(32)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private func petGlassCard(pet: Pet, index: Int) -> some View {
        Button(action: {
            selectedPet = pet
            showPetDetail = true
        }) {
            VStack(spacing: 12) {
                // Pet image with glass effect
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    ZStack {
                        Circle()
                            .fill(neonGradients[index % neonGradients.count])

                        Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                            .font(.system(size: 32))
                    }
                }
                .frame(width: 80, height: 80)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(neonGradients[index % neonGradients.count], lineWidth: 3)
                )
                .shadow(color: .pink.opacity(0.5), radius: 10, x: 0, y: 5)

                VStack(spacing: 4) {
                    Text(pet.name)
                        .font(.system(size: 16, weight: .bold, design: .rounded))
                        .foregroundColor(.white)

                    Text(pet.breed ?? "Unknown Breed")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))

                    // Health indicator with comprehensive scoring
                    HStack(spacing: 4) {
                        let healthScore = realDataService.getHealthScore(for: pet)
                        Circle()
                            .fill(healthScore > 0.8 ? .green : healthScore > 0.6 ? .yellow : .red)
                            .frame(width: 8, height: 8)

                        Text("\(Int(healthScore * 100))%")
                            .font(.system(size: 10, weight: .semibold))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
            .frame(width: 140, height: 160)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(neonGradients[index % neonGradients.count], lineWidth: 1)
                    )
            )
            .shadow(color: .pink.opacity(0.3), radius: 15, x: 0, y: 8)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(Double(index) * 0.1 + 0.7), value: animateCards)
    }

    // MARK: - Quick Actions Glass
    private var quickActionsGlass: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions ⚡")
                .font(.system(size: 24, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.yellow, .orange, .red],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 3), spacing: 12) {
                quickActionTile(
                    icon: "camera.fill",
                    title: "Capture",
                    gradient: neonGradients[4],
                    action: { /* Add memory action */ }
                )

                quickActionTile(
                    icon: "heart.text.square.fill",
                    title: "Health",
                    gradient: neonGradients[5],
                    action: { /* Health check action */ }
                )

                quickActionTile(
                    icon: "calendar.badge.plus",
                    title: "Schedule",
                    gradient: neonGradients[6],
                    action: { /* Schedule action */ }
                )

                quickActionTile(
                    icon: "bag.fill",
                    title: "Shop",
                    gradient: neonGradients[7],
                    action: { /* Shopping action */ }
                )

                quickActionTile(
                    icon: "gamecontroller.fill",
                    title: "Play",
                    gradient: neonGradients[8],
                    action: { /* Play action */ }
                )

                quickActionTile(
                    icon: "sparkles",
                    title: "More",
                    gradient: neonGradients[0],
                    action: { /* More actions */ }
                )
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.9), value: animateCards)
    }

    private func quickActionTile(
        icon: String,
        title: String,
        gradient: LinearGradient,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(gradient)
                        .frame(width: 40, height: 40)
                        .shadow(color: .pink.opacity(0.5), radius: 8, x: 0, y: 4)

                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                }

                Text(title)
                    .font(.system(size: 12, weight: .semibold, design: .rounded))
                    .foregroundColor(.white)
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(gradient, lineWidth: 1)
                    )
            )
            .shadow(color: .pink.opacity(0.3), radius: 10, x: 0, y: 5)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(glowAnimation ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true).delay(Double.random(in: 0...1)), value: glowAnimation)
    }

    private func startAnimations() {
        withAnimation(.linear(duration: 10).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }

        withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
            glowAnimation = true
        }
    }
}
*/

// Simple placeholder view
struct GenZDashboardView: View {
    @Binding var tabSelection: Int
    @EnvironmentObject var realDataService: RealDataService
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Gen Z Dashboard")
                    .font(.title)
                    .padding()
                
                if !realDataService.pets.isEmpty {
                    Text("Found \(realDataService.pets.count) pets")
                        .foregroundColor(.green)
                } else {
                    Text("No pets found")
                        .foregroundColor(.red)
                }
                
                Spacer()
            }
            .navigationTitle("Dashboard")
        }
    }
}

@available(iOS 18.0, *)
struct GenZDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        if #available(iOS 18.0, *) {
            GenZDashboardView(tabSelection: .constant(0))
                .environmentObject(RealDataService())
                .environmentObject(PetAISupportService.shared)
        } else {
            Text("iOS 18.0+ Required")
        }
    }
}
