//
//  PerformanceDashboardView.swift
//  PetCapsule
//
//  🍎 Phase 2: Apple Intelligence Performance Dashboard
//  Real-time monitoring of AI performance metrics
//

import SwiftUI
import Charts

@available(iOS 18.0, *)
struct PerformanceDashboardView: View {
    @StateObject private var appleIntelligence = EnhancedAppleIntelligenceService.shared
    @State private var showingDetailedMetrics = false
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Apple Intelligence Performance")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    // Performance Metrics
                    HStack(spacing: 20) {
                        VStack {
                            Text("Response Time")
                                .font(.caption)
                            Text("\(Int(appleIntelligence.averageResponseTime * 1000))ms")
                                .font(.title3)
                                .fontWeight(.bold)
                        }
                        .padding()
                        .background(.ultraThinMaterial)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        
                        VStack {
                            Text("Cache Hit Rate")
                                .font(.caption)
                            Text("\(Int(appleIntelligence.cacheHitRate * 100))%")
                                .font(.title3)
                                .fontWeight(.bold)
                        }
                        .padding()
                        .background(.ultraThinMaterial)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                    
                    // Phase 2 Features
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Phase 2 Optimizations")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        PerformanceFeatureRow(title: "Smart Caching", isEnabled: true)
                        PerformanceFeatureRow(title: "Concurrent Processing", isEnabled: true)
                        PerformanceFeatureRow(title: "Device Optimization", isEnabled: true)
                        PerformanceFeatureRow(title: "Vision Pipeline", isEnabled: true)
                    }
                    .padding()
                    .background(.ultraThinMaterial)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                }
                .padding()
            }
            .navigationTitle("Performance")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Details") {
                        showingDetailedMetrics.toggle()
                    }
                    .foregroundStyle(.blue)
                }
            }
        }
        .sheet(isPresented: $showingDetailedMetrics) {
            DetailedMetricsView()
        }
    }
    
    // MARK: - Performance Chart (Phase 3 Feature)
    // Chart functionality will be added in Phase 3
    
    // MARK: - Performance Insights
    
    private var performanceInsights: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Performance Insights")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(appleIntelligence.performanceInsights, id: \.message) { insight in
                HStack(spacing: 12) {
                    Image(systemName: insightIcon(for: insight.type))
                        .foregroundStyle(insightColor(for: insight.type))
                        .font(.title2)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(insight.message)
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text(insight.recommendation)
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    
                    Spacer()
                    
                    Text(String(format: "%.2f", insight.value))
                        .font(.caption)
                        .fontWeight(.semibold)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(insightColor(for: insight.type).opacity(0.2))
                        .clipShape(Capsule())
                }
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            
            if appleIntelligence.performanceInsights.isEmpty {
                HStack(spacing: 12) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundStyle(.green)
                        .font(.title2)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("All systems optimal")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("Apple Intelligence is performing at peak efficiency")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    
                    Spacer()
                }
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
        }
    }
    
    // MARK: - Optimization Features
    
    private var optimizationFeatures: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Phase 2 Optimizations")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                
                OptimizationFeatureCard(
                    title: "Smart Caching",
                    description: "Intelligent response caching with TTL",
                    icon: "memorychip.fill",
                    color: .blue,
                    isActive: true
                )
                
                OptimizationFeatureCard(
                    title: "Concurrent Processing",
                    description: "Multi-core parallel AI processing",
                    icon: "cpu.fill",
                    color: .orange,
                    isActive: true
                )
                
                OptimizationFeatureCard(
                    title: "Device Optimization",
                    description: "A17 Pro enhanced performance",
                    icon: "iphone",
                    color: .purple,
                    isActive: true
                )
                
                OptimizationFeatureCard(
                    title: "Vision Pipeline",
                    description: "Optimized image analysis",
                    icon: "eye.fill",
                    color: .green,
                    isActive: true
                )
            }
        }
    }
    
    // MARK: - Advanced Diagnostics
    
    private var advancedDiagnostics: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Advanced Diagnostics")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                DiagnosticRow(
                    title: "Apple Intelligence Status",
                    value: "Operational",
                    status: .success
                )
                
                DiagnosticRow(
                    title: "Vision Framework",
                    value: "Optimized",
                    status: .success
                )
                
                DiagnosticRow(
                    title: "Speech Recognition",
                    value: "Available",
                    status: .success
                )
                
                DiagnosticRow(
                    title: "Local Processing",
                    value: "100% Local",
                    status: .success
                )
                
                DiagnosticRow(
                    title: "Cache Efficiency",
                    value: String(format: "%.1f%%", appleIntelligence.cacheHitRate * 100),
                    status: appleIntelligence.cacheHitRate > 0.5 ? .success : .warning
                )
            }
            .padding()
            .background(.ultraThinMaterial)
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
    
    // MARK: - Computed Properties
    
    private var responseTimeColor: Color {
        if appleIntelligence.averageResponseTime < 0.5 {
            return .green
        } else if appleIntelligence.averageResponseTime < 1.0 {
            return .orange
        } else {
            return .red
        }
    }
    
    private var responseTrend: PerformanceMetricCard.Trend {
        if appleIntelligence.averageResponseTime < 0.5 {
            return .up
        } else {
            return .stable
        }
    }
    
    private var cacheColor: Color {
        if appleIntelligence.cacheHitRate > 0.6 {
            return .green
        } else if appleIntelligence.cacheHitRate > 0.3 {
            return .orange
        } else {
            return .red
        }
    }
    
    private var performanceScore: String {
        let score = calculatePerformanceScore()
        return "\(Int(score * 100))/100"
    }
    
    private var scoreColor: Color {
        let score = calculatePerformanceScore()
        if score > 0.8 {
            return .green
        } else if score > 0.6 {
            return .orange
        } else {
            return .red
        }
    }
    
    private func calculatePerformanceScore() -> Double {
        let responseScore = max(0, min(1, 1 - (appleIntelligence.averageResponseTime / 2.0)))
        let cacheScore = appleIntelligence.cacheHitRate
        let tasksScore = max(0, min(1, 1 - (Double(appleIntelligence.activeProcessingTasks) / 10.0)))
        
        return (responseScore + cacheScore + tasksScore) / 3.0
    }
    
    private func insightIcon(for type: PerformanceInsight.InsightType) -> String {
        switch type {
        case .success: return "checkmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .info: return "info.circle.fill"
        case .error: return "xmark.circle.fill"
        }
    }
    
    private func insightColor(for type: PerformanceInsight.InsightType) -> Color {
        switch type {
        case .success: return .green
        case .warning: return .orange
        case .info: return .blue
        case .error: return .red
        }
    }
    
    // Sample data for Phase 3 charts
}

// MARK: - Supporting Views

struct PerformanceMetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let trend: Trend
    
    enum Trend {
        case up, down, stable
        
        var icon: String {
            switch self {
            case .up: return "arrow.up.right"
            case .down: return "arrow.down.right"
            case .stable: return "minus"
            }
        }
        
        var color: Color {
            switch self {
            case .up: return .green
            case .down: return .red
            case .stable: return .secondary
            }
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundStyle(color)
                    .font(.title2)
                
                Spacer()
                
                Image(systemName: trend.icon)
                    .foregroundStyle(trend.color)
                    .font(.caption)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(title)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

struct OptimizationFeatureCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let isActive: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundStyle(color)
                    .font(.title3)
                
                Spacer()
                
                if isActive {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundStyle(.green)
                        .font(.caption)
                }
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                    .lineLimit(2)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

struct DiagnosticRow: View {
    let title: String
    let value: String
    let status: Status
    
    enum Status {
        case success, warning, error
        
        var color: Color {
            switch self {
            case .success: return .green
            case .warning: return .orange
            case .error: return .red
            }
        }
        
        var icon: String {
            switch self {
            case .success: return "checkmark.circle.fill"
            case .warning: return "exclamationmark.triangle.fill"
            case .error: return "xmark.circle.fill"
            }
        }
    }
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
            
            Spacer()
            
            HStack(spacing: 6) {
                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Image(systemName: status.icon)
                    .foregroundStyle(status.color)
                    .font(.caption)
            }
        }
    }
}

@available(iOS 18.0, *)
struct DetailedMetricsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Detailed Performance Metrics")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Coming in Phase 3...")
                        .font(.headline)
                        .foregroundStyle(.secondary)
                }
                .padding()
            }
            .navigationTitle("Detailed Metrics")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct PerformanceFeatureRow: View {
    let title: String
    let isEnabled: Bool
    
    var body: some View {
        HStack {
            Image(systemName: isEnabled ? "checkmark.circle.fill" : "circle")
                .foregroundStyle(isEnabled ? .green : .secondary)
            
            Text(title)
                .font(.subheadline)
            
            Spacer()
        }
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        PerformanceDashboardView()
    } else {
        Text("Requires iOS 18.0+")
    }
} 