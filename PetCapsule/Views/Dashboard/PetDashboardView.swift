//
//  PetDashboardView.swift
//  PetCapsule
//
//  Enhanced Dashboard with Fixed UX/UI Issues
//

import SwiftUI
import CoreHaptics
import Combine

struct PetDashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var selectedTab = 0
    @State private var showingAddPet = false
    @State private var hapticEngine: CHHapticEngine?
    @Environment(\.colorScheme) private var colorScheme
    @State private var animateCards = false
    
    // MARK: - Navigation State Variables
    @State private var showHealthView = false
    @State private var showMemoriesView = false
    @State private var showAIHealthCenter = false
    @State private var selectedPet: Pet?
    @State private var showPetDetail = false
    @State private var showUpgradeSheet = false

    // Additional navigation states for quick actions
    @State private var showAddMemory = false
    @State private var showEmergency = false
    @State private var showAlertsView = false
    
    // Tab selection binding for navigation
    @Binding var tabSelection: Int
    
    var body: some View {
        NavigationView {
            ScrollView {
                mainContent
            }
            .navigationTitle("Your Pet's Daily Life")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingAddPet = true
                    }) {
                        Image(systemName: "plus")
                            .font(.title2)
                            .foregroundColor(.purple)
                    }
                }
            }
            .sheet(isPresented: $showingAddPet) {
                AddPetView()
            }
            .sheet(isPresented: $showUpgradeSheet) {
                SubscriptionView()
            }
            .sheet(isPresented: $showPetDetail) {
                if let pet = selectedPet {
                    PetDetailView(pet: pet)
                }
            }
            .sheet(isPresented: $showingAddPet) {
                AddPetView()
            }
            .sheet(isPresented: $showAddMemory) {
                if #available(iOS 18.0, *) {
                    EnhancedMemoryCreationView()
                } else {
                    Text("Memory creation requires iOS 18.0+")
                }
            }
            .sheet(isPresented: $showEmergency) {
                EmergencyContactsView()
            }
            .sheet(isPresented: $showAlertsView) {
                if #available(iOS 18.0, *) {
                    PetHealthView()
                } else {
                    Text("Health alerts require iOS 18.0+")
                }
            }
            .sheet(isPresented: $showMemoriesView) {
                if #available(iOS 18.0, *) {
                    ComprehensiveMemoryView()
                } else {
                    Text("Memories require iOS 18.0+")
                }
            }
            .sheet(isPresented: $showAIHealthCenter) {
                if #available(iOS 18.0, *) {
                    PetAIAgentChatView(
                        agent: AIAgent(
                            name: "Pet Health Advisor",
                            description: "Your AI pet health assistant",
                            specialties: ["Health monitoring", "Emergency guidance"],
                            isPremium: false,
                            iconName: "heart.circle.fill",
                            gradientColors: ["#FF6B6B", "#4ECDC4"],
                            personality: AIPersonality(
                                temperature: 0.7,
                                tone: "caring",
                                responseStyle: "detailed",
                                expertise: "intermediate"
                            )
                        ),
                        selectedPet: selectedPet
                    )
                } else {
                    Text("AI Chat requires iOS 18.0+")
                }
            }
            .onAppear {
                setupHaptics()
                withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                    animateCards = true
                }
            }
        }
    }
    
    // MARK: - Main Content
    
    private var mainContent: some View {
        VStack(spacing: 20) {
            contentSections
        }
        .padding(.bottom, 100)
    }
    
    private var contentSections: some View {
        Group {
            // Header Section
            headerSection
            
            // Premium Pet Cards Section
            premiumPetCardsSection
            
            // Quick Actions Section
            quickActionsSection
            
            // Health Overview Section
            healthOverviewSection
            
            // Recent Activities Section
            recentActivitiesSection
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Welcome Message
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Welcome back!")
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.primary)
                    
                    Text("Your pets are doing great today")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Subscription Status
                if subscriptionService.subscriptionStatus == .pawStarter {
                    Button(action: {
                        showUpgradeSheet = true
                        hapticFeedback()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "crown.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.yellow)
                            
                            Text("Upgrade")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.purple)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.purple.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.purple.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 20)
            
            // Stats Overview
            statsOverviewSection
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }
    
    // MARK: - Premium Pet Cards Section
    
    private var premiumPetCardsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            petCardsHeaderView
            petCardsContentView
        }
    }
    
    private var petCardsHeaderView: some View {
        HStack {
            Text("Your Pets")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.primary)
            
            Spacer()
            
            if viewModel.pets.count > 2 {
                Button("View All") {
                    tabSelection = 1 // Navigate to MyPetsView
                }
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.purple)
            }
        }
        .padding(.horizontal, 20)
    }
    
    private var petCardsContentView: some View {
        Group {
            if viewModel.pets.isEmpty {
                emptyPetsView
            } else {
                petCardsList
            }
        }
    }
    
    private var petCardsList: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 20) {
                ForEach(Array(viewModel.pets.enumerated()), id: \.element.id) { index, pet in
                    petCardView(pet: pet, index: index)
                }
            }
            .padding(.horizontal, 20)
            .scrollTargetLayout()
        }
        .scrollTargetBehavior(.viewAligned)
    }
    
    private func petCardView(pet: Pet, index: Int) -> AnyView {
        return AnyView(
            SimpleDashboardPetCard(
                pet: pet,
                onTap: {
                    selectedPet = pet
                    showPetDetail = true
                }
            )
            .scaleEffect(animateCards ? 1.0 : 0.8)
            .opacity(animateCards ? 1.0 : 0.0)
            .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(Double(index) * 0.1 + 0.3), value: animateCards)
        )
    }
    
    // MARK: - Empty Pets View
    
    private var emptyPetsView: some View {
        VStack(spacing: 20) {
            // Animated Pet Icon
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color.purple.opacity(0.2), Color.blue.opacity(0.2)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                
                Image(systemName: "pawprint.circle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.purple)
                    .scaleEffect(animateCards ? 1.0 : 0.8)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.5), value: animateCards)
            }
            
            VStack(spacing: 8) {
                Text("Add Your First Pet")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
                
                Text("Start tracking your pet's health, memories, and more")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: {
                showingAddPet = true
                hapticFeedback()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus")
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text("Add Pet")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [Color.purple, Color.blue],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 40)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                )
        )
        .padding(.horizontal, 20)
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.4), value: animateCards)
    }
    
    // MARK: - Stats Overview Section
    
    private var statsOverviewSection: some View {
        HStack(spacing: 16) {
            statCard(
                title: "Total Pets",
                value: "\(viewModel.totalPets)",
                icon: "pawprint.fill",
                color: .purple,
                action: { tabSelection = 1 } // Navigate to MyPetsView
            )

            statCard(
                title: "Health Score",
                value: "\(Int(viewModel.averageHealthScore * 100))%",
                icon: "heart.fill",
                color: .red,
                action: { showHealthView = true }
            )

            statCard(
                title: "Memories",
                value: "\(viewModel.totalMemories)",
                icon: "photo.fill",
                color: .blue,
                action: { showMemoriesView = true }
            )

            statCard(
                title: "Alerts",
                value: "\(viewModel.healthAlerts)",
                icon: "bell.fill",
                color: viewModel.healthAlerts > 0 ? .orange : .green,
                action: { showAlertsView = true }
            )
        }
        .padding(.horizontal, 20)
    }
    
    private func statCard(title: String, value: String, icon: String, color: Color, action: @escaping () -> Void = {}) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(color)

                Text(value)
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.primary)

                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(color.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Quick Actions Section
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Quick Actions")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.horizontal, 20)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    quickActionCard(
                        title: "Add Memory",
                        icon: "plus.circle.fill",
                        color: .blue,
                        action: { showAddMemory = true }
                    )

                    quickActionCard(
                        title: "Health Check",
                        icon: "heart.circle.fill",
                        color: .red,
                        action: { showHealthView = true }
                    )

                    quickActionCard(
                        title: "AI Chat",
                        icon: "brain.head.profile",
                        color: .purple,
                        action: { showAIHealthCenter = true }
                    )

                    quickActionCard(
                        title: "Emergency",
                        icon: "cross.circle.fill",
                        color: .orange,
                        action: { showEmergency = true }
                    )
                }
                .padding(.horizontal, 20)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.6), value: animateCards)
    }
    
    private func quickActionCard(title: String, icon: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: {
            action()
            hapticFeedback()
        }) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(color)
                
                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.primary)
            }
            .frame(width: 80, height: 80)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(color.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Health Overview Section
    
    private var healthOverviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Health Overview")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("View All") {
                    showHealthView = true
                }
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.purple)
            }
            .padding(.horizontal, 20)
            
            if viewModel.recentHealthEvents.isEmpty {
                // No health events
                VStack(spacing: 12) {
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.green)
                    
                    Text("All pets are healthy!")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                    
                    Text("No health alerts or upcoming appointments")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 30)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.green.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.green.opacity(0.2), lineWidth: 1)
                        )
                )
                .padding(.horizontal, 20)
            } else {
                // Health events list
                VStack(spacing: 12) {
                    ForEach(viewModel.recentHealthEvents.prefix(3), id: \.id) { event in
                        healthEventCard(event: event)
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.8), value: animateCards)
    }
    
    private func healthEventCard(event: HealthEvent) -> some View {
        HStack(spacing: 12) {
            Image(systemName: event.type.icon)
                .font(.system(size: 20))
                .foregroundColor(event.type.color)
                .frame(width: 40, height: 40)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(event.type.color.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(event.title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                
                Text(event.description)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                HStack {
                    Text(event.petName ?? "Unknown Pet")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(.purple)
                    
                    Text("•")
                        .font(.system(size: 11))
                        .foregroundColor(.secondary)
                    
                    Text(event.timeAgo)
                        .font(.system(size: 11))
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.system(size: 12))
                .foregroundColor(.secondary)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.gray.opacity(0.1), lineWidth: 1)
                )
        )
        .onTapGesture {
            // Handle health event tap
            hapticFeedback()
        }
    }
    
    // MARK: - Recent Activities Section
    
    private var recentActivitiesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Activities")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("View All") {
                    showMemoriesView = true
                }
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.purple)
            }
            .padding(.horizontal, 20)
            
            VStack(spacing: 12) {
                // Sample activities
                activityCard(
                    title: "Memory Added",
                    description: "New photo of Buddy at the park",
                    icon: "photo.fill",
                    color: .blue,
                    timeAgo: "2 hours ago"
                )
                
                activityCard(
                    title: "Health Check",
                    description: "Wellness checkup completed",
                    icon: "heart.fill",
                    color: .red,
                    timeAgo: "1 day ago"
                )
                
                activityCard(
                    title: "AI Recommendation",
                    description: "New exercise routine suggested",
                    icon: "brain.head.profile",
                    color: .purple,
                    timeAgo: "2 days ago"
                )
            }
            .padding(.horizontal, 20)
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(1.0), value: animateCards)
    }
    
    private func activityCard(title: String, description: String, icon: String, color: Color, timeAgo: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(color)
                .frame(width: 32, height: 32)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(color.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                
                Text(timeAgo)
                    .font(.system(size: 11))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gray.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Action Handlers
    
    private func handleAlertAction(alertId: String, pet: Pet) {
        hapticFeedback()
        // Handle specific alert actions
        print("Alert action for \(alertId) on pet \(pet.name)")
    }
    
    private func handleMarketplaceClick(itemId: String, pet: Pet) {
        hapticFeedback()
        // Handle marketplace item click
        print("Marketplace item \(itemId) clicked for pet \(pet.name)")
    }
    
    private func setupHaptics() {
        // Only setup haptics on real devices, not in simulator
        #if targetEnvironment(simulator)
        print("ℹ️ Haptic engine disabled in simulator")
        return
        #endif
        
        guard CHHapticEngine.capabilitiesForHardware().supportsHaptics else {
            print("ℹ️ Device doesn't support haptics")
            return
        }
        
        do {
            hapticEngine = try CHHapticEngine()
            
            // Handle engine reset
            hapticEngine?.resetHandler = { 
                print("🔄 Haptic engine reset, attempting restart")
                try? hapticEngine?.start()
            }
            
            // Handle engine stop
            hapticEngine?.stoppedHandler = { reason in
                print("⚠️ Haptic engine stopped: \(reason)")
            }
            
            try hapticEngine?.start()
            print("✅ Haptic engine started successfully")
        } catch {
            print("⚠️ Haptic engine setup failed (normal in simulator): \(error.localizedDescription)")
            hapticEngine = nil
        }
    }
    
    private func hapticFeedback() {
        // Use simple impact feedback instead of complex haptics for better compatibility
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.prepare()
        impactFeedback.impactOccurred()
    }
}

// MARK: - Health Event Extensions

extension HealthEvent {
    enum EventType {
        case vaccination
        case checkup
        case medication
        case emergency
        case general
        
        var icon: String {
            switch self {
            case .vaccination: return "shield.fill"
            case .checkup: return "heart.circle.fill"
            case .medication: return "pills.fill"
            case .emergency: return "exclamationmark.triangle.fill"
            case .general: return "info.circle.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .vaccination: return .blue
            case .checkup: return .green
            case .medication: return .purple
            case .emergency: return .red
            case .general: return .gray
            }
        }
    }
    
    var eventType: EventType {
        switch self.type {
        case .vaccination: return .vaccination
        case .checkup: return .checkup
        case .medication: return .medication
        case .emergency: return .emergency
        case .routine: return .general
        }
    }
}

// MARK: - Enhanced Stat Card with Unique Visual Styles

struct EnhancedStatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let gradient: [Color]
    let progress: Double
    let visualStyle: StatCardVisualStyle
    let animationDelay: Double
    
    @State private var animateProgress = false
    @State private var animateIcon = false
    
    var body: some View {
        VStack(spacing: 16) {
            // Header with enhanced typography hierarchy
            HStack {
                VStack(alignment: .leading, spacing: 6) {
                    Text(title)
                        .font(.system(size: 13, weight: .semibold))
                        .foregroundStyle(.secondary)
                        .textCase(.uppercase)
                        .tracking(0.8)
                    
                    Text(value)
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundStyle(.primary)
                        .contentTransition(.numericText())
                    
                    Text(subtitle)
                        .font(.system(size: 11, weight: .medium))
                        .foregroundStyle(.tertiary)
                }
                
                Spacer()
                
                // Enhanced icon with unique visual style
                ZStack {
                    // Style-specific background
                    visualStyleBackground
                    
                    Image(systemName: icon)
                        .font(.system(size: 22, weight: .semibold))
                        .foregroundStyle(.white)
                        .scaleEffect(animateIcon ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: animateIcon)
                }
                .frame(width: 50, height: 50)
            }
            
            // Enhanced progress indicator with context
            HStack {
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.15), lineWidth: 4)
                        .frame(width: 12, height: 12)
                    
                    Circle()
                        .trim(from: 0, to: animateProgress ? progress : 0)
                        .stroke(
                            LinearGradient(
                                colors: gradient,
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            style: StrokeStyle(lineWidth: 4, lineCap: .round)
                        )
                        .frame(width: 12, height: 12)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeOut(duration: 1.0).delay(animationDelay), value: animateProgress)
                }
                
                Text("Progress")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundStyle(.quaternary)
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundStyle(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(gradient[0].opacity(0.1))
                    )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20, style: .continuous)
                        .stroke(gradient[0].opacity(0.2), lineWidth: 1)
                )
        )
        .onAppear {
            withAnimation(.easeOut(duration: 0.8).delay(animationDelay)) {
                animateProgress = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + animationDelay + 0.5) {
                animateIcon = true
            }
        }
    }
    
    @ViewBuilder
    private var visualStyleBackground: some View {
        switch visualStyle {
        case .pawPattern:
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: gradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                // Subtle paw prints
                VStack(spacing: 2) {
                    HStack(spacing: 2) {
                        Circle().fill(.white.opacity(0.2)).frame(width: 3, height: 3)
                        Circle().fill(.white.opacity(0.2)).frame(width: 3, height: 3)
                    }
                    Circle().fill(.white.opacity(0.3)).frame(width: 4, height: 4)
                }
                .offset(x: 8, y: -8)
            }
            
        case .heartbeat:
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: gradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                // Heartbeat line
                Path { path in
                    path.move(to: CGPoint(x: 15, y: 25))
                    path.addLine(to: CGPoint(x: 20, y: 25))
                    path.addLine(to: CGPoint(x: 22, y: 20))
                    path.addLine(to: CGPoint(x: 25, y: 30))
                    path.addLine(to: CGPoint(x: 28, y: 15))
                    path.addLine(to: CGPoint(x: 30, y: 25))
                    path.addLine(to: CGPoint(x: 35, y: 25))
                }
                .stroke(.white.opacity(0.3), lineWidth: 1.5)
            }
            
        case .photoStack:
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: gradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                // Photo stack effect
                VStack(spacing: 1) {
                    Rectangle().fill(.white.opacity(0.2)).frame(width: 12, height: 8).cornerRadius(1)
                    Rectangle().fill(.white.opacity(0.3)).frame(width: 10, height: 6).cornerRadius(1)
                    Rectangle().fill(.white.opacity(0.4)).frame(width: 8, height: 4).cornerRadius(1)
                }
                .offset(y: 2)
            }
            
        case .alert:
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: gradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                // Alert rays
                ForEach(0..<6) { index in
                    Rectangle()
                        .fill(.white.opacity(0.2))
                        .frame(width: 1, height: 8)
                        .offset(y: -16)
                        .rotationEffect(.degrees(Double(index) * 60))
                }
            }
        }
    }
}

enum StatCardVisualStyle {
    case pawPattern
    case heartbeat
    case photoStack
    case alert
}

// MARK: - Dashboard Pet Card

struct DashboardPetCard: View {
    let pet: Pet
    let index: Int
    let onTap: () -> Void
    @State private var isPressed = false
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Pet avatar - simplified
                petAvatarView
                
                // Pet info
                petInfoView
                
                // Health indicator
                healthIndicatorView
            }
            .frame(width: 140, height: 160)
            .padding(16)
            .background(cardBackground)
            .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
    
    // MARK: - Subviews
    
    private var petAvatarView: some View {
        ZStack {
            // Outer glow
            Circle()
                .fill(glowGradient)
                .frame(width: 100, height: 100)
            
            // Main avatar
            Circle()
                .fill(avatarLinearGradient)
                .frame(width: 80, height: 80)
                .overlay(Circle().stroke(.white.opacity(0.3), lineWidth: 2))
                .shadow(color: petColor.opacity(0.4), radius: 8, x: 0, y: 4)
            
            // Icon or initial
            petIconView
        }
    }
    
    private var petInfoView: some View {
        VStack(spacing: 6) {
            Text(pet.name)
                .font(.system(size: 16, weight: .semibold))
                .foregroundStyle(.primary)
                .lineLimit(1)
            
            Text(pet.breed ?? pet.species.capitalized)
                .font(.system(size: 12, weight: .medium))
                .foregroundStyle(.secondary)
                .lineLimit(1)
        }
    }
    
    private var healthIndicatorView: some View {
        HStack(spacing: 6) {
            Image(systemName: "heart.fill")
                .font(.system(size: 10))
                .foregroundStyle(healthColor)
            
            Text("\(Int(pet.healthScore * 100))% Healthy")
                .font(.system(size: 11, weight: .medium))
                .foregroundStyle(healthColor)
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 4)
        .background(healthIndicatorBackground)
    }
    
    private var petIconView: some View {
        Group {
            if let icon = speciesIcon {
                Image(systemName: icon)
                    .font(.system(size: 28, weight: .semibold))
                    .foregroundStyle(.white)
            } else {
                Text(String(pet.name.prefix(1)).uppercased())
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundStyle(.white)
            }
        }
        .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Background Views
    
    private var glowGradient: RadialGradient {
        RadialGradient(
            colors: [
                petColor.opacity(0.3),
                petColor.opacity(0.1),
                Color.clear
            ],
            center: .center,
            startRadius: 30,
            endRadius: 50
        )
    }
    
    private var avatarLinearGradient: LinearGradient {
        LinearGradient(
            colors: avatarGradient,
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var healthIndicatorBackground: some View {
        Capsule()
            .fill(healthColor.opacity(0.1))
            .overlay(
                Capsule()
                    .stroke(healthColor.opacity(0.3), lineWidth: 1)
            )
    }
    
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 20, style: .continuous)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 20, style: .continuous)
                    .stroke(petColor.opacity(0.2), lineWidth: 1)
            )
    }
    
    private var petColor: Color {
        switch pet.species.lowercased() {
        case "dog": return .blue
        case "cat": return .purple
        case "bird": return .orange
        case "fish": return .cyan
        case "rabbit": return .pink
        default: return .teal
        }
    }
    
    private var avatarGradient: [Color] {
        switch pet.species.lowercased() {
        case "dog": return [Color.blue, Color.blue.opacity(0.7)]
        case "cat": return [Color.purple, Color.indigo]
        case "bird": return [Color.orange, Color.red]
        case "fish": return [Color.cyan, Color.blue]
        case "rabbit": return [Color.pink, Color.purple.opacity(0.7)]
        default: return [Color.teal, Color.blue.opacity(0.7)]
        }
    }
    
    private var speciesIcon: String? {
        switch pet.species.lowercased() {
        case "dog": return "pawprint.fill"
        case "cat": return "cat.fill"
        case "bird": return "bird.fill"
        case "fish": return "fish.fill"
        case "rabbit": return "hare.fill"
        default: return nil
        }
    }
    
    private var healthColor: Color {
        if pet.healthScore > 0.8 {
            return .green
        } else if pet.healthScore > 0.6 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Enhanced Health Event Card

struct EnhancedHealthEventCard: View {
    let event: HealthEvent
    let index: Int
    
    var body: some View {
        HStack(spacing: 16) {
            // Event type indicator
            ZStack {
                Circle()
                    .fill(event.type.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: event.type.icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundStyle(event.type.color)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(event.title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundStyle(.primary)
                        .lineLimit(1)
                    
                    Spacer()
                    
                    Text(event.timeAgo)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundStyle(.tertiary)
                }
                
                Text(event.description)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundStyle(.secondary)
                    .lineLimit(2)
                
                if let petName = event.petName {
                    Text(petName)
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundStyle(.blue)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(.blue.opacity(0.1))
                        )
                }
            }
            
            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(event.type.color.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Enhanced Button Style

struct EnhancedButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Dashboard ViewModel (Enhanced with Comprehensive Data Manager)

@MainActor
class DashboardViewModel: ObservableObject {
    @Published var totalPets: Int = 0
    @Published var averageHealthScore: Double = 0.0
    @Published var totalMemories: Int = 0
    @Published var healthAlerts: Int = 0
    @Published var pets: [Pet] = []
    @Published var recentHealthEvents: [HealthEvent] = []
    
    // Reference to centralized data manager
    private let dataManager = PetDataManager.shared
    
    init() {
        setupDataBinding()
        loadDashboardData()
    }
    
    private func setupDataBinding() {
        // Bind to PetDataManager updates
        dataManager.$pets
            .receive(on: DispatchQueue.main)
            .sink { [weak self] pets in
                self?.pets = pets
                self?.updateDashboardMetrics()
            }
            .store(in: &cancellables)
        
        dataManager.$selectedPet
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateDashboardMetrics()
            }
            .store(in: &cancellables)
        
        // Listen for data updates
        NotificationCenter.default.addObserver(
            forName: .petDataUpdated,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.updateDashboardMetrics()
            }
        }
    }
    
    private func loadDashboardData() {
        // Load real data from PetDataManager
        pets = dataManager.pets
        updateDashboardMetrics()
        loadRecentHealthEvents()
    }
    
    private func updateDashboardMetrics() {
        totalPets = pets.count
        
        // Calculate comprehensive health scores using PetDataManager
        if !pets.isEmpty {
            averageHealthScore = dataManager.selectedPetHealthScore
            healthAlerts = dataManager.selectedPetHealthAlerts
            totalMemories = pets.reduce(0) { $0 + $1.memoryIDs.count }
        } else {
            averageHealthScore = 0.0
            healthAlerts = 0
            totalMemories = 0
        }
    }
    
    private func loadRecentHealthEvents() {
        // Generate health events based on actual pet data
        recentHealthEvents = generateHealthEvents()
    }
    
    private func generateHealthEvents() -> [HealthEvent] {
        var events: [HealthEvent] = []
        
        for pet in pets {
            // Vaccination events
            if let lastVaccination = pet.vaccinationRecords.last {
                events.append(HealthEvent(
                    id: "vacc_\(pet.id)",
                    title: "Vaccination Update",
                    description: "Recent vaccination: \(lastVaccination.vaccineName)",
                    type: .vaccination,
                    petName: pet.name,
                    timeAgo: timeAgoString(from: lastVaccination.dateAdministered)
                ))
            }
            
            // Medication events
            if !pet.medications.isEmpty {
                events.append(HealthEvent(
                    id: "med_\(pet.id)",
                    title: "Medication Schedule",
                    description: "Active medications: \(pet.medications.count)",
                    type: .medication,
                    petName: pet.name,
                    timeAgo: "Today"
                ))
            }
            
            // Health checkup events
            if let lastCheckup = pet.lastCheckupDate {
                events.append(HealthEvent(
                    id: "checkup_\(pet.id)",
                    title: "Health Checkup",
                    description: "Last veterinary checkup completed",
                    type: .checkup,
                    petName: pet.name,
                    timeAgo: timeAgoString(from: lastCheckup)
                ))
            }
            
            // Health alert events
            for alert in pet.healthAlerts where alert.isActive {
                events.append(HealthEvent(
                    id: "alert_\(pet.id)_\(alert.id)",
                    title: alert.title,
                    description: alert.description,
                    type: alert.severity == .high ? .emergency : .routine,
                    petName: pet.name,
                    timeAgo: timeAgoString(from: alert.triggeredAt)
                ))
            }
        }
        
        // Sort by most recent and return top 5
        return events.sorted { event1, event2 in
            // Simple sort by pet name for now - could be enhanced with actual dates
            event1.petName ?? "" < event2.petName ?? ""
        }.prefix(5).map { $0 }
    }
    
    private func timeAgoString(from date: Date) -> String {
        let interval = Date().timeIntervalSince(date)
        let days = Int(interval / 86400)
        let hours = Int((interval.truncatingRemainder(dividingBy: 86400)) / 3600)
        
        if days > 0 {
            return "\(days) day\(days == 1 ? "" : "s") ago"
        } else if hours > 0 {
            return "\(hours) hour\(hours == 1 ? "" : "s") ago"
        } else {
            return "Recently"
        }
    }
    
    // MARK: - Cancellables
    private var cancellables = Set<AnyCancellable>()
    
    deinit {
        cancellables.removeAll()
    }
}

// MARK: - Health Event Model (Enhanced)

struct HealthEvent: Identifiable {
    let id: String
    let title: String
    let description: String
    let type: HealthEventType
    let petName: String?
    let timeAgo: String
}

enum HealthEventType {
    case vaccination
    case checkup
    case medication
    case emergency
    case routine
    
    var color: Color {
        switch self {
        case .vaccination: return .green
        case .checkup: return .blue
        case .medication: return .orange
        case .emergency: return .red
        case .routine: return .purple
        }
    }
    
    var icon: String {
        switch self {
        case .vaccination: return "syringe.fill"
        case .checkup: return "stethoscope"
        case .medication: return "pills.fill"
        case .emergency: return "exclamationmark.triangle.fill"
        case .routine: return "checkmark.circle.fill"
        }
    }
}

// MARK: - Notification Names Extension (removed duplicate declarations)

// MARK: - Simple Dashboard Pet Card

struct SimpleDashboardPetCard: View {
    let pet: Pet
    let onTap: () -> Void
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Pet avatar - simple circular image
                petAvatarView
                
                // Pet name and breed
                VStack(spacing: 4) {
                    Text(pet.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    Text(pet.breed ?? pet.species.capitalized)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                // Simple health indicator
                healthIndicatorView
            }
            .frame(width: 120, height: 140)
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(colorScheme == .dark ? Color(.systemGray6) : Color.white)
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var petAvatarView: some View {
        ZStack {
            Circle()
                .fill(petColor.opacity(0.2))
                .frame(width: 60, height: 60)
            
            if let profileImageURL = pet.profileImageURL,
               let url = URL(string: profileImageURL) {
                AsyncImage(url: url) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    petInitialView
                }
                .frame(width: 60, height: 60)
                .clipShape(Circle())
            } else {
                petInitialView
            }
        }
    }
    
    private var petInitialView: some View {
        Text(String(pet.name.prefix(1)).uppercased())
            .font(.system(size: 24, weight: .bold))
            .foregroundColor(petColor)
    }
    
    private var healthIndicatorView: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(healthColor)
                .frame(width: 6, height: 6)
            
            Text(healthStatusText)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.secondary)
        }
    }
    
    private var petColor: Color {
        switch pet.species.lowercased() {
        case "dog": return .blue
        case "cat": return .orange
        case "bird": return .green
        case "fish": return .cyan
        case "rabbit": return .purple
        default: return .gray
        }
    }
    
    private var healthColor: Color {
        let score = calculateHealthScore()
        if score >= 0.8 { return .green }
        else if score >= 0.6 { return .yellow }
        else { return .red }
    }
    
    private var healthStatusText: String {
        let score = calculateHealthScore()
        if score >= 0.8 { return "Great" }
        else if score >= 0.6 { return "Good" }
        else { return "Needs Care" }
    }
    
    private func calculateHealthScore() -> Double {
        // Simple calculation based on available data
        var score = 0.85 // Base score
        
        // Deduct for medical conditions
        score -= Double(pet.medicalConditions.count) * 0.1
        
        // Deduct for medications (indicating ongoing treatment)
        score -= Double(pet.medications.count) * 0.05
        
        return max(0.0, min(1.0, score))
    }
}

#Preview {
    PetDashboardView(tabSelection: .constant(0))
}

