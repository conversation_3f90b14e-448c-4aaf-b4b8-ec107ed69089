//
//  PremiumShowcaseView.swift
//  PetCapsule
//
//  Showcase view demonstrating monetization potential
//

import SwiftUI

struct PremiumShowcaseView: View {
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var showUpgradeSheet = false
    @State private var selectedPet: Pet?
    
    // Sample pets for demonstration
    private let samplePets: [Pet] = [
        Pet(
            name: "<PERSON>",
            species: "dog",
            breed: "Golden Retriever",
            age: 36,
            weight: 25.5,
            healthScore: 0.85,
            subscriptionTier: "free"
        ),
        <PERSON>(
            name: "Whiskers",
            species: "cat",
            breed: "Persian",
            age: 24,
            weight: 4.2,
            healthScore: 0.92,
            subscriptionTier: "premium"
        ),
        <PERSON>(
            name: "<PERSON>",
            species: "dog",
            breed: "Labrador",
            age: 60,
            weight: 30.0,
            healthScore: 0.76,
            subscriptionTier: "free"
        )
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 30) {
                    // Revenue Metrics Header
                    revenueMetricsSection
                    
                    // Premium Pet Cards Showcase
                    petCardsShowcaseSection
                    
                    // Monetization Features
                    monetizationFeaturesSection
                    
                    // Revenue Projections
                    revenueProjectionsSection
                }
                .padding(.vertical, 20)
            }
            .navigationTitle("Premium Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Upgrade") {
                        showUpgradeSheet = true
                    }
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        LinearGradient(
                            colors: [Color.purple, Color.pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                    )
                }
            }
            .sheet(isPresented: $showUpgradeSheet) {
                SubscriptionView()
            }
        }
    }
    
    // MARK: - Revenue Metrics Section
    
    private var revenueMetricsSection: some View {
        VStack(spacing: 16) {
            Text("Revenue Potential")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.primary)
            
            HStack(spacing: 16) {
                revenueMetricCard(
                    title: "Monthly Target",
                    value: "$2M",
                    subtitle: "100K Premium Users",
                    color: .green,
                    icon: "target"
                )
                
                revenueMetricCard(
                    title: "Current ARR",
                    value: "$12M",
                    subtitle: "50K Active Users",
                    color: .blue,
                    icon: "chart.line.uptrend.xyaxis"
                )
            }
            
            HStack(spacing: 16) {
                revenueMetricCard(
                    title: "Conversion Rate",
                    value: "18%",
                    subtitle: "Free → Premium",
                    color: .purple,
                    icon: "arrow.triangle.2.circlepath"
                )
                
                revenueMetricCard(
                    title: "Marketplace",
                    value: "$300K",
                    subtitle: "Monthly Revenue",
                    color: .orange,
                    icon: "cart.fill"
                )
            }
        }
        .padding(.horizontal, 20)
    }
    
    private func revenueMetricCard(title: String, value: String, subtitle: String, color: Color, icon: String) -> some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(color)
                
                Spacer()
                
                Text(value)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(color)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(color.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Pet Cards Showcase Section
    
    private var petCardsShowcaseSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Premium Pet Cards")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("Swipe to explore →")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 20) {
                    ForEach(samplePets, id: \.id) { pet in
                        if #available(iOS 18.0, *) {
                            PremiumPetDashboardCard(
                                pet: pet,
                                onUpgrade: {
                                    showUpgradeSheet = true
                                },
                                onAlertAction: { alertId in
                                    print("Alert action: \(alertId)")
                                },
                                onMarketplaceClick: { itemId in
                                    print("Marketplace click: \(itemId)")
                                },
                                onTap: {
                                    print("Pet card tapped")
                                }
                            )
                        } else {
                            // Fallback for iOS 17 and earlier
                            Text("Premium Dashboard requires iOS 18.0+")
                                .frame(width: 300, height: 400)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(24)
                        }
                    }
                }
                .padding(.horizontal, 20)
                .scrollTargetLayout()
            }
            .scrollTargetBehavior(.viewAligned)
            
            // Feature Highlights
            VStack(spacing: 12) {
                Text("Key Monetization Features")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                HStack(spacing: 16) {
                    featureHighlight(
                        icon: "crown.fill",
                        title: "Premium Upgrades",
                        description: "Prominent upgrade CTAs"
                    )
                    
                    featureHighlight(
                        icon: "bell.fill",
                        title: "Health Alerts",
                        description: "Drive engagement"
                    )
                    
                    featureHighlight(
                        icon: "cart.fill",
                        title: "Marketplace",
                        description: "AI recommendations"
                    )
                }
            }
            .padding(.horizontal, 20)
        }
    }
    
    private func featureHighlight(icon: String, title: String, description: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(.purple)
            
            Text(title)
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.primary)
            
            Text(description)
                .font(.system(size: 10))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.purple.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Monetization Features Section
    
    private var monetizationFeaturesSection: some View {
        VStack(spacing: 16) {
            Text("Monetization Strategy")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                monetizationFeatureCard(
                    title: "Freemium Model",
                    description: "1 pet limit, basic features only",
                    price: "Free",
                    features: ["Basic health tracking", "Limited memories", "Standard support"],
                    color: .gray,
                    conversionRate: "Base tier"
                )
                
                monetizationFeatureCard(
                    title: "Premium Subscription",
                    description: "Full AI features, unlimited pets",
                    price: "$29.99/month",
                    features: ["AI health analysis", "Unlimited memories", "Priority support", "Advanced analytics"],
                    color: .purple,
                    conversionRate: "18% conversion"
                )
                
                monetizationFeatureCard(
                    title: "Family Plan",
                    description: "Multi-user access, up to 10 pets",
                    price: "$49.99/month",
                    features: ["Everything in Premium", "Family sharing", "Multiple users", "Advanced reporting"],
                    color: .blue,
                    conversionRate: "8% of Premium users"
                )
                
                monetizationFeatureCard(
                    title: "Professional Plan",
                    description: "For vets, breeders, and pet services",
                    price: "$129.99/month",
                    features: ["Unlimited pets", "Business tools", "API access", "White-label options"],
                    color: .orange,
                    conversionRate: "3% of user base"
                )
            }
        }
        .padding(.horizontal, 20)
    }
    
    private func monetizationFeatureCard(title: String, description: String, price: String, features: [String], color: Color, conversionRate: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.primary)
                    
                    Text(description)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text(price)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(color)
                    
                    Text(conversionRate)
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                }
            }
            
            VStack(alignment: .leading, spacing: 4) {
                ForEach(features, id: \.self) { feature in
                    HStack(spacing: 8) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 12))
                            .foregroundColor(color)
                        
                        Text(feature)
                            .font(.system(size: 12))
                            .foregroundColor(.primary)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(color.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(color.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Revenue Projections Section
    
    private var revenueProjectionsSection: some View {
        VStack(spacing: 16) {
            Text("Revenue Projections")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                revenueProjectionCard(
                    period: "Month 1",
                    users: "10K",
                    revenue: "$200K",
                    description: "Initial launch with freemium model"
                )
                
                revenueProjectionCard(
                    period: "Month 6",
                    users: "50K",
                    revenue: "$1M",
                    description: "Premium features driving conversions"
                )
                
                revenueProjectionCard(
                    period: "Month 12",
                    users: "100K",
                    revenue: "$2M",
                    description: "Target achieved with full feature set"
                )
            }
        }
        .padding(.horizontal, 20)
    }
    
    private func revenueProjectionCard(period: String, users: String, revenue: String, description: String) -> some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 4) {
                Text(period)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(revenue)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.green)
                
                Text("\(users) users")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.green.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

#Preview {
    PremiumShowcaseView()
} 