//
//  EmergencyPlanningView.swift
//  PetCapsule
//
//  Emergency planning dashboard with contacts, evacuation plans, and emergency kits
//

import SwiftUI
import CoreLocation
import MapKit
#if canImport(MessageUI)
import MessageUI
#endif

struct EmergencyModeView: View {
    let emergencyType: EmergencyType
    let selectedPet: Pet?
    
    @Environment(\.dismiss) private var dismiss
    @StateObject private var locationManager = LocationManager()
    @StateObject private var emergencyService = EmergencyCallService.shared
    @State private var nearbyVets: [VeterinaryClinic] = []
    @State private var isLoadingVets = false
    @State private var showingCallConfirmation = false
    @State private var selectedVet: VeterinaryClinic?
    @State private var showingFirstAid = false
    @State private var showingPetInfo = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // EMERGENCY HEADER
                    emergencyHeader
                    
                    // IMMEDIATE ACTIONS
                    immediateActionsSection
                    
                    // PET INFORMATION CARD
                    if let pet = selectedPet {
                        petInformationCard(pet)
                    }
                    
                    // NEARBY EMERGENCY SERVICES
                    nearbyServicesSection
                    
                    // FIRST AID GUIDANCE
                    firstAidSection
                    
                    // EMERGENCY CONTACTS
                    emergencyContactsSection
                }
                .padding()
            }
            .navigationTitle("Always Prepared")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
            .background(Color(.systemGroupedBackground))
        }
        .onAppear {
            loadNearbyVets()
        }
        .sheet(isPresented: $showingFirstAid) {
            FirstAidGuidesView(selectedPet: selectedPet, emergencyType: emergencyType)
        }
        .sheet(isPresented: $showingPetInfo) {
            if let pet = selectedPet {
                EmergencyPetInfoView(pet: pet)
            }
        }
        .alert("Call Emergency Vet?", isPresented: $showingCallConfirmation) {
            if let vet = selectedVet {
                Button("Call \(vet.name)") {
                    callVet(vet)
                }
                Button("Cancel", role: .cancel) { }
            }
        } message: {
            if let vet = selectedVet {
                Text("This will call \(vet.name) at \(vet.phoneNumber)")
            }
        }
    }
    
    // MARK: - Emergency Header
    private var emergencyHeader: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: emergencyType.icon)
                    .font(.title)
                    .foregroundColor(emergencyType.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(emergencyType.displayName)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Emergency assistance for \(selectedPet?.name ?? "your pet")")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            // Emergency status indicator
            HStack {
                Circle()
                    .fill(Color.red)
                    .frame(width: 8, height: 8)
                
                Text("EMERGENCY MODE ACTIVE")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.red)
                
                Spacer()
                
                Text(Date().formatted(.dateTime.hour().minute()))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(emergencyType.color.opacity(0.3), lineWidth: 2)
        )
    }
    
    // MARK: - Immediate Actions
    private var immediateActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🚨 Immediate Actions")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 8) {
                immediateActionButton(
                    title: "Call 911 (Life Threatening)",
                    subtitle: "For severe emergencies",
                    icon: "phone.fill",
                    color: .red,
                    isPrimary: true
                ) {
                    call911()
                }
                
                switch emergencyType {
                case .poison:
                    immediateActionButton(
                        title: "Pet Poison Control",
                        subtitle: "(************* - 24/7 Hotline",
                        icon: "exclamationmark.triangle.fill",
                        color: .purple
                    ) {
                        callPoisonControl()
                    }
                    
                case .veterinary, .injury:
                    immediateActionButton(
                        title: "Call Emergency Vet",
                        subtitle: "Nearest 24/7 animal hospital",
                        icon: "cross.fill",
                        color: .orange
                    ) {
                        if let nearestVet = nearbyVets.first(where: { $0.is24Hour }) {
                            selectedVet = nearestVet
                            showingCallConfirmation = true
                        }
                    }
                    
                case .lost:
                    immediateActionButton(
                        title: "Report Lost Pet",
                        subtitle: "Activate community alerts",
                        icon: "location.magnifyingglass",
                        color: .yellow
                    ) {
                        reportLostPet()
                    }
                    
                default:
                    immediateActionButton(
                        title: "Get Help",
                        subtitle: "Contact emergency services",
                        icon: "phone.fill",
                        color: .blue
                    ) {
                        getGeneralHelp()
                    }
                }
                
                immediateActionButton(
                    title: "First Aid Instructions",
                    subtitle: "Step-by-step emergency care",
                    icon: "book.fill",
                    color: .green
                ) {
                    showingFirstAid = true
                }
            }
        }
    }
    
    private func immediateActionButton(
        title: String,
        subtitle: String,
        icon: String,
        color: Color,
        isPrimary: Bool = false,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(isPrimary ? .white : color)
                    .frame(width: 40)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(isPrimary ? .white : .primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(isPrimary ? .white.opacity(0.8) : .secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(isPrimary ? .white.opacity(0.7) : .secondary)
            }
            .padding()
            .background(isPrimary ? color : Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(color.opacity(isPrimary ? 0 : 0.3), lineWidth: isPrimary ? 0 : 1)
            )
        }
    }
    
    // MARK: - Pet Information Card
    private func petInformationCard(_ pet: Pet) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("🐕 Pet Information")
                    .font(.headline)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("Share with Vet") {
                    showingPetInfo = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                petInfoRow("Name", pet.name)
                petInfoRow("Species", pet.species.capitalized)
                if let breed = pet.breed {
                    petInfoRow("Breed", breed)
                }
                let age = pet.ageInYears
                petInfoRow("Age", "\(String(format: "%.1f", age)) years")
                if let weight = pet.weight {
                    petInfoRow("Weight", "\(String(format: "%.1f", weight)) lbs")
                }
                
                if !pet.allergies.isEmpty {
                    petInfoRow("Allergies", pet.allergies.joined(separator: ", "), isAlert: true)
                }
                
                if !pet.medications.isEmpty {
                    let activeMeds = pet.medications.filter { $0.isActive }
                    if !activeMeds.isEmpty {
                        petInfoRow("Medications", activeMeds.map { $0.name }.joined(separator: ", "), isAlert: true)
                    }
                }
                
                if let microchip = pet.microchipId {
                    petInfoRow("Microchip", microchip)
                }
                
                if !pet.emergencyContacts.isEmpty {
                    petInfoRow("Emergency Contact", pet.emergencyContactInfo)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    private func petInfoRow(_ label: String, _ value: String, isAlert: Bool = false) -> some View {
        HStack {
            Text(label + ":")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.caption)
                .foregroundColor(isAlert ? .red : .primary)
                .fontWeight(isAlert ? .semibold : .regular)
            
            Spacer()
        }
    }
    
    // MARK: - Nearby Services
    private var nearbyServicesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("🏥 Nearby Emergency Services")
                    .font(.headline)
                    .fontWeight(.bold)
                
                Spacer()
                
                if isLoadingVets {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            if nearbyVets.isEmpty && !isLoadingVets {
                Text("No nearby emergency services found")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
            } else {
                ForEach(nearbyVets.prefix(3), id: \.id) { vet in
                    vetServiceCard(vet)
                }
            }
        }
    }
    
    private func vetServiceCard(_ vet: VeterinaryClinic) -> some View {
        Button(action: {
            selectedVet = vet
            showingCallConfirmation = true
        }) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(vet.name)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(vet.address)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        if vet.is24Hour {
                            Text("24/7")
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(4)
                        }
                        
                        Text("\(String(format: "%.1f", vet.distance)) mi")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                HStack {
                    Image(systemName: "phone.fill")
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Text(vet.phoneNumber)
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    if vet.rating > 0 {
                        HStack(spacing: 2) {
                            Image(systemName: "star.fill")
                                .font(.caption2)
                                .foregroundColor(.yellow)
                            
                            Text("\(String(format: "%.1f", vet.rating))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
            )
        }
    }
    
    // MARK: - First Aid Section
    private var firstAidSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🩹 Quick First Aid")
                .font(.headline)
                .fontWeight(.bold)
            
            Button(action: {
                showingFirstAid = true
            }) {
                HStack {
                    Image(systemName: "cross.fill")
                        .font(.title2)
                        .foregroundColor(.red)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Emergency First Aid Guide")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text("Step-by-step instructions for common emergencies")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
            }
        }
    }
    
    // MARK: - Emergency Contacts
    private var emergencyContactsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📞 Emergency Contacts")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 8) {
                if let pet = selectedPet, !pet.emergencyContacts.isEmpty {
                    ForEach(Array(pet.emergencyContacts.prefix(3)), id: \.id) { contact in
                        emergencyContactRow(contact)
                    }
                } else {
                    Text("No emergency contacts configured")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                }
            }
        }
    }
    
    private func emergencyContactRow(_ contact: EmergencyContact) -> some View {
        Button(action: {
            callContact(contact)
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(contact.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(contact.type.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Text(contact.phoneNumber)
                    .font(.caption)
                    .foregroundColor(.blue)
                
                Image(systemName: "phone.fill")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Actions
    private func loadNearbyVets() {
        guard let location = locationManager.currentLocation else { return }
        
        isLoadingVets = true
        
        Task {
            do {
                let vets = try await VetSearchService.shared.findNearbyVets(
                    location: location.coordinate,
                    radius: 25000, // 25km
                    emergencyOnly: true
                )
                
                await MainActor.run {
                    self.nearbyVets = vets
                    self.isLoadingVets = false
                }
            } catch {
                await MainActor.run {
                    self.isLoadingVets = false
                    print("❌ Failed to load nearby vets: \(error)")
                }
            }
        }
    }
    
    private func call911() {
        if let url = URL(string: "tel://911") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callPoisonControl() {
        if let url = URL(string: "tel://8557647661") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callVet(_ vet: VeterinaryClinic) {
        let phoneNumber = vet.phoneNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        if let url = URL(string: "tel://\(phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callContact(_ contact: EmergencyContact) {
        let phoneNumber = contact.phoneNumber.replacingOccurrences(of: "[^0-9]", with: "", options: NSString.CompareOptions.regularExpression)
        if let url = URL(string: "tel://\(phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func reportLostPet() {
        // Implementation for reporting lost pet
        print("📢 Reporting lost pet...")
    }
    
    private func getGeneralHelp() {
        // Implementation for general emergency help
        print("🆘 Getting general emergency help...")
    }
}

// MARK: - Supporting Views

struct FirstAidGuidesView: View {
    let selectedPet: Pet?
    let emergencyType: EmergencyType?
    
    @Environment(\.dismiss) private var dismiss
    
    init(selectedPet: Pet?, emergencyType: EmergencyType? = nil) {
        self.selectedPet = selectedPet
        self.emergencyType = emergencyType
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Emergency-specific guides based on type
                    if let emergencyType = emergencyType {
                        emergencySpecificGuide(emergencyType)
                    }
                    
                    // Common first aid guides
                    commonFirstAidGuides
                }
                .padding()
            }
            .navigationTitle("First Aid Guide")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
    
    private func emergencySpecificGuide(_ type: EmergencyType) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🚨 \(type.displayName)")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(type.color)
            
            switch type {
            case .poison:
                poisonEmergencyGuide
            case .injury:
                injuryEmergencyGuide
            case .veterinary:
                veterinaryEmergencyGuide
            default:
                generalEmergencyGuide
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    private var poisonEmergencyGuide: some View {
        VStack(alignment: .leading, spacing: 8) {
            firstAidStep("1", "DO NOT induce vomiting unless instructed by poison control")
            firstAidStep("2", "Call Pet Poison Control: (*************")
            firstAidStep("3", "Have product packaging ready for reference")
            firstAidStep("4", "Note time of ingestion and amount consumed")
            firstAidStep("5", "Follow poison control instructions exactly")
            
            Text("⚠️ Common pet toxins: chocolate, grapes, onions, xylitol, antifreeze")
                .font(.caption)
                .foregroundColor(.red)
                .padding(.top, 8)
        }
    }
    
    private var injuryEmergencyGuide: some View {
        VStack(alignment: .leading, spacing: 8) {
            firstAidStep("1", "Keep your pet calm and still")
            firstAidStep("2", "Control bleeding with clean cloth and pressure")
            firstAidStep("3", "Do not remove objects embedded in wounds")
            firstAidStep("4", "Cover wounds with clean, damp cloth")
            firstAidStep("5", "Transport carefully to emergency vet")
        }
    }
    
    private var veterinaryEmergencyGuide: some View {
        VStack(alignment: .leading, spacing: 8) {
            firstAidStep("1", "Assess breathing and consciousness")
            firstAidStep("2", "Keep airway clear")
            firstAidStep("3", "Apply pressure to bleeding wounds")
            firstAidStep("4", "Keep pet warm and calm")
            firstAidStep("5", "Get to emergency vet immediately")
        }
    }
    
    private var generalEmergencyGuide: some View {
        VStack(alignment: .leading, spacing: 8) {
            firstAidStep("1", "Stay calm and assess the situation")
            firstAidStep("2", "Ensure your safety first")
            firstAidStep("3", "Check pet's vital signs")
            firstAidStep("4", "Contact emergency veterinary services")
            firstAidStep("5", "Follow professional guidance")
        }
    }
    
    private var commonFirstAidGuides: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("📋 Common Emergency Procedures")
                .font(.headline)
                .fontWeight(.bold)
            
            firstAidCard("Choking", "Remove visible objects, lift hind legs, compress chest")
            firstAidCard("Seizures", "Clear area, time seizure, do not restrain, contact vet")
            firstAidCard("Heatstroke", "Move to cool area, wet with cool water, fan, vet immediately")
            firstAidCard("Bleeding", "Apply pressure with clean cloth, elevate if possible")
            firstAidCard("Broken Bones", "Immobilize, use makeshift splint, transport carefully")
        }
    }
    
    private func firstAidStep(_ number: String, _ instruction: String) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Text(number)
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 20, height: 20)
                .background(Circle().fill(Color.red))
            
            Text(instruction)
                .font(.subheadline)
                .foregroundColor(.primary)
        }
    }
    
    private func firstAidCard(_ title: String, _ description: String) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.semibold)
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

struct EmergencyPetInfoView: View {
    let pet: Pet
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text(pet.quickMedicalSummary)
                        .font(.body)
                        .padding()
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                    
                    // Share buttons
                    VStack(spacing: 12) {
                        Button("Share via Text") {
                            shareViaText()
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                        
                        Button("Email to Vet") {
                            emailToVet()
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                }
                .padding()
            }
            .navigationTitle("Pet Emergency Info")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
    
    private func shareViaText() {
        let message = pet.quickMedicalSummary
        if let url = URL(string: "sms:?body=\(message.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") {
            UIApplication.shared.open(url)
        }
    }
    
    private func emailToVet() {
        let subject = "Emergency Pet Information - \(pet.name)"
        let body = pet.quickMedicalSummary
        if let url = URL(string: "mailto:?subject=\(subject.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")&body=\(body.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") {
            UIApplication.shared.open(url)
        }
    }
}

// MARK: - Supporting Models

struct VeterinaryClinic: Identifiable, Codable {
    let id: String
    let name: String
    let address: String
    let phoneNumber: String
    let is24Hour: Bool
    let distance: Double // in miles
    let rating: Double
    let specialties: [String]
    let coordinate: CLLocationCoordinate2D
    
    init(
        id: String = UUID().uuidString,
        name: String,
        address: String,
        phoneNumber: String,
        is24Hour: Bool = false,
        distance: Double = 0.0,
        rating: Double = 0.0,
        specialties: [String] = [],
        coordinate: CLLocationCoordinate2D = CLLocationCoordinate2D(latitude: 0, longitude: 0)
    ) {
        self.id = id
        self.name = name
        self.address = address
        self.phoneNumber = phoneNumber
        self.is24Hour = is24Hour
        self.distance = distance
        self.rating = rating
        self.specialties = specialties
        self.coordinate = coordinate
    }
}

// LocationManager is defined in EnhancedMapView.swift

#Preview {
    EmergencyModeView(
        emergencyType: .veterinary,
        selectedPet: Pet.samplePet
    )
}
