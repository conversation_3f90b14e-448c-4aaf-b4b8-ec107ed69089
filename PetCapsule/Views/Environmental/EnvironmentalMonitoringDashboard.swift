//
//  EnvironmentalMonitoringDashboard.swift
//  PetCapsule
//
//  Unified environmental monitoring dashboard with real-time data and alerts
//

import SwiftUI
import Charts

// MARK: - Quick Action Button

struct EnvironmentalQuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct EnvironmentalMonitoringDashboard: View {
    @StateObject private var monitoringService = UnifiedEnvironmentalMonitoringService.shared
    @State private var selectedTimeRange: TimeRange = .today
    @State private var showingSettings = false
    @State private var showingAlertDetails = false
    @State private var selectedAlert: EnvironmentalAlert?
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Monitoring Status Header
                    monitoringStatusHeader
                    
                    // Current Environmental State
                    if let currentState = monitoringService.currentEnvironmentalState {
                        currentEnvironmentalStateCard(currentState)
                    }
                    
                    // Active Alerts
                    if !monitoringService.activeAlerts.isEmpty {
                        activeAlertsSection
                    }
                    
                    // Environmental Trends
                    environmentalTrendsSection
                    
                    // Risk Assessment
                    riskAssessmentSection
                    
                    // Quick Actions
                    quickActionsSection
                }
                .padding()
            }
            .navigationTitle("Safe Adventures")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: toggleMonitoring) {
                        Image(systemName: monitoringService.isMonitoring ? "pause.circle.fill" : "play.circle.fill")
                            .foregroundColor(monitoringService.isMonitoring ? .orange : .green)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingSettings = true }) {
                        Image(systemName: "gear")
                    }
                }
            }
            .sheet(isPresented: $showingSettings) {
                EnvironmentalMonitoringSettingsView()
            }
            .sheet(isPresented: $showingAlertDetails) {
                if let alert = selectedAlert {
                    EnvironmentalAlertDetailsView(alert: alert)
                }
            }
            .onAppear {
                if !monitoringService.isMonitoring {
                    Task {
                        await monitoringService.startMonitoring()
                    }
                }
            }
        }
    }
    
    // MARK: - Monitoring Status Header
    
    private var monitoringStatusHeader: some View {
        VStack(spacing: 12) {
            HStack {
                // Connection Status
                HStack(spacing: 8) {
                    Image(systemName: monitoringService.connectionStatus.icon)
                        .foregroundColor(monitoringService.connectionStatus.color)
                    
                    Text(monitoringService.connectionStatus.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(monitoringService.connectionStatus.color)
                }
                
                Spacer()
                
                // Last Update Time
                if let lastUpdate = monitoringService.lastUpdateTime {
                    Text("Updated \(lastUpdate.formatted(.relative(presentation: .named)))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Risk Level Indicator
            HStack {
                Image(systemName: monitoringService.getCurrentRiskLevel().icon)
                    .font(.title2)
                    .foregroundColor(monitoringService.getCurrentRiskLevel().color)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Current Risk Level")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(monitoringService.getCurrentRiskLevel().displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(monitoringService.getCurrentRiskLevel().color)
                }
                
                Spacer()
                
                // Active Alerts Count
                if monitoringService.getActiveAlertsCount() > 0 {
                    VStack {
                        Text("\(monitoringService.getActiveAlertsCount())")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.red)
                        
                        Text("Active Alerts")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    // MARK: - Current Environmental State
    
    private func currentEnvironmentalStateCard(_ state: EnvironmentalState) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Current Conditions")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                EnvironmentalMetricCard(
                    title: "Temperature",
                    value: "\(state.weather.temperature)°F",
                    icon: "thermometer",
                    color: getTemperatureColor(state.weather.temperature)
                )
                
                EnvironmentalMetricCard(
                    title: "Air Quality",
                    value: "AQI \(state.airQuality.index)",
                    icon: "aqi.medium",
                    color: getAQIColor(state.airQuality.index)
                )
                
                EnvironmentalMetricCard(
                    title: "Pollen",
                    value: getPollenLevel(state.pollen),
                    icon: "leaf.fill",
                    color: getPollenColor(state.pollen)
                )
                
                EnvironmentalMetricCard(
                    title: "Walk Score",
                    value: "\(Int(state.environmentalScore.overallScore * 100))",
                    icon: "figure.walk",
                    color: getScoreColor(state.environmentalScore.overallScore)
                )
            }
            
            // Recommendations
            if !state.recommendations.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Recommendations")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(state.recommendations.prefix(3), id: \.self) { recommendation in
                        HStack(spacing: 8) {
                            Image(systemName: "lightbulb.fill")
                                .font(.caption)
                                .foregroundColor(.orange)
                            
                            Text(recommendation)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Active Alerts Section
    
    private var activeAlertsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Active Alerts")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(monitoringService.activeAlerts.count)")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.2))
                    .foregroundColor(.red)
                    .cornerRadius(8)
            }
            
            ForEach(monitoringService.activeAlerts.prefix(3)) { alert in
                EnvironmentalAlertCard(alert: alert) {
                    selectedAlert = alert
                    showingAlertDetails = true
                } dismissAction: {
                    monitoringService.dismissAlert(alert.id)
                }
            }
            
            if monitoringService.activeAlerts.count > 3 {
                Button("View All Alerts") {
                    // Navigate to full alerts view
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
    }
    
    // MARK: - Environmental Trends Section
    
    private var environmentalTrendsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Environmental Trends")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Picker("Time Range", selection: $selectedTimeRange) {
                    ForEach(TimeRange.allCases, id: \.self) { range in
                        Text(range.displayName).tag(range)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 200)
            }
            
            if #available(iOS 16.0, *) {
                environmentalTrendsChart
            } else {
                Text("Environmental trends chart requires iOS 16+")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    @available(iOS 16.0, *)
    private var environmentalTrendsChart: some View {
        let historyData = monitoringService.getHistoryForTimeRange(selectedTimeRange.timeInterval)
        
        return Chart(historyData) { snapshot in
            LineMark(
                x: .value("Time", snapshot.timestamp),
                y: .value("Score", snapshot.data.environmentalScore.overallScore * 100)
            )
            .foregroundStyle(.blue)
        }
        .frame(height: 150)
        .chartYAxis {
            AxisMarks(position: .leading)
        }
        .chartXAxis {
            AxisMarks(position: .bottom)
        }
    }
    
    // MARK: - Risk Assessment Section
    
    private var riskAssessmentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Risk Assessment")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 16) {
                ForEach(RiskLevel.allCases.filter { $0 != .unknown }, id: \.self) { riskLevel in
                    VStack(spacing: 4) {
                        Image(systemName: riskLevel.icon)
                            .font(.title2)
                            .foregroundColor(riskLevel.color)
                        
                        Text(riskLevel.displayName)
                            .font(.caption)
                            .fontWeight(.medium)
                        
                        Text(getRiskCount(riskLevel))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(riskLevel.color.opacity(0.1))
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Quick Actions Section
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 12) {
                EnvironmentalQuickActionButton(
                    title: "Refresh Data",
                    icon: "arrow.clockwise",
                    color: .blue
                ) {
                    Task {
                        await monitoringService.performEnvironmentalCheck()
                    }
                }
                
                EnvironmentalQuickActionButton(
                    title: "View History",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .green
                ) {
                    // Navigate to history view
                }
                
                EnvironmentalQuickActionButton(
                    title: "Settings",
                    icon: "gear",
                    color: .gray
                ) {
                    showingSettings = true
                }
                
                EnvironmentalQuickActionButton(
                    title: monitoringService.isMonitoring ? "Pause" : "Start",
                    icon: monitoringService.isMonitoring ? "pause" : "play",
                    color: monitoringService.isMonitoring ? .orange : .green
                ) {
                    toggleMonitoring()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Helper Methods
    
    private func toggleMonitoring() {
        Task {
            if monitoringService.isMonitoring {
                monitoringService.stopMonitoring()
            } else {
                await monitoringService.startMonitoring()
            }
        }
    }
    
    private func getTemperatureColor(_ temperature: Int) -> Color {
        switch temperature {
        case ..<32: return .blue
        case 32..<50: return .cyan
        case 50..<70: return .green
        case 70..<85: return .orange
        default: return .red
        }
    }
    
    private func getAQIColor(_ aqi: Int) -> Color {
        switch aqi {
        case 0...50: return .green
        case 51...100: return .yellow
        case 101...150: return .orange
        default: return .red
        }
    }
    
    private func getPollenLevel(_ pollen: PollenData) -> String {
        let total = pollen.treeIndex + pollen.grassIndex + pollen.weedIndex
        switch total {
        case 0...5: return "Low"
        case 6...12: return "Moderate"
        case 13...20: return "High"
        default: return "Very High"
        }
    }
    
    private func getPollenColor(_ pollen: PollenData) -> Color {
        let total = pollen.treeIndex + pollen.grassIndex + pollen.weedIndex
        switch total {
        case 0...5: return .green
        case 6...12: return .yellow
        case 13...20: return .orange
        default: return .red
        }
    }
    
    private func getScoreColor(_ score: Double) -> Color {
        switch score {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .yellow
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
    
    private func getRiskCount(_ riskLevel: RiskLevel) -> String {
        let count = monitoringService.getHistoryForTimeRange(86400).filter { $0.riskLevel == riskLevel }.count
        return "\(count) today"
    }
}

// MARK: - Supporting Views

struct EnvironmentalMetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
}

struct EnvironmentalAlertCard: View {
    let alert: EnvironmentalAlert
    let action: () -> Void
    let dismissAction: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: alert.alertType.icon)
                .font(.title2)
                .foregroundColor(alert.severity.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(alert.alertType.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(alert.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            VStack(spacing: 4) {
                Button("View", action: action)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .foregroundColor(.blue)
                    .cornerRadius(6)
                
                Button("Dismiss", action: dismissAction)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.gray)
                    .cornerRadius(6)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(alert.severity.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(alert.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Placeholder Detail Views

struct EnvironmentalMonitoringSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Environmental Monitoring Settings")
                    .font(.headline)
                
                Text("Settings interface would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct EnvironmentalAlertDetailsView: View {
    let alert: EnvironmentalAlert
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Alert Details")
                    .font(.headline)
                
                Text("Alert details would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle(alert.alertType.displayName)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    EnvironmentalMonitoringDashboard()
}
