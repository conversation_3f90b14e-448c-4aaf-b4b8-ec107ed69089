//
//  EnvironmentalScoringView.swift
//  PetCapsule
//
//  Environmental scoring dashboard with real-time conditions and recommendations
//

import SwiftUI

struct EnvironmentalScoringView: View {
    @StateObject private var scoringService = EnvironmentalScoringService.shared
    @EnvironmentObject var petService: RealDataService
    @State private var selectedPet: Pet?
    @State private var showingDetailedScore = false
    @State private var showingRecommendations = false
    @State private var isRefreshing = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Pet Selector
                    if !petService.pets.isEmpty {
                        petSelectorSection
                    }
                    
                    // Current Environmental Score
                    if let score = scoringService.currentEnvironmentalScore {
                        currentScoreSection(score)
                    } else {
                        loadingScoreSection
                    }
                    
                    // Quick Recommendations
                    if !scoringService.walkRecommendations.isEmpty {
                        quickRecommendationsSection
                    }
                    
                    // Component Breakdown
                    if let score = scoringService.currentEnvironmentalScore {
                        componentBreakdownSection(score)
                    }
                    
                    // Best Walking Times
                    bestWalkingTimesSection
                    
                    // Environmental Trends
                    environmentalTrendsSection
                }
                .padding()
            }
            .navigationTitle("Environmental Conditions")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: refreshScore) {
                        Image(systemName: "arrow.clockwise")
                            .rotationEffect(.degrees(isRefreshing ? 360 : 0))
                            .animation(.linear(duration: 1).repeatCount(isRefreshing ? 10 : 0), value: isRefreshing)
                    }
                }
            }
            .sheet(isPresented: $showingDetailedScore) {
                if let score = scoringService.currentEnvironmentalScore {
                    DetailedScoreView(score: score)
                }
            }
            .sheet(isPresented: $showingRecommendations) {
                PersonalizedRecommendationsView(pet: selectedPet)
            }
            .onAppear {
                if selectedPet == nil && !petService.pets.isEmpty {
                    selectedPet = petService.pets.first
                }
                
                Task {
                    await loadEnvironmentalScore()
                }
            }
        }
    }
    
    // MARK: - Pet Selector Section
    
    private var petSelectorSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Pet")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(petService.pets, id: \.id) { pet in
                        Button(action: {
                            selectedPet = pet
                            Task {
                                await generatePersonalizedRecommendations()
                            }
                        }) {
                            VStack {
                                Circle()
                                    .fill(selectedPet?.id == pet.id ? Color.blue : Color.gray)
                                    .frame(width: 60, height: 60)
                                    .overlay(
                                        Text(String(pet.name.prefix(1)).uppercased())
                                            .font(.title2)
                                            .fontWeight(.bold)
                                            .foregroundColor(.white)
                                    )
                                Text(pet.name)
                                    .font(.caption)
                                    .lineLimit(1)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Current Score Section
    
    private func currentScoreSection(_ score: EnvironmentalScore) -> some View {
        VStack(spacing: 16) {
            // Main Score Display
            HStack(spacing: 20) {
                // Score Circle
                ZStack {
                    Circle()
                        .stroke(Color(.systemGray5), lineWidth: 12)
                        .frame(width: 120, height: 120)
                    
                    Circle()
                        .trim(from: 0, to: score.overallScore)
                        .stroke(score.scoreColor, style: StrokeStyle(lineWidth: 12, lineCap: .round))
                        .frame(width: 120, height: 120)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 1), value: score.overallScore)
                    
                    VStack(spacing: 4) {
                        Text("\(Int(score.overallScore * 100))")
                            .font(.system(size: 32, weight: .bold, design: .rounded))
                            .foregroundColor(score.scoreColor)
                        
                        Text("SCORE")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Score Details
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: score.scoreCategory.icon)
                            .foregroundColor(score.scoreColor)
                        
                        Text(score.scoreCategory.displayName)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(score.scoreColor)
                    }
                    
                    Text("Walking Conditions")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    if let lastUpdate = scoringService.lastUpdateTime {
                        Text("Updated \(lastUpdate.formatted(.relative(presentation: .named)))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Button("View Details") {
                        showingDetailedScore = true
                    }
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(score.scoreColor.opacity(0.2))
                    .foregroundColor(score.scoreColor)
                    .cornerRadius(8)
                }
                
                Spacer()
            }
            
            // Quick Status
            HStack {
                Image(systemName: score.overallScore > 0.7 ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                    .foregroundColor(score.overallScore > 0.7 ? .green : .orange)
                
                Text(score.overallScore > 0.7 ? "Great conditions for walking!" : "Check recommendations before walking")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Loading Score Section
    
    private var loadingScoreSection: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Analyzing Environmental Conditions...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Quick Recommendations Section
    
    private var quickRecommendationsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Quick Recommendations")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if selectedPet != nil {
                    Button("Personalized") {
                        showingRecommendations = true
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .foregroundColor(.blue)
                    .cornerRadius(6)
                }
            }
            
            // Temporarily disabled - build issues
            Text("Walk recommendations temporarily unavailable")
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Component Breakdown Section
    
    private func componentBreakdownSection(_ score: EnvironmentalScore) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Environmental Factors")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ComponentScoreCard(
                    title: "Temperature",
                    score: score.components.temperature,
                    icon: "thermometer",
                    value: "\(score.rawData.weather.temperature)°F"
                )
                
                ComponentScoreCard(
                    title: "Air Quality",
                    score: score.components.airQuality,
                    icon: "aqi.medium",
                    value: "AQI \(score.rawData.airQuality.index)"
                )
                
                ComponentScoreCard(
                    title: "Humidity",
                    score: score.components.humidity,
                    icon: "humidity.fill",
                    value: "\(score.rawData.weather.humidity)%"
                )
                
                ComponentScoreCard(
                    title: "Pollen",
                    score: score.components.pollen,
                    icon: "leaf.fill",
                    value: pollenLevelText(score.rawData.pollen)
                )
            }
        }
    }
    
    // MARK: - Best Walking Times Section
    
    private var bestWalkingTimesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Best Walking Times Today")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 12) {
                TimeSlotCard(
                    timeRange: "7:00 - 9:00 AM",
                    score: 0.9,
                    label: "Morning",
                    isRecommended: true
                )
                
                TimeSlotCard(
                    timeRange: "6:00 - 8:00 PM",
                    score: 0.85,
                    label: "Evening",
                    isRecommended: true
                )
                
                TimeSlotCard(
                    timeRange: "12:00 - 2:00 PM",
                    score: 0.4,
                    label: "Midday",
                    isRecommended: false
                )
            }
        }
    }
    
    // MARK: - Environmental Trends Section
    
    private var environmentalTrendsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Environmental Trends")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                TrendRow(
                    title: "Air Quality",
                    trend: .improving,
                    description: "Expected to improve throughout the day"
                )
                
                TrendRow(
                    title: "Pollen Levels",
                    trend: .stable,
                    description: "Moderate levels, stable for next 6 hours"
                )
                
                TrendRow(
                    title: "Temperature",
                    trend: .worsening,
                    description: "Rising to 87°F by afternoon"
                )
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func refreshScore() {
        isRefreshing = true
        
        Task {
            await loadEnvironmentalScore()
            
            await MainActor.run {
                withAnimation(.easeOut(duration: 0.5)) {
                    isRefreshing = false
                }
            }
        }
    }
    
    private func loadEnvironmentalScore() async {
        do {
            _ = try await scoringService.getCurrentEnvironmentalScore()
        } catch {
            print("❌ Failed to load environmental score: \(error)")
        }
    }
    
    private func generatePersonalizedRecommendations() async {
        guard let pet = selectedPet else { return }
        
        // Placeholder implementation - would call actual service method when available
        await MainActor.run {
            // scoringService.walkRecommendations = [] // Would set actual recommendations
        }
        print("✅ Generated personalized recommendations for pet: \(pet.name)")
    }
    
    private func pollenLevelText(_ pollen: Int) -> String {
        switch pollen {
        case 0...5: return "Low"
        case 6...12: return "Moderate"
        case 13...20: return "High"
        default: return "Very High"
        }
    }
}

// MARK: - Supporting Views

struct ComponentScoreCard: View {
    let title: String
    let score: ComponentScore
    let icon: String
    let value: String
    
    // Helper to determine color based on score value
    private var scoreColor: Color {
        switch score.score {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .blue
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(scoreColor)
                    .font(.title3)
                
                Spacer()
                
                Text(value)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(score.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            // Score Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(scoreColor)
                        .frame(width: geometry.size.width * score.score, height: 4)
                        .cornerRadius(2)
                        .animation(.easeInOut(duration: 0.8), value: score.score)
                }
            }
            .frame(height: 4)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct TimeSlotCard: View {
    let timeRange: String
    let score: Double
    let label: String
    let isRecommended: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            Text(label)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(isRecommended ? .green : .orange)
            
            Text(timeRange)
                .font(.caption2)
                .foregroundColor(.secondary)
            
            ZStack {
                Circle()
                    .stroke(Color(.systemGray5), lineWidth: 4)
                    .frame(width: 40, height: 40)
                
                Circle()
                    .trim(from: 0, to: score)
                    .stroke(isRecommended ? Color.green : Color.orange, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 40, height: 40)
                    .rotationEffect(.degrees(-90))
                
                Text("\(Int(score * 100))")
                    .font(.caption2)
                    .fontWeight(.bold)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isRecommended ? Color.green.opacity(0.1) : Color.orange.opacity(0.1))
        )
    }
}

struct TrendRow: View {
    let title: String
    let trend: EnvironmentalTrendDirection
    let description: String
    
    var body: some View {
        HStack {
            Image(systemName: trend.icon)
                .foregroundColor(trend.color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

enum EnvironmentalTrendDirection {
    case improving
    case stable
    case worsening
    
    var icon: String {
        switch self {
        case .improving: return "arrow.up.circle.fill"
        case .stable: return "minus.circle.fill"
        case .worsening: return "arrow.down.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .improving: return .green
        case .stable: return .blue
        case .worsening: return .red
        }
    }
}

// MARK: - Placeholder Detail Views

struct DetailedScoreView: View {
    let score: EnvironmentalScore
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Detailed Environmental Score")
                    .font(.headline)
                
                Text("Comprehensive breakdown would be displayed here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Score Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct PersonalizedRecommendationsView: View {
    let pet: Pet?
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Personalized Recommendations")
                    .font(.headline)
                
                if let pet = pet {
                    Text("Recommendations for \(pet.name)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("Recommendations")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    EnvironmentalScoringView()
}
