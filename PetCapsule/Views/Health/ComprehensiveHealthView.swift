//
//  ComprehensiveHealthView.swift
//  PetCapsule
//
//  Comprehensive health monitoring interface with symptoms, medications, and appointments
//
import SwiftUI
// Type alias to resolve ambiguity - using String to avoid conflicts
// typealias ComprehensiveHealthAlert = String
struct ComprehensiveHealthView: View {
    @EnvironmentObject var petService: RealDataService
    @StateObject private var healthService = ComprehensiveHealthMonitoringService.shared
    @StateObject private var analyticsService = PetHealthAnalyticsService.shared
    @State private var selectedPet: Pet?
    @State private var showingHealthDetail = false
    @State private var showingVitalSigns = false
    @State private var showingMedication = false
    @State private var selectedTab = 0
    @State private var showingAddRecord = false
    @State private var showingAddSymptom = false
    @State private var showingAddMedication = false
    @State private var showingScheduleAppointment = false
    @State private var showingAddPet = false
    @State private var showTabLabels = false
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Pet Selector
                if !petService.pets.isEmpty {
                    petSelectorHeader
                }
                // Tab Navigation
                tabNavigation
                // Content
                TabView(selection: $selectedTab) {
                    // Overview Tab
                    overviewTab
                        .tag(0)
                    // Records Tab
                    recordsTab
                        .tag(1)
                    // Symptoms Tab
                    symptomsTab
                        .tag(2)
                    // Medications Tab
                    medicationsTab
                        .tag(3)
                    // Appointments Tab
                    appointmentsTab
                        .tag(4)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Your Pets Daily Life")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.purple.opacity(0.8),
                        Color.blue.opacity(0.6)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                for: .navigationBar
            )
            .toolbarBackground(.visible, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Add Health Record") {
                            showingAddRecord = true
                        }
                        Button("Log Symptom") {
                            showingAddSymptom = true
                        }
                        Button("Add Medication") {
                            showingAddMedication = true
                        }
                        Button("Schedule Appointment") {
                            showingScheduleAppointment = true
                        }
                    } label: {
                        Image(systemName: "plus")
                    }
                    .disabled(selectedPet == nil)
                }
            }
            .sheet(isPresented: $showingAddRecord) {
                if let pet = selectedPet {
                    AddHealthRecordView(petId: pet.id)
                }
            }
            .sheet(isPresented: $showingAddSymptom) {
                if let pet = selectedPet {
                    LogSymptomView(pet: pet)
                }
            }
            .sheet(isPresented: $showingAddMedication) {
                if let pet = selectedPet {
                    AddMedicationView(pet: pet)
                }
            }
            .sheet(isPresented: $showingScheduleAppointment) {
                if let pet = selectedPet {
                    ScheduleAppointmentView(pet: pet)
                }
            }
            .sheet(isPresented: $showingAddPet) {
                AddPetView()
            }
            .onAppear {
                if selectedPet == nil && !petService.pets.isEmpty {
                    selectedPet = petService.pets.first
                }
            }
        }
    }
    // MARK: - Modern Pet Cards Header
    private var petSelectorHeader: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 20) {
                // Add Pet Button
                modernAddPetCard
                ForEach(Array(petService.pets.enumerated()), id: \.element.id) { index, pet in
                    modernPetCard(for: pet, index: index)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple.opacity(0.8),
                    Color.blue.opacity(0.6),
                    Color.purple.opacity(0.8)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
    private var modernAddPetCard: some View {
        Button(action: {
            showingAddPet = true
        }) {
            VStack(spacing: 0) {
                // Top section with gradient
                VStack(spacing: 16) {
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.9),
                                        Color.green.opacity(0.3)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 80, height: 80)
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.5), lineWidth: 3)
                            )
                        Image(systemName: "plus")
                            .font(.title)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                    VStack(spacing: 4) {
                        Text("✨ Add")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        Text("New Pet")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white.opacity(0.9))
                    }
                }
                .padding(.top, 20)
                .padding(.horizontal, 16)
                // Bottom section
                VStack(spacing: 8) {
                    Text("0 Memories")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Color.white.opacity(0.2)
                                .clipShape(Capsule())
                        )
                }
                .padding(.bottom, 16)
            }
            .frame(width: 160, height: 200)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.green.opacity(0.8),
                        Color.mint.opacity(0.9)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(Color.white.opacity(0.3), lineWidth: 1.5)
            )
            .shadow(color: Color.green.opacity(0.3), radius: 15, x: 0, y: 10)
        }
        .buttonStyle(PlainButtonStyle())
    }
    // MARK: - Tab Navigation
    private var tabNavigation: some View {
        VStack(spacing: 0) {
            HStack {
                Text("📊 Quick Actions")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                Spacer()
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showTabLabels.toggle()
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: showTabLabels ? "text.justify" : "square.grid.3x3")
                            .font(.caption)
                        Text(showTabLabels ? "Hide Labels" : "Show Labels")
                            .font(.caption2)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 8)
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 20) {
                    ModernIconTabButton(
                        title: "Overview",
                        icon: "house.fill",
                        isSelected: selectedTab == 0,
                        showLabel: showTabLabels
                    ) {
                        selectedTab = 0
                    }
                    ModernIconTabButton(
                        title: "Records",
                        icon: "doc.text.fill",
                        isSelected: selectedTab == 1,
                        showLabel: showTabLabels
                    ) {
                        selectedTab = 1
                    }
                    ModernIconTabButton(
                        title: "Symptoms",
                        icon: "stethoscope",
                        isSelected: selectedTab == 2,
                        showLabel: showTabLabels
                    ) {
                        selectedTab = 2
                    }
                    ModernIconTabButton(
                        title: "Medications",
                        icon: "pills.fill",
                        isSelected: selectedTab == 3,
                        showLabel: showTabLabels
                    ) {
                        selectedTab = 3
                    }
                    ModernIconTabButton(
                        title: "Appointments",
                        icon: "calendar.badge.clock",
                        isSelected: selectedTab == 4,
                        showLabel: showTabLabels
                    ) {
                        selectedTab = 4
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 16)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 0)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(.systemBackground),
                            Color(.systemGray6).opacity(0.3)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
        )
    }
    // MARK: - Overview Tab
    private var overviewTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if let pet = selectedPet {
                    // Pet Details Card
                    ComprehensivePetDetailsCard(pet: pet)
                    // Health Alerts
                    if !healthService.healthAlerts.filter({ $0.petId == pet.id && $0.isActive }).isEmpty {
                        HealthAlertsCard(petId: pet.id)
                    }
                    // Health Summary
                    HealthSummaryCard(pet: pet)
                    // Recent Activity
                    RecentHealthActivityCard(petId: pet.id)
                    // Quick Actions
                    HealthQuickActionsCard(pet: pet)
                }
            }
            .padding()
        }
    }
    // MARK: - Records Tab
    private var recordsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if let pet = selectedPet {
                    HealthRecordsView(petId: pet.id)
                }
            }
            .padding()
        }
    }
    // MARK: - Symptoms Tab
    private var symptomsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if let pet = selectedPet {
                    SymptomsView(petId: pet.id)
                }
            }
            .padding()
        }
    }
    // MARK: - Medications Tab
    private var medicationsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if let pet = selectedPet {
                    MedicationsView(petId: pet.id)
                }
            }
            .padding()
        }
    }
    // MARK: - Appointments Tab
    private var appointmentsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if let pet = selectedPet {
                    AppointmentsView(petId: pet.id)
                }
            }
            .padding()
        }
    }
    // MARK: - Modern Pet Card
    private func modernPetCard(for pet: Pet, index: Int) -> some View {
        Button(action: {
            selectedPet = pet
        }) {
            VStack(spacing: 0) {
                // Top section with gradient
                VStack(spacing: 16) {
                    modernPetAvatar(for: pet)
                    VStack(spacing: 4) {
                        Text(pet.name)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        Text(pet.breed ?? "Mixed Breed")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white.opacity(0.9))
                    }
                }
                .padding(.top, 20)
                .padding(.horizontal, 16)
                // Bottom section with stats
                VStack(spacing: 8) {
                    HStack(spacing: 16) {
                        StatBadge(
                            value: "\(calculateHealthScore(for: pet))", 
                            label: "Health", 
                            color: .white.opacity(0.9)
                        )
                        StatBadge(
                            value: "\(pet.age)mo", 
                            label: "Age", 
                            color: .white.opacity(0.9)
                        )
                    }
                    // Status indicators
                    VStack(spacing: 4) {
                        StatusIndicator(
                            icon: "heart.fill", 
                            text: getHealthStatus(for: pet),
                            color: .white.opacity(0.8)
                        )
                        StatusIndicator(
                            icon: "calendar", 
                            text: getLastCheckup(for: pet),
                            color: .white.opacity(0.8)
                        )
                        StatusIndicator(
                            icon: "pills", 
                            text: getMedicationStatus(for: pet),
                            color: .white.opacity(0.8)
                        )
                    }
                }
                .padding(.bottom, 16)
            }
            .frame(width: 160, height: 200)
            .background(getGradientForPet(index: index))
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(selectedPet?.id == pet.id ? Color.white : Color.white.opacity(0.3), 
                           lineWidth: selectedPet?.id == pet.id ? 3 : 1.5)
            )
            .shadow(
                color: getGradientColors(index: index).0.opacity(0.3), 
                radius: selectedPet?.id == pet.id ? 20 : 15, 
                x: 0, 
                y: selectedPet?.id == pet.id ? 15 : 10
            )
            .scaleEffect(selectedPet?.id == pet.id ? 1.05 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedPet?.id)
        }
        .buttonStyle(PlainButtonStyle())
    }
    private func modernPetAvatar(for pet: Pet) -> some View {
        ZStack {
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.9),
                            Color.white.opacity(0.3)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 80, height: 80)
                .overlay(
                    Circle()
                        .stroke(Color.white.opacity(0.5), lineWidth: 3)
                )
            modernPetImageContent(for: pet)
        }
    }
    @ViewBuilder
    private func modernPetImageContent(for pet: Pet) -> some View {
        if let profileImageURL = pet.profileImageURL, !profileImageURL.isEmpty {
            AsyncImage(url: URL(string: profileImageURL)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 70, height: 70)
                    .clipShape(Circle())
            } placeholder: {
                Text(petEmoji(for: pet.breed ?? "Unknown"))
                    .font(.system(size: 35))
            }
        } else {
            Text(petEmoji(for: pet.breed ?? "Unknown"))
                .font(.system(size: 35))
        }
    }
    // MARK: - Modern Card Helper Functions
    private func getGradientForPet(index: Int) -> LinearGradient {
        let colors = getGradientColors(index: index)
        return LinearGradient(
            gradient: Gradient(colors: [colors.0, colors.1]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    private func getGradientColors(index: Int) -> (Color, Color) {
        let gradients: [(Color, Color)] = [
            (Color.purple.opacity(0.8), Color.pink.opacity(0.9)),      // Purple-Pink
            (Color.orange.opacity(0.8), Color.red.opacity(0.9)),       // Orange-Red  
            (Color.blue.opacity(0.8), Color.cyan.opacity(0.9)),        // Blue-Cyan
            (Color.green.opacity(0.8), Color.mint.opacity(0.9)),       // Green-Mint
            (Color.indigo.opacity(0.8), Color.purple.opacity(0.9)),    // Indigo-Purple
            (Color.yellow.opacity(0.8), Color.orange.opacity(0.9))     // Yellow-Orange
        ]
        return gradients[index % gradients.count]
    }
    private func calculateHealthScore(for pet: Pet) -> Int {
        // Calculate dynamic health score based on pet data
        var score = 85
        // Deduct for symptoms
        let symptomCount = pet.medicalConditions.count
        score -= min(symptomCount * 10, 30)
        // Deduct for medications
        let medicationCount = pet.medications.count
        score -= min(medicationCount * 5, 20)
        // Boost for recent checkup
        if let lastCheckup = pet.lastCheckupDate,
           lastCheckup.timeIntervalSinceNow > -30 * 24 * 60 * 60 { // Within 30 days
            score += 10
        }
        return max(min(score, 100), 50) // Clamp between 50-100
    }
    private func getHealthStatus(for pet: Pet) -> String {
        let score = calculateHealthScore(for: pet)
        switch score {
        case 90...100: return "Excellent"
        case 80...89: return "Very Good"
        case 70...79: return "Good"
        case 60...69: return "Fair"
        default: return "Needs Care"
        }
    }
    private func getLastCheckup(for pet: Pet) -> String {
        guard let lastCheckup = pet.lastCheckupDate else {
            return "No recent visit"
        }
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: lastCheckup, relativeTo: Date())
    }
    private func getMedicationStatus(for pet: Pet) -> String {
        let count = pet.medications.count
        switch count {
        case 0: return "No medications"
        case 1: return "1 medication"
        default: return "\(count) medications"
        }
    }
    // MARK: - Helper Functions
    private func petEmoji(for breed: String) -> String {
        let breedLower = breed.lowercased()
        // Dogs
        if breedLower.contains("golden") || breedLower.contains("retriever") {
            return "🐕"
        } else if breedLower.contains("german shepherd") || breedLower.contains("shepherd") {
            return "🐕‍🦺"
        } else if breedLower.contains("husky") || breedLower.contains("malamute") {
            return "🐺"
        } else if breedLower.contains("poodle") || breedLower.contains("doodle") {
            return "🐩"
        } else if breedLower.contains("bulldog") || breedLower.contains("bull") {
            return "🐶"
        } else if breedLower.contains("terrier") {
            return "🐕"
        } else if breedLower.contains("beagle") || breedLower.contains("hound") {
            return "🐕"
        } else if breedLower.contains("chihuahua") || breedLower.contains("small") {
            return "🐶"
        } else if breedLower.contains("labrador") || breedLower.contains("lab") {
            return "🐕"
        } 
        // Cats
        else if breedLower.contains("persian") || breedLower.contains("maine coon") {
            return "😸"
        } else if breedLower.contains("siamese") || breedLower.contains("bengal") {
            return "🐱"
        } else if breedLower.contains("british") || breedLower.contains("scottish") {
            return "😺"
        } else if breedLower.contains("ragdoll") || breedLower.contains("birman") {
            return "😻"
        } 
        // Generic fallbacks
        else if breedLower.contains("cat") || breedLower.contains("kitten") {
            return "🐱"
        } else if breedLower.contains("dog") || breedLower.contains("puppy") {
            return "🐶"
        } else if breedLower.contains("bird") || breedLower.contains("parrot") {
            return "🦜"
        } else if breedLower.contains("rabbit") || breedLower.contains("bunny") {
            return "🐰"
        } else if breedLower.contains("hamster") || breedLower.contains("guinea") {
            return "🐹"
        } else if breedLower.contains("fish") {
            return "🐟"
        } else {
            return "🐾" // Generic pet paw print
        }
    }
}
// MARK: - Health Alerts Card
struct HealthAlertsCard: View {
    let petId: String
    @StateObject private var healthService = ComprehensiveHealthMonitoringService.shared
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
                Text("Health Alerts")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                let alertCount = activeAlerts.count
                if alertCount > 0 {
                    Text("\(alertCount)")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.red)
                        .cornerRadius(12)
                }
            }
            ForEach(activeAlerts.prefix(3)) { alert in
                HealthAlertRow(alert: alert)
            }
            if activeAlerts.count > 3 {
                Text("+ \(activeAlerts.count - 3) more alerts")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                )
        )
    }
    private var activeAlerts: [HealthAlert] {
        healthService.healthAlerts.filter { $0.petId == petId && $0.isActive }
    }
}
// MARK: - Health Summary Card
struct HealthSummaryCard: View {
    let pet: Pet
    @StateObject private var healthService = ComprehensiveHealthMonitoringService.shared
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "heart.circle.fill")
                    .foregroundColor(.pink)
                    .font(.title2)
                Text("How They're Doing")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            HStack(spacing: 20) {
                // Health Score
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(Color(.systemGray5), lineWidth: 8)
                            .frame(width: 80, height: 80)
                        Circle()
                            .trim(from: 0, to: dynamicHealthScore)
                            .stroke(healthScoreColor, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                            .frame(width: 80, height: 80)
                            .rotationEffect(.degrees(-90))
                        Text("\(Int(dynamicHealthScore * 100))")
                            .font(.headline)
                            .fontWeight(.bold)
                    }
                    Text("Wellness")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                // Health Stats
                VStack(alignment: .leading, spacing: 12) {
                    HealthStat(
                        title: activeSymptoms == 0 ? "Feeling Great" : "Needs Attention",
                        value: activeSymptoms == 0 ? "😊" : "\(activeSymptoms) issue\(activeSymptoms == 1 ? "" : "s")",
                        icon: activeSymptoms == 0 ? "heart.fill" : "exclamationmark.circle",
                        color: activeSymptoms > 0 ? .orange : .green
                    )
                    HealthStat(
                        title: "Daily Care",
                        value: activeMedications == 0 ? "All good" : "\(activeMedications) med\(activeMedications == 1 ? "" : "s")",
                        icon: activeMedications == 0 ? "checkmark.circle.fill" : "pills",
                        color: activeMedications == 0 ? .green : .blue
                    )
                    HealthStat(
                        title: "Next Check-up",
                        value: nextAppointmentText,
                        icon: "calendar.badge.clock",
                        color: .purple
                    )
                }
                Spacer()
            }
        }
        .padding()
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.pink.opacity(0.05),
                    Color.blue.opacity(0.05)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.pink.opacity(0.1), lineWidth: 1)
        )
        .cornerRadius(12)
    }
    private var dynamicHealthScore: Double {
        // Calculate health score based on actual health factors
        var score = 1.0 // Start with perfect health
        // Reduce score for active symptoms
        let symptoms = activeSymptoms
        if symptoms > 0 {
            score -= Double(symptoms) * 0.1 // Each symptom reduces by 10%
        }
        // Boost score for recent vet visits
        let recentAppointments = healthService.vetAppointments
            .filter { $0.petId == pet.id && $0.status == .completed }
            .filter { Calendar.current.isDate($0.scheduledDate, equalTo: Date(), toGranularity: .month) }
        if !recentAppointments.isEmpty {
            score += 0.1 // Recent vet visit boosts health
        }
        // Consider overdue vaccinations
        let overdueVaccinations = healthService.vetAppointments
            .filter { $0.petId == pet.id && $0.type == .vaccination && $0.scheduledDate < Date() && $0.status == .scheduled }
        if !overdueVaccinations.isEmpty {
            score -= 0.15 // Overdue vaccinations reduce health
        }
        // Ensure score stays within bounds
        return max(0.3, min(1.0, score)) // Minimum 30%, maximum 100%
    }
    private var healthScoreColor: Color {
        switch dynamicHealthScore {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .yellow
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
    private var activeSymptoms: Int {
        healthService.symptoms.filter { 
            $0.petId == pet.id && $0.status == .active 
        }.count
    }
    private var activeMedications: Int {
        healthService.medications.filter { 
            $0.petId == pet.id && $0.isActive 
        }.count
    }
    private var nextAppointmentText: String {
        let upcomingAppointments = healthService.vetAppointments
            .filter { $0.petId == pet.id && $0.status == .scheduled }
            .sorted { $0.scheduledDate < $1.scheduledDate }
        if let nextAppointment = upcomingAppointments.first {
            let formatter = RelativeDateTimeFormatter()
            return formatter.localizedString(for: nextAppointment.scheduledDate, relativeTo: Date())
        } else {
            return "None scheduled"
        }
    }
}
// MARK: - Supporting Views
struct HealthAlertRow: View {
    let alert: HealthAlert
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: alertTypeIcon(alert.type))
                .font(.title3)
                .foregroundColor(severityColor(alert.severity))
                .frame(width: 24)
            VStack(alignment: .leading, spacing: 2) {
                Text(alert.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text(alert.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            Spacer()
            Text(alert.triggeredAt.formatted(.relative(presentation: .named)))
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    private func alertTypeIcon(_ type: String) -> String {
        switch type.lowercased() {
        case "medical", "health": return "stethoscope"
        case "medication": return "pills"
        case "emergency": return "exclamationmark.triangle.fill"
        case "vaccination": return "syringe"
        case "nutrition": return "fork.knife"
        case "exercise": return "figure.walk"
        case "behavior": return "brain"
        case "environmental": return "leaf"
        default: return "info.circle"
        }
    }
    private func severityColor(_ severity: MonitoringAlertSeverity) -> Color {
        switch severity {
        case .low: return .green
        case .moderate: return .orange
        case .warning: return .yellow
        case .high: return .red
        case .critical: return .purple
        }
    }
}
struct HealthStat: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 16)
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
        }
    }
}
// MARK: - Placeholder Views
struct RecentHealthActivityCard: View {
    let petId: String
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Activity")
                .font(.headline)
                .fontWeight(.semibold)
            Text("Recent health activity would be displayed here")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}
struct HealthQuickActionsCard: View {
    let pet: Pet
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                Button(action: {
                    // Log symptom action
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "exclamationmark.circle")
                            .font(.title2)
                            .foregroundColor(.orange)
                        
                        Text("Log Symptom")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(6)
                }
                .buttonStyle(.plain)
                
                Button(action: {
                    // Add record action
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "plus.circle")
                            .font(.title2)
                            .foregroundColor(.blue)
                        
                        Text("Add Record")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(6)
                }
                .buttonStyle(.plain)
                
                Button(action: {
                    // Schedule vet action
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "calendar.badge.plus")
                            .font(.title2)
                            .foregroundColor(.green)
                        
                        Text("Schedule Vet")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(6)
                }
                .buttonStyle(.plain)
                
                Button(action: {
                    // Medication action
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "pills")
                            .font(.title2)
                            .foregroundColor(.purple)
                        
                        Text("Medication")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(6)
                }
                .buttonStyle(.plain)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}
// MARK: - Placeholder Detail Views
struct HealthRecordsView: View {
    let petId: String
    @State private var showingAddRecord = false
    @State private var healthRecords: [SimpleHealthRecord] = []
    @State private var isLoading = true
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Health Records")
                    .font(.headline)
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
                Spacer()
                Button(action: {
                    showingAddRecord = true
                }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title3)
                }
            }
            if healthRecords.isEmpty && !isLoading {
                VStack(spacing: 12) {
                    Image(systemName: "doc.text")
                        .font(.largeTitle)
                        .foregroundColor(.gray)
                    Text("No health records yet")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    Text("Tap the + button to add your first health record")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            } else {
                ForEach(healthRecords, id: \.id) { record in
                    RealHealthRecordRow(record: record) {
                        Task {
                            await loadHealthRecords()
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .sheet(isPresented: $showingAddRecord) {
            AddHealthRecordView(petId: petId)
        }
        .task {
            await loadHealthRecords()
        }
    }
    private func loadHealthRecords() async {
        isLoading = true
        defer { isLoading = false }
        do {
            print("Loading health records for pet: \(petId)")
            let response: [DatabaseHealthRecord] = []
            // TODO: Implement Apple Native health record loading
            healthRecords = response.map { dbRecord in
                SimpleHealthRecord(
                    id: dbRecord.id.uuidString,
                    petId: petId,
                    date: dbRecord.recordedAt,
                    type: dbRecord.recordType,
                    veterinarian: dbRecord.veterinarian ?? "Unknown Vet",
                    notes: dbRecord.notes ?? "",
                    attachments: [],
                    severity: .normal,
                    followUpRequired: false,
                    cost: dbRecord.cost
                )
            }
        } catch {
            print("❌ Failed to load health records: \(error)")
        }
    }
}
// MARK: - Database Models for Health Records
struct DatabaseHealthRecord: Codable {
    let id: UUID
    let petId: UUID
    let recordType: String
    let title: String
    let description: String?
    let notes: String?
    let veterinarian: String?
    let cost: Double?
    let recordedAt: Date
    let createdAt: Date
    enum CodingKeys: String, CodingKey {
        case id
        case petId = "pet_id"
        case recordType = "record_type"
        case title, description, notes, veterinarian, cost
        case recordedAt = "recorded_at"
        case createdAt = "created_at"
    }
}
struct SimpleHealthRecord {
    let id: String
    let petId: String
    let date: Date
    let type: String
    let veterinarian: String
    let notes: String
    let attachments: [String]
    let severity: RecordSeverity
    let followUpRequired: Bool
    let cost: Double?
}
enum RecordSeverity {
    case normal, concerning, urgent
}
struct RealHealthRecordRow: View {
    let record: SimpleHealthRecord
    let onUpdate: () -> Void
    @State private var showingDetail = false
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(record.type)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    if let cost = record.cost, cost > 0 {
                        Text("$\(cost, specifier: "%.2f")")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.green.opacity(0.1))
                            .foregroundColor(.green)
                            .cornerRadius(4)
                    }
                    Spacer()
                    Text(record.date.formatted(.dateTime.month().day()))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Text(record.veterinarian)
                    .font(.caption)
                    .foregroundColor(.blue)
                Text(record.notes)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            Spacer()
            Button("Edit") {
                showingDetail = true
            }
            .font(.caption)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.blue.opacity(0.1))
            .foregroundColor(.blue)
            .cornerRadius(6)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(8)
        .sheet(isPresented: $showingDetail) {
            EditRealHealthRecordView(record: record, onUpdate: onUpdate)
        }
    }
}
struct SymptomsView: View {
    let petId: String
    @State private var behaviorLogs: [SimpleBehaviorLog] = []
    @State private var isLoading = true
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Behavior & Symptoms")
                    .font(.headline)
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
                Spacer()
            }
            if behaviorLogs.isEmpty && !isLoading {
                VStack(spacing: 12) {
                    Image(systemName: "stethoscope")
                        .font(.largeTitle)
                        .foregroundColor(.gray)
                    Text("No symptoms logged yet")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    Text("Behavior and symptom tracking helps monitor your pet's wellbeing")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            } else {
                ForEach(behaviorLogs.prefix(5), id: \.id) { log in
                    BehaviorLogRow(log: log)
                }
                if behaviorLogs.count > 5 {
                    Text("+ \(behaviorLogs.count - 5) more entries")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .padding(.top, 8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .task {
            await loadBehaviorLogs()
        }
    }
    private func loadBehaviorLogs() async {
        isLoading = true
        defer { isLoading = false }
        do {
            print("Loading behavior logs for pet: \(petId)")
            let response: [DatabaseBehaviorLog] = []
            // TODO: Implement Apple Native behavior log loading
            behaviorLogs = response.map { dbLog in
                SimpleBehaviorLog(
                    id: dbLog.id.uuidString,
                    petId: petId,
                    behaviorType: dbLog.behaviorType,
                    description: dbLog.notes ?? "",
                    severity: .normal,
                    recordedAt: dbLog.recordedAt,
                    moodScore: dbLog.moodScore ?? 5,
                    energyLevel: dbLog.energyLevel ?? 5
                )
            }
        } catch {
            print("❌ Failed to load behavior logs: \(error)")
        }
    }
}
struct MedicationsView: View {
    let petId: String
    @State private var medications: [MedicationEntry] = []
    @State private var isLoading = true
    @StateObject private var healthService = ComprehensiveHealthMonitoringService.shared
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Medications")
                    .font(.headline)
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
                Spacer()
                Button(action: {
                    // Add medication action
                }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title3)
                }
            }
            if medications.isEmpty && !isLoading {
                VStack(spacing: 12) {
                    Image(systemName: "pills")
                        .font(.largeTitle)
                        .foregroundColor(.gray)
                    Text("No medications")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    Text("Track medications and supplements here")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            } else {
                ForEach(medications, id: \.id) { medication in
                    MedicationRow(medication: medication)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .onAppear {
            loadMedications()
        }
    }
    private func loadMedications() {
        isLoading = true
        medications = healthService.medications
            .filter { $0.petId == petId && $0.isActive }
            .map { med in
                MedicationEntry(
                    id: med.id,
                    name: med.name,
                    dosage: med.dosage,
                    frequency: med.frequency.rawValue,
                    isActive: med.isActive,
                    startDate: med.startDate,
                    endDate: med.endDate
                )
            }
        isLoading = false
    }
}
struct AppointmentsView: View {
    let petId: String
    @State private var appointments: [VetAppointment] = []
    @State private var isLoading = true
    @StateObject private var healthService = ComprehensiveHealthMonitoringService.shared
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Appointments")
                    .font(.headline)
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
                Spacer()
                Button(action: {
                    // Schedule appointment action
                }) {
                    Image(systemName: "calendar.badge.plus")
                        .foregroundColor(.blue)
                        .font(.title3)
                }
            }
            if appointments.isEmpty && !isLoading {
                VStack(spacing: 12) {
                    Image(systemName: "calendar")
                        .font(.largeTitle)
                        .foregroundColor(.gray)
                    Text("No appointments")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    Text("Schedule vet visits and checkups here")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            } else {
                ForEach(appointments, id: \.id) { appointment in
                    AppointmentRow(appointment: appointment)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .onAppear {
            loadAppointments()
        }
    }
    private func loadAppointments() {
        isLoading = true
        appointments = healthService.vetAppointments
            .filter { $0.petId == petId }
            .sorted { $0.scheduledDate < $1.scheduledDate }
        isLoading = false
    }
}
// MARK: - Add/Edit Views
struct AddHealthRecordView: View {
    let petId: String
    @Environment(\.dismiss) private var dismiss
    @State private var recordType = "Checkup"
    @State private var veterinarian = ""
    @State private var notes = ""
    @State private var date = Date()
    private let recordTypes = ["Checkup", "Vaccination", "Medication", "Surgery", "Dental", "Emergency", "Lab Results"]
    var body: some View {
        NavigationView {
            Form {
                Section("Record Details") {
                    Picker("Type", selection: $recordType) {
                        ForEach(recordTypes, id: \.self) { type in
                            Text(type).tag(type)
                        }
                    }
                    DatePicker("Date", selection: $date, displayedComponents: .date)
                    TextField("Veterinarian", text: $veterinarian)
                        .textContentType(.name)
                }
                Section("Notes") {
                    TextField("Add notes about this record...", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("New Health Record")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // Here you would save to your data service
                        dismiss()
                    }
                    .disabled(veterinarian.isEmpty)
                }
            }
        }
    }
}
struct EditHealthRecordView: View {
    let record: SimpleHealthRecord
    @Environment(\.dismiss) private var dismiss
    @State private var recordType: String
    @State private var veterinarian: String
    @State private var notes: String
    @State private var date: Date
    private let recordTypes = ["Checkup", "Vaccination", "Medication", "Surgery", "Dental", "Emergency", "Lab Results"]
    init(record: SimpleHealthRecord) {
        self.record = record
        self._recordType = State(initialValue: record.type)
        self._veterinarian = State(initialValue: record.veterinarian)
        self._notes = State(initialValue: record.notes)
        self._date = State(initialValue: record.date)
    }
    var body: some View {
        NavigationView {
            Form {
                Section("Record Details") {
                    Picker("Type", selection: $recordType) {
                        ForEach(recordTypes, id: \.self) { type in
                            Text(type).tag(type)
                        }
                    }
                    DatePicker("Date", selection: $date, displayedComponents: .date)
                    TextField("Veterinarian", text: $veterinarian)
                        .textContentType(.name)
                }
                Section("Notes") {
                    TextField("Add notes about this record...", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
                Section {
                    Button("Delete Record", role: .destructive) {
                        // Here you would delete the record
                        dismiss()
                    }
                }
            }
            .navigationTitle("Edit Health Record")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // Here you would save changes to your data service
                        dismiss()
                    }
                    .disabled(veterinarian.isEmpty)
                }
            }
        }
    }
}
struct LogSymptomView: View {
    let pet: Pet
    @Environment(\.dismiss) private var dismiss
    var body: some View {
        NavigationView {
            VStack {
                Text("Log Symptom")
                    .font(.headline)
                Text("Symptom logging interface would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Log Symptom")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}
struct AddMedicationView: View {
    let pet: Pet
    @Environment(\.dismiss) private var dismiss
    var body: some View {
        NavigationView {
            VStack {
                Text("Add Medication")
                    .font(.headline)
                Text("Medication entry interface would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("New Medication")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}
struct ScheduleAppointmentView: View {
    let pet: Pet
    @Environment(\.dismiss) private var dismiss
    var body: some View {
        NavigationView {
            VStack {
                Text("Schedule Appointment")
                    .font(.headline)
                Text("Appointment scheduling interface would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("New Appointment")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}
// MARK: - Icon Tab Button
struct IconTabButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let showLabel: Bool
    let action: () -> Void
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .blue : .gray)
                    .scaleEffect(isSelected ? 1.1 : 1.0)
                if showLabel {
                    Text(title)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(isSelected ? .blue : .gray)
                        .transition(.opacity.combined(with: .scale))
                }
            }
            .padding(.vertical, 8)
            .padding(.horizontal, showLabel ? 12 : 8)
            .background(
                Group {
                    if isSelected {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                            )
                    } else {
                        Color.clear
                    }
                }
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .animation(.easeInOut(duration: 0.3), value: showLabel)
    }
}
// MARK: - Comprehensive Pet Details Card
struct ComprehensivePetDetailsCard: View {
    let pet: Pet
    @State private var showingEditSheet = false
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Text("\(pet.name)'s Profile")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Button("Edit") {
                    showingEditSheet = true
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue.opacity(0.1))
                .foregroundColor(.blue)
                .cornerRadius(8)
            }
            // Basic Info
            VStack(alignment: .leading, spacing: 12) {
                PetDetailRow(title: "Species", value: pet.species.capitalized)
                PetDetailRow(title: "Breed", value: pet.breed ?? "Mixed")
                PetDetailRow(title: "Age", value: "\(pet.age) months")
                if let weight = pet.weight {
                    PetDetailRow(title: "Weight", value: String(format: "%.1f lbs", weight))
                }
                PetDetailRow(title: "Activity Level", value: pet.activityLevel.capitalized)
                if !pet.personalityTraits.isEmpty {
                    PetDetailRow(
                        title: "Personality", 
                        value: pet.personalityTraits.joined(separator: ", ")
                    )
                }
                if let microchipId = pet.microchipId, !microchipId.isEmpty {
                    PetDetailRow(title: "Microchip ID", value: microchipId)
                }
                if !pet.vaccinations.isEmpty {
                    PetDetailRow(
                        title: "Vaccinations", 
                        value: pet.vaccinations.joined(separator: ", ")
                    )
                }
                if !pet.medications.isEmpty {
                    PetDetailRow(
                        title: "Current Medications", 
                        value: pet.medications.map { $0.name }.joined(separator: ", ")
                    )
                }
            }
        }
        .padding()
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.05),
                    Color.purple.opacity(0.05)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.blue.opacity(0.1), lineWidth: 1)
        )
        .cornerRadius(12)
        .sheet(isPresented: $showingEditSheet) {
            EditPetDetailsView(pet: pet)
        }
    }
}
struct PetDetailRow: View {
    let title: String
    let value: String
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 100, alignment: .leading)
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
            Spacer()
        }
    }
}
struct EditPetDetailsView: View {
    let pet: Pet
    @Environment(\.dismiss) private var dismiss
    var body: some View {
        NavigationView {
            Form {
                Section("Basic Information") {
                    Text("Editing pet details coming soon...")
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("Edit \(pet.name)")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") { dismiss() }
                }
            }
        }
    }
}
// MARK: - Supporting Data Models
struct DatabaseBehaviorLog: Codable {
    let id: UUID
    let petId: UUID
    let behaviorType: String
    let notes: String?
    let moodScore: Int?
    let energyLevel: Int?
    let recordedAt: Date
    enum CodingKeys: String, CodingKey {
        case id
        case petId = "pet_id"
        case behaviorType = "behavior_type"
        case notes
        case moodScore = "mood_score"
        case energyLevel = "energy_level"
        case recordedAt = "recorded_at"
    }
}
struct SimpleBehaviorLog {
    let id: String
    let petId: String
    let behaviorType: String
    let description: String
    let severity: RecordSeverity
    let recordedAt: Date
    let moodScore: Int
    let energyLevel: Int
}
struct MedicationEntry {
    let id: String
    let name: String
    let dosage: String
    let frequency: String
    let isActive: Bool
    let startDate: Date
    let endDate: Date?
}
// MARK: - Supporting Row Views
struct BehaviorLogRow: View {
    let log: SimpleBehaviorLog
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(log.behaviorType.capitalized)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                    Text(log.recordedAt.formatted(.dateTime.month().day()))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Text(log.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                HStack {
                    MoodIndicator(score: log.moodScore, label: "Mood")
                    Spacer()
                    MoodIndicator(score: log.energyLevel, label: "Energy")
                }
            }
            Spacer()
        }
        .padding()
        .background(Color.white)
        .cornerRadius(8)
    }
}
struct MoodIndicator: View {
    let score: Int
    let label: String
    var body: some View {
        HStack(spacing: 4) {
            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
            HStack(spacing: 2) {
                ForEach(1...5, id: \.self) { index in
                    Circle()
                        .fill(index <= score ? Color.green : Color.gray.opacity(0.3))
                        .frame(width: 6, height: 6)
                }
            }
        }
    }
}
struct MedicationRow: View {
    let medication: MedicationEntry
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(medication.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text("\(medication.dosage) • \(medication.frequency)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                if let endDate = medication.endDate {
                    Text("Until \(endDate.formatted(.dateTime.month().day()))")
                        .font(.caption2)
                        .foregroundColor(.orange)
                } else {
                    Text("Ongoing")
                        .font(.caption2)
                        .foregroundColor(.green)
                }
            }
            Spacer()
            Circle()
                .fill(medication.isActive ? Color.green : Color.gray)
                .frame(width: 8, height: 8)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(8)
    }
}
struct AppointmentRow: View {
    let appointment: VetAppointment
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(appointment.type.rawValue.capitalized)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text(appointment.veterinarian)
                    .font(.caption)
                    .foregroundColor(.blue)
                Text(appointment.scheduledDate.formatted(.dateTime.weekday().month().day().hour().minute()))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            Spacer()
            appointmentStatusBadge
        }
        .padding()
        .background(Color.white)
        .cornerRadius(8)
    }
    @ViewBuilder
    private var appointmentStatusBadge: some View {
        switch appointment.status {
        case .scheduled:
            Text("Scheduled")
                .font(.caption2)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.blue.opacity(0.1))
                .foregroundColor(.blue)
                .cornerRadius(6)
        case .confirmed:
            Text("Confirmed")
                .font(.caption2)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.green.opacity(0.1))
                .foregroundColor(.green)
                .cornerRadius(6)
        case .inProgress:
            Text("In Progress")
                .font(.caption2)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.orange.opacity(0.1))
                .foregroundColor(.orange)
                .cornerRadius(6)
        case .completed:
            Text("Completed")
                .font(.caption2)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.green.opacity(0.1))
                .foregroundColor(.green)
                .cornerRadius(6)
        case .cancelled:
            Text("Cancelled")
                .font(.caption2)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.red.opacity(0.1))
                .foregroundColor(.red)
                .cornerRadius(6)
        case .noShow:
            Text("No Show")
                .font(.caption2)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.gray.opacity(0.1))
                .foregroundColor(.gray)
                .cornerRadius(6)
        }
    }
}
struct EditRealHealthRecordView: View {
    let record: SimpleHealthRecord
    let onUpdate: () -> Void
    @Environment(\.dismiss) private var dismiss
    @State private var recordType: String
    @State private var veterinarian: String
    @State private var notes: String
    @State private var date: Date
    private let recordTypes = ["Checkup", "Vaccination", "Medication", "Surgery", "Dental", "Emergency", "Lab Results"]
    init(record: SimpleHealthRecord, onUpdate: @escaping () -> Void) {
        self.record = record
        self.onUpdate = onUpdate
        self._recordType = State(initialValue: record.type)
        self._veterinarian = State(initialValue: record.veterinarian)
        self._notes = State(initialValue: record.notes)
        self._date = State(initialValue: record.date)
    }
    var body: some View {
        NavigationView {
            Form {
                Section("Record Details") {
                    Picker("Type", selection: $recordType) {
                        ForEach(recordTypes, id: \.self) { type in
                            Text(type).tag(type)
                        }
                    }
                    DatePicker("Date", selection: $date, displayedComponents: .date)
                    TextField("Veterinarian", text: $veterinarian)
                        .textContentType(.name)
                }
                Section("Notes") {
                    TextField("Add notes about this record...", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
                if let cost = record.cost, cost > 0 {
                    Section("Cost") {
                        Text("$\(cost, specifier: "%.2f")")
                            .foregroundColor(.green)
                    }
                }
                Section {
                    Button("Delete Record", role: .destructive) {
                        onUpdate()
                        dismiss()
                    }
                }
            }
            .navigationTitle("Edit Health Record")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        onUpdate()
                        dismiss()
                    }
                    .disabled(veterinarian.isEmpty)
                }
            }
        }
    }
}
// MARK: - Modern UI Components
struct StatBadge: View {
    let value: String
    let label: String
    let color: Color
    var body: some View {
        VStack(spacing: 2) {
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(color)
            Text(label)
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(color.opacity(0.8))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Color.white.opacity(0.2)
                .clipShape(RoundedRectangle(cornerRadius: 8))
        )
    }
}
struct StatusIndicator: View {
    let icon: String
    let text: String
    let color: Color
    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(color)
            Text(text)
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(color)
                .lineLimit(1)
        }
    }
}
struct ModernIconTabButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let showLabel: Bool
    let action: () -> Void
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: isSelected ? 
                                    [Color.blue.opacity(0.8), Color.purple.opacity(0.9)] :
                                    [Color(.systemGray5), Color(.systemGray4)]
                                ),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                        .overlay(
                            Circle()
                                .stroke(isSelected ? Color.white : Color.clear, lineWidth: 2)
                        )
                        .shadow(color: isSelected ? Color.blue.opacity(0.3) : Color.clear, radius: 8, x: 0, y: 4)
                    Image(systemName: icon)
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(isSelected ? .white : .gray)
                }
                if showLabel {
                    Text(title)
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(isSelected ? .blue : .gray)
                        .transition(.opacity.combined(with: .scale))
                }
            }
            .scaleEffect(isSelected ? 1.1 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
            .animation(.easeInOut(duration: 0.3), value: showLabel)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
#Preview {
    ComprehensiveHealthView()
}
