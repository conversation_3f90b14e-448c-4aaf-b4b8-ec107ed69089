//
//  MainAppView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

@available(iOS 18.0, *)
struct MainAppView: View {
    @EnvironmentObject var authService: AuthenticationService
    @State private var showOnboarding = true
    @State private var hasSkippedAuth = false

    var body: some View {
        Group {
            if showOnboarding && !authService.isAuthenticated && !hasSkippedAuth {
                OnboardingView(showOnboarding: $showOnboarding, hasSkippedAuth: $hasSkippedAuth)
                    .environmentObject(authService)
            } else if authService.isAuthenticated || hasSkippedAuth {
                if #available(iOS 18.0, *) {
                    MainTabView()
                        .environmentObject(authService)
                } else {
                    Text("iOS 18.0+ Required")
                }
            } else {
                AuthenticationView()
                    .environmentObject(authService)
            }
        }
    }
}



struct MainTabView: View {
    @EnvironmentObject var authService: AuthenticationService
    @EnvironmentObject var realDataService: RealDataService
    @EnvironmentObject var subscriptionService: SubscriptionService
    @EnvironmentObject var themeManager: ThemeManager
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Dashboard Tab
            if #available(iOS 18.0, *) {
                PetDashboardView(tabSelection: $selectedTab)
                    .environmentObject(authService)
                    .tabItem {
                        Image(systemName: "house.fill")
                        Text("Daily Life")
                    }
                    .tag(0)
            } else {
                Text("iOS 18.0+ Required")
                    .tabItem {
                        Image(systemName: "house.fill")
                        Text("Daily Life")
                    }
                    .tag(0)
            }
            
            // My Pets Tab
            if #available(iOS 18.0, *) {
                NavigationStack {
                    MyPetsListView()
                        .environmentObject(authService)
                }
                .tabItem {
                    Image(systemName: "heart.circle")
                    Text("My Pets")
                }
                .tag(1)
            } else {
                Text("iOS 18.0+ Required")
                    .tabItem {
                        Image(systemName: "heart.circle")
                        Text("My Pets")
                    }
                    .tag(1)
            }
            
            // Memories Tab
            if #available(iOS 18.0, *) {
                ComprehensiveMemoryView()
                    .environmentObject(authService)
                    .environmentObject(realDataService)
                    .tabItem {
                        Image(systemName: "photo.stack")
                        Text("Memories")
                    }
                    .tag(2)
            } else {
                Text("iOS 18.0+ Required")
                    .tabItem {
                        Image(systemName: "photo.stack")
                        Text("Memories")
                    }
                    .tag(2)
            }
            
            // AI Support Tab
            if #available(iOS 18.0, *) {
                PetSupportView()
                    .environmentObject(authService)
                    .tabItem {
                        Image(systemName: "brain.head.profile")
                        Text("Care Team")
                    }
                    .tag(3)
            } else {
                Text("iOS 18.0+ Required")
                    .tabItem {
                        Image(systemName: "brain.head.profile")
                        Text("Care Team")
                    }
                    .tag(3)
            }
            
            // More Tab
            MoreView()
                .environmentObject(authService)
                .environmentObject(realDataService)
                .environmentObject(subscriptionService)
                .environmentObject(themeManager)
                .tabItem {
                    Image(systemName: "ellipsis.circle")
                    Text("More")
                }
                .tag(4)
        }
        .accentColor(.purple)
    }
}

// MARK: - Placeholder Views (to be implemented)

struct HomeView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                PetTimeCapsuleLogo(size: 100, showText: true)
                    .padding(.top, 40)

                VStack(spacing: 16) {
                    Text("Welcome to PetTime Capsule")
                        .font(PetTimeBrandFonts.largeTitle())
                        .foregroundStyle(PetTimeBrandGradients.primary)
                        .multilineTextAlignment(.center)

                    Text("Your pet's memories, preserved forever")
                        .font(PetTimeBrandFonts.subheadline())
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                Spacer()
            }
            .padding()
            .background(PetTimeBrandGradients.background)
            .navigationTitle("Welcome Home")
        }
    }
}

struct MemoriesView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Memories")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Precious Moments")
        }
    }
}

struct VaultsView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Time Vaults")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Memory Collections")
        }
    }
}

struct NetworkView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Pet Network")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Pet Community")
        }
    }
}



@available(iOS 18.0, *)
#Preview {
    MainAppView()
        .environmentObject(AuthenticationService.shared)
}
