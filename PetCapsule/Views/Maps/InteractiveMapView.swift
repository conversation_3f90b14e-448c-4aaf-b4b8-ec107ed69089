//
//  InteractiveMapView.swift
//  PetCapsule
//
//  Enhanced interactive map with custom pet-themed markers and smooth animations
//

import SwiftUI
import MapKit

struct InteractiveMapView: View {
    @StateObject private var mapService = AppleMapService.shared
    @StateObject private var locationManager = LocationManager()
    @StateObject private var plannerService = UnifiedPlannerService.shared
    
    @State private var cameraPosition: MapCameraPosition = .automatic
    @State private var selectedLocation: PetFriendlyLocation?
    @State private var selectedMemory: WalkMemory?
    @State private var selectedEvent: CommunityEvent?
    @State private var showingLocationDetails = false
    @State private var showingMemoryDetails = false
    @State private var showingEventDetails = false
    @State private var mapStyle: MapStyle = .standard
    @State private var showingFilters = false
    @State private var selectedRoute: WalkRoute?
    @State private var isTrackingWalk = false
    @State private var mapSelection: MapFeature?
    
    // Filter states
    @State private var showPetFriendlyLocations = true
    @State private var showWalkMemories = true
    @State private var showCommunityEvents = true
    @State private var showEmergencyLocations = false
    @State private var selectedLocationTypes: Set<LocationType> = Set(LocationType.allCases)
    
    let initialRegion: MKCoordinateRegion?
    let showControls: Bool
    
    init(initialRegion: MKCoordinateRegion? = nil, showControls: Bool = true) {
        self.initialRegion = initialRegion
        self.showControls = showControls
    }
    
    var body: some View {
        ZStack {
            // Main Map
            Map(position: $cameraPosition, selection: $mapSelection) {
                // User location with custom marker
                if let userLocation = locationManager.currentLocation {
                    Annotation("Your Location", coordinate: userLocation.coordinate) {
                        UserLocationMarker()
                    }
                }
                
                // Pet-friendly locations
                if showPetFriendlyLocations {
                    ForEach(filteredPetLocations, id: \.id) { location in
                        Annotation(location.name, coordinate: location.coordinate) {
                            PetLocationMarker(location: location)
                                .onTapGesture {
                                    selectedLocation = location
                                    showingLocationDetails = true
                                    animateToLocation(location.coordinate)
                                }
                        }
                    }
                }
                
                // Walk memories
                if showWalkMemories {
                    ForEach(plannerService.walkMemories, id: \.id) { memory in
                        Annotation(memory.title, coordinate: CLLocationCoordinate2D(latitude: memory.latitude, longitude: memory.longitude)) {
                            WalkMemoryMarker(memory: memory)
                                .onTapGesture {
                                    selectedMemory = memory
                                    showingMemoryDetails = true
                                    animateToLocation(CLLocationCoordinate2D(latitude: memory.latitude, longitude: memory.longitude))
                                }
                        }
                    }
                }
                
                // Community events
                if showCommunityEvents {
                    ForEach(plannerService.communityEvents, id: \.id) { event in
                        Annotation(event.title, coordinate: CLLocationCoordinate2D(latitude: event.latitude, longitude: event.longitude)) {
                            EventMarker(event: event)
                                .onTapGesture {
                                    selectedEvent = event
                                    showingEventDetails = true
                                    animateToLocation(CLLocationCoordinate2D(latitude: event.latitude, longitude: event.longitude))
                                }
                        }
                    }
                }
                
                // Emergency locations
                if showEmergencyLocations {
                    ForEach(emergencyLocations, id: \.id) { location in
                        Annotation(location.name, coordinate: location.coordinate) {
                            EmergencyMarker(type: .veterinary)
                                .onTapGesture {
                                    selectedLocation = location
                                    showingLocationDetails = true
                                    animateToLocation(location.coordinate)
                                }
                        }
                    }
                }
                
                // Walk route overlay
                if let route = selectedRoute {
                    MapPolyline(coordinates: route.coordinates)
                        .stroke(
                            LinearGradient(
                                colors: [.blue, .cyan, .blue],
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            style: StrokeStyle(lineWidth: 6, lineCap: .round, lineJoin: .round)
                        )
                }
                
                // Current walk tracking
                if isTrackingWalk, !plannerService.currentWalkPath.isEmpty {
                    MapPolyline(coordinates: plannerService.currentWalkPath)
                        .stroke(
                            LinearGradient(
                                colors: [.green, .yellow, .green],
                                startPoint: .leading,
                                endPoint: .trailing
                            ),
                            style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round, dash: [10, 5])
                        )
                }
            }
            .mapStyle(mapStyle)
            .mapControls {
                if showControls {
                    MapUserLocationButton()
                    MapCompass()
                    MapScaleView()
                }
            }
            
            // Control Overlay
            if showControls {
                VStack {
                    HStack {
                        // Map Style Selector
                        mapStyleSelector
                        
                        Spacer()
                        
                        // Filter Button
                        filterButton
                    }
                    .padding()
                    
                    Spacer()
                    
                    // Bottom Controls
                    bottomControls
                }
            }
        }
        .sheet(isPresented: $showingLocationDetails) {
            if let location = selectedLocation {
                LocationDetailSheet(location: location)
            }
        }
        .sheet(isPresented: $showingMemoryDetails) {
            if let memory = selectedMemory {
                MemoryDetailSheet(memory: memory)
            }
        }
        .sheet(isPresented: $showingEventDetails) {
            if let event = selectedEvent {
                EventDetailSheet(event: event)
            }
        }
        .sheet(isPresented: $showingFilters) {
            MapFiltersSheet(
                showPetFriendlyLocations: $showPetFriendlyLocations,
                showWalkMemories: $showWalkMemories,
                showCommunityEvents: $showCommunityEvents,
                showEmergencyLocations: $showEmergencyLocations,
                selectedLocationTypes: $selectedLocationTypes
            )
        }
        .onAppear {
            setupInitialCamera()
            loadMapData()
        }
    }
    
    // MARK: - Map Style Selector
    
    private var mapStyleSelector: some View {
        Menu {
            Button("Standard") { mapStyle = .standard }
            Button("Satellite") { mapStyle = .imagery }
            Button("Hybrid") { mapStyle = .hybrid }
        } label: {
            Image(systemName: "map")
                .font(.title2)
                .foregroundColor(.primary)
                .padding(12)
                .background(Color(.systemBackground).opacity(0.9))
                .clipShape(Circle())
                .shadow(radius: 2)
        }
    }
    
    // MARK: - Filter Button
    
    private var filterButton: some View {
        Button(action: {
            showingFilters = true
        }) {
            Image(systemName: "line.3.horizontal.decrease.circle")
                .font(.title2)
                .foregroundColor(.primary)
                .padding(12)
                .background(Color(.systemBackground).opacity(0.9))
                .clipShape(Circle())
                .shadow(radius: 2)
        }
    }
    
    // MARK: - Bottom Controls
    
    private var bottomControls: some View {
        HStack(spacing: 16) {
            // Center on User Location
            Button(action: centerOnUserLocation) {
                Image(systemName: "location.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding(12)
                    .background(Color.blue)
                    .clipShape(Circle())
                    .shadow(radius: 3)
            }
            
            // Start Walk Tracking
            Button(action: toggleWalkTracking) {
                Image(systemName: isTrackingWalk ? "stop.circle.fill" : "play.circle.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding(12)
                    .background(isTrackingWalk ? Color.red : Color.green)
                    .clipShape(Circle())
                    .shadow(radius: 3)
            }
            
            // Find Nearby Locations
            Button(action: findNearbyLocations) {
                Image(systemName: "magnifyingglass.circle.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding(12)
                    .background(Color.orange)
                    .clipShape(Circle())
                    .shadow(radius: 3)
            }
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - Computed Properties
    
    private var filteredPetLocations: [PetFriendlyLocation] {
        mapService.nearbyPetFriendlyLocations.filter { location in
            selectedLocationTypes.contains(location.type)
        }
    }
    
    private var emergencyLocations: [PetFriendlyLocation] {
        mapService.nearbyPetFriendlyLocations.filter { location in
            location.type == .veterinary
        }
    }
    
    // MARK: - Helper Methods
    
    private func setupInitialCamera() {
        if let region = initialRegion {
            cameraPosition = .region(region)
        } else if let userLocation = locationManager.currentLocation {
            cameraPosition = .region(MKCoordinateRegion(
                center: userLocation.coordinate,
                latitudinalMeters: 5000,
                longitudinalMeters: 5000
            ))
        }
    }
    
    private func loadMapData() {
        Task {
            if let userLocation = locationManager.currentLocation {
                try? await mapService.searchPetFriendlyPlaces(near: userLocation.coordinate)
            }
        }
    }
    
    private func animateToLocation(_ coordinate: CLLocationCoordinate2D) {
        withAnimation(.easeInOut(duration: 1.0)) {
            cameraPosition = .region(MKCoordinateRegion(
                center: coordinate,
                latitudinalMeters: 1000,
                longitudinalMeters: 1000
            ))
        }
    }
    
    private func centerOnUserLocation() {
        guard let userLocation = locationManager.currentLocation else { return }
        
        withAnimation(.easeInOut(duration: 0.8)) {
            cameraPosition = .region(MKCoordinateRegion(
                center: userLocation.coordinate,
                latitudinalMeters: 2000,
                longitudinalMeters: 2000
            ))
        }
    }
    
    private func toggleWalkTracking() {
        isTrackingWalk.toggle()
        
        if isTrackingWalk {
            plannerService.startWalkTracking()
        } else {
            plannerService.stopWalkTracking()
        }
    }
    
    private func findNearbyLocations() {
        guard let userLocation = locationManager.currentLocation else { return }
        
        Task {
            try? await mapService.searchPetFriendlyPlaces(near: userLocation.coordinate)
        }
        
        // Animate to show search area
        withAnimation(.easeInOut(duration: 1.0)) {
            cameraPosition = .region(MKCoordinateRegion(
                center: userLocation.coordinate,
                latitudinalMeters: 10000,
                longitudinalMeters: 10000
            ))
        }
    }
}

// MARK: - Preview

#Preview {
    InteractiveMapView()
}
