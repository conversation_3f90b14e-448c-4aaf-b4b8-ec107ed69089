//
//  MapDetailSheets.swift
//  PetCapsule
//
//  Detail sheets for map interactions
//

import SwiftUI
import MapKit

// MARK: - Location Detail Sheet

struct LocationDetailSheet: View {
    let location: PetFriendlyLocation
    @Environment(\.dismiss) private var dismiss
    @StateObject private var mapService = AppleMapService.shared
    @State private var showingDirections = false
    @State private var walkRoute: WalkRoute?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header Image
                    AsyncImage(url: URL(string: location.imageURL)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Rectangle()
                            .fill(LinearGradient(
                                colors: [location.type.color.opacity(0.3), location.type.color.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .overlay(
                                Image(systemName: location.type.icon)
                                    .font(.system(size: 40))
                                    .foregroundColor(location.type.color)
                            )
                    }
                    .frame(height: 200)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    
                    // Location Info
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(location.name)
                                    .font(.title2)
                                    .fontWeight(.bold)
                                
                                Text(location.type.rawValue.capitalized)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing, spacing: 4) {
                                HStack(spacing: 4) {
                                    Image(systemName: "star.fill")
                                        .foregroundColor(.yellow)
                                        .font(.caption)
                                    
                                    Text(String(format: "%.1f", location.rating))
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                }
                                
                                Text("\(String(format: "%.1f", location.distance)) mi")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        // Address
                        HStack {
                            Image(systemName: "location")
                                .foregroundColor(.secondary)
                            
                            Text(location.address)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        // Status
                        HStack {
                            Image(systemName: location.isOpen ? "clock" : "clock.badge.xmark")
                                .foregroundColor(location.isOpen ? .green : .red)
                            
                            Text(location.isOpen ? "Open Now" : "Closed")
                                .font(.subheadline)
                                .foregroundColor(location.isOpen ? .green : .red)
                                .fontWeight(.medium)
                        }
                    }
                    
                    // Amenities
                    if !location.amenities.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Amenities")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            LazyVGrid(columns: [
                                GridItem(.flexible()),
                                GridItem(.flexible())
                            ], spacing: 8) {
                                ForEach(location.amenities, id: \.self) { amenity in
                                    HStack {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.green)
                                            .font(.caption)
                                        
                                        Text(amenity)
                                            .font(.caption)
                                            .foregroundColor(.primary)
                                        
                                        Spacer()
                                    }
                                }
                            }
                        }
                    }
                    
                    // Action Buttons
                    VStack(spacing: 12) {
                        Button(action: getDirections) {
                            HStack(spacing: 8) {
                                Image(systemName: "location.north.line.fill")
                                    .font(.system(size: 16, weight: .semibold))
                                Text("Get Directions")
                                    .font(.system(size: 16, weight: .semibold))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(
                                LinearGradient(
                                    colors: [.blue, .blue.opacity(0.8)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                in: RoundedRectangle(cornerRadius: 16)
                            )
                            .foregroundColor(.white)
                            .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
                        }

                        HStack(spacing: 12) {
                            if let phoneNumber = location.phoneNumber {
                                Button(action: callLocation) {
                                    HStack(spacing: 6) {
                                        Image(systemName: "phone.fill")
                                            .font(.system(size: 14, weight: .semibold))
                                        Text("Call")
                                            .font(.system(size: 14, weight: .semibold))
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 14)
                                    .background(
                                        LinearGradient(
                                            colors: [.green, .green.opacity(0.8)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        in: RoundedRectangle(cornerRadius: 14)
                                    )
                                    .foregroundColor(.white)
                                    .shadow(color: .green.opacity(0.3), radius: 6, x: 0, y: 3)
                                }
                            }

                            if let website = location.website {
                                Button(action: openWebsite) {
                                    HStack(spacing: 6) {
                                        Image(systemName: "safari.fill")
                                            .font(.system(size: 14, weight: .semibold))
                                        Text("Website")
                                            .font(.system(size: 14, weight: .semibold))
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 14)
                                    .background(
                                        LinearGradient(
                                            colors: [.orange, .orange.opacity(0.8)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        in: RoundedRectangle(cornerRadius: 14)
                                    )
                                    .foregroundColor(.white)
                                    .shadow(color: .orange.opacity(0.3), radius: 6, x: 0, y: 3)
                                }
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle(location.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundStyle(.secondary)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        shareLocation()
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                            .font(.title2)
                            .foregroundStyle(.blue)
                    }
                }
            }
        }
    }
    
    private func getDirections() {
        // Open in Apple Maps
        let placemark = MKPlacemark(coordinate: location.coordinate)
        let mapItem = MKMapItem(placemark: placemark)
        mapItem.name = location.name
        mapItem.openInMaps(launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeWalking])
    }
    
    private func callLocation() {
        if let phoneNumber = location.phoneNumber,
           let url = URL(string: "tel:\(phoneNumber.replacingOccurrences(of: " ", with: "").replacingOccurrences(of: "(", with: "").replacingOccurrences(of: ")", with: "").replacingOccurrences(of: "-", with: ""))") {
            UIApplication.shared.open(url)
        }
    }

    private func openWebsite() {
        if let website = location.website,
           let url = URL(string: website) {
            UIApplication.shared.open(url)
        }
    }

    private func shareLocation() {
        let shareText = """
        \(location.name)
        \(location.address)

        Rating: \(String(format: "%.1f", location.rating)) ⭐
        Distance: \(String(format: "%.1f", location.distance)) km

        Amenities: \(location.amenities.joined(separator: ", "))
        """

        let activityController = UIActivityViewController(
            activityItems: [shareText],
            applicationActivities: nil
        )

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.present(activityController, animated: true)
        }
    }
}

// MARK: - Memory Detail Sheet

struct MemoryDetailSheet: View {
    let memory: WalkMemory
    @Environment(\.dismiss) private var dismiss
    
    private var memoryPhotosView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(memory.mediaUrls, id: \.self) { imageUrl in
                    AsyncImage(url: URL(string: imageUrl)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                    }
                    .frame(width: 200, height: 150)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
            }
            .padding(.horizontal)
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Memory Photos
                    if !memory.mediaUrls.isEmpty {
                        memoryPhotosView
                    }
                    
                    // Memory Details
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text(memory.title)
                                .font(.title2)
                                .fontWeight(.bold)
                            
                            Spacer()
                            
                            if memory.isFavorite {
                                Image(systemName: "heart.fill")
                                    .foregroundColor(.red)
                            }
                        }
                        
                        if let description = memory.description {
                            Text(description)
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                        
                        // Location Info
                        if let locationName = memory.locationName {
                            HStack {
                                Image(systemName: "location")
                                    .foregroundColor(.blue)
                                
                                Text(locationName)
                                    .font(.subheadline)
                            }
                        }
                        
                        // Walk Stats
                        if let duration = memory.durationMinutes, let distance = memory.distanceMeters {
                            HStack(spacing: 20) {
                                StatView(
                                    icon: "clock",
                                    value: "\(duration)",
                                    unit: "min",
                                    color: .blue
                                )
                                
                                StatView(
                                    icon: "figure.walk",
                                    value: String(format: "%.1f", distance / 1000),
                                    unit: "km",
                                    color: .green
                                )
                            }
                        }
                        
                        // Environmental Data
                        if let temperature = memory.temperature,
                           let humidity = memory.humidity {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Environmental Conditions")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                
                                HStack(spacing: 20) {
                                    StatView(
                                        icon: "thermometer",
                                        value: "\(temperature)",
                                        unit: "°F",
                                        color: .orange
                                    )
                                    
                                    StatView(
                                        icon: "humidity",
                                        value: "\(humidity)",
                                        unit: "%",
                                        color: .cyan
                                    )
                                }
                            }
                        }
                        
                        // Tags
                        if !memory.tags.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Tags")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                
                                LazyVGrid(columns: [
                                    GridItem(.adaptive(minimum: 80))
                                ], spacing: 8) {
                                    ForEach(memory.tags, id: \.self) { tag in
                                        Text(tag)
                                            .font(.caption)
                                            .padding(.horizontal, 8)
                                            .padding(.vertical, 4)
                                            .background(Color.blue.opacity(0.1))
                                            .foregroundColor(.blue)
                                            .cornerRadius(8)
                                    }
                                }
                            }
                        }
                        
                        // Date
                        Text("Created \(memory.createdAt.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                }
            }
            .navigationTitle("Walk Memory")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Event Detail Sheet

struct EventDetailSheet: View {
    let event: CommunityEvent
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Event Header
                    VStack(alignment: .leading, spacing: 12) {
                        Text(event.title)
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text(event.description ?? "No description available")
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        // Event Details
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "calendar")
                                    .foregroundColor(.blue)
                                
                                Text(event.date.formatted(date: .abbreviated, time: .shortened))
                                    .font(.subheadline)
                            }
                            
                            HStack {
                                Image(systemName: "location")
                                    .foregroundColor(.blue)
                                
                                Text(event.location)
                                    .font(.subheadline)
                            }
                            
                            HStack {
                                Image(systemName: "person.2")
                                    .foregroundColor(.blue)
                                
                                Text("\(event.currentParticipants)/20 participants")
                                    .font(.subheadline)
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    // RSVP Button
                    Button(action: rsvpToEvent) {
                        Text("RSVP to Event")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                    }
                    .padding(.horizontal)
                }
            }
            .navigationTitle("Event Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func rsvpToEvent() {
        // Handle RSVP
        print("RSVP to \(event.title)")
    }
}

// MARK: - Supporting Views

struct StatView: View {
    let icon: String
    let value: String
    let unit: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.caption)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
            
            Text(unit)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Extensions
// coordinate property is now defined in PetFriendlyLocation struct in PlannerModels.swift

#Preview {
    LocationDetailSheet(location: PetFriendlyLocation.sampleData[0])
}
