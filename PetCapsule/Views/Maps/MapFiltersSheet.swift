//
//  MapFiltersSheet.swift
//  PetCapsule
//
//  Map filters and display options
//

import SwiftUI

struct MapFiltersSheet: View {
    @Environment(\.dismiss) private var dismiss
    
    @Binding var showPetFriendlyLocations: <PERSON><PERSON>
    @Binding var showWalkMemories: <PERSON><PERSON>
    @Binding var showCommunityEvents: <PERSON><PERSON>
    @Binding var showEmergencyLocations: Bo<PERSON>
    @Binding var selectedLocationTypes: Set<LocationType>
    
    var body: some View {
        NavigationView {
            Form {
                // Display Options
                Section("Display Options") {
                    Toggle("Pet-Friendly Locations", isOn: $showPetFriendlyLocations)
                        .toggleStyle(SwitchToggleStyle(tint: .blue))
                    
                    Toggle("Walk Memories", isOn: $showWalkMemories)
                        .toggleStyle(SwitchToggleStyle(tint: .purple))
                    
                    Toggle("Community Events", isOn: $showCommunityEvents)
                        .toggleStyle(SwitchToggleStyle(tint: .orange))
                    
                    Toggle("Emergency Locations", isOn: $showEmergencyLocations)
                        .toggleStyle(SwitchToggleStyle(tint: .red))
                }
                
                // Location Types
                Section("Location Types") {
                    ForEach(LocationType.allCases, id: \.self) { locationType in
                        HStack {
                            Image(systemName: locationType.icon)
                                .foregroundColor(locationType.color)
                                .frame(width: 20)
                            
                            Text(locationType.rawValue.capitalized)
                                .font(.subheadline)
                            
                            Spacer()
                            
                            Toggle("", isOn: Binding(
                                get: { selectedLocationTypes.contains(locationType) },
                                set: { isSelected in
                                    if isSelected {
                                        selectedLocationTypes.insert(locationType)
                                    } else {
                                        selectedLocationTypes.remove(locationType)
                                    }
                                }
                            ))
                            .toggleStyle(SwitchToggleStyle(tint: locationType.color))
                        }
                    }
                }
                
                // Quick Actions
                Section("Quick Actions") {
                    Button("Select All Location Types") {
                        selectedLocationTypes = Set(LocationType.allCases)
                    }
                    .foregroundColor(.blue)
                    
                    Button("Deselect All Location Types") {
                        selectedLocationTypes.removeAll()
                    }
                    .foregroundColor(.red)
                    
                    Button("Reset to Defaults") {
                        resetToDefaults()
                    }
                    .foregroundColor(.orange)
                }
            }
            .navigationTitle("Map Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Reset") {
                        resetToDefaults()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private func resetToDefaults() {
        showPetFriendlyLocations = true
        showWalkMemories = true
        showCommunityEvents = true
        showEmergencyLocations = false
        selectedLocationTypes = Set(LocationType.allCases)
    }
}

#Preview {
    MapFiltersSheet(
        showPetFriendlyLocations: .constant(true),
        showWalkMemories: .constant(true),
        showCommunityEvents: .constant(true),
        showEmergencyLocations: .constant(false),
        selectedLocationTypes: .constant(Set(LocationType.allCases))
    )
}
