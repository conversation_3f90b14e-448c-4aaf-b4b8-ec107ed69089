//
//  PetFriendlyPlacesView.swift
//  PetCapsule
//
//  Enhanced Pet-Friendly Places with Glassmorphism Design & Proper Location Handling
//
import SwiftUI
import MapKit
import CoreLocation
struct PetFriendlyPlacesView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var locationService = PetFriendlyPlacesLocationService()
    @State private var selectedLocationTypes: Set<LocationType> = [.park, .store, .restaurant, .veterinary]
    @State private var searchText = ""
    @State private var selectedLocation: PetFriendlyLocation?
    @State private var showingLocationDetails = false
    @State private var searchTask: Task<Void, Never>?
    @State private var cameraPosition: MapCameraPosition = .automatic
    @State private var showingLocationPermissionAlert = false
    @State private var hasRequestedPermission = false
    // Performance optimization: Debounced search
    private var debouncedSearch: String {
        searchText
    }
    var body: some View {
        ZStack {
            // Clean background
            Color(.systemGroupedBackground)
                .ignoresSafeArea()
            VStack(spacing: 0) {
                    // Custom Navigation Header
                    navigationHeader
                    // Search and Filter Section
                    searchAndFilterSection
                        .padding(.horizontal, 20)
                        .padding(.bottom, 16)
                    // Main Content
                    if locationService.authorizationStatus == .notDetermined && !hasRequestedPermission {
                        locationPermissionPrompt
                    } else if locationService.authorizationStatus == .denied || locationService.authorizationStatus == .restricted {
                        locationDeniedView
                    } else {
                        mapContentView
                    }
                }
            }
        .sheet(isPresented: $showingLocationDetails) {
            if let location = selectedLocation {
                LocationDetailSheet(location: location)
            }
        }
        .alert("Location Permission Required", isPresented: $showingLocationPermissionAlert) {
            Button("Enable in Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("PetTime Capsule needs location access to find pet-friendly places near you in Dublin, CA and surrounding areas.")
        }
        .onAppear {
            requestLocationPermissionIfNeeded()
        }
        .onDisappear {
            searchTask?.cancel()
        }
        .onChange(of: searchText) { _, newValue in
            searchTask?.cancel()
            searchTask = Task {
                try? await Task.sleep(nanoseconds: 300_000_000) // 0.3 second debounce
                if !Task.isCancelled {
                    // Trigger filtered results update
                }
            }
        }
        .onChange(of: locationService.userLocation) { _, newLocation in
            if let location = newLocation {
                withAnimation(.easeInOut(duration: 1.0)) {
                    cameraPosition = .region(MKCoordinateRegion(
                        center: location.coordinate,
                        latitudinalMeters: 10000,
                        longitudinalMeters: 10000
                    ))
                }
            }
        }
    }
    // MARK: - Navigation Header
    private var navigationHeader: some View {
        HStack {
            Button(action: {
                Task {
                    await locationService.searchNearbyPlaces()
                }
            }) {
                Image(systemName: locationService.isSearching ? "arrow.clockwise" : "location.magnifyingglass")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
                    .frame(width: 44, height: 44)
                    .background(Color(.systemBackground))
                    .clipShape(Circle())
                    .shadow(radius: 2)
                    .rotationEffect(.degrees(locationService.isSearching ? 360 : 0))
                    .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: locationService.isSearching)
            }
            .disabled(locationService.isSearching)
            Spacer()
            VStack(spacing: 2) {
                Text("Pet-Friendly Places")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                if !locationService.nearbyPlaces.isEmpty {
                    Text("\(filteredPlaces.count) found")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            Spacer()
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .frame(width: 44, height: 44)
                    .background(Color(.systemBackground))
                    .clipShape(Circle())
                    .shadow(radius: 2)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
        .padding(.bottom, 16)
        .background(Color(.systemGroupedBackground))
    }
    // MARK: - Search and Filter Section
    private var searchAndFilterSection: some View {
        VStack(spacing: 12) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                TextField("Search pet-friendly places...", text: $searchText)
                    .foregroundColor(.primary)
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(radius: 2)
            // Filter Options
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(LocationType.allCases, id: \.self) { type in
                        FilterButton(
                            title: type.displayName,
                            icon: type.icon,
                            isSelected: selectedLocationTypes.contains(type),
                            color: type.color
                        ) {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                if selectedLocationTypes.contains(type) {
                                    selectedLocationTypes.remove(type)
                                } else {
                                    selectedLocationTypes
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.horizontal, 20)
    }
    // MARK: - Location Permission Prompt
    private var locationPermissionPrompt: some View {
        VStack(spacing: 24) {
            Spacer()
            VStack(spacing: 20) {
                // Icon
                ZStack {
                    Circle()
                        .fill(LinearGradient.glassAccentGradient)
                        .frame(width: 80, height: 80)
                    Image(systemName: "location.circle.fill")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(.white)
                }
                // Content
                VStack(spacing: 12) {
                    Text("Find Pet Places Near You")
                        .font(.glassTitle)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                    Text("We'll help you discover amazing pet-friendly locations in Dublin, CA and surrounding areas including parks, stores, restaurants, and veterinary clinics.")
                        .font(.glassBody)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                }
                // Permission Button
                MagicButton(
                    title: "Enable Location Access",
                    icon: "location.fill",
                    style: .primary
                ) {
                    requestLocationPermission()
                }
                .padding(.horizontal, 40)
            }
            .glassCard(intensity: 0.3)
            .padding(.horizontal, 24)
            Spacer()
        }
    }
    // MARK: - Location Denied View
    private var locationDeniedView: some View {
        VStack(spacing: 24) {
            Spacer()
            VStack(spacing: 20) {
                // Icon
                ZStack {
                    Circle()
                        .fill(Color.red.opacity(0.8))
                        .frame(width: 80, height: 80)
                    Image(systemName: "location.slash")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(.white)
                }
                // Content
                VStack(spacing: 12) {
                    Text("Location Access Needed")
                        .font(.glassTitle)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                    Text("To find pet-friendly places in Dublin, CA, please enable location access in Settings. We respect your privacy and only use location to show nearby results.")
                        .font(.glassBody)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                }
                // Settings Button
                MagicButton(
                    title: "Open Settings",
                    icon: "gear",
                    style: .secondary
                ) {
                    if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsUrl)
                    }
                }
                .padding(.horizontal, 40)
            }
            .glassCard(intensity: 0.3)
            .padding(.horizontal, 24)
            Spacer()
        }
    }
    // MARK: - Map Content View
    private var mapContentView: some View {
        ZStack {
            // Map
            Map(position: $cameraPosition) {
                // User location
                if let userLocation = locationService.userLocation {
                    Annotation("Your Location", coordinate: userLocation.coordinate) {
                        ZStack {
                            Circle()
                                .fill(LinearGradient.glassAccentGradient)
                                .frame(width: 20, height: 20)
                                .shadow(color: .blue.opacity(0.5), radius: 8)
                            Circle()
                                .stroke(Color.white, lineWidth: 3)
                                .frame(width: 20, height: 20)
                        }
                    }
                }
                // Pet-friendly locations
                ForEach(filteredLocations, id: \.id) { location in
                    Annotation(location.name, coordinate: location.coordinate) {
                        LocationMarker(location: location)
                            .onTapGesture {
                                selectedLocation = location
                                showingLocationDetails = true
                                withAnimation(.easeInOut(duration: 0.5)) {
                                    cameraPosition = .region(MKCoordinateRegion(
                                        center: location.coordinate,
                                        latitudinalMeters: 5000,
                                        longitudinalMeters: 5000
                                    ))
                                }
                            }
                    }
                }
            }
            .clipShape(RoundedRectangle(cornerRadius: 24))
            .overlay(
                RoundedRectangle(cornerRadius: 24)
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
            // Loading/Error Overlay
            if locationService.isSearching || locationService.searchError != nil {
                searchStatusOverlay
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
    // MARK: - Search Status Overlay
    private var searchStatusOverlay: some View {
        VStack {
            if locationService.isSearching {
                HStack(spacing: 12) {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(.white)
                    Text(locationService.searchError ?? "Finding amazing places...")
                        .font(.glassCaption)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .fill(Color.black.opacity(0.6))
                        .background(.ultraThinMaterial)
                )
                .padding(.top, 20)
            } else if let error = locationService.searchError {
                HStack(spacing: 12) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    Text(error)
                        .font(.glassCaption)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .fill(Color.red.opacity(0.6))
                        .background(.ultraThinMaterial)
                )
                .padding(.top, 20)
            }
            Spacer()
        }
    }
    // MARK: - Computed Properties
    private var filteredLocations: [PetFriendlyLocation] {
        let typeFiltered = locationService.nearbyPlaces.filter { location in
            selectedLocationTypes.contains(location.type)
        }
        if searchText.isEmpty {
            return typeFiltered
        } else {
            return typeFiltered.filter { location in
                location.name.localizedCaseInsensitiveContains(searchText) ||
                location.address.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    private var filteredPlaces: [PetFriendlyLocation] {
        return filteredLocations
    }
    // MARK: - Helper Methods
    private func requestLocationPermissionIfNeeded() {
        if locationService.authorizationStatus == .notDetermined && !hasRequestedPermission {
            // Don't auto-request, wait for user action
            return
        }
        if locationService.authorizationStatus == .authorizedWhenInUse || 
           locationService.authorizationStatus == .authorizedAlways {
            Task {
                await locationService.getCurrentLocationAndSearchPlaces()
            }
        }
    }
    private func requestLocationPermission() {
        hasRequestedPermission = true
        Task {
            await locationService.getCurrentLocationAndSearchPlaces()
        }
    }
}
// MARK: - Filter Chip Component
struct PlacesFilterChip: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                Text(title)
                    .font(.system(size: 14, weight: .semibold, design: .rounded))
            }
            .foregroundColor(isSelected ? .white : .white.opacity(0.8))
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                Capsule()
                    .fill(
                        isSelected 
                        ? AnyShapeStyle(LinearGradient.glassAccentGradient)
                        : AnyShapeStyle(Color.white.opacity(0.2))
                    )
                    .background(.ultraThinMaterial)
            )
            .overlay(
                Capsule()
                    .stroke(
                        isSelected 
                        ? Color.white.opacity(0.5)
                        : Color.white.opacity(0.3), 
                        lineWidth: 1
                    )
            )
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .shadow(
                color: isSelected ? Color.blue.opacity(0.3) : Color.clear,
                radius: isSelected ? 8 : 0
            )
        }
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}
// MARK: - Location Marker Component
struct LocationMarker: View {
    let location: PetFriendlyLocation
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .fill(getMarkerColor())
                .frame(width: 36, height: 36)
                .shadow(color: .black.opacity(0.3), radius: 4)
            // Icon
            Image(systemName: location.type.icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
        }
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.2), value: location.id)
    }
    private func getMarkerColor() -> Color {
        switch location.type {
        case .park:
            return .green
        case .veterinary:
            return .red
        case .store:
            return .purple
        case .restaurant:
            return .orange
        case .hotel:
            return .blue
        case .trail:
            return .brown
        case .beach:
            return .cyan
        }
    }
}
// MARK: - Enhanced Location Service
@MainActor
class PetFriendlyPlacesLocationService: NSObject, ObservableObject {
    @Published var nearbyPlaces: [PetFriendlyLocation] = []
    @Published var isSearching = false
    @Published var searchError: String?
    @Published var userLocation: CLLocation?
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined
    private let locationManager = CLLocationManager()
    private var locationCompletion: ((CLLocation?) -> Void)?
    private let searchRadius: CLLocationDistance = 10000 // 10km
    private var isWaitingForLocation = false
    override init() {
        super.init()
        setupLocationManager()
    }
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        authorizationStatus = locationManager.authorizationStatus
    }
    func getCurrentLocationAndSearchPlaces() async {
        let authStatus = locationManager.authorizationStatus
        switch authStatus {
        case .notDetermined:
            print("📍 Requesting location permission...")
            searchError = "Requesting location permission..."
            await requestLocationPermission()
            if locationManager.authorizationStatus == .authorizedWhenInUse || 
               locationManager.authorizationStatus == .authorizedAlways {
                await performLocationSearch()
            } else {
                searchError = "Location permission is required to find nearby pet-friendly places."
                print("❌ Location permission denied")
            }
        case .denied, .restricted:
            searchError = "Location access is required to find nearby places. Please enable in Settings."
            print("❌ Location access denied or restricted")
        case .authorizedWhenInUse, .authorizedAlways:
            print("✅ Location permission granted, performing search...")
            await performLocationSearch()
        @unknown default:
            searchError = "Unknown location authorization status"
        }
    }
    private func requestLocationPermission() async {
        locationManager.requestWhenInUseAuthorization()
        // Wait for permission response
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        authorizationStatus = locationManager.authorizationStatus
    }
    private func performLocationSearch() async {
        isSearching = true
        searchError = "Getting your location..."
        print("📍 Requesting current location...")
        guard !isWaitingForLocation else {
            print("⚠️ Already waiting for location, skipping request")
            return
        }
        isWaitingForLocation = true
        defer { isWaitingForLocation = false }
        await withCheckedContinuation { continuation in
            locationCompletion = { location in
                continuation.resume()
            }
            locationManager.requestLocation()
        }
        guard let currentLocation = userLocation else {
            searchError = "Unable to determine your current location. Please ensure location services are enabled."
            isSearching = false
            print("❌ Failed to get current location")
            return
        }
        print("🗺️ Got location: \(currentLocation.coordinate.latitude), \(currentLocation.coordinate.longitude)")
        await searchNearbyPlaces(location: currentLocation)
    }
    func searchNearbyPlaces(location: CLLocation? = nil) async {
        let searchLocation = location ?? userLocation
        guard let searchLocation = searchLocation else {
            searchError = "Location not available"
            return
        }
        isSearching = true
        searchError = "Discovering amazing pet places..."
        var allResults: [PetFriendlyLocation] = []
        let searchQueries = [
            "dog park",
            "veterinarian animal hospital",
            "pet store pet supplies",
            "pet grooming",
            "pet friendly restaurant",
            "hiking trail dogs allowed",
            "pet hotel boarding"
        ]
        for query in searchQueries {
            do {
                let places = try await searchPlaces(query: query, near: searchLocation)
                allResults.append(contentsOf: places)
            } catch {
                print("Error searching for \(query): \(error)")
            }
        }
        // Remove duplicates and sort by distance
        let uniquePlaces = removeDuplicates(from: allResults)
        let sortedPlaces = uniquePlaces.sorted { $0.distance < $1.distance }
        nearbyPlaces = sortedPlaces
        isSearching = false
        searchError = nil
        print("✅ Found \(nearbyPlaces.count) pet-friendly places")
    }
    private func searchPlaces(query: String, near location: CLLocation) async throws -> [PetFriendlyLocation] {
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = query
        request.region = MKCoordinateRegion(
            center: location.coordinate,
            latitudinalMeters: searchRadius * 2,
            longitudinalMeters: searchRadius * 2
        )
        let search = MKLocalSearch(request: request)
        let response = try await search.start()
        return response.mapItems.compactMap { mapItem in
            createPetFriendlyLocation(from: mapItem, userLocation: location)
        }
    }
    private func createPetFriendlyLocation(from mapItem: MKMapItem, userLocation: CLLocation) -> PetFriendlyLocation? {
        guard let coordinate = mapItem.placemark.location?.coordinate else { return nil }
        let placeLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        let distance = userLocation.distance(from: placeLocation) / 1000.0 // Convert to kilometers
        guard distance <= searchRadius / 1000.0 * 2 else { return nil }
        let locationType = determineLocationType(from: mapItem)
        let amenities = extractAmenities(for: locationType, from: mapItem)
        return PetFriendlyLocation(
            name: mapItem.name ?? "Unknown Place",
            type: locationType,
            rating: Double.random(in: 3.5...4.8),
            distance: distance,
            address: formatAddress(from: mapItem.placemark),
            imageURL: getPlaceholderImageURL(for: locationType),
            amenities: amenities,
            isOpen: isCurrentlyOpen(mapItem),
            coordinate: coordinate,
            phoneNumber: mapItem.phoneNumber,
            website: mapItem.url?.absoluteString,
            hours: getBusinessHours(for: mapItem)
        )
    }
    private func determineLocationType(from mapItem: MKMapItem) -> LocationType {
        let name = mapItem.name?.lowercased() ?? ""
        let category = mapItem.pointOfInterestCategory
        if category == .hospital || category == .pharmacy || name.contains("vet") || name.contains("animal hospital") {
            return .veterinary
        } else if name.contains("pet") && name.contains("store") {
            return .store
        } else if name.contains("park") || name.contains("dog park") || category == .park {
            return .park
        } else if category == .restaurant || category == .cafe || name.contains("restaurant") || name.contains("cafe") {
            return .restaurant
        } else if name.contains("trail") || name.contains("hiking") || category == .nationalPark {
            return .trail
        } else if name.contains("beach") || name.contains("shore") {
            return .beach
        } else if category == .hotel || name.contains("hotel") || name.contains("boarding") {
            return .hotel
        } else {
            return .store // Default fallback
        }
    }
    private func extractAmenities(for type: LocationType, from mapItem: MKMapItem) -> [String] {
        switch type {
        case .park:
            return ["Off-leash area", "Water fountains", "Waste bags", "Seating"]
        case .veterinary:
            return ["Emergency care", "Vaccinations", "Surgery", "Grooming"]
        case .store:
            return ["Pet food", "Toys", "Accessories", "Grooming supplies"]
        case .restaurant:
            return ["Outdoor seating", "Water bowls", "Pet menu", "Dog-friendly patio"]
        case .hotel:
            return ["Pet beds", "Walking service", "Pet sitting", "Special pet menu"]
        case .trail:
            return ["Scenic views", "Moderate difficulty", "Water access", "Parking available"]
        case .beach:
            return ["Off-leash allowed", "Water access", "Scenic views", "Parking"]
        }
    }
    private func getPlaceholderImageURL(for type: LocationType) -> String {
        switch type {
        case .park:
            return "https://images.unsplash.com/photo-1544737151-6e4b9e0e4d5a?w=400&h=300&fit=crop"
        case .veterinary:
            return "https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=400&h=300&fit=crop"
        case .store:
            return "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop"
        case .restaurant:
            return "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop"
        case .hotel:
            return "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=300&fit=crop"
        case .trail:
            return "https://images.unsplash.com/photo-1551632811-561732d1e306?w=400&h=300&fit=crop"
        case .beach:
            return "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=400&h=300&fit=crop"
        }
    }
    private func isCurrentlyOpen(_ mapItem: MKMapItem) -> Bool {
        // Placeholder logic - in a real app, you'd parse actual business hours
        return Bool.random()
    }
    private func getBusinessHours(for mapItem: MKMapItem) -> String? {
        let locationType = determineLocationType(from: mapItem)
        switch locationType {
        case .park, .trail, .beach:
            return "6:00 AM - 10:00 PM"
        case .veterinary:
            return "8:00 AM - 6:00 PM"
        case .store:
            return "9:00 AM - 8:00 PM"
        case .restaurant:
            return "11:00 AM - 10:00 PM"
        case .hotel:
            return "24 Hours"
        }
    }
    private func formatAddress(from placemark: CLPlacemark) -> String {
        var components: [String] = []
        if let streetNumber = placemark.subThoroughfare {
            components.append(streetNumber)
        }
        if let streetName = placemark.thoroughfare {
            components.append(streetName)
        }
        if let city = placemark.locality {
            components.append(city)
        }
        if let state = placemark.administrativeArea {
            components.append(state)
        }
        return components.joined(separator: " ")
    }
    private func removeDuplicates(from places: [PetFriendlyLocation]) -> [PetFriendlyLocation] {
        var uniquePlaces: [PetFriendlyLocation] = []
        var seenNames: Set<String> = []
        for place in places {
            let key = "\(place.name.lowercased())_\(place.address.prefix(20))"
            if !seenNames.contains(key) {
                seenNames
                uniquePlaces.append(place)
            }
        }
        return uniquePlaces
    }
}
// MARK: - CLLocationManagerDelegate
extension PetFriendlyPlacesLocationService: CLLocationManagerDelegate {
    nonisolated func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        Task { @MainActor in
            self.userLocation = location
            if let completion = self.locationCompletion {
                completion(location)
                self.locationCompletion = nil
            }
        }
    }
    nonisolated func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("⚠️ Location error: \(error.localizedDescription)")
        Task { @MainActor in
            self.searchError = "Failed to get location: \(error.localizedDescription)"
            self.isSearching = false
            if let completion = self.locationCompletion {
                completion(nil)
                self.locationCompletion = nil
            }
        }
    }
    nonisolated func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        Task { @MainActor in
            self.authorizationStatus = status
            switch status {
            case .authorizedWhenInUse, .authorizedAlways:
                print("✅ Location access granted")
            case .denied, .restricted:
                print("❌ Location access denied")
                self.searchError = "Location access denied"
                self.isSearching = false
            case .notDetermined:
                break
            @unknown default:
                break
            }
        }
    }
} 