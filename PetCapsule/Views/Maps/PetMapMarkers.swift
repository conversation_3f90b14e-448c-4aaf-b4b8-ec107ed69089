//
//  PetMapMarkers.swift
//  PetCapsule
//
//  Custom pet-themed map markers for Apple Maps integration
//

import SwiftUI
import MapKit

// MARK: - Pet Location Marker

struct PetLocationMarker: View {
    let location: PetFriendlyLocation
    @State private var isAnimating = false

    var body: some View {
        ZStack {
            // Background circle with glow effect
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            location.type.color.opacity(0.3),
                            location.type.color.opacity(0.1),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 5,
                        endRadius: 25
                    )
                )
                .frame(width: 50, height: 50)
                .scaleEffect(isAnimating ? 1.2 : 1.0)
                .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: isAnimating)

            // Main marker
            ZStack {
                // Outer ring
                Circle()
                    .fill(Color.white)
                    .frame(width: 32, height: 32)
                    .shadow(color: .black.opacity(0.2), radius: 3, x: 0, y: 2)

                // Inner circle with icon
                Circle()
                    .fill(location.type.color)
                    .frame(width: 26, height: 26)

                // Icon
                Image(systemName: location.type.icon)
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.white)
            }
        }
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - User Location Marker

struct UserLocationMarker: View {
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        ZStack {
            // Pulse effect
            Circle()
                .fill(Color.blue.opacity(0.3))
                .frame(width: 40, height: 40)
                .scaleEffect(pulseScale)
                .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: pulseScale)
            
            // Main location dot
            Circle()
                .fill(Color.blue)
                .frame(width: 16, height: 16)
                .overlay(
                    Circle()
                        .stroke(Color.white, lineWidth: 3)
                )
                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
        }
        .onAppear {
            pulseScale = 1.5
        }
    }
}

// MARK: - Walk Memory Marker

struct WalkMemoryMarker: View {
    let memory: WalkMemory
    @State private var isSelected = false
    
    var body: some View {
        Button(action: {
            isSelected.toggle()
        }) {
            ZStack {
                // Memory photo background
                if let firstImageUrl = memory.mediaUrls.first,
                   let url = URL(string: firstImageUrl) {
                    AsyncImage(url: url) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                    }
                    .frame(width: 40, height: 40)
                    .clipShape(Circle())
                } else {
                    // Default memory icon
                    Circle()
                        .fill(LinearGradient(
                            colors: [Color.purple, Color.pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 40, height: 40)
                        .overlay(
                            Image(systemName: "camera.fill")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                        )
                }
                
                // Border and shadow
                Circle()
                    .stroke(Color.white, lineWidth: 3)
                    .frame(width: 40, height: 40)
                    .shadow(color: .black.opacity(0.2), radius: 3, x: 0, y: 2)
                
                // Favorite indicator
                if memory.isFavorite {
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: "heart.fill")
                                .font(.system(size: 10))
                                .foregroundColor(.red)
                                .background(
                                    Circle()
                                        .fill(Color.white)
                                        .frame(width: 16, height: 16)
                                )
                                .offset(x: 6, y: -6)
                        }
                        Spacer()
                    }
                    .frame(width: 40, height: 40)
                }
            }
        }
        .scaleEffect(isSelected ? 1.2 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isSelected)
    }
}

// MARK: - Event Marker

struct EventMarker: View {
    let event: CommunityEvent
    @State private var isAnimating = false
    
    var body: some View {
        ZStack {
            // Event glow effect
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color.orange.opacity(0.4),
                            Color.orange.opacity(0.2),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 5,
                        endRadius: 30
                    )
                )
                .frame(width: 60, height: 60)
                .scaleEffect(isAnimating ? 1.3 : 1.0)
                .animation(.easeInOut(duration: 2.5).repeatForever(autoreverses: true), value: isAnimating)
            
            // Main event marker
            ZStack {
                // Background
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.orange)
                    .frame(width: 36, height: 36)
                    .shadow(color: .black.opacity(0.3), radius: 3, x: 0, y: 2)
                
                // Event icon
                Image(systemName: "calendar.badge.plus")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)
            }
            
            // Participant count badge
            if event.attendeeIDs.count > 0 {
                VStack {
                    HStack {
                        Spacer()
                        ParticipantBadge(count: event.attendeeIDs.count)
                            .offset(x: 10, y: -10)
                    }
                    Spacer()
                }
                .frame(width: 36, height: 36)
            }
        }
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Emergency Marker

struct EmergencyMarker: View {
    let type: MapEmergencyType
    @State private var isFlashing = false
    
    var body: some View {
        ZStack {
            // Emergency pulse
            Circle()
                .fill(Color.red.opacity(0.3))
                .frame(width: 50, height: 50)
                .scaleEffect(isFlashing ? 1.5 : 1.0)
                .opacity(isFlashing ? 0.3 : 0.8)
                .animation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true), value: isFlashing)
            
            // Main emergency marker
            ZStack {
                Circle()
                    .fill(Color.red)
                    .frame(width: 32, height: 32)
                    .shadow(color: .black.opacity(0.4), radius: 4, x: 0, y: 2)
                
                Image(systemName: type.icon)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
            }
        }
        .onAppear {
            isFlashing = true
        }
    }
}

// MARK: - Supporting Views

struct RatingBadge: View {
    let rating: Double
    
    var body: some View {
        HStack(spacing: 2) {
            Image(systemName: "star.fill")
                .font(.system(size: 8))
                .foregroundColor(.yellow)
            
            Text(String(format: "%.1f", rating))
                .font(.system(size: 8, weight: .bold))
                .foregroundColor(.white)
        }
        .padding(.horizontal, 4)
        .padding(.vertical, 2)
        .background(
            Capsule()
                .fill(Color.black.opacity(0.7))
        )
    }
}

struct ParticipantBadge: View {
    let count: Int
    
    var body: some View {
        Text("\(count)")
            .font(.system(size: 10, weight: .bold))
            .foregroundColor(.white)
            .frame(width: 18, height: 18)
            .background(
                Circle()
                    .fill(Color.green)
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 2)
                    )
            )
    }
}

// MARK: - Supporting Enums

enum MapEmergencyType {
    case veterinary
    case animalHospital
    case emergencyClinic
    
    var icon: String {
        switch self {
        case .veterinary: return "cross.fill"
        case .animalHospital: return "building.2.fill"
        case .emergencyClinic: return "plus.circle.fill"
        }
    }
}

// MARK: - Location Type Extension
// Using LocationType extension from PlannerModels.swift to avoid conflicts

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        PetLocationMarker(location: PetFriendlyLocation.sampleData[0])
        UserLocationMarker()
        // EventMarker(event: CommunityEvent.sampleData[0]) // Commented out - no sample data available
        EmergencyMarker(type: .veterinary)
    }
    .padding()
}
