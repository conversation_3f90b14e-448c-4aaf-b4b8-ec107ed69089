//
//  MarketplaceView.swift
//  PetCapsule
//
//  Marketplace for $300K/month additional revenue
//

import SwiftUI

struct MarketplaceView: View {
    @StateObject private var marketplaceService = MarketplaceService.shared
    @State private var searchText = ""
    @State private var selectedCategory: ProductCategory?
    @State private var showingCart = false
    @State private var showingRevenueDashboard = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Revenue Dashboard (for business users)
                    if SubscriptionService.shared.hasFeature(.businessTools) {
                        RevenueDashboardCard()
                    }
                    
                    // Search Bar
                    SearchBar(text: $searchText)
                    
                    // Categories
                    CategoriesSection(
                        categories: marketplaceService.categories,
                        selectedCategory: $selectedCategory
                    )
                    
                    // Featured Products
                    FeaturedProductsSection(
                        products: filteredProducts,
                        onProductTap: { product in
                            // Navigate to product detail
                        }
                    )
                    
                    // Popular Categories
                    PopularCategoriesSection()
                    
                    // Success Stories
                    SuccessStoriesSection()
                }
                .padding(.horizontal)
                .padding(.top)
            }
            .navigationTitle("Everything They Need")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingCart = true
                    }) {
                        Image(systemName: "cart.fill")
                            .font(.title3)
                    }
                }
                
                if SubscriptionService.shared.hasFeature(.businessTools) {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Revenue") {
                            showingRevenueDashboard = true
                        }
                        .font(.callout)
                        .foregroundColor(.green)
                    }
                }
            }
            .sheet(isPresented: $showingCart) {
                CartView()
            }
            .sheet(isPresented: $showingRevenueDashboard) {
                RevenueDashboardView()
            }
        }
    }
    
    private var filteredProducts: [MarketplaceProduct] {
        var products = marketplaceService.featuredProducts
        
        if let category = selectedCategory {
            products = products.filter { $0.categoryId == category.id }
        }
        
        if !searchText.isEmpty {
            products = products.filter {
                $0.name.localizedCaseInsensitiveContains(searchText) ||
                $0.description.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return products
    }
}

struct RevenueDashboardCard: View {
    @StateObject private var marketplaceService = MarketplaceService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                Text("Marketplace Revenue")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Text("This Month")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("$\(marketplaceService.monthlyRevenue, specifier: "%.0f")")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text("Commission Earned")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(marketplaceService.recentPurchases.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    
                    Text("Orders")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            Text("💡 Promote products to your community to increase earnings")
                .font(.caption)
                .foregroundColor(.blue)
                .padding(.top, 4)
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(12)
    }
}

struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("Search products...", text: $text)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !text.isEmpty {
                Button(action: {
                    text = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
}

struct CategoriesSection: View {
    let categories: [ProductCategory]
    @Binding var selectedCategory: ProductCategory?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Categories")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // All category
                    CategoryChip(
                        name: "All",
                        icon: "square.grid.2x2",
                        isSelected: selectedCategory == nil,
                        onTap: {
                            selectedCategory = nil
                        }
                    )
                    
                    ForEach(categories) { category in
                        CategoryChip(
                            name: category.name,
                            icon: category.icon,
                            isSelected: selectedCategory?.id == category.id,
                            onTap: {
                                selectedCategory = selectedCategory?.id == category.id ? nil : category
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

struct CategoryChip: View {
    let name: String
    let icon: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.title3)
                
                Text(name)
                    .font(.caption)
                    .foregroundColor(isSelected ? .white : .primary)
                    .multilineTextAlignment(.center)
            }
            .frame(width: 80, height: 70)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(isSelected ? Color.blue : Color.white)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
        }
    }
}

struct FeaturedProductsSection: View {
    let products: [MarketplaceProduct]
    let onProductTap: (MarketplaceProduct) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Featured Products")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to all products
                }
                .font(.callout)
                .foregroundColor(.blue)
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(products.prefix(6)) { product in
                    ProductCard(product: product, onTap: {
                        onProductTap(product)
                    })
                }
            }
        }
    }
}

struct ProductCard: View {
    let product: MarketplaceProduct
    let onTap: () -> Void
    @StateObject private var marketplaceService = MarketplaceService.shared
    @State private var showingPurchase = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                // Product Image
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 120)
                    .overlay(
                        VStack {
                            Image(systemName: getProductIcon())
                                .font(.system(size: 30))
                            
                            if product.isPopular {
                                Text("POPULAR")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 4)
                                    .padding(.vertical, 2)
                                    .background(Color.red)
                                    .cornerRadius(4)
                            }
                        }
                    )
                
                    .overlay(
                        VStack {
                            HStack {
                                if let discount = product.discountPercentage {
                                    Text("\(discount)% OFF")
                                        .font(.caption)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 4)
                                        .padding(.vertical, 2)
                                        .background(Color.red)
                                        .cornerRadius(4)
                                }
                                Spacer()
                            }
                            Spacer()
                        }
                        .padding(4),
                        alignment: .topLeading
                    )
                
                // Product Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name)
                        .font(.callout)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .lineLimit(2)
                    
                    HStack {
                        HStack(spacing: 2) {
                            Image(systemName: "star.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text("\(product.rating, specifier: "%.1f")")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 2) {
                            if let originalPrice = product.formattedOriginalPrice {
                                Text(originalPrice)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .strikethrough()
                            }
                            
                            Text(product.formattedPrice)
                                .font(.callout)
                                .fontWeight(.bold)
                        }
                    }
                }
                
                // Quick Buy Button
                Button("Quick Buy") {
                    showingPurchase = true
                }
                .font(.caption)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 4)
                .background(Color.blue)
                .cornerRadius(8)
            }
            .padding(8)
            .background(Color.white)
            .cornerRadius(10)
            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
        .alert("Purchase \(product.name)?", isPresented: $showingPurchase) {
            Button("Cancel", role: .cancel) { }
            Button("Buy Now") {
                Task {
                    try? await marketplaceService.purchaseProduct(product)
                }
            }
        } message: {
            Text("Price: \(product.formattedPrice)")
        }
    }
    
    private func getProductIcon() -> String {
        switch product.categoryId {
        case "memory_books": return "book.fill"
        case "custom_portraits": return "paintbrush.fill"
        case "memorial_items": return "heart.fill"
        case "pet_care": return "cross.case.fill"
        case "accessories": return "tag.fill"
        case "services": return "person.2.fill"
        default: return "gift.fill"
        }
    }
}

struct PopularCategoriesSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Popular This Month")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                PopularCategoryRow(
                    name: "Memory Books",
                    sales: "2,847 sold",
                    revenue: "$142,350",
                    icon: "book.fill",
                    color: .blue
                )
                
                PopularCategoryRow(
                    name: "Custom Portraits",
                    sales: "1,923 sold",
                    revenue: "$96,150",
                    icon: "paintbrush.fill",
                    color: .purple
                )
                
                PopularCategoryRow(
                    name: "Memorial Items",
                    sales: "856 sold",
                    revenue: "$68,480",
                    icon: "heart.fill",
                    color: .pink
                )
            }
        }
    }
}

struct PopularCategoryRow: View {
    let name: String
    let sales: String
    let revenue: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(name)
                    .font(.callout)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(sales)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(revenue)
                    .font(.callout)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                
                Text("Revenue")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(Color.white)
        .cornerRadius(10)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct SuccessStoriesSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Success Stories")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                SuccessStoryCard(
                    title: "Small Business Owner Increases Sales by 50%",
                    description: "A local pet bakery used our marketplace to reach a wider audience and saw a huge boost in sales.",
                    earnings: "+$5,000/month",
                    category: "Business Tools"
                )
                
                SuccessStoryCard(
                    title: "Pet Photographer Gets Fully Booked",
                    description: "A freelance photographer listed their services and is now fully booked for the next 3 months.",
                    earnings: "+$12,000/month",
                    category: "Services"
                )
            }
        }
    }
}

struct SuccessStoryCard: View {
    let title: String
    let description: String
    let earnings: String
    let category: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text(description)
                .font(.callout)
                .foregroundColor(.secondary)
            
            HStack {
                Text(earnings)
                    .font(.callout)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                
                Spacer()
                
                Text(category)
                    .font(.caption)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding(12)
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(10)
    }
}

// Placeholder views
struct CartView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Shopping Cart")
                    .font(.title2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct RevenueDashboardView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Revenue Dashboard")
                    .font(.title2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    MarketplaceView()
}
