import SwiftUI

@available(iOS 18.0, *)
struct ApplePhotosMemoryView: View {
    @EnvironmentObject var realDataService: RealDataService
    @StateObject private var productionMemoryService = ProductionMemoryService.shared
    
    @State private var showAddMemory = false
    @State private var selectedMemory: Memory?
    @State private var showMemoryDetail = false
    @State private var searchText = ""
    @State private var showSearch = false
    @State private var selectedDate: Date = Date()
    @State private var viewMode: ViewMode = .months
    
    enum ViewMode: String, CaseIterable {
        case months = "Months"
        case years = "Years"
        case all = "All"
        
        var icon: String {
            switch self {
            case .months: return "calendar"
            case .years: return "calendar.badge.clock"
            case .all: return "square.grid.3x3"
            }
        }
    }
    
    // Apple Photos style grid
    private let columns = [
        GridItem(.adaptive(minimum: 120), spacing: 2),
        GridItem(.adaptive(minimum: 120), spacing: 2),
        GridItem(.adaptive(minimum: 120), spacing: 2)
    ]
    
    private var photoVideoMemories: [Memory] {
        let filtered = realDataService.memories.filter { memory in
            memory.type == .photo || memory.type == .video
        }
        
        // Apply search filter if active
        if !searchText.isEmpty {
            return filtered.filter { memory in
                memory.title.localizedCaseInsensitiveContains(searchText) ||
                memory.content.localizedCaseInsensitiveContains(searchText) ||
                memory.tags.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }
        
        return filtered
    }
    
    private var groupedMemories: [String: [Memory]] {
        switch viewMode {
        case .months:
            return Dictionary(grouping: photoVideoMemories) { memory in
                DateFormatter.monthYear.string(from: memory.createdAt)
            }
        case .years:
            return Dictionary(grouping: photoVideoMemories) { memory in
                DateFormatter.year.string(from: memory.createdAt)
            }
        case .all:
            return ["All Memories": photoVideoMemories]
        }
    }
    
    private var sortedSections: [String] {
        groupedMemories.keys.sorted { date1, date2 in
            guard let d1 = DateFormatter.monthYear.date(from: date1),
                  let d2 = DateFormatter.monthYear.date(from: date2) else {
                return date1 > date2
            }
            return d1 > d2
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Apple Photos style header
                headerView
                
                // Photos grid
                photosGrid
            }
            .navigationTitle("Precious Moments")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 16) {
                        Button("Sample") {
                            Task {
                                await realDataService.loadUserMemories()
                            }
                        }
                        .font(.subheadline)
                        .foregroundColor(.blue)
                        
                        Button(action: { showAddMemory = true }) {
                            Image(systemName: "plus")
                                .font(.title2)
                                .foregroundColor(.primary)
                        }
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { showSearch.toggle() }) {
                        Image(systemName: "magnifyingglass")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                }
            }
        }
        .sheet(isPresented: $showAddMemory) {
            AddMemoryView()
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showMemoryDetail) {
            if let selectedMemory = selectedMemory {
                ApplePhotosDetailView(memory: selectedMemory)
                    .environmentObject(realDataService)
            }
        }
        .onAppear {
            productionMemoryService.setRealDataService(realDataService)
            
            if realDataService.memories.isEmpty && !realDataService.isLoading {
                Task {
                    await realDataService.refreshAllData()
                }
            }
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 0) {
            // View Mode Selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(ViewMode.allCases, id: \.self) { mode in
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                viewMode = mode
                            }
                        }) {
                            HStack(spacing: 6) {
                                Image(systemName: mode.icon)
                                    .font(.caption)
                                Text(mode.rawValue)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(viewMode == mode ? .white : .primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                Capsule()
                                    .fill(viewMode == mode ? Color.blue : Color(.systemGray6))
                            )
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
            .padding(.vertical, 12)
            
            if showSearch {
                HStack {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                        
                        TextField("Search memories", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                    
                    Button("Cancel") {
                        showSearch = false
                        searchText = ""
                    }
                    .foregroundColor(.blue)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color(.systemBackground))
                .transition(.move(edge: .top))
            }
            
            Divider()
        }
    }
    
    private var photosGrid: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                if photoVideoMemories.isEmpty {
                    emptyStateView
                } else {
                    ForEach(sortedSections, id: \.self) { section in
                        sectionView(for: section)
                    }
                }
            }
        }
        .background(Color(.systemBackground))
    }
    
    private func sectionView(for section: String) -> some View {
        VStack(spacing: 0) {
            // Section header
            HStack {
                Text(section)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("\(groupedMemories[section]?.count ?? 0)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 16)
            .padding(.top, 20)
            .padding(.bottom, 8)
            
            // Photo grid for this section
            let memories = groupedMemories[section] ?? []
            LazyVGrid(columns: columns, spacing: 2) {
                ForEach(memories, id: \.id) { memory in
                    ApplePhotosMemoryThumbnail(memory: memory) {
                        selectedMemory = memory
                        showMemoryDetail = true
                    }
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "photo.stack")
                .font(.system(size: 64, weight: .ultraLight))
                .foregroundColor(.secondary)
            
            VStack(spacing: 12) {
                Text("No Photos or Videos")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("Start capturing memories with your pets")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: { showAddMemory = true }) {
                Text("Add Your First Memory")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(25)
            }
        }
        .padding(40)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Apple Photos Memory Thumbnail
@available(iOS 18.0, *)
struct ApplePhotosMemoryThumbnail: View {
    let memory: Memory
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Thumbnail image
                AsyncImage(url: URL(string: memory.thumbnailURL ?? memory.mediaURL ?? "")) { phase in
                    switch phase {
                    case .success(let image):
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 120)
                            .clipped()
                    case .failure(_):
                        // Fallback for failed loading
                        Rectangle()
                            .fill(Color(.systemGray5))
                            .frame(width: 120, height: 120)
                            .overlay(
                                Image(systemName: memory.type.systemImage)
                                    .font(.title2)
                                    .foregroundColor(.secondary)
                            )
                    case .empty:
                        // Loading state
                        Rectangle()
                            .fill(Color(.systemGray6))
                            .frame(width: 120, height: 120)
                            .overlay(
                                ProgressView()
                                    .scaleEffect(0.8)
                            )
                    @unknown default:
                        Rectangle()
                            .fill(Color(.systemGray5))
                            .frame(width: 120, height: 120)
                    }
                }
                
                // Video indicator (like Apple Photos)
                if memory.type == .video {
                    VStack {
                        Spacer()
                        HStack {
                            Image(systemName: "play.fill")
                                .font(.caption2)
                                .foregroundColor(.white)
                            
                            if let duration = memory.formattedDuration {
                                Text(duration)
                                    .font(.caption2)
                                    .foregroundColor(.white)
                            }
                            
                            Spacer()
                        }
                        .padding(.horizontal, 6)
                        .padding(.vertical, 4)
                        .background(
                            Color.black.opacity(0.6)
                                .cornerRadius(4)
                        )
                    }
                    .padding(6)
                }
                
                // Favorite indicator
                if memory.isFavorite {
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: "heart.fill")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(4)
                                .background(
                                    Circle()
                                        .fill(Color.black.opacity(0.6))
                                )
                        }
                        Spacer()
                    }
                    .padding(6)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .aspectRatio(1, contentMode: .fit)
        .cornerRadius(8)
    }
}

// MARK: - Apple Photos Detail View
@available(iOS 18.0, *)
struct ApplePhotosDetailView: View {
    let memory: Memory
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realDataService: RealDataService
    @State private var showingShareSheet = false
    @State private var showingEditView = false
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                VStack {
                    // Main image/video view
                    AsyncImage(url: URL(string: memory.mediaURL ?? "")) { phase in
                        switch phase {
                        case .success(let image):
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .scaleEffect(scale)
                                .offset(offset)
                                .gesture(
                                    MagnificationGesture()
                                        .onChanged { value in
                                            scale = value
                                        }
                                        .onEnded { _ in
                                            withAnimation(.spring()) {
                                                scale = max(1.0, min(scale, 5.0))
                                                if scale == 1.0 {
                                                    offset = .zero
                                                }
                                            }
                                        }
                                )
                                .gesture(
                                    DragGesture()
                                        .onChanged { value in
                                            offset = value.translation
                                        }
                                        .onEnded { _ in
                                            withAnimation(.spring()) {
                                                if scale <= 1.0 {
                                                    offset = .zero
                                                }
                                            }
                                        }
                                )
                        case .failure(_):
                            Rectangle()
                                .fill(Color(.systemGray5))
                                .aspectRatio(1, contentMode: .fit)
                                .overlay(
                                    VStack {
                                        Image(systemName: "exclamationmark.triangle")
                                            .font(.largeTitle)
                                            .foregroundColor(.secondary)
                                        Text("Failed to load")
                                            .font(.headline)
                                            .foregroundColor(.secondary)
                                    }
                                )
                        case .empty:
                            Rectangle()
                                .fill(Color(.systemGray6))
                                .aspectRatio(1, contentMode: .fit)
                                .overlay(
                                    ProgressView()
                                        .scaleEffect(1.5)
                                )
                        @unknown default:
                            Rectangle()
                                .fill(Color(.systemGray5))
                                .aspectRatio(1, contentMode: .fit)
                        }
                    }
                    
                    // Memory details
                    VStack(alignment: .leading, spacing: 12) {
                        Text(memory.title)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text(memory.createdAt.formatted(date: .complete, time: .omitted))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        if !memory.content.isEmpty {
                            Text(memory.content)
                                .font(.body)
                                .foregroundColor(.white)
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 16)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Button(action: { showingShareSheet = true }) {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.white)
                        }
                        
                        Button(action: { showingEditView = true }) {
                            Image(systemName: "pencil")
                                .foregroundColor(.white)
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            EditMemoryView(memory: memory)
                .environmentObject(realDataService)
        }
    }
}

// MARK: - Date Formatter Extension
extension DateFormatter {
    static let monthYear: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter
    }()
    
    static let year: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy"
        return formatter
    }()
}

#Preview {
    if #available(iOS 18.0, *) {
        ApplePhotosMemoryView()
            .environmentObject(RealDataService())
    } else {
        Text("iOS 18.0+ Required")
    }
} 