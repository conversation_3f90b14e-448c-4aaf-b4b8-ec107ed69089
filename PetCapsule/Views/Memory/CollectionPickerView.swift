import SwiftUI

@available(iOS 18.0, *)
struct CollectionPickerView: View {
    @ObservedObject var memoryService: EnhancedMemoryService
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        List(MemoryCollection.allCases, id: \.self) { collection in
            HStack {
                VStack(alignment: .leading) {
                    Text(collection.displayName)
                        .font(.headline)
                    Text("\(getMemoryCount(for: collection)) memories")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
        .navigationTitle("Collections")
    }
    
    private func selectCollection(_ collection: MemoryCollection) {
        memoryService.selectedCollection = collection
        
        // Handle vault authentication if needed
        if collection == .vault && !memoryService.isVaultUnlocked {
            Task {
                await memoryService.unlockVault()
            }
        }
        
        // Auto-dismiss after selection
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            dismiss()
        }
    }
    
    @available(iOS 18.0, *)
    private func getMemoryCount(for collection: MemoryCollection) -> Int {
        switch collection {
        case .all:
            return ProductionMemoryService.shared.memories.count
        case .vault:
            return ProductionMemoryService.shared.memories.filter { $0.tags.contains("secure") }.count
        case .memorial:
            return ProductionMemoryService.shared.memories.filter { $0.tags.contains("memorial") }.count
        case .favorites:
            return ProductionMemoryService.shared.memories.filter { $0.isFavorite }.count
        case .recent:
            let oneWeekAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
            return ProductionMemoryService.shared.memories.filter { $0.createdAt >= oneWeekAgo }.count
        case .videos:
            return ProductionMemoryService.shared.memories.filter { $0.type == .video }.count
        case .milestones:
            return ProductionMemoryService.shared.memories.filter { $0.milestone != nil }.count
        case .thisMonth:
            let calendar = Calendar.current
            let startOfMonth = calendar.dateInterval(of: .month, for: Date())?.start ?? Date()
            return ProductionMemoryService.shared.memories.filter { $0.createdAt >= startOfMonth }.count
        case .thisYear:
            let calendar = Calendar.current
            let startOfYear = calendar.dateInterval(of: .year, for: Date())?.start ?? Date()
            return ProductionMemoryService.shared.memories.filter { $0.createdAt >= startOfYear }.count
        }
    }
}

// MARK: - Collection Row

struct CollectionRow: View {
    let collection: MemoryCollection
    let isSelected: Bool
    let memoryCount: Int
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // Icon
                ZStack {
                    Circle()
                        .fill(collection.color.opacity(0.2))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: collection.icon)
                        .font(.title2)
                        .foregroundColor(collection.color)
                }
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(collection.displayName)
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(collection.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // Count and selection
                VStack(spacing: 8) {
                    Text("\(memoryCount)")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
                
                // Special indicators
                if collection == .vault {
                    VStack {
                        Image(systemName: "lock")
                            .font(.caption)
                            .foregroundColor(.purple)
                        
                        Spacer()
                    }
                }
            }
            .padding(16)
            .background(
                Rectangle()
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemBackground))
            )
            .overlay(
                Rectangle()
                    .fill(Color(.systemGray4))
                    .frame(height: 1)
                    .offset(y: 0.5),
                alignment: .bottom
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Memory Collection Enum is defined in EnhancedMemory.swift

// MARK: - Preview
@available(iOS 18.0, *)
#Preview {
    CollectionPickerView(memoryService: EnhancedMemoryService.shared)
} 