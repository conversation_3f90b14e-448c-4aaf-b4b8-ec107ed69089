import SwiftUI
import AVKit
import Photos

@available(iOS 18.0, *)
struct ComprehensiveMemoryView: View {
    @StateObject private var memoryService = EnhancedMemoryService.shared
    @EnvironmentObject var realDataService: RealDataService
    
    @State private var showAddMemory = false
    @State private var showMemoryDetail = false
    @State private var selectedMemory: EnhancedMemory?
    @State private var showFilters = false
    @State private var showCollectionPicker = false
    @State private var showSearch = false
    @State private var showVaultAuthentication = false
    @State private var showMemorialCreation = false
    @State private var selectedPet: Pet?
    @State private var viewMode: ViewMode = .grid
    @State private var showVideoPlayer = false
    @State private var videoPlayerMemory: EnhancedMemory?
    
    private let columns = [
        GridItem(.adaptive(minimum: 160), spacing: 2),
        GridItem(.adaptive(minimum: 160), spacing: 2)
    ]
    
    enum ViewMode: String, CaseIterable {
        case grid = "Grid"
        case timeline = "Timeline"
        case events = "Events"
        case map = "Map"
        
        var icon: String {
            switch self {
            case .grid: return "square.grid.2x2"
            case .timeline: return "timeline.selection"
            case .events: return "calendar"
            case .map: return "map"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with collection picker and search
                headerView
                
                // Filter chips
                if !memoryService.selectedEventTypes.isEmpty || !memoryService.selectedMoods.isEmpty || memoryService.dateRange != nil {
                    filterChipsView
                }
                
                // Memorial banner (if applicable)
                if memoryService.selectedCollection == .memorial {
                    memorialBannerView
                }
                
                // Main content
                mainContentView
            }
            .background(Color(.systemBackground))
            .navigationBarHidden(true)
            .task {
                await memoryService.loadMemories()
            }
        }
        .sheet(isPresented: $showAddMemory) {
            EnhancedMemoryCreationView()
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showMemoryDetail) {
            if let memory = selectedMemory {
                MemoryDetailView(memory: memory) {
                    showMemoryDetail = false
                    selectedMemory = nil
                }
            }
        }
        .sheet(isPresented: $showFilters) {
            MemoryFiltersView(memoryService: memoryService)
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showCollectionPicker) {
            CollectionPickerView(memoryService: memoryService)
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showVaultAuthentication) {
            VaultAuthenticationView(memoryService: memoryService) {
                showVaultAuthentication = false
            }
        }
        .sheet(isPresented: $showMemorialCreation) {
            MemorialCreationView(selectedPet: selectedPet)
        }
        .fullScreenCover(isPresented: $showVideoPlayer) {
            if let memory = videoPlayerMemory {
                VideoPlayerView(memory: memory) {
                    showVideoPlayer = false
                    videoPlayerMemory = nil
                }
            }
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 0) {
            // Search bar (when active)
            if showSearch {
                searchBarView
            }
            
            // Main header
            HStack {
                // Collection title
                Button(action: { showCollectionPicker = true }) {
                    HStack {
                        Image(systemName: memoryService.selectedCollection.icon)
                            .foregroundColor(memoryService.selectedCollection.color)
                        Text(memoryService.selectedCollection.displayName)
                            .font(.title2)
                            .fontWeight(.bold)
                        Image(systemName: "chevron.down")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                
                Spacer()
                
                // Action buttons
                HStack(spacing: 16) {
                    // Search button
                    Button(action: { showSearch.toggle() }) {
                        Image(systemName: "magnifyingglass")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                    
                    // Filter button
                    Button(action: { showFilters = true }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.title2)
                            .foregroundColor(hasActiveFilters ? .blue : .primary)
                    }
                    
                    // View mode toggle
                    Button(action: { cycleViewMode() }) {
                        Image(systemName: viewMode.icon)
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                    
                    // Add memory button
                    Button(action: { showAddMemory = true }) {
                        Image(systemName: "plus")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 12)
            
            // Memory count
            HStack {
                Text("\(memoryService.filteredMemories.count) memories")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // Vault status
                if memoryService.selectedCollection == .vault {
                    HStack {
                        Image(systemName: memoryService.isVaultUnlocked ? "lock.open" : "lock")
                        Text(memoryService.isVaultUnlocked ? "Unlocked" : "Locked")
                    }
                    .font(.caption)
                    .foregroundColor(memoryService.isVaultUnlocked ? .green : .orange)
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 8)
        }
        .background(Color(.systemBackground))
    }
    
    private var searchBarView: some View {
        HStack {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search memories", text: $memoryService.searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !memoryService.searchText.isEmpty {
                    Button(action: { memoryService.searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            
            Button("Cancel") {
                showSearch = false
                memoryService.searchText = ""
            }
            .foregroundColor(.blue)
        }
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
    
    private var filterChipsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                // Event type chips
                ForEach(Array(memoryService.selectedEventTypes), id: \.self) { eventType in
                    FilterChip(
                        text: eventType.displayName,
                        color: eventType.color,
                        icon: eventType.icon,
                        onRemove: { memoryService.selectedEventTypes.remove(eventType) }
                    )
                }
                
                // Mood chips
                ForEach(Array(memoryService.selectedMoods), id: \.self) { mood in
                    FilterChip(
                        text: mood.rawValue.capitalized,
                        color: mood.color,
                        icon: nil,
                        emoji: mood.emoji,
                        onRemove: { memoryService.selectedMoods.remove(mood) }
                    )
                }
                
                // Date range chip
                if let dateRange = memoryService.dateRange {
                    FilterChip(
                        text: formatDateRange(dateRange),
                        color: .blue,
                        icon: "calendar",
                        onRemove: { memoryService.dateRange = nil }
                    )
                }
                
                // Clear all button
                Button(action: { memoryService.clearFilters() }) {
                    HStack {
                        Image(systemName: "xmark")
                        Text("Clear")
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray5))
                    .foregroundColor(.primary)
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal)
        }
        .padding(.bottom, 8)
    }
    
    private var memorialBannerView: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "heart.circle")
                    .font(.title2)
                    .foregroundColor(.purple)
                
                Text("Memorial Garden")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Add Memorial") {
                    showMemorialCreation = true
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.purple.opacity(0.1))
                .foregroundColor(.purple)
                .cornerRadius(8)
            }
            
            Text("A special place to remember and honor our beloved pets who have crossed the rainbow bridge.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(Color.purple.opacity(0.05))
        .cornerRadius(12)
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
    
    private var mainContentView: some View {
        Group {
            if memoryService.isLoading {
                LoadingView()
            } else if memoryService.filteredMemories.isEmpty {
                EmptyStateView(collection: memoryService.selectedCollection)
            } else {
                switch viewMode {
                case .grid:
                    gridView
                case .timeline:
                    timelineView
                case .events:
                    eventsView
                case .map:
                    mapView
                }
            }
        }
    }
    
    private var gridView: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: 2) {
                ForEach(memoryService.filteredMemories) { memory in
                    MemoryThumbnailView(memory: memory) {
                        selectedMemory = memory
                        
                        if memory.hasVideo {
                            videoPlayerMemory = memory
                            showVideoPlayer = true
                        } else {
                            showMemoryDetail = true
                        }
                    }
                }
            }
            .padding(.horizontal, 1)
        }
        .scrollTargetLayout()
        .scrollTargetBehavior(.viewAligned)
    }
    
    private var timelineView: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(Array(memoryService.groupedMemories.keys.sorted(by: >)), id: \.self) { monthYear in
                    TimelineSection(
                        title: monthYear,
                        memories: memoryService.groupedMemories[monthYear] ?? []
                    ) { memory in
                        selectedMemory = memory
                        showMemoryDetail = true
                    }
                }
            }
        }
    }
    
    private var eventsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(MemoryEventType.allCases, id: \.self) { eventType in
                    let memories = memoryService.filteredMemories.filter { $0.eventType == eventType }
                    
                    if !memories.isEmpty {
                        EventTypeSection(
                            eventType: eventType,
                            memories: memories
                        ) { memory in
                            selectedMemory = memory
                            showMemoryDetail = true
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    private var mapView: some View {
        MemoryMapView(memoryService: memoryService) { memory in
            selectedMemory = memory
            showMemoryDetail = true
        }
    }
    
    private var hasActiveFilters: Bool {
        !memoryService.selectedEventTypes.isEmpty ||
        !memoryService.selectedMoods.isEmpty ||
        memoryService.dateRange != nil ||
        !memoryService.selectedPets.isEmpty
    }
    
    private func cycleViewMode() {
        let modes = ViewMode.allCases
        let currentIndex = modes.firstIndex(of: viewMode) ?? 0
        let nextIndex = (currentIndex + 1) % modes.count
        viewMode = modes[nextIndex]
    }
    
    private func formatDateRange(_ range: DateRange) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return "\(formatter.string(from: range.start)) - \(formatter.string(from: range.end))"
    }
}

// MARK: - Memory Thumbnail View
@available(iOS 18.0, *)
struct MemoryThumbnailView: View {
    let memory: EnhancedMemory
    let onTap: () -> Void
    @StateObject private var memoryService = EnhancedMemoryService.shared
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background image
                AsyncImage(url: URL(string: memory.thumbnailURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                } placeholder: {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .overlay(
                            Image(systemName: "photo")
                                .font(.title2)
                                .foregroundColor(.secondary)
                        )
                }
                
                // Video overlay
                if memory.hasVideo {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            
                            // Play button
                            Button(action: onTap) {
                                Image(systemName: "play.circle.fill")
                                    .font(.title)
                                    .foregroundColor(.white)
                                    .background(Color.black.opacity(0.3))
                                    .clipShape(Circle())
                            }
                            
                            Spacer()
                        }
                        Spacer()
                    }
                    
                    // Duration indicator
                    VStack {
                        Spacer()
                        HStack {
                            if let duration = memory.primaryMediaItem?.duration {
                                Text(formatDuration(duration))
                                    .font(.caption2)
                                    .fontWeight(.medium)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.black.opacity(0.7))
                                    .cornerRadius(4)
                            }
                            
                            Spacer()
                        }
                        .padding(.bottom, 4)
                        .padding(.leading, 4)
                    }
                }
                
                // Top overlay with indicators
                VStack {
                    HStack {
                        // Multiple media indicator
                        if memory.hasMultipleMedia {
                            Image(systemName: "square.stack.3d.up.fill")
                                .font(.caption)
                                .foregroundColor(.white)
                                .padding(4)
                                .background(Color.black.opacity(0.7))
                                .clipShape(Circle())
                        }
                        
                        Spacer()
                        
                        // Special indicators
                        HStack(spacing: 4) {
                            // Vault indicator
                            if memory.isSecureVault {
                                Image(systemName: "lock.shield.fill")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                                    .padding(4)
                                    .background(Color.black.opacity(0.7))
                                    .clipShape(Circle())
                            }
                            
                            // Memorial indicator
                            if memory.isMemorial {
                                Image(systemName: "heart.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.purple)
                                    .padding(4)
                                    .background(Color.black.opacity(0.7))
                                    .clipShape(Circle())
                            }
                            
                            // Favorite indicator
                            if memory.tags.contains("favorite") {
                                Image(systemName: "heart.fill")
                                    .font(.caption)
                                    .foregroundColor(.red)
                                    .padding(4)
                                    .background(Color.black.opacity(0.7))
                                    .clipShape(Circle())
                            }
                        }
                    }
                    .padding(4)
                    
                    Spacer()
                }
                
                // Bottom overlay with event type
                VStack {
                    Spacer()
                    
                    HStack {
                        HStack {
                            Image(systemName: memory.eventType.icon)
                                .font(.caption2)
                            Text(memory.eventType.displayName)
                                .font(.caption2)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(memory.eventType.color.opacity(0.8))
                        .cornerRadius(8)
                        
                        Spacer()
                    }
                    .padding(.bottom, 4)
                    .padding(.leading, 4)
                }
            }
        }
        .aspectRatio(1, contentMode: .fit)
        .cornerRadius(8)
        .onTapGesture {
            onTap()
        }
        .contextMenu {
            contextMenuItems
        }
    }
    
    private var contextMenuItems: some View {
        Group {
            // Favorite toggle
            Button(action: {
                Task {
                    await memoryService.toggleFavorite(for: memory)
                }
            }) {
                Label(
                    memory.tags.contains("favorite") ? "Remove from Favorites" : "Add to Favorites",
                    systemImage: memory.tags.contains("favorite") ? "heart.slash" : "heart"
                )
            }
            
            // Vault actions
            if memory.isSecureVault {
                Button(action: {
                    Task {
                        await memoryService.removeFromSecureVault(memory)
                    }
                }) {
                    Label("Remove from Vault", systemImage: "lock.open")
                }
            } else {
                Button(action: {
                    Task {
                        await memoryService.addToSecureVault(memory)
                    }
                }) {
                    Label("Add to Secure Vault", systemImage: "lock.shield")
                }
            }
            
            // Share
            Button(action: {
                // TODO: Implement sharing
            }) {
                Label("Share", systemImage: "square.and.arrow.up")
            }
            
            // Delete (if not protected)
            if !memory.isProtected {
                Button(role: .destructive, action: {
                    Task {
                        await memoryService.deleteMemory(memory)
                    }
                }) {
                    Label("Delete", systemImage: "trash")
                }
            }
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Filter Chip
@available(iOS 18.0, *)
struct FilterChip: View {
    let text: String
    let color: Color
    let icon: String?
    let emoji: String?
    let onRemove: () -> Void
    
    init(text: String, color: Color, icon: String?, emoji: String? = nil, onRemove: @escaping () -> Void) {
        self.text = text
        self.color = color
        self.icon = icon
        self.emoji = emoji
        self.onRemove = onRemove
    }
    
    var body: some View {
        HStack(spacing: 4) {
            if let emoji = emoji {
                Text(emoji)
                    .font(.caption)
            } else if let icon = icon {
                Image(systemName: icon)
                    .font(.caption2)
            }
            
            Text(text)
                .font(.caption)
                .fontWeight(.medium)
            
            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.caption2)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(color.opacity(0.1))
        .foregroundColor(color)
        .cornerRadius(12)
    }
}

// MARK: - Loading View
@available(iOS 18.0, *)
struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading memories...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Empty State View
@available(iOS 18.0, *)
struct EmptyStateView: View {
    let collection: MemoryCollection
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: collection.icon)
                .font(.system(size: 64))
                .foregroundColor(collection.color.opacity(0.3))
            
            VStack(spacing: 8) {
                Text(emptyStateTitle)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                
                Text(emptyStateMessage)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            if collection == .all {
                Button("Create Your First Memory") {
                    // TODO: Trigger add memory sheet
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
    
    private var emptyStateTitle: String {
        switch collection {
        case .all:
            return "No Memories Yet"
        case .recent:
            return "No Recent Memories"
        case .favorites:
            return "No Favorite Memories"
        case .videos:
            return "No Video Memories"
        case .milestones:
            return "No Milestone Memories"
        case .vault:
            return "Secure Vault Empty"
        case .memorial:
            return "Memorial Garden Empty"
        case .thisMonth:
            return "No Memories This Month"
        case .thisYear:
            return "No Memories This Year"
        }
    }
    
    private var emptyStateMessage: String {
        switch collection {
        case .all:
            return "Start capturing beautiful moments with your pets. Every memory is precious!"
        case .recent:
            return "No memories from the past week. Time to create some new ones!"
        case .favorites:
            return "Mark your special memories as favorites to see them here."
        case .videos:
            return "Capture video memories to bring your pet's personality to life."
        case .milestones:
            return "Record important milestones and achievements in your pet's life."
        case .vault:
            return "Keep your most precious memories safe and secure."
        case .memorial:
            return "A peaceful place to honor and remember beloved pets."
        case .thisMonth:
            return "No memories captured this month yet."
        case .thisYear:
            return "No memories captured this year yet."
        }
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        ComprehensiveMemoryView()
            .environmentObject(RealDataService())
    } else {
        Text("iOS 18.0 required")
    }
} 