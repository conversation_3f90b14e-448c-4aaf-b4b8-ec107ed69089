import SwiftUI
import PhotosUI
import AVKit
import Speech
@available(iOS 18.0, *)
struct EnhancedMemoryCreationView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var memoryService = EnhancedMemoryService.shared
    @EnvironmentObject var realDataService: RealDataService
    // Basic memory data
    @State private var title = ""
    @State private var content = ""
    @State private var selectedEventType: MemoryEventType = .general
    @State private var selectedMood: MemoryMood = .happy
    @State private var selectedPetIds: Set<String> = []
    @State private var tags: [String] = []
    @State private var newTag = ""
    // Media
    @State private var mediaItems: [MemoryMediaItem] = []
    @State private var selectedPhotoItems: [PhotosPickerItem] = []
    @State private var showPhotosPicker = false
    @State private var showCamera = false
    @State private var showVideoRecorder = false
    @State private var isRecordingVideo = false
    // Voice note
    @State private var voiceNoteURL: String?
    @State private var isRecordingVoice = false
    @State private var voiceRecordingPermission = false
    // Location
    @State private var includeLocation = false
    @State private var selectedLocation: MemoryLocation?
    @State private var showLocationPicker = false
    // Measurements
    @State private var includeMeasurements = false
    @State private var measurements: [PetMeasurement] = []
    @State private var showMeasurementEntry = false
    // Security & Special features
    @State private var isSecureVault = false
    @State private var isMemorial = false
    @State private var isProtected = false
    @State private var reminderDate: Date?
    @State private var setReminder = false
    // Milestone
    @State private var isMilestone = false
    @State private var milestoneData: MilestoneData?
    @State private var showMilestoneEntry = false
    // UI state
    @State private var showAdvancedOptions = false
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    headerSection
                    // Basic information
                    basicInformationSection
                    // Pet selection
                    petSelectionSection
                    // Event type and mood
                    eventTypeAndMoodSection
                    // Media section
                    mediaSection
                    // Advanced options
                    advancedOptionsSection
                    // Action buttons
                    actionButtonsSection
                }
                .padding()
            }
            .navigationBarHidden(true)
            .disabled(isLoading)
            .blur(radius: isLoading ? 2 : 0)
            .overlay(
                isLoading ? 
                LoadingOverlay()
                : nil
            )
        }
        .photosPicker(isPresented: $showPhotosPicker, selection: $selectedPhotoItems, maxSelectionCount: 10, matching: .any(of: [.images, .videos]))
        .onChange(of: selectedPhotoItems) { oldValue, newValue in
            processSelectedPhotos(newValue)
        }
        .sheet(isPresented: $showCamera) {
                                        MemoryCameraView { mediaItem in
                mediaItems.append(mediaItem)
            }
        }
        .sheet(isPresented: $showVideoRecorder) {
            VideoRecorderView { mediaItem in
                mediaItems.append(mediaItem)
            }
        }
        .sheet(isPresented: $showLocationPicker) {
                                        MemoryLocationPickerView(selectedLocation: $selectedLocation)
        }
        .sheet(isPresented: $showMeasurementEntry) {
            MeasurementEntryView(measurements: $measurements)
        }
        .sheet(isPresented: $showMilestoneEntry) {
            MilestoneEntryView(milestoneData: $milestoneData)
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .task {
            await requestPermissions()
        }
    }
    private var headerSection: some View {
        HStack {
            Button("Cancel") {
                dismiss()
            }
            .foregroundColor(.blue)
            Spacer()
            Text(isMemorial ? "Create Memorial" : "New Memory")
                .font(.headline)
                .fontWeight(.semibold)
            Spacer()
            Button("Save") {
                Task {
                    await saveMemory()
                }
            }
            .foregroundColor(.blue)
            .fontWeight(.medium)
            .disabled(title.isEmpty || content.isEmpty)
        }
        .padding(.bottom, 8)
    }
    private var basicInformationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Title
            VStack(alignment: .leading, spacing: 4) {
                Text("Title")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                TextField("Enter memory title", text: $title)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text("Description")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                TextEditor(text: $content)
                    .frame(height: 100)
                    .padding(4)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }
            // Tags
            VStack(alignment: .leading, spacing: 4) {
                Text("Tags")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                HStack {
                    TextField("Add tag", text: $newTag)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .onSubmit {
                            if !newTag.isEmpty {
                                tags.append(newTag)
                                newTag = ""
                            }
                        }
                    Button("Add") {
                        if !newTag.isEmpty {
                            tags.append(newTag)
                            newTag = ""
                        }
                    }
                    .disabled(newTag.isEmpty)
                }
                // Tag chips
                if !tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            ForEach(Array(tags.enumerated()), id: \.element) { index, tag in
                                TagChip(text: tag) {
                                    tags.remove(at: index)
                                }
                            }
                        }
                        .padding(.horizontal, 2)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    private var petSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Pets")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(realDataService.pets) { pet in
                        PetSelectionCard(
                            pet: pet,
                            isSelected: selectedPetIds.contains(pet.id)
                        ) {
                            if selectedPetIds.contains(pet.id) {
                                selectedPetIds.remove(pet.id)
                            } else {
                                selectedPetIds
                            }
                        }
                    }
                }
                .padding(.horizontal, 2)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    private var eventTypeAndMoodSection: some View {
        VStack(spacing: 16) {
            // Event Type
            VStack(alignment: .leading, spacing: 8) {
                Text("Event Type")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(MemoryEventType.allCases, id: \.self) { eventType in
                            EventTypeCard(
                                eventType: eventType,
                                isSelected: selectedEventType == eventType
                            ) {
                                selectedEventType = eventType
                                // Auto-set memorial flag for memorial events
                                if eventType.isMemorialType {
                                    isMemorial = true
                                    isProtected = true
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 2)
                }
            }
            // Mood
            VStack(alignment: .leading, spacing: 8) {
                Text("Mood")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(MemoryMood.allCases, id: \.self) { mood in
                            MoodCard(
                                mood: mood,
                                isSelected: selectedMood == mood
                            ) {
                                selectedMood = mood
                            }
                        }
                    }
                    .padding(.horizontal, 2)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    private var mediaSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Media")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                Spacer()
                Text("\(mediaItems.count) items")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            // Media actions
            HStack(spacing: 12) {
                Button(action: { showPhotosPicker = true }) {
                    Label("Photos", systemImage: "photo")
                }
                .buttonStyle(MediaButtonStyle())
                Button(action: { showCamera = true }) {
                    Label("Camera", systemImage: "camera")
                }
                .buttonStyle(MediaButtonStyle())
                Button(action: { showVideoRecorder = true }) {
                    Label("Video", systemImage: "video")
                }
                .buttonStyle(MediaButtonStyle())
                Button(action: { toggleVoiceRecording() }) {
                    Label("Voice", systemImage: isRecordingVoice ? "stop.circle.fill" : "mic")
                }
                .buttonStyle(MediaButtonStyle(isActive: isRecordingVoice))
            }
            // Media preview
            if !mediaItems.isEmpty {
                MediaPreviewGrid(mediaItems: $mediaItems)
            }
            // Voice note preview
            if let voiceNoteURL = voiceNoteURL {
                VoiceNotePreview(url: voiceNoteURL) {
                    self.voiceNoteURL = nil
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    private var advancedOptionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Advanced options header
            Button(action: { showAdvancedOptions.toggle() }) {
                HStack {
                    Text("Advanced Options")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    Spacer()
                    Image(systemName: showAdvancedOptions ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
            if showAdvancedOptions {
                VStack(spacing: 16) {
                    // Security options
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Security & Privacy")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        VStack(spacing: 8) {
                            Toggle("Add to Secure Vault", isOn: $isSecureVault)
                            Toggle("Protected Memory", isOn: $isProtected)
                            Toggle("Memorial Memory", isOn: $isMemorial)
                        }
                    }
                    // Location
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Location")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        Toggle("Include Location", isOn: $includeLocation)
                        if includeLocation {
                            Button("Select Location") {
                                showLocationPicker = true
                            }
                            .font(.subheadline)
                            .foregroundColor(.blue)
                        }
                    }
                    // Measurements
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Measurements")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        Toggle("Include Measurements", isOn: $includeMeasurements)
                        if includeMeasurements {
                            Button("Add Measurements") {
                                showMeasurementEntry = true
                            }
                            .font(.subheadline)
                            .foregroundColor(.blue)
                        }
                    }
                    // Milestone
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Milestone")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        Toggle("Mark as Milestone", isOn: $isMilestone)
                        if isMilestone {
                            Button("Add Milestone Details") {
                                showMilestoneEntry = true
                            }
                            .font(.subheadline)
                            .foregroundColor(.blue)
                        }
                    }
                    // Reminder
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Reminder")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        Toggle("Set Reminder", isOn: $setReminder)
                        if setReminder {
                            DatePicker("Reminder Date", selection: Binding(
                                get: { reminderDate ?? Date() },
                                set: { reminderDate = $0 }
                            ), displayedComponents: [.date, .hourAndMinute])
                            .datePickerStyle(CompactDatePickerStyle())
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // Primary save button
            Button(action: {
                Task {
                    await saveMemory()
                }
            }) {
                HStack {
                    Image(systemName: isMemorial ? "heart.circle" : "photo.badge.plus")
                    Text(isMemorial ? "Create Memorial" : "Save Memory")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
            }
            .disabled(title.isEmpty || content.isEmpty)
            // Secondary buttons
            HStack(spacing: 12) {
                Button("Save as Draft") {
                    Task {
                        await saveDraft()
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemGray6))
                .foregroundColor(.primary)
                .cornerRadius(12)
                Button("Quick Save") {
                    Task {
                        await quickSave()
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemGray6))
                .foregroundColor(.primary)
                .cornerRadius(12)
            }
        }
        .padding()
    }
    // MARK: - Helper Methods
    private func requestPermissions() async {
        // Request voice recording permission
        await withCheckedContinuation { continuation in
            SFSpeechRecognizer.requestAuthorization { status in
                Task { @MainActor in
                    self.voiceRecordingPermission = status == .authorized
                    continuation.resume()
                }
            }
        }
    }
    private func processSelectedPhotos(_ items: [PhotosPickerItem]) {
        Task {
            for item in items {
                if let _ = try? await item.loadTransferable(type: Data.self) {
                    // Process the media item
                    let mediaItem = MemoryMediaItem(
                        id: UUID().uuidString,
                        url: "temp_url_\(UUID().uuidString)",
                        thumbnailURL: "temp_thumb_\(UUID().uuidString)",
                        type: item.supportedContentTypes.contains(.movie) ? .video : .photo,
                        uploadedAt: Date()
                    )
                    await MainActor.run {
                        mediaItems.append(mediaItem)
                    }
                }
            }
        }
    }
    private func toggleVoiceRecording() {
        if isRecordingVoice {
            stopVoiceRecording()
        } else {
            startVoiceRecording()
        }
    }
    private func startVoiceRecording() {
        guard voiceRecordingPermission else { return }
        isRecordingVoice = true
        // TODO: Implement voice recording
    }
    private func stopVoiceRecording() {
        isRecordingVoice = false
        // TODO: Stop voice recording and get URL
        voiceNoteURL = "temp_voice_url_\(UUID().uuidString)"
    }
    private func saveMemory() async {
        isLoading = true
        let memory = EnhancedMemory(
            title: title,
            content: content,
            mediaItems: mediaItems,
            petIds: Array(selectedPetIds),
            location: includeLocation ? selectedLocation : nil,
            mood: selectedMood,
            eventType: selectedEventType,
            isSecureVault: isSecureVault,
            isMemorial: isMemorial,
            tags: tags,
            measurements: includeMeasurements ? measurements : nil,
            voiceNoteURL: voiceNoteURL,
            isProtected: isProtected,
            milestoneData: isMilestone ? milestoneData : nil,
            reminderDate: setReminder ? reminderDate : nil
        )
        await memoryService.createMemory(memory)
        await MainActor.run {
            dismiss()
        }
        isLoading = false
    }
    private func saveDraft() async {
        // TODO: Implement draft saving
        dismiss()
    }
    private func quickSave() async {
        // Auto-generate title if empty
        if title.isEmpty {
            title = "Memory from \(Date().formatted(date: .abbreviated, time: .omitted))"
        }
        // Auto-generate content if empty
        if content.isEmpty {
            content = "A special moment with \(selectedPetIds.isEmpty ? "my pet" : "my pets")."
        }
        await saveMemory()
    }
}
// MARK: - Supporting Views
@available(iOS 18.0, *)
struct TagChip: View {
    let text: String
    let onRemove: () -> Void
    var body: some View {
        HStack(spacing: 4) {
            Text(text)
                .font(.caption)
                .fontWeight(.medium)
            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.caption2)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.blue.opacity(0.1))
        .foregroundColor(.blue)
        .cornerRadius(12)
    }
}
@available(iOS 18.0, *)
struct PetSelectionCard: View {
    let pet: Pet
    let isSelected: Bool
    let onToggle: () -> Void
    var body: some View {
        Button(action: onToggle) {
            VStack(spacing: 6) {
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 50, height: 50)
                        .clipped()
                        .clipShape(Circle())
                } placeholder: {
                    Circle()
                        .fill(Color(.systemGray5))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Text(pet.name.prefix(1))
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                }
                .overlay(
                    Circle()
                        .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                )
                Text(pet.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .blue : .primary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}
@available(iOS 18.0, *)
struct EventTypeCard: View {
    let eventType: MemoryEventType
    let isSelected: Bool
    let onSelect: () -> Void
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 4) {
                Image(systemName: eventType.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : eventType.color)
                Text(eventType.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? eventType.color : Color(.systemGray6))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
@available(iOS 18.0, *)
struct MoodCard: View {
    let mood: MemoryMood
    let isSelected: Bool
    let onSelect: () -> Void
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 4) {
                Text(mood.emoji)
                    .font(.title2)
                Text(mood.rawValue.capitalized)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? mood.color : Color(.systemGray6))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
@available(iOS 18.0, *)
struct MediaButtonStyle: ButtonStyle {
    let isActive: Bool
    init(isActive: Bool = false) {
        self.isActive = isActive
    }
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(isActive ? .white : .blue)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isActive ? Color.red : Color.blue.opacity(0.1))
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
    }
}
@available(iOS 18.0, *)
struct LoadingOverlay: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.5)
            Text("Saving memory...")
                .font(.subheadline)
                .fontWeight(.medium)
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 8)
    }
}
// MARK: - Placeholder Views (to be implemented)
@available(iOS 18.0, *)
struct MemoryCameraView: View {
    let onMediaCaptured: (MemoryMediaItem) -> Void
    var body: some View {
        Text("Camera View")
            .font(.title)
            .foregroundColor(.secondary)
    }
}
@available(iOS 18.0, *)
struct VideoRecorderView: View {
    let onMediaCaptured: (MemoryMediaItem) -> Void
    var body: some View {
        Text("Video Recorder View")
            .font(.title)
            .foregroundColor(.secondary)
    }
}
@available(iOS 18.0, *)
struct MemoryLocationPickerView: View {
    @Binding var selectedLocation: MemoryLocation?
    var body: some View {
        Text("Location Picker")
            .font(.title)
            .foregroundColor(.secondary)
    }
}
@available(iOS 18.0, *)
struct MeasurementEntryView: View {
    @Binding var measurements: [PetMeasurement]
    var body: some View {
        Text("Measurement Entry")
            .font(.title)
            .foregroundColor(.secondary)
    }
}
@available(iOS 18.0, *)
struct MilestoneEntryView: View {
    @Binding var milestoneData: MilestoneData?
    var body: some View {
        Text("Milestone Entry")
            .font(.title)
            .foregroundColor(.secondary)
    }
}
@available(iOS 18.0, *)
struct MediaPreviewGrid: View {
    @Binding var mediaItems: [MemoryMediaItem]
    var body: some View {
        Text("Media Preview Grid")
            .font(.title)
            .foregroundColor(.secondary)
    }
}
@available(iOS 18.0, *)
struct VoiceNotePreview: View {
    let url: String
    let onRemove: () -> Void
    var body: some View {
        HStack {
            Text("Voice Note")
                .font(.subheadline)
                .fontWeight(.medium)
            Spacer()
            Button("Remove", action: onRemove)
                .font(.caption)
                .foregroundColor(.red)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}
#Preview {
    if #available(iOS 18.0, *) {
        EnhancedMemoryCreationView()
            .environmentObject(RealDataService())
    } else {
        Text("iOS 18.0 required")
    }
} 