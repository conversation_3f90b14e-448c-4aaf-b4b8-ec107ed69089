//
//  EnhancedMemoryDetailView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI

@available(iOS 18.0, *)
struct EnhancedMemoryDetailView: View {
    let memory: Memory
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realDataService: RealDataService
    @StateObject private var productionMemoryService = ProductionMemoryService.shared
    
    @State private var showingEditView = false
    @State private var showingDeleteAlert = false
    @State private var showingShareSheet = false
    @State private var isDeleting = false
    @State private var showError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            ZStack {
                ScrollView {
                    VStack(spacing: 0) {
                        // Hero Media Section
                        heroMediaSection

                        // Content Section
                        contentSection
                    }
                    .padding(.bottom, 100) // Add padding for floating button
                }

                // Floating Edit Button
                VStack {
                    Spacer()
                    HStack {
                        Spacer()

                        Button(action: { showingEditView = true }) {
                            HStack(spacing: 8) {
                                Image(systemName: "pencil")
                                    .font(.system(size: 16, weight: .semibold))
                                Text("Edit Memory")
                                    .font(.system(size: 16, weight: .semibold))
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 12)
                            .background(
                                LinearGradient(
                                    colors: [.blue, .purple],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(Capsule())
                            .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                        }
                        .padding(.trailing, 20)
                        .padding(.bottom, 30)
                    }
                }
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 16) {
                        // Edit Button - More prominent
                        Button(action: { showingEditView = true }) {
                            Image(systemName: "pencil.circle.fill")
                                .foregroundColor(.white)
                                .font(.title2)
                        }

                        // More options menu
                        Menu {
                            Button(action: { showingShareSheet = true }) {
                                Label("Share", systemImage: "square.and.arrow.up")
                            }

                            Button(action: toggleFavorite) {
                                Label(
                                    memory.isFavorite ? "Remove from Favorites" : "Add to Favorites",
                                    systemImage: memory.isFavorite ? "heart.fill" : "heart"
                                )
                            }

                            Divider()

                            Button(role: .destructive, action: { showingDeleteAlert = true }) {
                                Label("Delete Memory", systemImage: "trash")
                            }
                        } label: {
                            Image(systemName: "ellipsis.circle.fill")
                                .foregroundColor(.white)
                                .font(.title3)
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            EditMemoryView(memory: memory)
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [
                memory.title,
                memory.content,
                memory.mediaURL ?? ""
            ])
        }
        .alert("Delete Memory", isPresented: $showingDeleteAlert) {
            Button("Delete", role: .destructive) {
                deleteMemory()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Are you sure you want to delete this memory? This action cannot be undone.")
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .overlay {
            if isDeleting {
                deletingOverlay
            }
        }
    }
    
    private var heroMediaSection: some View {
        ZStack {
            // Background Image/Video
            if let mediaURL = memory.mediaURL, let url = URL(string: mediaURL) {
                AsyncImage(url: url) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    LinearGradient(
                        colors: [.purple.opacity(0.6), .blue.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    .overlay(
                        ProgressView()
                            .tint(.white)
                    )
                }
            } else {
                LinearGradient(
                    colors: [.purple.opacity(0.6), .blue.opacity(0.6)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            }
            
            // Gradient overlay
            LinearGradient(
                colors: [
                    .clear,
                    .clear,
                    .black.opacity(0.7)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
            
            // Content overlay
            VStack {
                Spacer()
                
                VStack(alignment: .leading, spacing: 12) {
                    // Memory type and date
                    HStack {
                        HStack(spacing: 6) {
                            Image(systemName: memory.type.systemImage)
                                .font(.caption)
                            Text(memory.type.displayName)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(.ultraThinMaterial)
                                .environment(\.colorScheme, .dark)
                        )
                        
                        Spacer()
                        
                        if memory.isFavorite {
                            Image(systemName: "heart.fill")
                                .foregroundColor(.pink)
                                .font(.title3)
                        }
                    }
                    
                    // Title
                    Text(memory.title)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil) // Allow unlimited lines
                        .fixedSize(horizontal: false, vertical: true) // Prevent truncation
                    
                    // Date
                    Text(memory.createdAt.formatted(date: .complete, time: .shortened))
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 30)
            }
        }
        .frame(height: 400)
        .clipped()
    }
    
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Content
            if !memory.content.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Story")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(memory.content)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineSpacing(4)
                }
            }
            
            // AI Insights
            if let milestone = memory.milestone {
                aiInsightCard(
                    title: "Milestone",
                    content: milestone,
                    icon: "star.fill",
                    color: .orange
                )
            }
            
            if let sentiment = memory.sentiment {
                aiInsightCard(
                    title: "Sentiment",
                    content: sentiment.capitalized,
                    icon: "heart.fill",
                    color: .pink
                )
            }
            
            // Tags
            if !memory.tags.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Tags")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                        ForEach(memory.tags, id: \.self) { tag in
                            Text(tag)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(.ultraThinMaterial)
                                )
                        }
                    }
                }
            }
            
            // Metadata
            metadataSection
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 24)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(.ultraThinMaterial)
                .ignoresSafeArea(edges: .bottom)
        )
        .offset(y: -24)
    }
    
    private func aiInsightCard(title: String, content: String, icon: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(color.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .textCase(.uppercase)
                
                Text(content)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var metadataSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Details")
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                metadataRow(label: "Created", value: memory.createdAt.formatted(date: .complete, time: .shortened))
                metadataRow(label: "Last Updated", value: memory.updatedAt.formatted(date: .complete, time: .shortened))
                metadataRow(label: "Type", value: memory.type.displayName)
                metadataRow(label: "Public", value: memory.isPublic ? "Yes" : "No")
            }
        }
    }
    
    private func metadataRow(label: String, value: String) -> some View {
        HStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
    
    private var deletingOverlay: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            VStack(spacing: 16) {
                ProgressView()
                    .tint(.white)
                
                Text("Deleting memory...")
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .environment(\.colorScheme, .dark)
            )
        }
    }
    
    private func toggleFavorite() {
        Task {
            // Get current user ID
            guard let userId = realDataService.getCurrentUserId() else {
                await MainActor.run {
                    errorMessage = "No authenticated user found"
                    showError = true
                }
                return
            }

            // Create a copy of the memory with updated favorite status
            let updatedMemory = Memory(
                title: memory.title,
                content: memory.content,
                type: memory.type,
                mediaURL: memory.mediaURL,
                thumbnailURL: memory.thumbnailURL,
                duration: memory.duration,
                milestone: memory.milestone,
                sentiment: memory.sentiment,
                tags: memory.tags,
                isPublic: memory.isPublic,
                isFavorite: !memory.isFavorite
            )

            // Copy the original ID and dates
            updatedMemory.id = memory.id
            updatedMemory.createdAt = memory.createdAt
            updatedMemory.updatedAt = Date()

            // Update in database
            let success = await realDataService.updateMemory(updatedMemory)

            await MainActor.run {
                if success {
                    // Update in ProductionMemoryService
                    if let index = productionMemoryService.memories.firstIndex(where: { $0.id == memory.id }) {
                        productionMemoryService.memories[index] = updatedMemory
                    }
                } else {
                    errorMessage = "Failed to update favorite status"
                    showError = true
                }
            }
        }
    }
    
    private func deleteMemory() {
        isDeleting = true
        
        Task {
            // Get current user ID
            guard let userId = realDataService.getCurrentUserId() else {
                await MainActor.run {
                    isDeleting = false
                    errorMessage = "No authenticated user found"
                    showError = true
                }
                return
            }

            // Delete from database
            let success = await realDataService.deleteMemory(memory.id, userId: userId)

            await MainActor.run {
                isDeleting = false
                if success {
                    // Remove from ProductionMemoryService
                    productionMemoryService.memories.removeAll { $0.id == memory.id }
                    dismiss()
                } else {
                    errorMessage = "Failed to delete memory"
                    showError = true
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    if #available(iOS 18.0, *) {
        let sampleMemory = Memory(
            title: "Beautiful sunset at the beach",
            content: "A wonderful evening watching the sunset with my beloved pet. The colors were absolutely stunning and we had such a peaceful time together.",
            type: .photo,
            mediaURL: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
            milestone: "First beach visit",
            sentiment: "joyful",
            tags: ["sunset", "beach", "peaceful", "first time"],
            isFavorite: true
        )
        
        return EnhancedMemoryDetailView(memory: sampleMemory)
            .environmentObject(RealDataService())
    } else {
        return VStack {
            Text("iOS 18.0+ Required")
        }
    }
}
