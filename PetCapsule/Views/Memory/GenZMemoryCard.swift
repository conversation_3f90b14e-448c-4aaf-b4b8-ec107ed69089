//
//  GenZMemoryCard.swift
//  PetCapsule
//
//  Created by AI Assistant on 6/6/25.
//

import SwiftUI

struct GenZMemoryCard: View {
    let memory: Memory
    let onTap: () -> Void
    @State private var isHovered = false
    @State private var glowAnimation = false
    @State private var particleAnimation = false
    @State private var rotationAngle: Double = 0
    
    // Gen Z neon gradients
    private let neonGradients = [
        LinearGradient(colors: [.pink, .purple], startPoint: .topLeading, endPoint: .bottomTrailing),
        LinearGradient(colors: [.purple, .blue], startPoint: .topLeading, endPoint: .bottomTrailing),
        LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing),
        LinearGradient(colors: [.cyan, .mint], startPoint: .topLeading, endPoint: .bottomTrailing),
        LinearGradient(colors: [.mint, .green], startPoint: .topLeading, endPoint: .bottomTrailing),
        LinearGradient(colors: [.green, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing),
        LinearGradient(colors: [.yellow, .orange], startPoint: .topLeading, endPoint: .bottomTrailing),
        LinearGradient(colors: [.orange, .red], startPoint: .topLeading, endPoint: .bottomTrailing),
        LinearGradient(colors: [.red, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
    ]
    
    private var cardGradient: LinearGradient {
        let index = abs(memory.title.hashValue) % neonGradients.count
        return neonGradients[index]
    }
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Glass morphism background
                RoundedRectangle(cornerRadius: 24)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 24)
                            .stroke(cardGradient, lineWidth: 2)
                    )
                    .shadow(color: .pink.opacity(0.4), radius: 20, x: 0, y: 10)
                
                // Holographic overlay
                RoundedRectangle(cornerRadius: 24)
                    .fill(
                        RadialGradient(
                            colors: [
                                .pink.opacity(0.1),
                                Color.clear
                            ],
                            center: .topLeading,
                            startRadius: 0,
                            endRadius: 200
                        )
                    )
                    .opacity(glowAnimation ? 0.8 : 0.4)
                    .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: glowAnimation)
                
                // Content
                VStack(spacing: 16) {
                    // Image section with neon border
                    imageSection
                    
                    // Content section
                    contentSection
                }
                .padding(20)
                
                // Floating particles
                if particleAnimation {
                    floatingParticles
                }
            }
            .scaleEffect(isHovered ? 1.05 : 1.0)
            .rotationEffect(.degrees(isHovered ? 2 : 0))
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isHovered)
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovered = hovering
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
                glowAnimation = true
            }
            
            withAnimation(.linear(duration: 10).repeatForever(autoreverses: false)) {
                rotationAngle = 360
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                particleAnimation = true
            }
        }
    }
    
    // MARK: - Image Section
    private var imageSection: some View {
        ZStack {
            // Image with glass effect
            AsyncImage(url: URL(string: memory.mediaURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 160)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(cardGradient, lineWidth: 1)
                    )
            } placeholder: {
                ZStack {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(cardGradient)
                        .frame(height: 160)
                    
                    VStack(spacing: 8) {
                        Image(systemName: memoryTypeIcon)
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(.white)
                        
                        Text("Loading... ✨")
                            .font(.system(size: 14, weight: .semibold, design: .rounded))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
            
            // Holographic overlay on image
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            .white.opacity(0.1),
                            .clear,
                            .pink.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(height: 160)
            
            // Top badges
            VStack {
                HStack {
                    // Memory type badge with neon effect
                    HStack(spacing: 4) {
                        Image(systemName: memoryTypeIcon)
                            .font(.caption2)
                            .foregroundColor(.white)
                        
                        Text(memory.type.rawValue.capitalized)
                            .font(.system(size: 10, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(.ultraThinMaterial)
                            .overlay(
                                Capsule()
                                    .stroke(cardGradient, lineWidth: 1)
                            )
                    )
                    .shadow(color: .pink.opacity(0.5), radius: 5, x: 0, y: 2)
                    
                    Spacer()
                    
                    // Favorite indicator with glow
                    if memory.isFavorite {
                        ZStack {
                            Circle()
                                .fill(
                                    RadialGradient(
                                        colors: [.pink, .purple],
                                        center: .center,
                                        startRadius: 0,
                                        endRadius: 15
                                    )
                                )
                                .frame(width: 24, height: 24)
                                .shadow(color: .pink.opacity(0.6), radius: 8, x: 0, y: 0)
                            
                            Image(systemName: "heart.fill")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(.white)
                        }
                        .scaleEffect(glowAnimation ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: glowAnimation)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.top, 12)
                
                Spacer()
                
                // Date with glass effect
                HStack {
                    Spacer()
                    
                    Text(memory.createdAt.formatted(date: .abbreviated, time: .omitted))
                        .font(.system(size: 10, weight: .semibold, design: .rounded))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    Capsule()
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 12)
            }
        }
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            titleSection
            descriptionSection
            tagsSection
            bottomInfoSection
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    private var titleSection: some View {
        Text(memory.title)
            .font(.system(size: 18, weight: .bold, design: .rounded))
            .foregroundStyle(cardGradient)
            .lineLimit(2)
            .multilineTextAlignment(.leading)
    }

    @ViewBuilder
    private var descriptionSection: some View {
        if !memory.content.isEmpty {
            Text(memory.content)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
                .lineLimit(3)
                .multilineTextAlignment(.leading)
        }
    }

    @ViewBuilder
    private var tagsSection: some View {
        if !memory.tags.isEmpty {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(Array(memory.tags.prefix(3)), id: \.self) { tag in
                        tagView(tag)
                    }
                }
                .padding(.horizontal, 1)
            }
        }
    }

    private func tagView(_ tag: String) -> some View {
        Text("#\(tag)")
            .font(.system(size: 10, weight: .bold, design: .rounded))
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                Capsule()
                    .fill(cardGradient.opacity(0.3))
                    .overlay(
                        Capsule()
                            .stroke(cardGradient, lineWidth: 1)
                    )
            )
            .shadow(color: .pink.opacity(0.3), radius: 3, x: 0, y: 1)
    }

    private var bottomInfoSection: some View {
        HStack {
            sentimentView
            Spacer()
            viewButton
        }
    }

    @ViewBuilder
    private var sentimentView: some View {
        if let sentiment = memory.sentiment {
            HStack(spacing: 4) {
                Text(sentimentEmoji(sentiment))
                    .font(.system(size: 12))

                Text(sentiment.capitalized)
                    .font(.system(size: 10, weight: .semibold, design: .rounded))
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                Capsule()
                    .fill(.ultraThinMaterial)
                    .overlay(
                        Capsule()
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
    }

    private var viewButton: some View {
        Text("View ✨")
            .font(.system(size: 12, weight: .bold, design: .rounded))
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(cardGradient)
                    .shadow(color: .pink.opacity(0.5), radius: 8, x: 0, y: 4)
            )
    }
    
    // MARK: - Floating Particles
    private var floatingParticles: some View {
        ZStack {
            ForEach(0..<5, id: \.self) { index in
                Circle()
                    .fill(.pink.opacity(0.6))
                    .frame(width: 4, height: 4)
                    .offset(
                        x: CGFloat.random(in: -100...100),
                        y: CGFloat.random(in: -100...100)
                    )
                    .scaleEffect(glowAnimation ? 1.5 : 0.5)
                    .animation(
                        .easeInOut(duration: Double.random(in: 2...4))
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.3),
                        value: glowAnimation
                    )
            }
        }
    }
    
    // MARK: - Helper Properties
    private var memoryTypeIcon: String {
        switch memory.type {
        case .photo: return "camera.fill"
        case .video: return "video.fill"
        case .audio: return "mic.fill"
        case .milestone: return "star.fill"
        case .text: return "text.quote"
        }
    }
    
    private func sentimentEmoji(_ sentiment: String) -> String {
        switch sentiment.lowercased() {
        case "joyful", "happy": return "😊"
        case "nostalgic": return "🥺"
        case "peaceful": return "😌"
        case "playful": return "🤪"
        case "excited": return "🤩"
        case "love": return "🥰"
        default: return "✨"
        }
    }
}

struct GenZMemoryCard_Previews: PreviewProvider {
    static var previews: some View {
        GenZMemoryCard(
            memory: Memory(
                title: "Beach Day Vibes! 🏖️",
                content: "Luna's first time at the beach and she's absolutely living her best life! The waves, the sand, the endless zoomies - pure joy! 🌊✨",
                type: .photo,
                sentiment: "joyful",
                tags: ["beach", "summer", "vibes", "bestlife"],
                isPublic: true,
                isFavorite: true
            ),
            onTap: {}
        )
        .frame(width: 280, height: 320)
        .padding()
        .background(Color.black)
    }
}
