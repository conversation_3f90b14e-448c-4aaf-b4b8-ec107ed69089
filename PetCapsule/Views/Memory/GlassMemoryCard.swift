//
//  GlassMemoryCard.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI

struct GlassMemoryCard: View {
    let memory: Memory
    let onEdit: () -> Void
    let onDelete: () -> Void
    let onTap: () -> Void
    
    @State private var isPressed = false
    @State private var showDeleteAlert = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Thumbnail Section
            thumbnailSection
            
            // Content Section
            contentSection
        }
        .background(glassBackground)
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: [.white.opacity(0.3), .white.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(
            color: .black.opacity(0.1),
            radius: 20,
            x: 0,
            y: 10
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        .onTapGesture {
            onTap()
        }
        .onLongPressGesture(
            minimumDuration: 0,
            maximumDistance: .infinity,
            pressing: { pressing in
                isPressed = pressing
            },
            perform: {}
        )
        .alert("Delete Memory", isPresented: $showDeleteAlert) {
            Button("Delete", role: .destructive) {
                onDelete()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Are you sure you want to delete this memory? This action cannot be undone.")
        }
    }
    
    private var thumbnailSection: some View {
        ZStack {
            // Background gradient if no image
            if memory.mediaURL == nil || memory.mediaURL?.isEmpty == true {
                LinearGradient(
                    colors: [
                        Color.purple.opacity(0.6),
                        Color.blue.opacity(0.6),
                        Color.pink.opacity(0.4)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            } else {
                // Use thumbnail URL if available, otherwise use media URL
                let imageURL = memory.thumbnailURL ?? memory.mediaURL ?? ""

                AsyncImage(url: URL(string: imageURL)) { phase in
                    switch phase {
                    case .success(let image):
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    case .failure(_):
                        // Show error state with memory type icon
                        LinearGradient(
                            colors: [
                                Color.red.opacity(0.3),
                                Color.orange.opacity(0.3)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        .overlay(
                            VStack(spacing: 4) {
                                Image(systemName: "exclamationmark.triangle")
                                    .font(.title2)
                                    .foregroundColor(.white)
                                Text("Failed to load")
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.8))
                            }
                        )
                    case .empty:
                        // Loading state
                        LinearGradient(
                            colors: [
                                Color.purple.opacity(0.6),
                                Color.blue.opacity(0.6)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        .overlay(
                            ProgressView()
                                .tint(.white)
                        )
                    @unknown default:
                        // Fallback
                        LinearGradient(
                            colors: [
                                Color.purple.opacity(0.6),
                                Color.blue.opacity(0.6)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    }
                }
            }
            
            // Overlay gradient for better text readability
            LinearGradient(
                colors: [
                    .clear,
                    .black.opacity(0.3)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
            
            // Memory type indicator
            VStack {
                HStack {
                    Spacer()
                    memoryTypeIndicator
                }
                Spacer()
            }
            .padding(12)
        }
        .frame(height: 140)
        .clipped()
    }
    
    private var memoryTypeIndicator: some View {
        HStack(spacing: 4) {
            Image(systemName: memory.type.systemImage)
                .font(.caption2)
            Text(memory.type.displayName)
                .font(.caption2)
                .fontWeight(.medium)
        }
        .foregroundColor(.white)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(.ultraThinMaterial)
                .environment(\.colorScheme, .dark)
        )
    }
    
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Title
            Text(memory.title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.primary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // Date and metadata
            HStack(spacing: 4) {
                Image(systemName: "calendar")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Text(memory.createdAt.formatted(date: .abbreviated, time: .omitted))
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if memory.isFavorite {
                    Image(systemName: "heart.fill")
                        .font(.caption2)
                        .foregroundColor(.pink)
                }
            }
            
            // Tags if available
            if !memory.tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 6) {
                        ForEach(memory.tags.prefix(3), id: \.self) { tag in
                            Text(tag)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    Capsule()
                                        .fill(Color.secondary.opacity(0.1))
                                )
                        }
                    }
                    .padding(.horizontal, 1)
                }
            }
            
            // Action buttons
            HStack {
                Spacer()
                
                Button(action: onEdit) {
                    Image(systemName: "pencil")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .frame(width: 28, height: 28)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                        )
                }
                .buttonStyle(PlainButtonStyle())
                
                Button(action: { showDeleteAlert = true }) {
                    Image(systemName: "trash")
                        .font(.caption)
                        .foregroundColor(.red)
                        .frame(width: 28, height: 28)
                        .background(
                            Circle()
                                .fill(.ultraThinMaterial)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(12)
    }
    
    private var glassBackground: some View {
        ZStack {
            // Base glass effect
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
            
            // Additional glass layers for depth
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            .white.opacity(0.1),
                            .white.opacity(0.05),
                            .clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        }
    }
}

// MARK: - Preview
#Preview {
    let sampleMemory = Memory(
        title: "Beautiful sunset at the beach",
        content: "A wonderful evening watching the sunset with my pet",
        type: .photo,
        mediaURL: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
        tags: ["sunset", "beach", "peaceful"],
        isFavorite: true
    )
    
    return VStack {
        GlassMemoryCard(
            memory: sampleMemory,
            onEdit: { print("Edit tapped") },
            onDelete: { print("Delete tapped") },
            onTap: { print("Card tapped") }
        )
        .frame(width: 280)
    }
    .padding()
    .background(
        LinearGradient(
            colors: [.purple.opacity(0.3), .blue.opacity(0.3)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
}
