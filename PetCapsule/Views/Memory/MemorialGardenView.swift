import SwiftUI

@available(iOS 18.0, *)
struct MemorialGardenView: View {
    @StateObject private var memoryService = EnhancedMemoryService.shared
    @EnvironmentObject var realDataService: RealDataService
    
    @State private var selectedPet: Pet?
    @State private var showMemorialCreation = false
    @State private var showTributeCreation = false
    @State private var showMemorialDetail = false
    @State private var selectedMemorial: EnhancedMemory?
    @State private var showAllMemorials = false
    
    private let columns = [
        GridItem(.adaptive(minimum: 300), spacing: 16)
    ]
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.purple.opacity(0.1),
                        Color.indigo.opacity(0.05),
                        Color.clear
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Header
                        memorialHeaderView
                        
                        // Memorial pets grid
                        if !memorialPets.isEmpty {
                            memorialPetsSection
                        }
                        
                        // Recent memorial memories
                        if !recentMemorialMemories.isEmpty {
                            recentMemorialMemoriesSection
                        }
                        
                        // Create memorial section
                        createMemorialSection
                        
                        // Memorial quotes
                        memorialQuotesSection
                    }
                    .padding()
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showMemorialCreation) {
            MemorialCreationView(selectedPet: selectedPet)
        }
        .sheet(isPresented: $showTributeCreation) {
            TributeCreationView(selectedPet: selectedPet)
        }
        .sheet(isPresented: $showMemorialDetail) {
            if let memorial = selectedMemorial {
                MemorialDetailView(memorial: memorial)
            }
        }
        .sheet(isPresented: $showAllMemorials) {
            AllMemorialsView()
        }
        .task {
            await memoryService.loadMemories()
        }
    }
    
    private var memorialHeaderView: some View {
        VStack(spacing: 16) {
            // Title with icon
            HStack {
                Image(systemName: "heart.circle")
                    .font(.title)
                    .foregroundColor(.purple)
                
                Text("Memorial Garden")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
            
            // Subtitle
            Text("A peaceful place to honor and remember our beloved companions who have crossed the rainbow bridge.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // Memorial count
            if !memorialPets.isEmpty {
                HStack {
                    Image(systemName: "pawprint.circle")
                        .font(.caption)
                        .foregroundColor(.purple)
                    
                    Text("\(memorialPets.count) beloved \(memorialPets.count == 1 ? "companion" : "companions") remembered")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground).opacity(0.8))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private var memorialPetsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("In Loving Memory")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("View All") {
                    showAllMemorials = true
                }
                .font(.subheadline)
                .foregroundColor(.purple)
            }
            
            LazyVGrid(columns: columns, spacing: 16) {
                ForEach(memorialPets.prefix(4)) { pet in
                    MemorialPetCard(pet: pet) {
                        selectedPet = pet
                        showMemorialDetail = true
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground).opacity(0.8))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private var recentMemorialMemoriesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Memories")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("View All") {
                    showAllMemorials = true
                }
                .font(.subheadline)
                .foregroundColor(.purple)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(recentMemorialMemories.prefix(6)) { memory in
                        MemorialMemoryCard(memory: memory) {
                            selectedMemorial = memory
                            showMemorialDetail = true
                        }
                    }
                }
                .padding(.horizontal, 2)
            }
        }
        .padding()
        .background(Color(.systemBackground).opacity(0.8))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private var createMemorialSection: some View {
        VStack(spacing: 16) {
            VStack(spacing: 8) {
                Image(systemName: "heart.text.square")
                    .font(.title)
                    .foregroundColor(.purple)
                
                Text("Create a Memorial")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("Honor your beloved companion with a beautiful memorial that celebrates their life and the joy they brought to your world.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 12) {
                Button(action: {
                    selectedPet = nil
                    showMemorialCreation = true
                }) {
                    HStack {
                        Image(systemName: "heart.circle")
                        Text("Create Memorial")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.purple)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                
                Button(action: {
                    selectedPet = nil
                    showTributeCreation = true
                }) {
                    HStack {
                        Image(systemName: "text.quote")
                        Text("Write Tribute")
                            .fontWeight(.medium)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.purple.opacity(0.1))
                    .foregroundColor(.purple)
                    .cornerRadius(12)
                }
            }
        }
        .padding(24)
        .background(Color(.systemBackground).opacity(0.8))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private var memorialQuotesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Words of Comfort")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(memorialQuotes, id: \.self) { quote in
                        MemorialQuoteCard(quote: quote)
                    }
                }
                .padding(.horizontal, 2)
            }
        }
        .padding()
        .background(Color(.systemBackground).opacity(0.8))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Computed Properties
    
    private var memorialPets: [Pet] {
        // For now, return sample memorial pets
        // In a real app, this would filter pets marked as deceased
        return []
    }
    
    private var recentMemorialMemories: [EnhancedMemory] {
        return memoryService.filteredMemories.filter { $0.isMemorial }.prefix(6).map { $0 }
    }
    
    private var memorialQuotes: [String] {
        return [
            "\"Dogs are not our whole life, but they make our lives whole.\" - Roger Caras",
            "\"The bond with a true companion is a life-changing experience.\" - Unknown",
            "\"Those we love never truly leave us. They live forever in our hearts.\" - Unknown",
            "\"A pet is never truly forgotten until it is no longer remembered.\" - Unknown",
            "\"The love of a pet is the purest love on earth.\" - Unknown",
            "\"Gone from our sight, but never from our hearts.\" - Unknown"
        ]
    }
}

// MARK: - Memorial Pet Card
@available(iOS 18.0, *)
struct MemorialPetCard: View {
    let pet: Pet
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Pet photo with memorial frame
                ZStack {
                    AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 120)
                            .clipped()
                            .clipShape(Circle())
                    } placeholder: {
                        Circle()
                            .fill(Color(.systemGray5))
                            .frame(width: 120, height: 120)
                            .overlay(
                                Text(pet.name.prefix(1))
                                    .font(.largeTitle)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            )
                    }
                    .overlay(
                        Circle()
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.purple, Color.indigo]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 3
                            )
                    )
                    
                    // Memorial heart
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(systemName: "heart.fill")
                                .font(.title2)
                                .foregroundColor(.purple)
                                .background(Color.white)
                                .clipShape(Circle())
                                .padding(4)
                                .background(Color.white.opacity(0.9))
                                .clipShape(Circle())
                        }
                    }
                }
                
                // Pet information
                VStack(spacing: 4) {
                    Text(pet.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text(pet.species)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    // Memorial date (placeholder)
                    Text("Forever in our hearts")
                        .font(.caption)
                        .foregroundColor(.purple)
                        .fontWeight(.medium)
                }
                
                // Quick stats
                HStack(spacing: 16) {
                    VStack {
                        Text("5")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                        Text("Years")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    VStack {
                        Text("12")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                        Text("Memories")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Memorial Memory Card
@available(iOS 18.0, *)
struct MemorialMemoryCard: View {
    let memory: EnhancedMemory
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                // Memory image
                AsyncImage(url: URL(string: memory.thumbnailURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 120, height: 120)
                        .clipped()
                        .cornerRadius(12)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray5))
                        .frame(width: 120, height: 120)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.title2)
                                .foregroundColor(.secondary)
                        )
                }
                .overlay(
                    // Memorial overlay
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(systemName: "heart.circle.fill")
                                .font(.title2)
                                .foregroundColor(.purple)
                                .background(Color.white.opacity(0.9))
                                .clipShape(Circle())
                        }
                        .padding(4)
                    }
                )
                
                // Memory title
                Text(memory.title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
                    .frame(width: 120)
                
                // Memory date
                Text(memory.formattedDate)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Memorial Quote Card
@available(iOS 18.0, *)
struct MemorialQuoteCard: View {
    let quote: String
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "quote.opening")
                .font(.title)
                .foregroundColor(.purple.opacity(0.6))
            
            Text(quote)
                .font(.subheadline)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .fixedSize(horizontal: false, vertical: true)
            
            Image(systemName: "quote.closing")
                .font(.title)
                .foregroundColor(.purple.opacity(0.6))
        }
        .padding(20)
        .frame(width: 280)
        .frame(minHeight: 160)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// MARK: - Memorial Creation View
@available(iOS 18.0, *)
struct MemorialCreationView: View {
    let selectedPet: Pet?
    @Environment(\.dismiss) private var dismiss
    
    @State private var petName = ""
    @State private var memorialDate = Date()
    @State private var tributeMessage = ""
    @State private var selectedPhoto: UIImage?
    @State private var showPhotoPicker = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "heart.circle")
                            .font(.title)
                            .foregroundColor(.purple)
                        
                        Text("Create Memorial")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Honor your beloved companion with a beautiful memorial")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    
                    // Form
                    VStack(spacing: 20) {
                        // Pet name
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Pet Name")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            TextField("Enter your pet's name", text: $petName)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        // Memorial date
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Memorial Date")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            DatePicker("Select date", selection: $memorialDate, displayedComponents: .date)
                                .datePickerStyle(GraphicalDatePickerStyle())
                        }
                        
                        // Memorial photo
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Memorial Photo")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Button(action: { showPhotoPicker = true }) {
                                if let photo = selectedPhoto {
                                    Image(uiImage: photo)
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                        .frame(height: 200)
                                        .clipped()
                                        .cornerRadius(12)
                                } else {
                                    VStack(spacing: 12) {
                                        Image(systemName: "photo.badge.plus")
                                            .font(.title)
                                            .foregroundColor(.purple)
                                        
                                        Text("Select Photo")
                                            .font(.subheadline)
                                            .foregroundColor(.purple)
                                    }
                                    .frame(height: 200)
                                    .frame(maxWidth: .infinity)
                                    .background(Color.purple.opacity(0.1))
                                    .cornerRadius(12)
                                }
                            }
                        }
                        
                        // Tribute message
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Tribute Message")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            TextEditor(text: $tributeMessage)
                                .frame(height: 120)
                                .padding(8)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                        }
                    }
                    .padding()
                    
                    // Action buttons
                    VStack(spacing: 12) {
                        Button("Create Memorial") {
                            // TODO: Create memorial
                            dismiss()
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple)
                        .cornerRadius(12)
                        .disabled(petName.isEmpty)
                        
                        Button("Cancel") {
                            dismiss()
                        }
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showPhotoPicker) {
            // TODO: Photo picker
        }
    }
}

// MARK: - Tribute Creation View
@available(iOS 18.0, *)
struct TributeCreationView: View {
    let selectedPet: Pet?
    @Environment(\.dismiss) private var dismiss
    
    @State private var tributeTitle = ""
    @State private var tributeContent = ""
    @State private var selectedPetId: String?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "text.quote")
                            .font(.title)
                            .foregroundColor(.purple)
                        
                        Text("Write Tribute")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Share your favorite memories and what made your pet special")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    
                    // Form
                    VStack(spacing: 20) {
                        // Tribute title
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Tribute Title")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            TextField("Enter tribute title", text: $tributeTitle)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        // Tribute content
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Your Tribute")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            TextEditor(text: $tributeContent)
                                .frame(height: 200)
                                .padding(8)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                        }
                        
                        // Suggested prompts
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Writing Prompts")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            VStack(alignment: .leading, spacing: 8) {
                                Text("• What was your pet's favorite thing to do?")
                                Text("• What made them special and unique?")
                                Text("• What are your favorite memories together?")
                                Text("• How did they change your life?")
                                Text("• What would you want others to know about them?")
                            }
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    
                    // Action buttons
                    VStack(spacing: 12) {
                        Button("Save Tribute") {
                            // TODO: Save tribute
                            dismiss()
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple)
                        .cornerRadius(12)
                        .disabled(tributeTitle.isEmpty || tributeContent.isEmpty)
                        
                        Button("Cancel") {
                            dismiss()
                        }
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .navigationBarHidden(true)
        }
    }
}

// MARK: - Placeholder Views
@available(iOS 18.0, *)
struct MemorialDetailView: View {
    let memorial: EnhancedMemory
    
    var body: some View {
        Text("Memorial Detail View")
            .font(.title)
            .foregroundColor(.secondary)
    }
}

@available(iOS 18.0, *)
struct AllMemorialsView: View {
    var body: some View {
        Text("All Memorials View")
            .font(.title)
            .foregroundColor(.secondary)
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        MemorialGardenView()
            .environmentObject(RealDataService())
    } else {
        Text("iOS 18.0 required")
    }
} 