import SwiftUI

@available(iOS 18.0, *)
struct MemoryComparisonView: View {
    @EnvironmentObject var realDataService: RealDataService
    @State private var showingApplePhotos = true
    
    var body: some View {
        NavigationView {
            VStack {
                // Style Toggle
                Picker("Style", selection: $showingApplePhotos) {
                    Text("Apple Photos Style").tag(true)
                    Text("Original Glass Style").tag(false)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // Show the selected style
                if showingApplePhotos {
                    ApplePhotosMemoryView()
                        .environmentObject(realDataService)
                } else {
                    MemoryVaultView()
                        .environmentObject(realDataService)
                }
            }
            .navigationTitle("Choose Your Style")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        MemoryComparisonView()
            .environmentObject(RealDataService())
    } else {
        Text("iOS 18.0+ Required")
    }
} 