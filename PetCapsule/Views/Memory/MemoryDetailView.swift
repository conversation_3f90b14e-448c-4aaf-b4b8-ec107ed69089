//
//  MemoryDetailView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 6/6/25.
//

import SwiftUI
import AVKit
import Photos

@available(iOS 17.0, *)
struct MemoryDetailView: View {
    let memory: EnhancedMemory
    let onDismiss: () -> Void
    
    @State private var selectedMediaIndex = 0
    @State private var showFullScreenMedia = false
    @State private var showEditView = false
    @State private var showShareSheet = false
    @State private var showDeleteAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with buttons
                HStack {
                    Button("Done") {
                        onDismiss()
                    }
                    .foregroundColor(.blue)

                    Spacer()

                    Menu {
                        Button("Edit Memory") {
                            showEditView = true
                        }

                        But<PERSON>("Share Memory") {
                            showShareSheet = true
                        }

                        But<PERSON>("Delete Memory", role: .destructive) {
                            showDeleteAlert = true
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(.blue)
                    }
                }
                .padding()
                .background(Color(.systemBackground))

                ScrollView(.vertical, showsIndicators: false) {
                    VStack(alignment: .leading, spacing: 20) {
                        // Media Carousel
                        if !memory.mediaItems.isEmpty {
                            mediaCarouselView
                        }

                        // Memory Details
                        memoryDetailsView
                            .padding()
                    }
                }
            }
            .navigationTitle("Memory Details")
            .navigationBarTitleDisplayMode(.inline)
        }
        .fullScreenCover(isPresented: $showFullScreenMedia) {
            FullScreenMediaView(
                mediaItems: memory.mediaItems,
                selectedIndex: selectedMediaIndex
            ) {
                showFullScreenMedia = false
            }
        }
        .sheet(isPresented: $showEditView) {
            if #available(iOS 18.0, *) {
                EnhancedMemoryCreationView()
                    .environmentObject(RealDataService())
            } else {
                // For now, show a placeholder since EditMemoryView expects Memory type
                Text("Edit functionality coming soon")
                    .padding()
            }
        }
        .alert("Delete Memory", isPresented: $showDeleteAlert) {
            Button("Delete", role: .destructive) {
                // TODO: Implement delete functionality
                onDismiss()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Are you sure you want to delete this memory? This action cannot be undone.")
        }
    }

    
    private var memoryDetailsView: some View {
        VStack(alignment: .leading, spacing: 16) {
            titleAndDateView
            eventBadgesView
            descriptionView
            locationView
            tagsView
            measurementsView
            voiceNotesView
            additionalNotesView
        }
    }
    
    private var titleAndDateView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(memory.title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(memory.createdAt.formatted(date: .complete, time: .shortened))
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    private var eventBadgesView: some View {
        HStack {
            EventBadge(eventType: memory.eventType)
            
            MoodBadge(mood: memory.mood)
            
            Spacer()
            
            if memory.isProtected {
                Image(systemName: "lock.shield")
                    .foregroundColor(.orange)
                    .font(.headline)
            }
            
            if memory.isSecureVault {
                Image(systemName: "lock.circle")
                    .foregroundColor(.purple)
                    .font(.headline)
            }
        }
    }
    
    @ViewBuilder
    private var descriptionView: some View {
        if !memory.content.isEmpty {
            VStack(alignment: .leading, spacing: 8) {
                Text("Description")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(memory.content)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    @ViewBuilder
    private var locationView: some View {
        if let location = memory.location {
            VStack(alignment: .leading, spacing: 8) {
                Text("Location")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                HStack {
                    Image(systemName: "location")
                        .foregroundColor(.blue)
                    Text(location.name)
                        .font(.body)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    @ViewBuilder
    private var tagsView: some View {
        if !memory.tags.isEmpty {
            VStack(alignment: .leading, spacing: 8) {
                Text("Tags")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                TagsView(tags: memory.tags)
            }
        }
    }
    
    @ViewBuilder
    private var measurementsView: some View {
        if let measurements = memory.measurements, !measurements.isEmpty {
            VStack(alignment: .leading, spacing: 8) {
                Text("Measurements")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(measurements, id: \.type) { measurement in
                        HStack {
                            Text(measurement.type.displayName)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(measurement.value, specifier: "%.1f") \(measurement.unit)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                        }
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    private var voiceNotesView: some View {
        if let voiceNoteURL = memory.voiceNoteURL {
            VStack(alignment: .leading, spacing: 8) {
                Text("Voice Notes")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                VStack(spacing: 8) {
                    VoiceNoteView(voiceNoteURL: voiceNoteURL)
                }
            }
        }
    }
    
    @ViewBuilder
    private var additionalNotesView: some View {
        if !memory.content.isEmpty {
            VStack(alignment: .leading, spacing: 8) {
                Text("Additional Notes")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(memory.content)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var mediaCarouselView: some View {
        VStack(spacing: 12) {
            // Main media display
            TabView(selection: $selectedMediaIndex) {
                ForEach(0..<memory.mediaItems.count, id: \.self) { index in
                    let mediaItem = memory.mediaItems[index]
                    
                    Button(action: {
                        showFullScreenMedia = true
                    }) {
                        MediaItemView(mediaItem: mediaItem)
                            .aspectRatio(16/9, contentMode: .fill)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                    .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .frame(height: 250)
            
            // Media thumbnails
            if memory.mediaItems.count > 1 {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(0..<memory.mediaItems.count, id: \.self) { index in
                            let mediaItem = memory.mediaItems[index]
                            
                            Button(action: {
                                withAnimation {
                                    selectedMediaIndex = index
                                }
                            }) {
                                MediaItemView(mediaItem: mediaItem)
                                    .aspectRatio(1, contentMode: .fill)
                                    .frame(width: 60, height: 60)
                                    .clipShape(RoundedRectangle(cornerRadius: 8))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(
                                                selectedMediaIndex == index ? Color.blue : Color.clear,
                                                lineWidth: 2
                                            )
                                    )
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct MediaItemView: View {
    let mediaItem: MemoryMediaItem
    
    var body: some View {
        ZStack {
            AsyncImage(url: URL(string: mediaItem.thumbnailURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color(.systemGray5))
                    .overlay(
                        Image(systemName: mediaItem.type == .video ? "video" : "photo")
                            .font(.title)
                            .foregroundColor(.secondary)
                    )
            }
            
            if mediaItem.type == .video {
                VStack {
                    HStack {
                        Spacer()
                        
                        if let duration = mediaItem.duration {
                            Text(formatDuration(duration))
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.black.opacity(0.6))
                                .clipShape(RoundedRectangle(cornerRadius: 4))
                        }
                    }
                    .padding(8)
                    
                    Spacer()
                    
                    Image(systemName: "play.circle.fill")
                        .font(.system(size: 44))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.3), radius: 2)
                    
                    Spacer()
                }
            }
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

struct EventBadge: View {
    let eventType: MemoryEventType
    
    var body: some View {
        HStack {
            Image(systemName: eventType.icon)
                .font(.caption)
            Text(eventType.displayName)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(eventType.color.opacity(0.2))
        .foregroundColor(eventType.color)
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

struct MoodBadge: View {
    let mood: MemoryMood
    
    var body: some View {
        HStack {
            Text(mood.emoji)
                .font(.caption)
            Text(mood.rawValue.capitalized)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(mood.color.opacity(0.2))
        .foregroundColor(mood.color)
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

struct TagsView: View {
    let tags: [String]
    
    var body: some View {
        LazyVGrid(columns: [
            GridItem(.adaptive(minimum: 80))
        ], alignment: .leading, spacing: 8) {
            ForEach(tags, id: \.self) { tag in
                Text("#\(tag)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            }
        }
    }
}

struct VoiceNoteView: View {
    let voiceNoteURL: String
    @State private var isPlaying = false
    
    var body: some View {
        HStack {
            Button(action: {
                // TODO: Implement voice note playback
                isPlaying.toggle()
            }) {
                Image(systemName: isPlaying ? "stop.circle.fill" : "play.circle.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text("Voice Note")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("00:45")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(12)
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

struct FullScreenMediaView: View {
    let mediaItems: [MemoryMediaItem]
    @State var selectedIndex: Int
    let onDismiss: () -> Void
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            TabView(selection: $selectedIndex) {
                ForEach(0..<mediaItems.count, id: \.self) { index in
                    let mediaItem = mediaItems[index]
                    
                    if mediaItem.type == .video {
                        VideoPlayer(player: AVPlayer(url: URL(string: mediaItem.url)!))
                            .tag(index)
                    } else {
                        AsyncImage(url: URL(string: mediaItem.url)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                        } placeholder: {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        }
                        .tag(index)
                    }
                }
            }
            .tabViewStyle(PageTabViewStyle())
            
            VStack {
                HStack {
                    Button(action: onDismiss) {
                        Image(systemName: "xmark")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding(12)
                            .background(Color.black.opacity(0.5))
                            .clipShape(Circle())
                    }
                    
                    Spacer()
                }
                .padding()
                
                Spacer()
            }
        }
    }
}

// MARK: - Preview
#Preview {
    MemoryDetailView(memory: EnhancedMemory(
        title: "Sample Memory",
        content: "A sample memory content for preview",
        petIds: ["sample-id"]
    )) {
        // Dismiss action
    }
}
