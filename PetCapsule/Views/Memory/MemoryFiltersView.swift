import SwiftUI
@available(iOS 17.0, *)
struct MemoryFiltersView: View {
    @ObservedObject var memoryService: EnhancedMemoryService
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realDataService: RealDataService
    @State private var selectedEventTypes: Set<MemoryEventType> = []
    @State private var selectedMoods: Set<MemoryMood> = []
    @State private var selectedPets: Set<String> = []
    @State private var startDate: Date?
    @State private var endDate: Date?
    @State private var showDatePicker = false
    @State private var datePickerSelection: Date = Date()
    @State private var isSelectingStartDate = true
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Event Types Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Event Types")
                            .font(.headline)
                            .fontWeight(.semibold)
                        LazyVGrid(columns: [
                            GridItem(.adaptive(minimum: 120))
                        ], spacing: 12) {
                            ForEach(MemoryEventType.allCases, id: \.self) { eventType in
                                EventTypeChip(
                                    eventType: eventType,
                                    isSelected: selectedEventTypes.contains(eventType)
                                ) {
                                    if selectedEventTypes.contains(eventType) {
                                        selectedEventTypes.remove(eventType)
                                    } else {
                                        selectedEventTypes.insert(eventType)
                                    }
                                }
                            }
                        }
                    }
                    // Moods Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Moods")
                            .font(.headline)
                            .fontWeight(.semibold)
                        LazyVGrid(columns: [
                            GridItem(.adaptive(minimum: 100))
                        ], spacing: 12) {
                            ForEach(MemoryMood.allCases, id: \.self) { mood in
                                MoodChip(
                                    mood: mood,
                                    isSelected: selectedMoods.contains(mood)
                                ) {
                                    if selectedMoods.contains(mood) {
                                        selectedMoods.remove(mood)
                                    } else {
                                        selectedMoods.insert(mood)
                                    }
                                }
                            }
                        }
                    }
                    // Pets Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Pets")
                            .font(.headline)
                            .fontWeight(.semibold)
                        LazyVGrid(columns: [
                            GridItem(.adaptive(minimum: 120))
                        ], spacing: 12) {
                            ForEach(realDataService.pets, id: \.id) { pet in
                                PetChip(
                                    pet: pet,
                                    isSelected: selectedPets.contains(pet.id)
                                ) {
                                    if selectedPets.contains(pet.id) {
                                        selectedPets.remove(pet.id)
                                    } else {
                                        selectedPets.insert(pet.id)
                                    }
                                }
                            }
                        }
                    }
                    // Date Range Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Date Range")
                            .font(.headline)
                            .fontWeight(.semibold)
                        HStack(spacing: 12) {
                            // Start Date
                            Button(action: {
                                isSelectingStartDate = true
                                datePickerSelection = startDate ?? Date()
                                showDatePicker = true
                            }) {
                                VStack {
                                    Text("From")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Text(startDate?.formatted(date: .abbreviated, time: .omitted) ?? "Select")
                                        .font(.body)
                                        .foregroundColor(startDate != nil ? .primary : .secondary)
                                }
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                            }
                            // End Date
                            Button(action: {
                                isSelectingStartDate = false
                                datePickerSelection = endDate ?? Date()
                                showDatePicker = true
                            }) {
                                VStack {
                                    Text("To")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Text(endDate?.formatted(date: .abbreviated, time: .omitted) ?? "Select")
                                        .font(.body)
                                        .foregroundColor(endDate != nil ? .primary : .secondary)
                                }
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                            }
                        }
                        // Clear dates button
                        if startDate != nil || endDate != nil {
                            Button("Clear Dates") {
                                startDate = nil
                                endDate = nil
                            }
                            .foregroundColor(.red)
                            .font(.caption)
                        }
                    }
                    // Quick Date Filters
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Quick Filters")
                            .font(.headline)
                            .fontWeight(.semibold)
                        HStack {
                            QuickDateButton(title: "Today") {
                                startDate = Calendar.current.startOfDay(for: Date())
                                endDate = Calendar.current.date(byAdding: .day, value: 1, to: startDate!)
                            }
                            QuickDateButton(title: "This Week") {
                                let calendar = Calendar.current
                                startDate = calendar.dateInterval(of: .weekOfYear, for: Date())?.start
                                endDate = calendar.dateInterval(of: .weekOfYear, for: Date())?.end
                            }
                            QuickDateButton(title: "This Month") {
                                let calendar = Calendar.current
                                startDate = calendar.dateInterval(of: .month, for: Date())?.start
                                endDate = calendar.dateInterval(of: .month, for: Date())?.end
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Clear All") {
                        selectedEventTypes.removeAll()
                        selectedMoods.removeAll()
                        selectedPets.removeAll()
                        startDate = nil
                        endDate = nil
                    }
                    .foregroundColor(.red)
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        applyFilters()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
            .sheet(isPresented: $showDatePicker) {
                NavigationView {
                    DatePicker(
                        isSelectingStartDate ? "Select Start Date" : "Select End Date",
                        selection: $datePickerSelection,
                        displayedComponents: .date
                    )
                    .datePickerStyle(GraphicalDatePickerStyle())
                    .navigationTitle(isSelectingStartDate ? "Start Date" : "End Date")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Cancel") {
                                showDatePicker = false
                            }
                        }
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Button("Done") {
                                if isSelectingStartDate {
                                    startDate = datePickerSelection
                                } else {
                                    endDate = datePickerSelection
                                }
                                showDatePicker = false
                            }
                        }
                    }
                }
            }
        }
        .onAppear {
            loadCurrentFilters()
        }
    }
    private func loadCurrentFilters() {
        selectedEventTypes = memoryService.selectedEventTypes
        selectedMoods = memoryService.selectedMoods
        selectedPets = memoryService.selectedPets
        if let dateRange = memoryService.dateRange {
            startDate = dateRange.start
            endDate = dateRange.end
        }
    }
    private func applyFilters() {
        memoryService.selectedEventTypes = selectedEventTypes
        memoryService.selectedMoods = selectedMoods
        memoryService.selectedPets = selectedPets
        if let start = startDate, let end = endDate {
            memoryService.dateRange = DateRange(start: start, end: end)
        } else {
            memoryService.dateRange = nil
        }
    }
    private func emojiForSpecies(_ species: String) -> String {
        switch species.lowercased() {
        case "dog":
            return "🐕"
        case "cat":
            return "🐱"
        case "bird":
            return "🦅"
        case "fish":
            return "🐟"
        case "rabbit":
            return "🐰"
        case "hamster":
            return "🐹"
        case "turtle":
            return "🐢"
        case "lizard":
            return "🦎"
        case "snake":
            return "🐍"
        case "horse":
            return "🐴"
        default:
            return "🐾"
        }
    }
}
// MARK: - Supporting Views
struct EventTypeChip: View {
    let eventType: MemoryEventType
    let isSelected: Bool
    let onTap: () -> Void
    var body: some View {
        Button(action: onTap) {
            HStack {
                Image(systemName: eventType.icon)
                    .font(.caption)
                Text(eventType.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                isSelected ? eventType.color : Color(.systemGray6)
            )
            .foregroundColor(
                isSelected ? .white : .primary
            )
            .cornerRadius(16)
        }
    }
}
struct MoodChip: View {
    let mood: MemoryMood
    let isSelected: Bool
    let onTap: () -> Void
    var body: some View {
        Button(action: onTap) {
            HStack {
                Text(mood.emoji)
                    .font(.caption)
                Text(mood.rawValue.capitalized)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                isSelected ? mood.color : Color(.systemGray6)
            )
            .foregroundColor(
                isSelected ? .white : .primary
            )
            .cornerRadius(16)
        }
    }
}
struct PetChip: View {
    let pet: Pet
    let isSelected: Bool
    let onTap: () -> Void
    var body: some View {
        Button(action: onTap) {
            HStack {
                Text(emojiForSpecies(pet.species))
                    .font(.caption)
                Text(pet.name)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                isSelected ? Color.blue : Color(.systemGray6)
            )
            .foregroundColor(
                isSelected ? .white : .primary
            )
            .cornerRadius(16)
        }
    }
    private func emojiForSpecies(_ species: String) -> String {
        switch species.lowercased() {
        case "dog":
            return "🐕"
        case "cat":
            return "🐱"
        case "bird":
            return "🦅"
        case "fish":
            return "🐟"
        case "rabbit":
            return "🐰"
        case "hamster":
            return "🐹"
        case "turtle":
            return "🐢"
        case "lizard":
            return "🦎"
        case "snake":
            return "🐍"
        case "horse":
            return "🐴"
        default:
            return "🐾"
        }
    }
}
struct QuickDateButton: View {
    let title: String
    let action: () -> Void
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
        }
    }
}
// DateRange is already defined in EnhancedMemoryService.swift 