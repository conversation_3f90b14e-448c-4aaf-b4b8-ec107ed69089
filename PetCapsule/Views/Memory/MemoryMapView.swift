import SwiftUI
import MapKit

@available(iOS 17.0, *)
struct MemoryMapView: View {
    @ObservedObject var memoryService: EnhancedMemoryService
    let onMemoryTap: (EnhancedMemory) -> Void
    
    @State private var region = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194), // San Francisco default
        span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
    )
    
    @State private var selectedMemory: EnhancedMemory?
    @State private var showMemoryCard = false
    @State private var cameraPosition = MapCameraPosition.region(
        MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194),
            span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
        )
    )
    
    private var memoriesWithLocation: [EnhancedMemory] {
        memoryService.filteredMemories.filter { $0.location != nil }
    }
    
    var body: some View {
        ZStack {
            // Map
            Map(position: $cameraPosition) {
                ForEach(memoriesWithLocation, id: \.id) { memory in
                    if let location = memory.location {
                        Annotation(memory.title, coordinate: location.coordinate) {
                            MemoryAnnotationView(memory: memory) {
                                selectedMemory = memory
                                showMemoryCard = true
                            }
                        }
                    }
                }
            }
            .mapStyle(.standard(elevation: .realistic))
            .mapControls {
                MapUserLocationButton()
                MapCompass()
                MapScaleView()
            }
            
            // Top overlay
            VStack {
                HStack {
                    // Map stats
                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(memoriesWithLocation.count) memories")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text("with location data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(.systemBackground).opacity(0.9))
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    
                    Spacer()
                    
                    // View all memories button
                    Button(action: {
                        zoomToShowAllMemories()
                    }) {
                        HStack {
                            Image(systemName: "viewfinder")
                                .font(.caption)
                            Text("View All")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.blue)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 6)
                        .background(Color(.systemBackground).opacity(0.9))
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                }
                .padding()
                
                Spacer()
            }
            
            // Bottom memory card
            if showMemoryCard, let memory = selectedMemory {
                VStack {
                    Spacer()
                    
                    MemoryCardView(memory: memory) {
                        onMemoryTap(memory)
                    } onDismiss: {
                        selectedMemory = nil
                        showMemoryCard = false
                    }
                    .transition(.move(edge: .bottom))
                    .animation(.spring(), value: showMemoryCard)
                }
            }
        }
        .onAppear {
            centerMapOnMemories()
        }
    }
    
    private func centerMapOnMemories() {
        let coordinates = memoriesWithLocation.compactMap { $0.location?.coordinate }
        guard !coordinates.isEmpty else { return }
        
        let avgLat = coordinates.map { $0.latitude }.reduce(0, +) / Double(coordinates.count)
        let avgLon = coordinates.map { $0.longitude }.reduce(0, +) / Double(coordinates.count)
        
        let center = CLLocationCoordinate2D(latitude: avgLat, longitude: avgLon)
        
        // Calculate span to show all memories
        let minLat = coordinates.map { $0.latitude }.min() ?? avgLat
        let maxLat = coordinates.map { $0.latitude }.max() ?? avgLat
        let minLon = coordinates.map { $0.longitude }.min() ?? avgLon
        let maxLon = coordinates.map { $0.longitude }.max() ?? avgLon
        
        let span = MKCoordinateSpan(
            latitudeDelta: max(0.01, (maxLat - minLat) * 1.5),
            longitudeDelta: max(0.01, (maxLon - minLon) * 1.5)
        )
        
        withAnimation(.easeInOut(duration: 1.0)) {
            cameraPosition = .region(MKCoordinateRegion(center: center, span: span))
        }
    }
    
    private func zoomToShowAllMemories() {
        centerMapOnMemories()
    }
}

// MARK: - Memory Annotation View

@available(iOS 17.0, *)
struct MemoryAnnotationView: View {
    let memory: EnhancedMemory
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Background circle
                Circle()
                    .fill(memory.eventType.color)
                    .frame(width: 40, height: 40)
                    .shadow(color: .black.opacity(0.3), radius: 3, x: 0, y: 2)
                
                // Event type icon
                Image(systemName: memory.eventType.icon)
                    .font(.title3)
                    .foregroundColor(.white)
                    .fontWeight(.medium)
                
                // Special indicators
                VStack {
                    HStack {
                        if memory.hasVideo {
                            Image(systemName: "video.fill")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(2)
                                .background(Color.black.opacity(0.7))
                                .clipShape(Circle())
                        }
                        
                        Spacer()
                        
                        if memory.isSecureVault {
                            Image(systemName: "lock.fill")
                                .font(.caption2)
                                .foregroundColor(.orange)
                                .padding(2)
                                .background(Color.black.opacity(0.7))
                                .clipShape(Circle())
                        }
                    }
                    .frame(width: 40, height: 40)
                    
                    Spacer()
                }
            }
        }
        .scaleEffect(1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: memory.id)
    }
}

// MARK: - Memory Card View

@available(iOS 17.0, *)
struct MemoryCardView: View {
    let memory: EnhancedMemory
    let onTap: () -> Void
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Memory image
            AsyncImage(url: URL(string: memory.thumbnailURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 120)
                    .clipped()
            } placeholder: {
                Rectangle()
                    .fill(Color(.systemGray5))
                    .frame(height: 120)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title2)
                            .foregroundColor(.secondary)
                    )
            }
            
            // Memory info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(memory.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .lineLimit(2)
                        
                        Text(memory.createdAt.formatted(date: .abbreviated, time: .omitted))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button(action: onDismiss) {
                        Image(systemName: "xmark")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(6)
                            .background(Color(.systemGray6))
                            .clipShape(Circle())
                    }
                }
                
                // Event type and location
                HStack {
                    HStack {
                        Image(systemName: memory.eventType.icon)
                            .font(.caption)
                        Text(memory.eventType.displayName)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(memory.eventType.color)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    
                    Spacer()
                    
                    if let location = memory.location {
                        HStack {
                            Image(systemName: "location")
                                .font(.caption)
                            Text(location.name)
                                .font(.caption)
                                .lineLimit(1)
                        }
                        .foregroundColor(.secondary)
                    }
                }
                
                // Action button
                Button(action: onTap) {
                    HStack {
                        Text("View Memory")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        Image(systemName: "arrow.right")
                            .font(.caption)
                    }
                    .foregroundColor(.blue)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.blue.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                }
            }
            .padding()
        }
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .padding(.horizontal)
        .padding(.bottom, 20)
    }
}

// MARK: - Location Extension

extension MemoryLocation {
    var coordinate: CLLocationCoordinate2D {
        if let coords = coordinates {
            return CLLocationCoordinate2D(latitude: coords.latitude, longitude: coords.longitude)
        } else {
            // Default to San Francisco
            return CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)
        }
    }
}

// MARK: - Preview

#Preview {
    MemoryMapView(memoryService: EnhancedMemoryService.shared) { memory in
        print("Memory tapped: \(memory.title)")
    }
} 