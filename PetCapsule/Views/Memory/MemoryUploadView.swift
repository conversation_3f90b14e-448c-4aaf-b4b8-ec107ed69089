//
//  MemoryUploadView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//
import SwiftUI
import PhotosUI
import AVFoundation
import SwiftData
// MARK: - Audio Recording Coordinator
class AudioRecordingCoordinator: NSObject, ObservableObject, AVAudioRecorderDelegate {
    @Published var isRecording = false
    @Published var recordingURL: URL?
    @Published var recordingDuration: TimeInterval = 0
    private var audioRecorder: AVAudioRecorder?
    private var recordingTimer: Timer?
    func startRecording() {
        // Request microphone permission and start recording
        if #available(iOS 17.0, *) {
            AVAudioApplication.requestRecordPermission { granted in
                if granted {
                    DispatchQueue.main.async {
                        self.setupAudioRecorder()
                        self.isRecording = true
                        self.audioRecorder?.record()
                        self.startRecordingTimer()
                    }
                }
            }
        } else {
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                if granted {
                    DispatchQueue.main.async {
                        self.setupAudioRecorder()
                        self.isRecording = true
                        self.audioRecorder?.record()
                        self.startRecordingTimer()
                    }
                }
            }
        }
    }
    private func setupAudioRecorder() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = documentsPath.appendingPathComponent("recording_\(Date().timeIntervalSince1970).m4a")
        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 12000,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]
        do {
            try AVAudioSession.sharedInstance().setCategory(.playAndRecord, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.prepareToRecord()
            recordingURL = audioFilename
        } catch {
            print("Failed to setup audio recorder: \(error)")
        }
    }
    private func startRecordingTimer() {
        recordingDuration = 0
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            self.recordingDuration += 0.1
        }
    }
    func stopRecording() {
        isRecording = false
        audioRecorder?.stop()
        recordingTimer?.invalidate()
        recordingTimer = nil
        try? AVAudioSession.sharedInstance().setActive(false)
    }
    // MARK: - AVAudioRecorderDelegate
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if flag {
            print("✅ Audio recording completed successfully")
            recordingURL = recorder.url
        } else {
            print("❌ Audio recording failed")
            recordingURL = nil
        }
    }
    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        print("❌ Audio recording error: \(error?.localizedDescription ?? "Unknown error")")
        recordingURL = nil
        isRecording = false
    }
}
@available(iOS 18.0, *)
struct MemoryUploadView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject private var memoryService: ProductionMemoryService
    @StateObject private var visualIntelligence = VisualIntelligenceService.shared
    @StateObject private var appleIntelligence = AppleIntelligenceService.shared
    @StateObject private var aiService = PetAISupportService.shared
    @Environment(\.colorScheme) private var colorScheme
    let pet: Pet
    @State private var selectedMemoryType: MemoryType = .photo
    @State private var title = ""
    @State private var content = ""
    @State private var selectedPhotos: [PhotosPickerItem] = []
    @State private var selectedImage: UIImage?
    @StateObject private var audioCoordinator = AudioRecordingCoordinator()
    @State private var audioPlayer: AVAudioPlayer?
    @State private var isUploading = false
    @State private var uploadProgress = 0.0
    @State private var showingError = false
    @State private var errorMessage: String?
    // Apple Intelligence + Visual Intelligence states
    @State private var isAnalyzingImage = false
    @State private var breedIdentificationResult: PetBreedIdentificationResult?
    @State private var healthAnalysisResult: VisualHealthAnalysisResult?
    @State private var productRecommendations: PetProductIdentificationResult?
    @State private var enhancedDescription = ""
    @State private var aiGeneratedTags: [String] = []
    // User input states
    @State private var uploadedImage: UIImage?
    @State private var showingAIAnalysis = false
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Apple Intelligence Status Banner
                    if visualIntelligence.isAvailable && appleIntelligence.isWritingToolsAvailable {
                        aiCapabilitiesBanner
                    }
                    // Image Upload Section
                    imageUploadSection
                    // AI Analysis Results (if available)
                    if showingAIAnalysis {
                        aiAnalysisResultsSection
                    }
                    // Manual Input Section
                    manualInputSection
                    // Upload Button
                    uploadButton
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .background(colorScheme == .dark ? Color.black : Color(.systemGroupedBackground))
            .navigationTitle("Add Memory")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") { dismiss() }
                }
            }
            .onChange(of: selectedPhotos) { _, newValue in
                handleImageSelection(newValue)
            }
            .alert("Upload Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") { errorMessage = nil }
            } message: {
                if let error = errorMessage {
                    Text(error)
                }
            }
        }
    }
    private var aiCapabilitiesBanner: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "brain.filled.head.profile")
                    .foregroundColor(.blue)
                    .font(.title2)
                VStack(alignment: .leading, spacing: 4) {
                    Text("🤖 Apple Intelligence Enabled")
                        .font(.headline)
                        .fontWeight(.bold)
                    Text("Auto breed ID • Health analysis • Smart descriptions")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
                if isAnalyzingImage {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            if isAnalyzingImage {
                ProgressView(value: uploadProgress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle())
                    .accentColor(.blue)
                Text("Analyzing with Apple Intelligence...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(16)
        .background(glassmorphicBackground)
    }
    private var imageUploadSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("📷 Upload Photo")
                .font(.headline)
                .fontWeight(.bold)
            if let image = uploadedImage {
                // Uploaded Image Preview
                VStack(spacing: 12) {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 200)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.blue, lineWidth: 2)
                        )
                    if isAnalyzingImage {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Apple Intelligence analyzing...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    Button("Change Photo") {
                        selectedPhotos = []
                        selectedImage = nil
                        resetAIResults()
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                }
            } else {
                // Photo Picker
                PhotosPicker(
                    selection: $selectedPhotos,
                    maxSelectionCount: 1,
                    matching: .images
                ) {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.blue.opacity(0.1))
                        .frame(height: 120)
                        .overlay(
                            VStack(spacing: 8) {
                                Image(systemName: "camera.fill")
                                    .font(.title2)
                                    .foregroundColor(.blue)
                                Text("Select Photo")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)
                                Text("Apple Intelligence will auto-analyze")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        )
                }
            }
        }
        .padding(20)
        .background(glassmorphicBackground)
    }
    private var aiAnalysisResultsSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("🤖 Apple Intelligence Analysis")
                .font(.headline)
                .fontWeight(.bold)
            // Breed Identification Results
            if let breedResult = breedIdentificationResult {
                breedIdentificationCard(breedResult)
            }
            // Health Analysis Results
            if let healthResult = healthAnalysisResult {
                healthAnalysisCard(healthResult)
            }
            // Product Recommendations
            if let productResult = productRecommendations {
                productRecommendationsCard(productResult)
            }
            // Enhanced Description
            if !enhancedDescription.isEmpty {
                enhancedDescriptionCard
            }
            // AI Generated Tags
            if !aiGeneratedTags.isEmpty {
                aiTagsCard
            }
        }
        .padding(20)
        .background(glassmorphicBackground)
        .animation(.easeInOut, value: showingAIAnalysis)
    }
    private func breedIdentificationCard(_ result: PetBreedIdentificationResult) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "pawprint.circle.fill")
                    .foregroundColor(.purple)
                    .font(.title3)
                VStack(alignment: .leading, spacing: 2) {
                    Text("Breed Identified")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Text("\(result.primaryBreed) (\(Int(result.confidence * 100))% confidence)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
                Button("Apply") {
                    title = "My \(result.primaryBreed)"
                    content = enhancedDescription.isEmpty ? 
                        result.careInstructions.first ?? "Beautiful \(result.primaryBreed) moment" : enhancedDescription
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.purple.opacity(0.2))
                .foregroundColor(.purple)
                .clipShape(Capsule())
            }
            Text(result.characteristics.joined(separator: " • "))
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(2)
        }
        .padding(12)
        .background(Color.purple.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 10))
    }
    private func healthAnalysisCard(_ result: VisualHealthAnalysisResult) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: result.shouldConsultVet ? "exclamationmark.triangle.fill" : "checkmark.circle.fill")
                    .foregroundColor(result.shouldConsultVet ? .orange : .green)
                    .font(.title3)
                VStack(alignment: .leading, spacing: 2) {
                    Text("Health Analysis")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Text(result.shouldConsultVet ? "Consider vet consultation" : "Looks healthy")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
                if result.shouldConsultVet {
                    NavigationLink("Book Vet") {
                        // Link to vet booking
                        Text("Vet Booking")
                    }
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.orange.opacity(0.2))
                    .foregroundColor(.orange)
                    .clipShape(Capsule())
                }
            }
            if !result.recommendations.isEmpty {
                Text(result.recommendations.prefix(2).joined(separator: " • "))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding(12)
        .background((result.shouldConsultVet ? Color.orange : Color.green).opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 10))
    }
    private func productRecommendationsCard(_ result: PetProductIdentificationResult) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            productCardHeader(result)
            productGrid(result)
        }
        .padding(12)
        .background(Color.blue.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 10))
    }
    private func productCardHeader(_ result: PetProductIdentificationResult) -> some View {
        HStack {
            Image(systemName: "cart.fill")
                .foregroundColor(.blue)
                .font(.title3)
            VStack(alignment: .leading, spacing: 2) {
                Text("Product Recommendations")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                Text("\(result.identifiedProducts.count) items found")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            Spacer()
            NavigationLink("Shop") {
                Text("Shopping View")
            }
            .font(.caption)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.blue.opacity(0.2))
            .foregroundColor(.blue)
            .clipShape(Capsule())
        }
    }
    private func productGrid(_ result: PetProductIdentificationResult) -> some View {
        LazyHGrid(rows: [GridItem(.flexible())], spacing: 8) {
            productForEach(result)
        }
    }
    private func productForEach(_ result: PetProductIdentificationResult) -> some View {
        ForEach(Array(result.identifiedProducts.prefix(3)), id: \.name) { product in
            productCell(product)
        }
    }
    private func productCell(_ product: IdentifiedProduct) -> some View {
        VStack(spacing: 4) {
            Text(product.name)
                .font(.caption2)
                .fontWeight(.medium)
                .lineLimit(1)
            Text(product.category)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.blue.opacity(0.1))
        .clipShape(Capsule())
    }
    private var enhancedDescriptionCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "text.bubble.fill")
                    .foregroundColor(.mint)
                    .font(.title3)
                Text("AI Enhanced Description")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                Spacer()
                Button("Use This") {
                    content = enhancedDescription
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.mint.opacity(0.2))
                .foregroundColor(.mint)
                .clipShape(Capsule())
            }
            Text(enhancedDescription)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(3)
                .padding(8)
                .background(Color.mint.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 6))
        }
        .padding(12)
        .background(Color.mint.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 10))
    }
    private var aiTagsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            tagsCardHeader
            tagsGrid
        }
        .padding(12)
        .background(Color.cyan.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 10))
    }
    private var tagsCardHeader: some View {
        HStack {
            Image(systemName: "tag.fill")
                .foregroundColor(.cyan)
                .font(.title3)
            Text("Smart Tags")
                .font(.subheadline)
                .fontWeight(.semibold)
            Spacer()
            Button("Add All") {
                // Logic to add tags to memory
            }
            .font(.caption)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.cyan.opacity(0.2))
            .foregroundColor(.cyan)
            .clipShape(Capsule())
        }
    }
    private var tagsGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
            ForEach(Array(aiGeneratedTags.prefix(6)), id: \.self) { tag in
                Text(tag)
                    .font(.caption2)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.cyan.opacity(0.2))
                    .foregroundColor(.cyan)
                    .clipShape(Capsule())
            }
        }
    }
    private var manualInputSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("✏️ Memory Details")
                .font(.headline)
                .fontWeight(.bold)
            // Pet Info Display
            HStack {
                Circle()
                    .fill(Color.blue.opacity(0.3))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(String(pet.name.prefix(2)).uppercased())
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.blue)
                    )
                VStack(alignment: .leading, spacing: 2) {
                    Text("Memory for \(pet.name)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Text("\(pet.species.capitalized) • \(pet.breed ?? "Mixed Breed")")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
            .padding(12)
            .background(Color.blue.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: 10))
            // Title Input
            VStack(alignment: .leading, spacing: 8) {
                Text("Memory Title")
                    .font(.subheadline)
                    .fontWeight(.medium)
                TextField("What's this memory about?", text: $title)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            // Description Input
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Description")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                    if appleIntelligence.isWritingToolsAvailable {
                        Button("✨ Enhance") {
                            enhanceDescriptionWithAI()
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
                TextEditor(text: $content)
                    .frame(height: 80)
                    .padding(8)
                    .background(Color(.systemGray6))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            }
        }
        .padding(20)
        .background(glassmorphicBackground)
    }
    private var uploadButton: some View {
        VStack(spacing: 12) {
            if isUploading {
                ProgressView("Uploading Memory...", value: uploadProgress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle())
                    .padding(.horizontal, 20)
            }
            Button(action: uploadMemory) {
                HStack {
                    if isUploading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: "cloud.upload.fill")
                            .font(.title3)
                    }
                    Text(isUploading ? "Uploading..." : "Save Memory")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    canUpload ? Color.blue : Color.gray.opacity(0.5)
                )
                .foregroundColor(.white)
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(!canUpload || isUploading)
        }
        .padding(.horizontal, 20)
    }
    private var canUpload: Bool {
        !title.isEmpty && (uploadedImage != nil || !content.isEmpty)
    }
    private var glassmorphicBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.regularMaterial)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: colorScheme == .dark ? 
                                [Color.white.opacity(0.12), Color.white.opacity(0.04)] :
                                [Color.white.opacity(0.8), Color.white.opacity(0.4)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            colors: [Color.white.opacity(0.3), Color.clear],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    private func handleImageSelection(_ items: [PhotosPickerItem]) {
        guard let item = items.first else { return }
        Task {
            if let data = try? await item.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {
                await MainActor.run {
                    uploadedImage = image
                    isAnalyzingImage = true
                    uploadProgress = 0.1
                }
                // Trigger Apple Intelligence + Visual Intelligence Analysis
                await performAppleIntelligenceAnalysis(image: image, imageData: data)
            }
        }
    }
    @MainActor
    private func performAppleIntelligenceAnalysis(image: UIImage, imageData: Data) async {
        guard visualIntelligence.isAvailable else {
            isAnalyzingImage = false
            return
        }
        do {
            let descriptor = SemanticContentDescriptor(
                imageData: imageData,
                metadata: ["contentType": "pet_photo", "source": "memory_upload"]
            )
            // Progress updates
            uploadProgress = 0.2
            // Breed Identification
            uploadProgress = 0.4
            breedIdentificationResult = try await visualIntelligence.identifyPetBreed(from: descriptor)
            // Health Analysis
            uploadProgress = 0.6
            healthAnalysisResult = try await visualIntelligence.analyzeHealthIndicators(from: descriptor)
            // Product Recommendations
            uploadProgress = 0.8
            productRecommendations = try await visualIntelligence.identifyPetProducts(from: descriptor)
            // Generate Enhanced Description using Apple Intelligence
            if appleIntelligence.isWritingToolsAvailable {
                await generateEnhancedDescription()
            }
            // Generate AI Tags
            generateAITags()
            uploadProgress = 1.0
            isAnalyzingImage = false
            showingAIAnalysis = true
        } catch {
            print("Apple Intelligence analysis failed: \(error)")
            isAnalyzingImage = false
            errorMessage = "AI analysis failed. You can still create the memory manually."
        }
    }
    private func generateEnhancedDescription() async {
        let baseDescription = content.isEmpty ? "A beautiful moment with my pet" : content
        let breedInfo = breedIdentificationResult?.primaryBreed ?? "pet"
        let healthInfo = healthAnalysisResult?.shouldConsultVet == false ? "looking healthy and happy" : "needing some attention"
        let contextualPrompt = "\(baseDescription). This \(breedInfo) is \(healthInfo)."
        appleIntelligence.enhanceMemoryDescription(contextualPrompt) { enhanced in
            DispatchQueue.main.async {
                self.enhancedDescription = enhanced
            }
        }
    }
    private func generateAITags() {
        var tags: [String] = []
        if let breed = breedIdentificationResult?.primaryBreed {
            tags.append(breed.lowercased())
        }
        if let healthResult = healthAnalysisResult {
            if healthResult.shouldConsultVet {
                tags.append("health-check")
            } else {
                tags.append("healthy")
            }
        }
        if let products = productRecommendations, !products.identifiedProducts.isEmpty {
            tags.append("products")
            tags.append("shopping")
        }
        // Add contextual tags based on image analysis
        tags.append(contentsOf: ["memory", "photo", "precious-moment"])
        aiGeneratedTags = Array(Set(tags)).prefix(8).map { $0 }
    }
    private func enhanceDescriptionWithAI() {
        guard appleIntelligence.isWritingToolsAvailable else { return }
        appleIntelligence.enhanceMemoryDescription(content) { enhanced in
            DispatchQueue.main.async {
                self.content = enhanced
            }
        }
    }
    private func resetAIResults() {
        breedIdentificationResult = nil
        healthAnalysisResult = nil
        productRecommendations = nil
        enhancedDescription = ""
        aiGeneratedTags = []
        showingAIAnalysis = false
    }
    private func uploadMemory() {
        isUploading = true
        uploadProgress = 0.0
        Task {
            do {
                // Create memory with AI enhancements
                let memory = Memory(
                    title: title,
                    content: enhancedDescription.isEmpty ? content : enhancedDescription,
                    type: uploadedImage != nil ? .photo : .text,
                    mediaURL: audioCoordinator.recordingURL?.absoluteString,
                    thumbnailURL: nil,
                    duration: selectedMemoryType == .audio ? audioCoordinator.recordingDuration : nil,
                    milestone: breedIdentificationResult?.primaryBreed,
                    sentiment: "positive",
                    tags: aiGeneratedTags.isEmpty ? await generateAITags(for: content) : aiGeneratedTags,
                    isPublic: false,
                    isFavorite: false
                )
                // Add memory ID to pet's memory list
                pet.memoryIDs.append(memory.id.uuidString)
                // Save to context
                modelContext
                uploadProgress = 0.5
                try modelContext.save()
                uploadProgress = 1.0
                await MainActor.run {
                    isUploading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isUploading = false
                    errorMessage = "Failed to save memory: \(error.localizedDescription)"
                }
            }
        }
    }
    private func generateAITags(for content: String) async -> [String] {
        do {
            let prompt = """
            Analyze this pet memory content and generate 3-5 relevant tags that would help categorize and search for this memory later. 
            Only return the tags as a comma-separated list, nothing else.
            Content: \(content)
            """
            // Create a basic AI agent for tag generation
            let tagAgent = AIAgent(
                name: "Tag Generator",
                description: "Specialized in generating relevant tags for pet memories",
                specialties: ["tagging", "categorization"],
                isPremium: false,
                iconName: "tag.fill",
                gradientColors: ["blue", "purple"],
                personality: AIPersonality(
                    temperature: 0.4,
                    tone: "concise",
                    responseStyle: "detailed",
                    expertise: "intermediate"
                )
            )
                                    let response = try await AppleIntelligenceService.shared.sendMessage(
                to: tagAgent,
                message: prompt,
                pet: pet
            )
            let tags = response.components(separatedBy: ",")
                .map { $0.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines) }
                .filter { !$0.isEmpty }
                .prefix(5)
            return Array(tags)
        } catch {
            print("❌ Error generating AI tags: \(error)")
            // Fallback to basic tags based on memory type
            switch selectedMemoryType {
            case .photo:
                return ["photo", "memory", pet.name.lowercased()]
            case .video:
                return ["video", "memory", pet.name.lowercased()]
            case .text:
                return ["note", "memory", pet.name.lowercased()]
            case .audio:
                return ["audio", "recording", pet.name.lowercased()]
            case .milestone:
                return ["milestone", "special", pet.name.lowercased()]
            }
        }
    }
}
#Preview {
    if #available(iOS 18.0, *) {
        MemoryUploadView(pet: Pet(
            name: "Buddy",
            species: "Dog",
            breed: "Golden Retriever",
            birthDate: Date(),
            adoptionDate: Date(),
            weight: 65.0,
            activityLevel: "moderate",
            personalityTraits: ["friendly"],
            healthConditions: [],
            medications: [],
            vaccinations: [],
            healthAlerts: [],
            aiRecommendations: [],
            profileImageURL: nil,
            lastCheckupDate: nil
        ))
        .environmentObject(ProductionMemoryService.shared)
    } else {
        Text("Requires iOS 18.0+")
            .foregroundColor(.gray)
    }
}
