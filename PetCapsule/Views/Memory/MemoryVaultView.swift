//
//  MemoryVaultView.swift
//  PetCapsule
//
//  Clean, Unified Memories & Vault Interface
//  ✅ Consistent design, integrated walk memories filter
//

import SwiftUI

@available(iOS 18.0, *)
struct MemoryVaultView: View {
    @EnvironmentObject var realDataService: RealDataService
    @EnvironmentObject var subscriptionService: SubscriptionService
    @StateObject private var productionMemoryService = ProductionMemoryService.shared
    @StateObject private var advancedMemoryService = AdvancedMemoryService.shared
    @StateObject private var vaultService = SecureVaultService.shared
    @StateObject private var plannerService = PetPlannerService.shared

    @State private var showAddMemory = false
    @State private var showCreateVault = false
    @State private var searchText = ""
    @State private var selectedFilter: MemoryFilter = .all
    @State private var animateCards = false
    @State private var showPremiumUpgrade = false
    @State private var showErrorAlert = false
    @State private var selectedMemory: Memory?
    @State private var showMemoryDetail = false

    enum MemoryFilter: String, CaseIterable {
        case all = "All"
        case photos = "Photos"
        case videos = "Videos"
        case walks = "Walks"
        case milestones = "Milestones"
        case vaults = "Vaults"
        case favorites = "Favorites"
        
        var icon: String {
            switch self {
            case .all: return "square.grid.2x2"
            case .photos: return "photo"
            case .videos: return "video"
            case .walks: return "figure.walk"
            case .milestones: return "star"
            case .vaults: return "folder"
            case .favorites: return "heart"
            }
        }
    }

    // MARK: - Computed Properties

    private var filteredContent: [MemoryItem] {
        var items: [MemoryItem] = []
        
        // Get regular memories
        let memories = realDataService.memories.map { MemoryItem.memory($0) }
        
        // Get walk memories
        let walkMemories = plannerService.walkMemories.map { MemoryItem.walkMemory($0) }
        
        // Get vaults
        let vaults = vaultService.vaults.map { MemoryItem.vault($0) }
        
        // Apply filter
        switch selectedFilter {
        case .all:
            items = memories + walkMemories + vaults
        case .photos:
            items = memories.filter { 
                if case .memory(let memory) = $0 { return memory.type == .photo }
                return false
            }
        case .videos:
            items = memories.filter { 
                if case .memory(let memory) = $0 { return memory.type == .video }
                return false
            }
        case .walks:
            items = walkMemories
        case .milestones:
            items = memories.filter { 
                if case .memory(let memory) = $0 { return memory.type == .milestone || memory.milestone != nil }
                return false
            }
        case .vaults:
            items = vaults
        case .favorites:
            items = memories.filter { 
                if case .memory(let memory) = $0 { return memory.isFavorite }
                return false
            } + walkMemories.filter {
                if case .walkMemory(let walkMemory) = $0 { return walkMemory.isFavorite }
                return false
            }
        }

        // Apply search
        if !searchText.isEmpty {
            items = items.filter { item in
                switch item {
                case .memory(let memory):
                    return memory.title.localizedCaseInsensitiveContains(searchText) ||
                           memory.content.localizedCaseInsensitiveContains(searchText) ||
                           memory.tags.contains { $0.localizedCaseInsensitiveContains(searchText) }
                case .walkMemory(let walkMemory):
                    return walkMemory.locationName?.localizedCaseInsensitiveContains(searchText) ?? false
                case .vault(let vault):
                    return vault.name.localizedCaseInsensitiveContains(searchText) ||
                           vault.vaultDescription.localizedCaseInsensitiveContains(searchText)
                }
            }
        }

        return items.sorted { item1, item2 in
            let date1 = item1.createdDate
            let date2 = item2.createdDate
            return date1 > date2
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // CLEAN HEADER
                cleanHeader
                
                // FILTER SELECTOR
                filterSelector
                
                // UNIFIED CONTENT GRID
                contentGrid
            }
            .navigationBarHidden(true)
            .background(Color(.systemGroupedBackground))
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2)) {
                    animateCards = true
                }
                
                productionMemoryService.setRealDataService(realDataService)
                
                if realDataService.memories.isEmpty && !realDataService.isLoading {
                    Task {
                        print("Refreshing data...")
                    }
                }
            }
        }
        .sheet(isPresented: $showAddMemory) {
            AddMemoryView()
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showCreateVault) {
            CreateVaultView()
                .environmentObject(SecureVaultService.shared)
        }
        .sheet(isPresented: $showPremiumUpgrade) {
            PremiumUpgradeView(feature: "Advanced Memory Features")
                .environmentObject(subscriptionService)
        }
        .sheet(isPresented: $showMemoryDetail) {
            if let selectedMemory = selectedMemory {
                EnhancedMemoryDetailView(memory: selectedMemory)
                    .environmentObject(realDataService)
            }
        }
        .alert("Error", isPresented: $showErrorAlert) {
            Button("OK") { }
        } message: {
            Text(productionMemoryService.error?.localizedDescription ?? "An unknown error occurred")
        }
    }
    
    // MARK: - Clean Header
    
    private var cleanHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Forever Memories")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Every precious moment, safely treasured")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                }
                
                Spacer()
                
                // Action buttons
                HStack(spacing: 12) {
                    Button(action: { showCreateVault = true }) {
                        Image(systemName: "folder.badge.plus")
                            .font(.title2)
                            .foregroundStyle(.secondary)
                    }
                    
                    Button(action: { showAddMemory = true }) {
                        Circle()
                            .fill(.blue.opacity(0.1))
                            .frame(width: 40, height: 40)
                            .overlay(
                                Image(systemName: "plus")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundStyle(.blue)
                            )
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    // MARK: - Filter Selector
    
    private var filterSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(MemoryFilter.allCases, id: \.self) { filter in
                    Button(action: {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            selectedFilter = filter
                        }
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: filter.icon)
                                .font(.caption)
                            
                            Text(filter.rawValue)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundStyle(selectedFilter == filter ? .white : .primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            Capsule()
                                .fill(selectedFilter == filter ? .blue : Color(.systemGray5))
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - Content Grid
    
    private var contentGrid: some View {
        ScrollView {
            if filteredContent.isEmpty {
                emptyStateView
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: 16),
                    GridItem(.flexible(), spacing: 16)
                ], spacing: 20) {
                    ForEach(Array(filteredContent.enumerated()), id: \.element.id) { index, item in
                        MemoryItemCard(item: item) {
                            handleItemTap(item)
                        }
                        .scaleEffect(animateCards ? 1.0 : 0.8)
                        .opacity(animateCards ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.05), value: animateCards)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
        }
    }
    
    // MARK: - Empty State
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: selectedFilter.icon)
                .font(.system(size: 64, weight: .light))
                .foregroundStyle(.secondary)
            
            VStack(spacing: 12) {
                Text("No \(selectedFilter.rawValue.lowercased()) yet")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text("Start creating beautiful memories with your pets")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: {
                if selectedFilter == .vaults {
                    showCreateVault = true
                } else {
                    showAddMemory = true
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: selectedFilter == .vaults ? "folder.badge.plus" : "plus.circle.fill")
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text(selectedFilter == .vaults ? "Create First Vault" : "Add First Memory")
                        .font(.system(size: 16, weight: .semibold))
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(.blue)
                .foregroundColor(.white)
                .clipShape(RoundedRectangle(cornerRadius: 25))
            }
        }
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 20, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
        .padding(.horizontal, 20)
        .padding(.top, 40)
    }
    
    // MARK: - Helper Functions
    
    private func handleItemTap(_ item: MemoryItem) {
        switch item {
        case .memory(let memory):
            selectedMemory = memory
            showMemoryDetail = true
        case .walkMemory(_):
            // Handle walk memory tap
            break
        case .vault(_):
            // Handle vault tap
            break
        }
    }
}

// MARK: - Memory Item Enum

enum MemoryItem: Identifiable {
    case memory(Memory)
    case walkMemory(WalkMemory)
    case vault(Vault)

    var id: String {
        switch self {
        case .memory(let memory): return memory.id.uuidString
        case .walkMemory(let walkMemory): return walkMemory.id
        case .vault(let vault): return vault.id.uuidString
        }
    }
    
    var createdDate: Date {
        switch self {
        case .memory(let memory): return memory.createdAt
        case .walkMemory(let walkMemory): return walkMemory.createdAt
        case .vault(let vault): return vault.createdAt
        }
    }
}

// MARK: - Memory Item Card

struct MemoryItemCard: View {
    let item: MemoryItem
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                // Content preview
                contentPreview
                
                // Item info
                itemInfo
            }
            .frame(maxWidth: .infinity)
            .frame(height: 180) // Fixed height for consistency
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(ScaleButtonStyle())
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        switch item {
        case .memory(let memory):
            Rectangle()
                .fill(.blue.opacity(0.1))
                .frame(height: 80)
                .overlay(
                    Image(systemName: memory.type == .photo ? "photo" : memory.type == .video ? "video" : "star")
                        .font(.title2)
                        .foregroundStyle(.blue)
                )
                .cornerRadius(12)
                
        case .walkMemory(_):
            Rectangle()
                .fill(.green.opacity(0.1))
                .frame(height: 80)
                .overlay(
                    Image(systemName: "figure.walk")
                        .font(.title2)
                        .foregroundStyle(.green)
                )
                .cornerRadius(12)
                
        case .vault(_):
            Rectangle()
                .fill(.purple.opacity(0.1))
                .frame(height: 80)
                .overlay(
                    Image(systemName: "folder")
                        .font(.title2)
                        .foregroundStyle(.purple)
                )
                .cornerRadius(12)
        }
    }
    
    @ViewBuilder
    private var itemInfo: some View {
        VStack(spacing: 8) {
            switch item {
            case .memory(let memory):
                VStack(spacing: 4) {
                    Text(memory.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                        .lineLimit(1)
                    
                    Text(memory.createdAt.formatted(date: .abbreviated, time: .omitted))
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
            case .walkMemory(let walkMemory):
                VStack(spacing: 4) {
                    Text(walkMemory.locationName ?? "Walk Memory")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                        .lineLimit(1)
                    
                    HStack(spacing: 8) {
                        if let duration = walkMemory.durationMinutes {
                            Text("\(duration) min")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                        
                        if let distance = walkMemory.distance {
                            Text(String(format: "%.1f km", distance / 1000))
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                    }
                }
                
            case .vault(let vault):
                VStack(spacing: 4) {
                    Text(vault.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                        .lineLimit(1)
                    
                    Text("\(vault.memoryCount) memories")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
        }
        .frame(height: 60) // Fixed height for consistency
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        MemoryVaultView()
            .environmentObject(RealDataService())
            .environmentObject(SubscriptionService())
    } else {
        Text("iOS 18+ Required")
    }
}
