//
//  PremiumUpgradeView.swift
//  PetCapsule
//
//  Premium Subscription Upgrade Flow
//  Conversion Driver: Free to Premium
//

import SwiftUI

struct PremiumUpgradeView: View {
    let feature: String
    @EnvironmentObject var subscriptionService: SubscriptionService
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedPlan: SubscriptionPlan?
    @State private var showingPurchase = false
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // Header
                    headerSection
                    
                    // Feature Highlight
                    featureHighlightSection
                    
                    // Subscription Plans
                    subscriptionPlansSection
                    
                    // Benefits Comparison
                    benefitsSection
                    
                    // Social Proof
                    socialProofSection
                    
                    // CTA Button
                    ctaSection
                }
                .padding()
            }
            .navigationTitle("Upgrade to Premium")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                selectedPlan = subscriptionService.availablePlans.first { $0.id == "premium" }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "crown.fill")
                .font(.system(size: 60))
                .foregroundColor(.orange)
            
            Text("Unlock Premium Features")
                .font(.largeTitle)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            Text("Transform your pet memories with AI-powered features")
                .font(.title3)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var featureHighlightSection: some View {
        VStack(spacing: 16) {
            Text("You're trying to access:")
                .font(.headline)
                .foregroundColor(.secondary)
            
            HStack {
                Image(systemName: "brain.head.profile.fill")
                    .font(.title2)
                    .foregroundColor(.purple)
                
                Text(feature)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.purple.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.purple, lineWidth: 2)
                    )
            )
        }
    }
    
    private var subscriptionPlansSection: some View {
        VStack(spacing: 16) {
            Text("Choose Your Plan")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                ForEach(subscriptionService.availablePlans.filter { $0.id != "free" }) { plan in
                    planCard(plan)
                }
            }
        }
    }
    
    private func planCard(_ plan: SubscriptionPlan) -> some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(plan.name)
                            .font(.title3)
                            .fontWeight(.bold)
                        
                        if plan.popularBadge {
                            Text("POPULAR")
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.orange)
                                )
                        }
                    }
                    
                    Text(plan.formattedPrice)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                }
                
                Spacer()
                
                Button(action: { selectedPlan = plan }) {
                    Image(systemName: selectedPlan?.id == plan.id ? "checkmark.circle.fill" : "circle")
                        .font(.title2)
                        .foregroundColor(selectedPlan?.id == plan.id ? .purple : .secondary)
                }
            }
            
            // Key Features
            VStack(alignment: .leading, spacing: 8) {
                ForEach(plan.features.prefix(4), id: \.self) { feature in
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                        
                        Text(feature)
                            .font(.subheadline)
                        
                        Spacer()
                    }
                }
                
                if plan.features.count > 4 {
                    Text("+ \(plan.features.count - 4) more features")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(selectedPlan?.id == plan.id ? Color.purple.opacity(0.1) : Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(selectedPlan?.id == plan.id ? Color.purple : Color.clear, lineWidth: 2)
                )
        )
        .onTapGesture {
            selectedPlan = plan
        }
    }
    
    private var benefitsSection: some View {
        VStack(spacing: 16) {
            Text("What You Get")
                .font(.title2)
                .fontWeight(.bold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                benefitCard(icon: "icloud.fill", title: "Unlimited Storage", description: "Never worry about running out of space")
                benefitCard(icon: "brain.head.profile", title: "AI Curation", description: "Smart collections and insights")
                benefitCard(icon: "video.badge.waveform", title: "HD Montages", description: "Professional quality videos")
                benefitCard(icon: "book.fill", title: "Photo Books", description: "Custom printed memories")
                benefitCard(icon: "person.2.fill", title: "Family Sharing", description: "Share with loved ones")
                benefitCard(icon: "crown.fill", title: "Premium Support", description: "Priority customer care")
            }
        }
    }
    
    private func benefitCard(icon: String, title: String, description: String) -> some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.purple)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.purple.opacity(0.1))
                )
            
            VStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
    
    private var socialProofSection: some View {
        VStack(spacing: 16) {
            Text("Join 50,000+ Happy Pet Parents")
                .font(.headline)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            VStack(spacing: 12) {
                testimonialCard(
                    name: "Sarah M.",
                    text: "The AI montages bring tears to my eyes every time. Worth every penny!",
                    rating: 5
                )
                
                testimonialCard(
                    name: "Mike R.",
                    text: "Family sharing lets grandparents see our puppy's milestones instantly.",
                    rating: 5
                )
            }
        }
    }
    
    private func testimonialCard(name: String, text: String, rating: Int) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                ForEach(0..<rating, id: \.self) { _ in
                    Image(systemName: "star.fill")
                        .font(.caption)
                        .foregroundColor(.yellow)
                }
                
                Spacer()
                
                Text(name)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
            }
            
            Text(text)
                .font(.subheadline)
                .italic()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    private var ctaSection: some View {
        VStack(spacing: 16) {
            if let plan = selectedPlan {
                Button(action: { purchasePlan(plan) }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Text("Start \(plan.name) - \(plan.formattedPrice)")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
                .disabled(isLoading)
                
                VStack(spacing: 8) {
                    Text("✓ 7-day free trial")
                        .font(.subheadline)
                        .foregroundColor(.green)
                    
                    Text("Cancel anytime • No commitment")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Revenue metrics for business tracking
            if subscriptionService.subscriptionStatus == .pawStarter {
                Text("Join the path to $2M/month revenue")
                    .font(.caption2)
                    .foregroundColor(.clear) // Hidden but tracked
            }
        }
    }
    
    private func purchasePlan(_ plan: SubscriptionPlan) {
        isLoading = true
        
        Task {
            do {
                try await subscriptionService.purchase(plan)
                
                await MainActor.run {
                    isLoading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    // Handle error
                }
            }
        }
    }
}

// MARK: - Preview

struct PremiumUpgradeView_Previews: PreviewProvider {
    static var previews: some View {
        PremiumUpgradeView(feature: "AI Memory Curation")
            .environmentObject(SubscriptionService.shared)
    }
}
