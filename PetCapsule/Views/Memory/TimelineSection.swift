import SwiftUI

@available(iOS 18.0, *)
struct TimelineSection: View {
    let title: String
    let memories: [EnhancedMemory]
    let onMemoryTap: (EnhancedMemory) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Section header
            HStack {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("\(memories.count) memories")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
            .padding(.top, 24)
            .padding(.bottom, 16)
            
            // Timeline entries
            ForEach(Array(memories.enumerated()), id: \.element.id) { index, memory in
                TimelineEntry(
                    memory: memory,
                    isFirst: index == 0,
                    isLast: index == memories.count - 1,
                    onTap: { onMemoryTap(memory) }
                )
            }
        }
    }
}

@available(iOS 18.0, *)
struct TimelineEntry: View {
    let memory: EnhancedMemory
    let isFirst: Bool
    let isLast: Bool
    let onTap: () -> Void
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // Timeline line and indicator
            VStack(spacing: 0) {
                // Top line
                Rectangle()
                    .fill(isFirst ? Color.clear : Color(.systemGray4))
                    .frame(width: 2, height: 16)
                
                // Timeline dot
                Circle()
                    .fill(memory.eventType.color)
                    .frame(width: 12, height: 12)
                    .overlay(
                        Circle()
                            .stroke(Color(.systemBackground), lineWidth: 2)
                    )
                
                // Bottom line
                Rectangle()
                    .fill(isLast ? Color.clear : Color(.systemGray4))
                    .frame(width: 2, height: 16)
            }
            
            // Memory content
            VStack(alignment: .leading, spacing: 8) {
                // Memory card
                TimelineMemoryCard(memory: memory, onTap: onTap)
                
                // Spacer for last item
                if isLast {
                    Spacer()
                        .frame(height: 24)
                }
            }
        }
        .padding(.horizontal)
    }
}

@available(iOS 18.0, *)
struct TimelineMemoryCard: View {
    let memory: EnhancedMemory
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Thumbnail
                AsyncImage(url: URL(string: memory.thumbnailURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 60, height: 60)
                        .clipped()
                        .cornerRadius(8)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.systemGray5))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.title2)
                                .foregroundColor(.secondary)
                        )
                }
                .overlay(
                    // Video indicator
                    memory.hasVideo ? 
                    Image(systemName: "play.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                        .background(Color.black.opacity(0.3))
                        .clipShape(Circle())
                    : nil
                )
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    // Title and event type
                    HStack {
                        Text(memory.title)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        // Event type badge
                        HStack {
                            Image(systemName: memory.eventType.icon)
                                .font(.caption2)
                            Text(memory.eventType.displayName)
                                .font(.caption2)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(memory.eventType.color)
                        .cornerRadius(6)
                    }
                    
                    // Content preview
                    Text(memory.content)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    // Date and indicators
                    HStack {
                        // Date
                        Text(memory.formattedDate)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        // Indicators
                        HStack(spacing: 4) {
                            // Mood emoji
                            Text(memory.mood.emoji)
                                .font(.caption)
                            
                            // Special indicators
                            if memory.isSecureVault {
                                Image(systemName: "lock.shield.fill")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                            
                            if memory.isMemorial {
                                Image(systemName: "heart.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.purple)
                            }
                            
                            if memory.tags.contains("favorite") {
                                Image(systemName: "heart.fill")
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                }
                
                Spacer()
            }
            .padding(12)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Event Type Section
@available(iOS 18.0, *)
struct EventTypeSection: View {
    let eventType: MemoryEventType
    let memories: [EnhancedMemory]
    let onMemoryTap: (EnhancedMemory) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Section header
            HStack {
                HStack {
                    Image(systemName: eventType.icon)
                        .font(.title2)
                        .foregroundColor(eventType.color)
                    
                    Text(eventType.displayName)
                        .font(.title2)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                Text("\(memories.count)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(eventType.color)
                    .cornerRadius(8)
            }
            
            // Memories grid
            let columns = [
                GridItem(.adaptive(minimum: 100), spacing: 8)
            ]
            
            LazyVGrid(columns: columns, spacing: 8) {
                ForEach(memories.prefix(6)) { memory in
                    EventMemoryThumbnail(memory: memory, onTap: { onMemoryTap(memory) })
                }
                
                // Show more button if there are more memories
                if memories.count > 6 {
                    Button(action: {
                        // TODO: Show all memories of this type
                    }) {
                        VStack {
                            Image(systemName: "ellipsis")
                                .font(.title2)
                                .foregroundColor(.secondary)
                            
                            Text("+\(memories.count - 6)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .frame(height: 80)
                        .frame(maxWidth: .infinity)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

@available(iOS 18.0, *)
struct EventMemoryThumbnail: View {
    let memory: EnhancedMemory
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            AsyncImage(url: URL(string: memory.thumbnailURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 80)
                    .clipped()
                    .cornerRadius(8)
            } placeholder: {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray5))
                    .frame(height: 80)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title2)
                            .foregroundColor(.secondary)
                    )
            }
            .overlay(
                // Video indicator
                memory.hasVideo ? 
                VStack {
                    Spacer()
                    HStack {
                        Image(systemName: "play.circle.fill")
                            .font(.caption)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                        
                        Spacer()
                    }
                    .padding(4)
                }
                : nil
            )
            .overlay(
                // Mood emoji
                VStack {
                    HStack {
                        Spacer()
                        Text(memory.mood.emoji)
                            .font(.caption)
                            .padding(4)
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                    }
                    .padding(4)
                    
                    Spacer()
                }
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        let sampleMemories = [
            EnhancedMemory(
                title: "Luna's First Swimming Adventure",
                content: "Today Luna discovered her love for water!",
                mood: .excited,
                eventType: .adventure
            ),
            EnhancedMemory(
                title: "Shadow's Adoption Day",
                content: "Three years ago today, Shadow came into our lives.",
                mood: .nostalgic,
                eventType: .adoption
            )
        ]
        
        ScrollView {
            VStack {
                TimelineSection(
                    title: "December 2024",
                    memories: sampleMemories,
                    onMemoryTap: { _ in }
                )
                
                EventTypeSection(
                    eventType: .adventure,
                    memories: sampleMemories,
                    onMemoryTap: { _ in }
                )
            }
        }
    } else {
        Text("iOS 18.0 required")
    }
} 