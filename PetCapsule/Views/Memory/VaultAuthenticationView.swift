import SwiftUI
import LocalAuthentication

@available(iOS 17.0, *)
struct VaultAuthenticationView: View {
    @ObservedObject var memoryService: EnhancedMemoryService
    let onDismiss: () -> Void
    
    @State private var isAuthenticating = false
    @State private var authError: String?
    @State private var showError = false
    @State private var biometricType: LABiometryType = .none
    @State private var canUseBiometrics = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(spacing: 16) {
                    Image(systemName: "lock.shield.fill")
                        .font(.system(size: 80))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.purple, .blue],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    Text("Secure Vault")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Your most private pet memories are protected with biometric security")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding(.top, 40)
                .padding(.bottom, 60)
                
                // Authentication Status
                VStack(spacing: 24) {
                    if memoryService.isVaultUnlocked {
                        // Vault is unlocked
                        VStack(spacing: 16) {
                            Image(systemName: "lock.open.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.green)
                            
                            Text("Vault Unlocked")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.green)
                            
                            Text("You can now access your secure memories")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .padding(20)
                        .background(Color.green.opacity(0.1))
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    } else {
                        // Vault is locked
                        VStack(spacing: 16) {
                            Image(systemName: "lock.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.orange)
                            
                            Text("Vault Locked")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)
                            
                            Text("Authenticate to access your secure memories")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .padding(20)
                        .background(Color.orange.opacity(0.1))
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Authentication Button
                VStack(spacing: 16) {
                    if !memoryService.isVaultUnlocked {
                        Button(action: authenticateVault) {
                            HStack {
                                if isAuthenticating {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: biometricIcon)
                                        .font(.title2)
                                }
                                
                                Text(isAuthenticating ? "Authenticating..." : biometricButtonText)
                                    .font(.headline)
                                    .fontWeight(.semibold)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                LinearGradient(
                                    colors: [.purple, .blue],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                        }
                        .disabled(isAuthenticating || !canUseBiometrics)
                        .opacity(canUseBiometrics ? 1 : 0.6)
                    } else {
                        Button(action: lockVault) {
                            HStack {
                                Image(systemName: "lock")
                                    .font(.title2)
                                
                                Text("Lock Vault")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.orange)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                        }
                    }
                    
                    // Error message
                    if let error = authError {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.red)
                            
                            Text(error)
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                        .padding(.horizontal)
                    }
                    
                    // Biometric availability info
                    if !canUseBiometrics {
                        VStack(spacing: 8) {
                            HStack {
                                Image(systemName: "info.circle")
                                    .foregroundColor(.blue)
                                
                                Text("Biometric authentication is not available")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Text("Please set up Face ID or Touch ID in Settings")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal)
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 40)
            }
            .navigationTitle("Vault Access")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        onDismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            checkBiometricAvailability()
        }
        .alert("Authentication Error", isPresented: $showError) {
            Button("OK") {
                authError = nil
            }
        } message: {
            if let error = authError {
                Text(error)
            }
        }
    }
    
    private var biometricIcon: String {
        switch biometricType {
        case .faceID:
            return "faceid"
        case .touchID:
            return "touchid"
        default:
            return "key"
        }
    }
    
    private var biometricButtonText: String {
        switch biometricType {
        case .faceID:
            return "Unlock with Face ID"
        case .touchID:
            return "Unlock with Touch ID"
        default:
            return "Unlock with Passcode"
        }
    }
    
    private func checkBiometricAvailability() {
        let context = LAContext()
        var error: NSError?
        
        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            canUseBiometrics = true
            biometricType = context.biometryType
        } else {
            canUseBiometrics = false
            if let error = error {
                authError = error.localizedDescription
            }
        }
    }
    
    private func authenticateVault() {
        guard !isAuthenticating else { return }
        
        isAuthenticating = true
        authError = nil
        
        Task {
            let success = await memoryService.unlockVault()
            
            await MainActor.run {
                isAuthenticating = false
                
                if success {
                    // Vault unlocked successfully
                    print("✅ Vault unlocked successfully")
                } else {
                    // Authentication failed
                    authError = "Authentication failed. Please try again."
                    showError = true
                }
            }
        }
    }
    
    private func lockVault() {
        memoryService.isVaultUnlocked = false
        authError = nil
    }
}

// MARK: - Preview
#Preview {
    VaultAuthenticationView(memoryService: EnhancedMemoryService.shared) {
        // Dismiss action
    }
} 