import SwiftUI
import AVKit
import AVFoundation

@available(iOS 17.0, *)
struct VideoPlayerView: View {
    let memory: EnhancedMemory
    let onDismiss: () -> Void
    
    @State private var player: AVPlayer?
    @State private var isPlaying = false
    @State private var showControls = true
    @State private var currentTime: Double = 0
    @State private var duration: Double = 0
    @State private var isLoading = true
    @State private var error: String?
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            if let player = player {
                VideoPlayer(player: player)
                    .onAppear {
                        setupPlayer()
                    }
                    .onDisappear {
                        player.pause()
                    }
            } else if isLoading {
                loadingView
            } else if error != nil {
                errorView
            }
            
            // Custom controls overlay
            if showControls {
                VStack {
                    // Top controls
                    HStack {
                        Button(action: onDismiss) {
                            Image(systemName: "xmark")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding(12)
                                .background(Color.black.opacity(0.5))
                                .clipShape(Circle())
                        }
                        
                        Spacer()
                        
                        Text(memory.title)
                            .font(.headline)
                            .foregroundColor(.white)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Menu {
                            Button("Share Video") {
                                // TODO: Implement share functionality
                            }
                            Button("Save to Photos") {
                                // TODO: Implement save to photos
                            }
                            Button("Delete Video") {
                                // TODO: Implement delete functionality
                            }
                        } label: {
                            Image(systemName: "ellipsis")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding(12)
                                .background(Color.black.opacity(0.5))
                                .clipShape(Circle())
                        }
                    }
                    .padding()
                    
                    Spacer()
                    
                    // Bottom controls
                    VStack(spacing: 16) {
                        // Progress bar
                        VStack(spacing: 8) {
                            HStack {
                                Text(formatTime(currentTime))
                                    .font(.caption)
                                    .foregroundColor(.white)
                                
                                Spacer()
                                
                                Text(formatTime(duration))
                                    .font(.caption)
                                    .foregroundColor(.white)
                            }
                            
                            ProgressView(value: duration > 0 ? currentTime / duration : 0)
                                .progressViewStyle(LinearProgressViewStyle(tint: .white))
                                .scaleEffect(y: 2)
                        }
                        
                        // Play/Pause controls
                        HStack(spacing: 32) {
                            Button(action: skipBackward) {
                                Image(systemName: "gobackward.15")
                                    .font(.title)
                                    .foregroundColor(.white)
                            }
                            
                            Button(action: togglePlayPause) {
                                Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                                    .font(.title)
                                    .foregroundColor(.white)
                            }
                            
                            Button(action: skipForward) {
                                Image(systemName: "goforward.15")
                                    .font(.title)
                                    .foregroundColor(.white)
                            }
                        }
                    }
                    .padding()
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [.clear, .black.opacity(0.8)]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                }
                .opacity(showControls ? 1 : 0)
                .animation(.easeInOut(duration: 0.3), value: showControls)
            }
        }
        .onTapGesture {
            withAnimation {
                showControls.toggle()
            }
        }
        .onAppear {
            setupVideoPlayer()
        }
        .onDisappear {
            cleanupPlayer()
        }
    }
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(2)
            
            Text("Loading video...")
                .font(.headline)
                .foregroundColor(.white)
        }
    }
    
    private var errorView: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 60))
                .foregroundColor(.white)
            
            Text("Unable to play video")
                .font(.headline)
                .foregroundColor(.white)
            
            if let error = error {
                Text(error)
                    .font(.body)
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            Button("Dismiss") {
                onDismiss()
            }
            .font(.headline)
            .foregroundColor(.blue)
            .padding()
        }
    }
    
    private func setupVideoPlayer() {
        // For now, use a placeholder video URL
        // In a real app, you would get the video URL from the memory
        guard let firstMedia = memory.mediaItems.first,
              firstMedia.type == .video else {
            error = "No video found in this memory"
            isLoading = false
            return
        }
        
        // Create player with video URL
        // For demo purposes, we'll use a sample video URL
        setupSampleVideo()
    }
    
    private func setupSampleVideo() {
        // Using a sample video URL for demonstration
        guard let url = URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4") else {
            error = "Invalid video URL"
            isLoading = false
            return
        }
        
        player = AVPlayer(url: url)
        setupPlayer()
    }
    
    private func setupPlayer() {
        guard let player = player else { return }
        
        // Setup player observers
        _ = player.addPeriodicTimeObserver(
            forInterval: CMTime(seconds: 1, preferredTimescale: 1),
            queue: .main
        ) { time in
            currentTime = time.seconds
        }
        
        // Get duration
        Task {
            if let duration = try? await player.currentItem?.asset.load(.duration) {
                await MainActor.run {
                    self.duration = duration.seconds
                    self.isLoading = false
                }
            }
        }
        
        // Setup playback state observer
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: player.currentItem,
            queue: .main
        ) { _ in
            player.seek(to: .zero)
            self.isPlaying = false
        }
        
        // Auto-hide controls after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            if showControls {
                withAnimation {
                    showControls = false
                }
            }
        }
    }
    
    private func cleanupPlayer() {
        player?.pause()
        NotificationCenter.default.removeObserver(self)
        player = nil
    }
    
    private func togglePlayPause() {
        guard let player = player else { return }
        
        if isPlaying {
            player.pause()
            isPlaying = false
        } else {
            player.play()
            isPlaying = true
        }
    }
    
    private func skipBackward() {
        guard let player = player else { return }
        let currentTime = player.currentTime()
        let newTime = CMTimeSubtract(currentTime, CMTime(seconds: 15, preferredTimescale: 1))
        player.seek(to: newTime)
    }
    
    private func skipForward() {
        guard let player = player else { return }
        let currentTime = player.currentTime()
        let newTime = CMTimeAdd(currentTime, CMTime(seconds: 15, preferredTimescale: 1))
        player.seek(to: newTime)
    }
    
    private func formatTime(_ seconds: Double) -> String {
        let minutes = Int(seconds) / 60
        let seconds = Int(seconds) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - Preview
#Preview {
    VideoPlayerView(memory: EnhancedMemory(
        title: "Sample Video",
        content: "Sample video description",
        mediaItems: [
            MemoryMediaItem(
                url: "https://example.com/video.mp4",
                type: .video,
                duration: 120.0
            )
        ],
        thumbnailURL: "https://example.com/thumbnail.jpg",
        petIds: ["sample-pet-id"],
        mood: .happy,
        eventType: .general
    )) {
        // Dismiss action
    }
} 