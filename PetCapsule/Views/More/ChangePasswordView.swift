//
//  ChangePasswordView.swift
//  PetCapsule
//
//  Password change view with proper validation
//

import SwiftUI

struct ChangePasswordView: View {
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentPassword: String = ""
    @State private var newPassword: String = ""
    @State private var confirmPassword: String = ""
    @State private var isLoading: Bool = false
    @State private var showingAlert: Bool = false
    @State private var alertMessage: String = ""
    @State private var alertTitle: String = ""
    @State private var showCurrentPassword: Bool = false
    @State private var showNewPassword: Bool = false
    @State private var showConfirmPassword: Bool = false
    
    private var isValidForm: Bool {
        !newPassword.isEmpty &&
        !confirmPassword.isEmpty &&
        newPassword == confirmPassword &&
        isValidPassword(newPassword)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    passwordStrengthIndicator
                } header: {
                    Text("Password Requirements")
                }
                
                Section {
                    HStack {
                        if showNewPassword {
                            TextField("New Password", text: $newPassword)
                                .textContentType(.newPassword)
                        } else {
                            SecureField("New Password", text: $newPassword)
                                .textContentType(.newPassword)
                        }
                        
                        Button(action: {
                            showNewPassword.toggle()
                        }) {
                            Image(systemName: showNewPassword ? "eye.slash" : "eye")
                                .foregroundColor(.gray)
                        }
                    }
                    
                    HStack {
                        if showConfirmPassword {
                            TextField("Confirm New Password", text: $confirmPassword)
                                .textContentType(.newPassword)
                        } else {
                            SecureField("Confirm New Password", text: $confirmPassword)
                                .textContentType(.newPassword)
                        }
                        
                        Button(action: {
                            showConfirmPassword.toggle()
                        }) {
                            Image(systemName: showConfirmPassword ? "eye.slash" : "eye")
                                .foregroundColor(.gray)
                        }
                    }
                    
                    if !confirmPassword.isEmpty && newPassword != confirmPassword {
                        Text("Passwords do not match")
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                    
                } header: {
                    Text("New Password")
                } footer: {
                    Text("Your new password must be at least 8 characters long and include uppercase, lowercase, numbers, and special characters.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Section {
                    Button(action: {
                        Task {
                            await changePassword()
                        }
                    }) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            }
                            
                            Text("Change Password")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 4)
                    }
                    .disabled(!isValidForm || isLoading)
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    .tint(.purple)
                }
                
                Section {
                    securityTipsSection
                } header: {
                    Text("Security Tips")
                }
            }
            .navigationTitle("Change Password")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.purple)
                }
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("OK") {
                    if alertTitle == "Success" {
                        dismiss()
                    }
                }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - Password Strength Indicator
    
    private var passwordStrengthIndicator: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: isValidPassword(newPassword) ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isValidPassword(newPassword) ? .green : .gray)
                Text("Strong password")
            }
            .font(.caption)
            
            VStack(alignment: .leading, spacing: 4) {
                passwordRequirement(
                    text: "At least 8 characters",
                    isMet: newPassword.count >= 8
                )
                
                passwordRequirement(
                    text: "Contains uppercase letter",
                    isMet: newPassword.range(of: "[A-Z]", options: .regularExpression) != nil
                )
                
                passwordRequirement(
                    text: "Contains lowercase letter",
                    isMet: newPassword.range(of: "[a-z]", options: .regularExpression) != nil
                )
                
                passwordRequirement(
                    text: "Contains number",
                    isMet: newPassword.range(of: "[0-9]", options: .regularExpression) != nil
                )
                
                passwordRequirement(
                    text: "Contains special character",
                    isMet: newPassword.range(of: "[^A-Za-z0-9]", options: .regularExpression) != nil
                )
            }
            .font(.caption2)
        }
    }
    
    private func passwordRequirement(text: String, isMet: Bool) -> some View {
        HStack {
            Image(systemName: isMet ? "checkmark.circle.fill" : "circle")
                .foregroundColor(isMet ? .green : .gray)
                .font(.caption2)
            Text(text)
                .foregroundColor(isMet ? .primary : .secondary)
        }
    }
    
    // MARK: - Security Tips Section
    
    private var securityTipsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            securityTip(
                icon: "shield.fill",
                text: "Use a unique password that you don't use anywhere else"
            )
            
            securityTip(
                icon: "key.fill",
                text: "Consider using a password manager to generate and store strong passwords"
            )
            
            securityTip(
                icon: "exclamationmark.triangle.fill",
                text: "Never share your password with anyone"
            )
            
            securityTip(
                icon: "arrow.clockwise",
                text: "Change your password regularly for better security"
            )
        }
    }
    
    private func securityTip(icon: String, text: String) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.purple)
                .font(.caption)
                .frame(width: 16)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
    
    // MARK: - Helper Methods
    
    private func isValidPassword(_ password: String) -> Bool {
        // At least 8 characters
        guard password.count >= 8 else { return false }
        
        // Contains uppercase
        guard password.range(of: "[A-Z]", options: .regularExpression) != nil else { return false }
        
        // Contains lowercase
        guard password.range(of: "[a-z]", options: .regularExpression) != nil else { return false }
        
        // Contains number
        guard password.range(of: "[0-9]", options: .regularExpression) != nil else { return false }
        
        // Contains special character
        guard password.range(of: "[^A-Za-z0-9]", options: .regularExpression) != nil else { return false }
        
        return true
    }
    
    private func changePassword() async {
        guard isValidForm else { return }
        
        isLoading = true
        
        do {
            let success = try await authService.updateUserPassword(currentPassword: currentPassword, newPassword: newPassword)
            
            isLoading = false
            
            if success {
                alertTitle = "Success"
                alertMessage = "Your password has been changed successfully."
            } else {
                alertTitle = "Error"
                alertMessage = "Failed to change password. Please try again."
            }
        } catch {
            isLoading = false
            alertTitle = "Error"
            alertMessage = error.localizedDescription
        }
        
        showingAlert = true
    }
}

#Preview {
    ChangePasswordView()
        .environmentObject(AuthenticationService())
} 