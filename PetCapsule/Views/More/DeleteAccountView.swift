//
//  DeleteAccountView.swift
//  PetCapsule
//
//  Account deletion view with proper warnings and confirmations
//

import SwiftUI

struct DeleteAccountView: View {
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    @State private var confirmationText: String = ""
    @State private var isLoading: Bool = false
    @State private var showingFinalConfirmation: Bool = false
    @State private var showingAlert: Bool = false
    @State private var alertMessage: String = ""
    @State private var alertTitle: String = ""
    
    private let requiredConfirmationText = "DELETE"
    private var isConfirmationValid: Bool {
        confirmationText.uppercased() == requiredConfirmationText
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    warningSection
                } header: {
                    Text("⚠️ Warning")
                }
                
                Section {
                    dataLossSection
                } header: {
                    Text("What Will Be Deleted")
                }
                
                Section {
                    alternativesSection
                } header: {
                    Text("Alternatives to Consider")
                }
                
                Section {
                    confirmationSection
                } header: {
                    Text("Confirmation Required")
                } footer: {
                    Text("Type \"DELETE\" in the field above to proceed with account deletion.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Section {
                    Button(action: {
                        showingFinalConfirmation = true
                    }) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            }
                            
                            Text("Delete My Account")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 4)
                    }
                    .disabled(!isConfirmationValid || isLoading)
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    .tint(.red)
                }
            }
            .navigationTitle("Delete Account")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.purple)
                }
            }
            .confirmationDialog("Final Confirmation", isPresented: $showingFinalConfirmation) {
                Button("Delete My Account", role: .destructive) {
                    Task {
                        await deleteAccount()
                    }
                }
                Button("Cancel", role: .cancel) {}
            } message: {
                Text("This action cannot be undone. Your account and all associated data will be permanently deleted.")
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("OK") {}
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - Warning Section
    
    private var warningSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("This action is permanent and cannot be undone!")
                .fontWeight(.bold)
                .foregroundColor(.red)
            
            Text("Deleting your account will permanently remove all your data from PetTime Capsule servers. This includes all pet profiles, memories, photos, and account information.")
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Data Loss Section
    
    private var dataLossSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            dataLossItem(
                icon: "pawprint.fill",
                title: "Pet Profiles",
                description: "All pet information, medical records, and preferences"
            )
            
            dataLossItem(
                icon: "photo.fill",
                title: "Memories & Photos",
                description: "All uploaded photos, videos, and memory entries"
            )
            
            dataLossItem(
                icon: "calendar",
                title: "Schedules & Reminders",
                description: "Medication schedules, vet appointments, and custom reminders"
            )
            
            dataLossItem(
                icon: "person.circle.fill",
                title: "Account Information",
                description: "Profile data, preferences, and subscription details"
            )
            
            dataLossItem(
                icon: "chart.line.uptrend.xyaxis",
                title: "Health & Activity Data",
                description: "Weight tracking, activity logs, and health insights"
            )
        }
    }
    
    private func dataLossItem(icon: String, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.red)
                .font(.title3)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
    
    // MARK: - Alternatives Section
    
    private var alternativesSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            alternativeItem(
                icon: "pause.circle.fill",
                title: "Temporarily Sign Out",
                description: "Keep your data safe while taking a break"
            )
            
            alternativeItem(
                icon: "icloud.and.arrow.down.fill",
                title: "Export Your Data",
                description: "Download your pet data before deleting (coming soon)"
            )
            
            alternativeItem(
                icon: "gear",
                title: "Adjust Privacy Settings",
                description: "Control what data is collected and used"
            )
        }
    }
    
    private func alternativeItem(icon: String, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.title3)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
    
    // MARK: - Confirmation Section
    
    private var confirmationSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("To confirm deletion, type \"DELETE\" below:")
                .fontWeight(.medium)
            
            TextField("Type DELETE to confirm", text: $confirmationText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .autocapitalization(.allCharacters)
                .autocorrectionDisabled()
            
            if !confirmationText.isEmpty && !isConfirmationValid {
                Text("Please type \"DELETE\" exactly as shown")
                    .foregroundColor(.red)
                    .font(.caption)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func deleteAccount() async {
        isLoading = true
        
        do {
            let success = try await authService.deleteUserAccount()
            
            isLoading = false
            
            if success {
                // Account deletion successful - user should be automatically signed out
                dismiss()
            } else {
                // Show error message
                alertMessage = "Failed to delete account. Please try again."
                alertTitle = "Error"
                showingAlert = true
            }
        } catch {
            isLoading = false
            alertMessage = error.localizedDescription
            alertTitle = "Error"
            showingAlert = true
        }
    }
}

#Preview {
    DeleteAccountView()
        .environmentObject(AuthenticationService())
} 