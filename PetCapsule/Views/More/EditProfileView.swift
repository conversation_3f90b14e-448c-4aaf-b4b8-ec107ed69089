//
//  EditProfileView.swift
//  PetCapsule
//
//  Beautiful profile editing with tags and modern UI
//
import SwiftUI
import PhotosUI
struct EditProfileView: View {
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    @State private var displayName: String = ""
    @State private var email: String = ""
    @State private var bio: String = ""
    @State private var location: String = ""
    @State private var selectedTags: Set<String> = []
    @State private var customTag: String = ""
    @State private var showingCustomTagField: Bool = false
    @State private var isLoading: Bool = false
    @State private var showingAlert: Bool = false
    @State private var alertMessage: String = ""
    @State private var alertTitle: String = ""
    @State private var hasChanges: Bool = false
    @State private var animateContent: Bool = false
    // Photo picker
    @State private var selectedPhotoItem: PhotosPickerItem?
    @State private var selectedProfileImage: UIImage?
    @State private var showImagePicker = false
    private let availableTags = [
        "🐕 Pet Owner", "🚶‍♂️ <PERSON> Walker", "👨‍⚕️ Veterinarian", "🏥 Vet Technician",
        "🎓 Dog Trainer", "💇‍♀️ Pet Groomer", "🏠 Pet Sitter", "🐾 Animal Rescuer",
        "📸 Pet Photographer", "🦮 Service Dog Owner", "🐕‍🦺 Therapy Dog Handler",
        "🏆 Dog Show Enthusiast", "🎯 Agility Trainer", "❤️ Foster Parent"
    ]
    private let originalDisplayName: String
    private let originalEmail: String
    private let originalBio: String
    private let originalLocation: String
    private let originalTags: Set<String>
    init() {
        // Initialize with current user data
        if let currentUser = AuthenticationService().currentUser {
            self.originalDisplayName = currentUser.displayName
            self.originalEmail = currentUser.email
            // We'll load bio, location, and tags from Apple native storage
            self.originalBio = ""
            self.originalLocation = ""
            self.originalTags = Set<String>()
        } else {
            self.originalDisplayName = ""
            self.originalEmail = ""
            self.originalBio = ""
            self.originalLocation = ""
            self.originalTags = Set<String>()
        }
    }
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // Profile Image Section
                    profileImageSection
                    // Profile Information
                    profileInfoSection
                    // User Tags Section
                    userTagsSection
                    // Stats Section
                    userStatsSection
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(.systemBackground),
                        Color(.systemGray6).opacity(0.3)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            )
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        Task {
                            await saveProfile()
                        }
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .disabled(!hasChanges || isLoading)
                    .opacity(hasChanges && !isLoading ? 1.0 : 0.6)
                }
            }
            .onAppear {
                loadCurrentUserData()
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateContent = true
                }
            }
            .onChange(of: displayName) { _, _ in checkForChanges() }
            .onChange(of: email) { _, _ in checkForChanges() }
            .onChange(of: bio) { _, _ in checkForChanges() }
            .onChange(of: location) { _, _ in checkForChanges() }
            .onChange(of: selectedTags) { _, _ in checkForChanges() }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("OK") {
                    if alertTitle == "Success" {
                        dismiss()
                    }
                }
            } message: {
                Text(alertMessage)
            }
            .overlay {
                if isLoading {
                    Color.black.opacity(0.3)
                        .overlay(
                            VStack(spacing: 16) {
                                ProgressView()
                                    .scaleEffect(1.5)
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                Text("Saving your profile...")
                                    .font(.petSubheadline)
                                    .foregroundColor(.white)
                            }
                        )
                        .ignoresSafeArea()
                }
            }
        }
        .sheet(isPresented: $showImagePicker) {
            PhotosPicker(
                selection: $selectedPhotoItem,
                matching: .images,
                photoLibrary: .shared()
            ) {
                VStack(spacing: 20) {
                    Text("Update Profile Photo")
                        .font(.petTitle2)
                        .fontWeight(.bold)
                    Text("Choose a photo from your library")
                        .font(.petBody)
                        .foregroundColor(.secondary)
                    Button("Cancel") {
                        showImagePicker = false
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            }
            .onChange(of: selectedPhotoItem) { _, newValue in
                Task {
                    if let newValue = newValue {
                        if let data = try? await newValue.loadTransferable(type: Data.self),
                           let image = UIImage(data: data) {
                            selectedProfileImage = image
                            await uploadProfileImage(image)
                        }
                    }
                    showImagePicker = false
                }
            }
        }
    }
    // MARK: - Profile Image Section
    private var profileImageSection: some View {
        VStack(spacing: 16) {
            Button(action: { showImagePicker = true }) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [.purple.opacity(0.8), .blue.opacity(0.6)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                        .shadow(color: .purple.opacity(0.3), radius: 10, x: 0, y: 5)
                    if let selectedImage = selectedProfileImage {
                        Image(uiImage: selectedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 120)
                            .clipShape(Circle())
                    } else if let user = authService.currentUser, !user.displayName.isEmpty {
                        Text(getUserInitials(from: user.displayName))
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: "person.fill")
                            .font(.largeTitle)
                            .foregroundColor(.white)
                    }
                    // Camera overlay
                    Circle()
                        .fill(Color.black.opacity(0.3))
                        .frame(width: 120, height: 120)
                    Image(systemName: "camera.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            .scaleEffect(animateContent ? 1.0 : 0.8)
            .opacity(animateContent ? 1.0 : 0.0)
            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateContent)
            Text("Tap to change photo")
                .font(.petCaption)
                .foregroundColor(.secondary)
                .opacity(animateContent ? 1.0 : 0.0)
                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateContent)
        }
    }
    // MARK: - Profile Information Section
    private var profileInfoSection: some View {
        VStack(spacing: 24) {
            // Modern Profile Fields
            VStack(spacing: 20) {
                profileField(
                    icon: "person.fill",
                    title: "Display Name",
                    text: $displayName,
                    placeholder: "Enter your name"
                )
                profileField(
                    icon: "envelope.fill",
                    title: "Email",
                    text: $email,
                    placeholder: "Enter your email",
                    keyboardType: .emailAddress
                )
                profileFieldMultiline(
                    icon: "text.alignleft",
                    title: "Bio",
                    text: $bio,
                    placeholder: "Tell us about yourself and your pets..."
                )
                profileField(
                    icon: "location.fill",
                    title: "Location",
                    text: $location,
                    placeholder: "City, State"
                )
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateContent)
    }
    private func profileField(
        icon: String,
        title: String,
        text: Binding<String>,
        placeholder: String,
        keyboardType: UIKeyboardType = .default
    ) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.petSubheadline)
                    .foregroundColor(.purple)
                    .frame(width: 20)
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
            TextField(placeholder, text: text)
                .textFieldStyle(.plain)
                .keyboardType(keyboardType)
                .autocapitalization(keyboardType == .emailAddress ? .none : .words)
                .font(.petBody)
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.purple.opacity(0.3), lineWidth: 1)
                        )
                )
        }
    }
    private func profileFieldMultiline(
        icon: String,
        title: String,
        text: Binding<String>,
        placeholder: String
    ) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.petSubheadline)
                    .foregroundColor(.purple)
                    .frame(width: 20)
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
            ZStack(alignment: .topLeading) {
                if text.wrappedValue.isEmpty {
                    Text(placeholder)
                        .font(.petBody)
                        .foregroundColor(.secondary)
                        .padding(16)
                }
                TextEditor(text: text)
                    .font(.petBody)
                    .padding(12)
                    .frame(minHeight: 80)
                    .scrollContentBackground(.hidden)
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.purple.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
    // MARK: - User Tags Section
    private var userTagsSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack(spacing: 8) {
                Image(systemName: "tag.fill")
                    .font(.petSubheadline)
                    .foregroundColor(.purple)
                    .frame(width: 20)
                Text("I am a...")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
            // Available Tags
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(availableTags, id: \.self) { tag in
                    tagButton(tag: tag, isSelected: selectedTags.contains(tag))
                }
            }
            // Custom Tag Input
            VStack(spacing: 12) {
                Button(action: {
                    withAnimation(.spring()) {
                        showingCustomTagField.toggle()
                    }
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(.purple)
                        Text("Add Custom Tag")
                            .font(.petSubheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.purple)
                        Spacer()
                    }
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.purple.opacity(0.3), lineWidth: 1)
                    )
                }
                if showingCustomTagField {
                    HStack {
                        TextField("Enter custom tag", text: $customTag)
                            .textFieldStyle(.roundedBorder)
                        Button("Add") {
                            if !customTag.isEmpty {
                                selectedTags.insert("🏷️ \(customTag)")
                                customTag = ""
                                showingCustomTagField = false
                            }
                        }
                        .disabled(customTag.isEmpty)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateContent)
    }
    private func tagButton(tag: String, isSelected: Bool) -> some View {
        Button(action: {
            withAnimation(.spring(response: 0.3)) {
                if isSelected {
                    selectedTags.remove(tag)
                } else {
                    selectedTags.insert(tag)
                }
            }
        }) {
            Text(tag)
                .font(.petCaption2)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? 
                              LinearGradient(gradient: Gradient(colors: [.purple, .blue]), startPoint: .leading, endPoint: .trailing) :
                              LinearGradient(gradient: Gradient(colors: [Color(.systemGray6)]), startPoint: .leading, endPoint: .trailing)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(isSelected ? Color.clear : Color.purple.opacity(0.3), lineWidth: 1)
                        )
                )
                .scaleEffect(isSelected ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }
    // MARK: - User Stats Section
    private var userStatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 8) {
                Image(systemName: "chart.bar.fill")
                    .font(.petSubheadline)
                    .foregroundColor(.purple)
                    .frame(width: 20)
                Text("Your Activity")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
                statCard(
                    title: "Pets",
                    value: "\(authService.currentUser?.petIDs.count ?? 0)",
                    icon: "pawprint.fill",
                    color: .purple
                )
                statCard(
                    title: "Memories",
                    value: "\(authService.currentUser?.totalUploads ?? 0)",
                    icon: "photo.stack.fill",
                    color: .blue
                )
                statCard(
                    title: "Days Active",
                    value: daysSinceJoined(),
                    icon: "calendar.badge.clock",
                    color: .green
                )
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.5), value: animateContent)
    }
    private func statCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            Text(value)
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: color.opacity(0.2), radius: 8, x: 0, y: 4)
        )
    }
    // MARK: - Helper Methods
    private func loadCurrentUserData() {
        if let user = authService.currentUser {
            displayName = user.displayName
            email = user.email
        }
        // Load additional data from Apple native storage
        Task {
            await loadUserProfileData()
        }
    }
    private func loadUserProfileData() async {
        guard let _ = authService.currentUser else { return }
        do {
            print("Profile update completed")
            // Update profile locally using Apple native storage
            await MainActor.run {
                // Profile updated successfully - no need to parse JSON response
                // since we're using local storage now
            }
        } catch {
            print("❌ Error updating user profile: \(error)")
        }
    }
    private func checkForChanges() {
        hasChanges = (displayName != originalDisplayName) ||
                    (email != originalEmail) ||
                    (bio != originalBio) ||
                    (location != originalLocation) ||
                    (selectedTags != originalTags)
    }
    private func saveProfile() async {
        guard hasChanges else { return }
        isLoading = true
        // Validate input
        if displayName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            await showAlert(title: "Error", message: "Display name cannot be empty.")
            isLoading = false
            return
        }
        if !isValidEmail(email) {
            await showAlert(title: "Error", message: "Please enter a valid email address.")
            isLoading = false
            return
        }
        // Update profile using Apple native storage
        print("Updating user profile locally: \(displayName)")
        // Update local auth service
        do {
            let _ = try await authService.updateUserProfile(
                fullName: displayName != originalDisplayName ? displayName : nil,
                bio: nil,
                location: nil
            )
            await showAlert(title: "Success", message: "Your profile has been updated successfully!")
        } catch {
            await showAlert(title: "Error", message: "Failed to update profile: \(error.localizedDescription)")
        }
        isLoading = false
    }
    private func updateUserProfile() async -> Bool {
        guard let _ = authService.currentUser else { return false }
        // Create a proper codable struct for the update
        struct UserUpdate: Codable {
            let display_name: String
            let full_name: String
            let bio: String
            let location: String
            let user_tags: [String]
        }
        let _ = UserUpdate(
            display_name: displayName,
            full_name: displayName,
            bio: bio,
            location: location,
            user_tags: Array(selectedTags)
        )
        print("Profile update completed")
        print("✅ Profile updated successfully")
        return true
    }
    private func uploadProfileImage(_ image: UIImage) async {
        guard let _ = authService.currentUser,
              let imageData = image.jpegData(compressionQuality: 0.8) else {
            return
        }
        do {
            let storageService = AppleNativeStorageService.shared
            if let imageURL = await storageService.uploadPetProfile(
                from: imageData,
                fileName: "profile_\(UUID().uuidString).jpg"
            ) {
                // Update profile image URL in database
                print("Profile update completed")
                print("✅ Profile image uploaded successfully")
            }
        } catch {
            print("❌ Error uploading profile image: \(error)")
        }
    }
         private func showAlert(title: String, message: String) async {
         await MainActor.run {
             alertTitle = title
             alertMessage = message
             showingAlert = true
         }
     }
    private func getUserInitials(from name: String) -> String {
        let components = name.components(separatedBy: " ")
        let initials = components.compactMap { $0.first }.prefix(2)
        return String(initials).uppercased()
    }
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    private func daysSinceJoined() -> String {
        guard let _ = authService.currentUser else {
            return "0"
        }
        let createdAt = Date()
        let days = Calendar.current.dateComponents([.day], from: createdAt, to: Date()).day ?? 0
        return "\(days)"
    }
}
// Helper struct for decoding API response
struct AnyCodable: Codable {
    let value: Any
    init<T>(_ value: T?) {
        self.value = value ?? ()
    }
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if let string = try? container.decode(String.self) {
            value = string
        } else if let int = try? container.decode(Int.self) {
            value = int
        } else if let double = try? container.decode(Double.self) {
            value = double
        } else if let bool = try? container.decode(Bool.self) {
            value = bool
        } else if let array = try? container.decode([AnyCodable].self) {
            value = array.map { $0.value }
        } else {
            value = ()
        }
    }
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        if let string = value as? String {
            try container.encode(string)
        } else if let int = value as? Int {
            try container.encode(int)
        } else if let double = value as? Double {
            try container.encode(double)
        } else if let bool = value as? Bool {
            try container.encode(bool)
        }
    }
}
#Preview {
    EditProfileView()
        .environmentObject(AuthenticationService())
} 