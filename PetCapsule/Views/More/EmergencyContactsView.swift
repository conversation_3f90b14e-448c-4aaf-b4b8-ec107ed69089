//
//  EmergencyContactsView.swift
//  PetCapsule
//
//  Emergency contacts for pet care
//

import SwiftUI
import Contacts // Added for CNContact

// Type alias to resolve ambiguity



struct EmergencyContactsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var emergencyService = EmergencyContactsService.shared
    @State private var showAddContact = false
    @State private var showImportContacts = false
    @State private var showEditContact = false

    @State private var editingContact: EmergencyContact?
    @State private var animateItems = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: [Color.red.opacity(0.1), Color.orange.opacity(0.05)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                if emergencyService.isLoading {
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.5)
                        Text("Loading Emergency Contacts...")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                } else {
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            // Header
                            headerSection
                            
                            // Quick Action Emergency Contacts
                            if !emergencyService.primaryContacts.isEmpty {
                                quickEmergencySection
                            }
                            
                            // All Emergency Contacts
                            savedContactsSection
                        }
                        .padding(.horizontal)
                        .padding(.top, 8)
                    }
                }
            }
            .navigationTitle("Emergency Contacts")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: {
                            showAddContact = true
                        }) {
                            Label("Add New Contact", systemImage: "plus")
                        }
                        
                        Button(action: {
                            showImportContacts = true
                        }) {
                            Label("Import from Contacts", systemImage: "person.crop.circle.badge.plus")
                        }
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.red)
                    }
                }
            }
            .sheet(isPresented: $showAddContact) {
                ContactsAddEmergencyContactView()
                    .environmentObject(emergencyService)
            }
            .sheet(isPresented: $showEditContact) {
                if let contact = editingContact {
                    ContactsEditEmergencyContactView(contact: contact)
                        .environmentObject(emergencyService)
                }
            }
            .sheet(isPresented: $showImportContacts) {
                ContactImportView()
                    .environmentObject(emergencyService)
            }

            .onAppear {
                withAnimation(.easeOut(duration: 0.6).delay(0.1)) {
                    animateItems = true
                }
                
                // Clean up duplicates and reload contacts when view appears
                Task {
                    // First clean up any duplicates
                    await emergencyService.cleanupDuplicateContacts()
                    
                    // Then load contacts if needed (cleanup will reload if changes were made)
                    if emergencyService.emergencyContacts.isEmpty {
                        await emergencyService.loadEmergencyContacts()
                    }
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "cross.circle.fill")
                .font(.system(size: 50))
                .foregroundColor(.red)
                .scaleEffect(animateItems ? 1.0 : 0.8)
                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateItems)
            
            Text("Emergency Contacts")
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            Text("Quick access to critical contacts for your pet's emergency situations")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 8)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.easeOut(duration: 0.8).delay(0.2), value: animateItems)
    }
    
    // MARK: - Quick Emergency Section
    
    private var quickEmergencySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Quick Emergency")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
                Text("Priority Contacts")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(Array(emergencyService.primaryContacts.enumerated()), id: \.element.id) { index, contact in
                    let animationDelay = Double(index) * 0.1 + 0.3
                    let springAnimation = Animation.spring(response: 0.6, dampingFraction: 0.8).delay(animationDelay)
                    let emergencyContact = emergencyService.convertToEmergencyContact(contact)
                    
                    quickEmergencyCard(contact: emergencyContact)
                        .scaleEffect(animateItems ? 1.0 : 0.9)
                        .opacity(animateItems ? 1.0 : 0.0)
                        .animation(springAnimation, value: animateItems)
                }
            }
        }
        .padding(.vertical, 8)
    }
    
    private func quickEmergencyCard(contact: EmergencyContact) -> some View {
        Button(action: {
            emergencyService.callEmergencyContact(contact)
        }) {
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(contactTypeColor(contact.type).opacity(0.15))
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: contact.type.icon)
                        .font(.title3)
                        .foregroundColor(contactTypeColor(contact.type))
                }
                
                Text(contact.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                
                Text(contact.phoneNumber)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.background)
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(contactTypeColor(contact.type).opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .contextMenu {
            Button(action: {
                editingContact = contact
                showEditContact = true
            }) {
                Label("Edit Contact", systemImage: "pencil")
            }
            
            Button(action: {
                emergencyService.callEmergencyContact(contact)
            }) {
                Label("Call Now", systemImage: "phone.fill")
            }
        }
    }
    
    // MARK: - Saved Contacts Section
    
    private var savedContactsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("All Emergency Contacts")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
                Text("\(emergencyService.emergencyContacts.count) contacts")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if emergencyService.emergencyContacts.isEmpty {
                emptyStateView
            } else {
                List {
                    ForEach(Array(emergencyService.emergencyContacts.enumerated()), id: \.element.id) { index, contact in
                        let animationDelay = Double(index) * 0.05 + 0.4
                        let springAnimation = Animation.spring(response: 0.6, dampingFraction: 0.8).delay(animationDelay)
                        let emergencyContact = emergencyService.convertToEmergencyContact(contact)
                        
                        emergencyContactCard(contact: emergencyContact)
                            .scaleEffect(animateItems ? 1.0 : 0.95)
                            .opacity(animateItems ? 1.0 : 0.0)
                            .animation(springAnimation, value: animateItems)
                            .listRowInsets(EdgeInsets(top: 4, leading: 0, bottom: 4, trailing: 0))
                            .listRowBackground(Color.clear)
                            .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                                Button(role: .destructive, action: {
                                    deleteContact(emergencyContact)
                                }) {
                                    Label("Delete", systemImage: "trash")
                                }
                                
                                Button(action: {
                                    editingContact = emergencyContact
                                    showEditContact = true
                                }) {
                                    Label("Edit", systemImage: "pencil")
                                }
                                .tint(.blue)
                            }
                    }
                }
                .listStyle(PlainListStyle())
                .scrollContentBackground(.hidden)
                .frame(height: CGFloat(emergencyService.emergencyContacts.count) * 120)
                .scrollDisabled(true)
            }
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Helper Views
    
    private func emergencyContactCard(contact: EmergencyContact) -> some View {
        HStack(spacing: 16) {
            // Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(contactTypeColor(contact.type).opacity(0.15))
                    .frame(width: 50, height: 50)
                
                Image(systemName: contact.type.icon)
                    .font(.title3)
                    .foregroundColor(contactTypeColor(contact.type))
            }
            
            // Info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(contact.name)
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    if contact.isPrimary {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    
                    Spacer()
                }
                
                Text(contact.phoneNumber)
                    .font(.subheadline)
                    .foregroundColor(.blue)
                
                if let description = contact.description, !description.isEmpty {
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                HStack {
                    Text(contact.type.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(contactTypeColor(contact.type).opacity(0.1))
                        .foregroundColor(contactTypeColor(contact.type))
                        .clipShape(Capsule())
                    
                    Spacer()
                }
            }
            
            // Call Button
            Button(action: {
                emergencyService.callEmergencyContact(contact)
            }) {
                Image(systemName: "phone.fill")
                    .font(.title3)
                    .foregroundColor(.white)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(contactTypeColor(contact.type))
                    )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.background)
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.crop.circle.badge.plus")
                .font(.system(size: 50))
                .foregroundColor(.gray)
            
            Text("No Emergency Contacts")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("Add emergency contacts to quickly reach help when your pet needs it most")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button(action: {
                showAddContact = true
            }) {
                Text("Add Contact")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(.red)
                    )
            }
        }
        .padding(.vertical, 32)
    }
    

    
    // MARK: - Helper Functions
    
    private func contactTypeColor(_ type: EmergencyContactType) -> Color {
        switch type {
        case .emergency: return .red
        case .poisonControl: return .orange
        case .veterinarian: return .blue
        case .animalHospital: return .green
        case .custom: return .purple
        case .family: return .green
        case .friend: return .orange
        }
    }
    
    private func deleteContact(_ contact: EmergencyContact) {
        Task {
            // Find the original ServiceEmergencyContact to delete
            if let serviceContact = emergencyService.emergencyContacts.first(where: { $0.id.uuidString == contact.id }) {
                try? await emergencyService.deleteEmergencyContact(serviceContact)
            }
        }
    }
}

// MARK: - Add Emergency Contact View

struct ContactsAddEmergencyContactView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var emergencyService: EmergencyContactsService
    @State private var name = ""
    @State private var phoneNumber = ""
    @State private var notes = ""
    @State private var selectedRelationship = "veterinarian"
    @State private var isAdding = false
    
    var isFormValid: Bool {
        !name.isEmpty && !phoneNumber.isEmpty
    }
    
    var body: some View {
        NavigationView {
            formContent
                .navigationTitle("Add Emergency Contact")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Cancel") {
                            dismiss()
                        }
                    }
                }
        }
    }
    
    private var formContent: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(spacing: 20) {
                    contactNameField
                    phoneNumberField
                    relationshipField
                    notesField
                }
                
                addButton
            }
            .padding()
        }
    }
    
    private var contactNameField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Contact Name")
                .font(.headline)
            
            TextField("Enter contact name", text: $name)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
    
    private var phoneNumberField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Phone Number")
                .font(.headline)
            
            TextField("Enter phone number", text: $phoneNumber)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .keyboardType(.phonePad)
        }
    }
    
    private var relationshipField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Relationship")
                .font(.headline)
            
            Picker("Relationship", selection: $selectedRelationship) {
                Text("Veterinarian").tag("veterinarian")
                Text("Family").tag("family")
                Text("Friend").tag("friend")
                Text("Emergency").tag("emergency")
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    private var notesField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Notes (Optional)")
                .font(.headline)
            
            TextField("Additional information", text: $notes, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(3...6)
        }
    }
    
    private var addButton: some View {
        Button(action: addContact) {
            HStack {
                if isAdding {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(.white)
                }
                Text("Add Emergency Contact")
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(isFormValid ? Color.red : Color.gray.opacity(0.3))
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .disabled(!isFormValid || isAdding)
    }
    
    private func addContact() {
        isAdding = true
        
        Task {
            // Create the contact with proper EmergencyContactType mapping
            let contactType: EmergencyContactType
            switch selectedRelationship {
            case "veterinarian":
                contactType = .veterinarian
            case "emergency":
                contactType = .emergency
            case "family", "friend":
                contactType = .custom
            default:
                contactType = .custom
            }
            
            let newContact = EmergencyContact(
                id: UUID().uuidString,
                name: name,
                phoneNumber: phoneNumber,
                type: contactType,
                country: emergencyService.userCountryCode,
                isEditable: true,
                description: notes.isEmpty ? nil : notes,
                isPrimary: contactType == .emergency // Emergency contacts are primary by default
            )
            
            // Save the contact using the async service
            let success = await emergencyService.addEmergencyContact(
                name: newContact.name,
                relationship: newContact.type.displayName,
                primaryPhone: newContact.phoneNumber,
                secondaryPhone: nil,
                email: nil,
                address: nil,
                notes: newContact.description,
                isVeterinarian: newContact.type == .veterinarian || newContact.type == .animalHospital,
                clinicName: nil
            )
            
            await MainActor.run {
                isAdding = false
                
                if success {
                    // Reset form fields
                    name = ""
                    phoneNumber = ""
                    notes = ""
                    selectedRelationship = "veterinarian"
                    
                    dismiss()
                } else {
                    // Handle error - could show an alert here
                    print("Failed to add emergency contact")
                }
            }
        }
    }
}

// MARK: - Edit Emergency Contact View

struct ContactsEditEmergencyContactView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var emergencyService: EmergencyContactsService
    
    let contact: EmergencyContact
    @State private var name: String
    @State private var phoneNumber: String
    @State private var notes: String
    @State private var selectedRelationship: String
    @State private var isUpdating = false
    
    init(contact: EmergencyContact) {
        self.contact = contact
        self._name = State(initialValue: contact.name)
        self._phoneNumber = State(initialValue: contact.phoneNumber)
        self._notes = State(initialValue: contact.description ?? "")
        
        // Map contact type back to relationship string
        let relationship: String
        switch contact.type {
        case .veterinarian:
            relationship = "veterinarian"
        case .emergency:
            relationship = "emergency"
        case .custom:
            relationship = "friend"
        default:
            relationship = "friend"
        }
        self._selectedRelationship = State(initialValue: relationship)
    }
    
    var isFormValid: Bool {
        !name.isEmpty && !phoneNumber.isEmpty
    }
    
    var body: some View {
        NavigationView {
            formContent
                .navigationTitle("Edit Contact")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Cancel") {
                            dismiss()
                        }
                    }
                    
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Save") {
                            updateContact()
                        }
                        .disabled(!isFormValid || isUpdating)
                    }
                }
        }
    }
    
    private var formContent: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(spacing: 20) {
                    contactNameField
                    phoneNumberField
                    relationshipField
                    notesField
                }
                
                updateButton
            }
            .padding()
        }
    }
    
    private var contactNameField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Contact Name")
                .font(.headline)
            
            TextField("Enter contact name", text: $name)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
    }
    
    private var phoneNumberField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Phone Number")
                .font(.headline)
            
            TextField("Enter phone number", text: $phoneNumber)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .keyboardType(.phonePad)
        }
    }
    
    private var relationshipField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Relationship")
                .font(.headline)
            
            Picker("Relationship", selection: $selectedRelationship) {
                Text("Veterinarian").tag("veterinarian")
                Text("Family").tag("family")
                Text("Friend").tag("friend")
                Text("Emergency").tag("emergency")
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    private var notesField: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Notes (Optional)")
                .font(.headline)
            
            TextField("Additional information", text: $notes, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(3...6)
        }
    }
    
    private var updateButton: some View {
        Button(action: updateContact) {
            HStack {
                if isUpdating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(.white)
                }
                Text("Update Contact")
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(isFormValid ? Color.blue : Color.gray.opacity(0.3))
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .disabled(!isFormValid || isUpdating)
    }
    
    private func updateContact() {
        isUpdating = true
        
        Task {
            // Create the contact with proper EmergencyContactType mapping
            let contactType: EmergencyContactType
            switch selectedRelationship {
            case "veterinarian":
                contactType = .veterinarian
            case "emergency":
                contactType = .emergency
            case "family", "friend":
                contactType = .custom
            default:
                contactType = .custom
            }
            
            var updatedContact = contact
            updatedContact.name = name
            updatedContact.phoneNumber = phoneNumber
            updatedContact.type = contactType
            updatedContact.description = notes.isEmpty ? nil : notes
            updatedContact.isPrimary = contactType == .emergency || contact.isPrimary // Keep primary status or set for emergency
            
            // Convert EmergencyContact to ServiceEmergencyContact for update
            let serviceContact = ServiceEmergencyContact(
                name: updatedContact.name,
                relationship: updatedContact.type.displayName,
                primaryPhone: updatedContact.phoneNumber,
                secondaryPhone: nil,
                email: nil,
                address: nil,
                notes: updatedContact.description,
                isVeterinarian: updatedContact.type == .veterinarian || updatedContact.type == .animalHospital,
                clinicName: nil
            )
            
            // Update the contact using the async service
            let success = await emergencyService.updateEmergencyContact(serviceContact)
            
            await MainActor.run {
                isUpdating = false
                
                if success {
                    dismiss()
                } else {
                    // Handle error - could show an alert here
                    print("Failed to update emergency contact")
                }
            }
        }
    }
}

// MARK: - Contact Import View

struct ContactImportView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var emergencyService: EmergencyContactsService
    
    @State private var contacts: [CNContact] = []
    @State private var isLoading = false
    @State private var searchText = ""
    @State private var selectedContactType: EmergencyContactType = .custom
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    var filteredContacts: [CNContact] {
        if searchText.isEmpty {
            return contacts
        } else {
            return contacts.filter { contact in
                let name = "\(contact.givenName) \(contact.familyName)".lowercased()
                return name.contains(searchText.lowercased())
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                LinearGradient(
                    colors: [Color.blue.opacity(0.1), Color.green.opacity(0.05)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack {
                    if isLoading {
                        VStack(spacing: 16) {
                            ProgressView()
                            Text("Loading contacts...")
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else if contacts.isEmpty {
                        VStack(spacing: 16) {
                            Image(systemName: "person.2.slash")
                                .font(.system(size: 60))
                                .foregroundColor(.gray)
                            
                            Text("No Contacts Found")
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text("Make sure you've granted permission to access your contacts")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                            
                            Button("Refresh") {
                                Task {
                                    await loadContacts()
                                }
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else {
                        // Contact Type Selection
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Import as:")
                                .font(.headline)
                                .padding(.leading)
                            
                            Picker("Contact Type", selection: $selectedContactType) {
                                Text("Emergency Contact").tag(EmergencyContactType.emergency)
                                Text("Veterinarian").tag(EmergencyContactType.veterinarian)
                                Text("Custom Contact").tag(EmergencyContactType.custom)
                            }
                            .pickerStyle(.segmented)
                            .padding(.horizontal)
                        }
                        .padding(.top)
                        
                        // Search Bar
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.gray)
                            TextField("Search contacts...", text: $searchText)
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(10)
                        .padding(.horizontal)
                        
                        // Contacts List
                        List(filteredContacts, id: \.identifier) { contact in
                            ContactRowView(contact: contact) {
                                Task {
                                    await importContact(contact)
                                }
                            }
                        }
                        .listStyle(.plain)
                    }
                }
            }
            .navigationTitle("Import Contact")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("Import Result", isPresented: $showAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
        .task {
            await loadContacts()
        }
    }
    
    private func loadContacts() async {
        isLoading = true
        contacts = await emergencyService.fetchContacts()
        isLoading = false
    }
    
    private func importContact(_ contact: CNContact) async {
        isLoading = true
        let success = await emergencyService.importContactAsEmergencyContact(contact, type: selectedContactType)
        isLoading = false
        
        if success {
            alertMessage = "Contact imported successfully!"
            showAlert = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                dismiss()
            }
        } else {
            alertMessage = "Failed to import contact. Please try again."
            showAlert = true
        }
    }
}

// MARK: - Contact Row View

struct ContactRowView: View {
    let contact: CNContact
    let onImport: () -> Void
    
    var contactName: String {
        let name = "\(contact.givenName) \(contact.familyName)".trimmingCharacters(in: .whitespaces)
        return name.isEmpty ? "Unknown Contact" : name
    }
    
    var primaryPhone: String {
        contact.phoneNumbers.first?.value.stringValue ?? "No phone"
    }
    
    var contactEmail: String? {
        contact.emailAddresses.first?.value as String?
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // Contact Icon
            ZStack {
                Circle()
                    .fill(Color.blue.opacity(0.15))
                    .frame(width: 50, height: 50)
                
                Text(String(contactName.prefix(1).uppercased()))
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }
            
            // Contact Info
            VStack(alignment: .leading, spacing: 4) {
                Text(contactName)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(primaryPhone)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                if let email = contactEmail {
                    Text(email)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                if !contact.organizationName.isEmpty {
                    Text(contact.organizationName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
            
            Spacer()
            
            // Import Button
            Button(action: onImport) {
                HStack(spacing: 4) {
                    Image(systemName: "plus.circle.fill")
                    Text("Import")
                }
                .font(.caption)
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue)
                .cornerRadius(16)
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    EmergencyContactsView()
        .environmentObject(EmergencyContactsService.shared)
}
