//
//  KnowledgeBaseView.swift
//  PetCapsule
//
//  Knowledge base for storing pet-related documents and information
//  ✅ Updated to match main page UX/UI design patterns
//

import SwiftUI

struct KnowledgeBaseView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var knowledgeService = KnowledgeBaseService.shared
    @State private var selectedFolder: KnowledgeFolder?
    @State private var showCreateFolder = false
    @State private var showAddDocument = false
    @State private var searchText = ""
    @State private var animateItems = false
    
    var filteredFolders: [KnowledgeFolder] {
        if searchText.isEmpty {
            return knowledgeService.folders
        } else {
            return knowledgeService.folders.filter { folder in
                folder.name.localizedCaseInsensitiveContains(searchText) ||
                folder.documents.contains { $0.title.localizedCaseInsensitiveContains(searchText) }
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Clean Header
                    cleanHeader
                    
                    // Search Section
                    searchSection
                    
                    // Content
                    if knowledgeService.folders.isEmpty {
                        emptyStateView
                    } else {
                        foldersGrid
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .background(colorScheme == .dark ? Color.black : Color(.systemGroupedBackground))
            .navigationTitle("Everything You Need to Know")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showCreateFolder = true }) {
                        Image(systemName: "folder.badge.plus")
                            .font(.title3)
                            .foregroundColor(.blue)
                    }
                }
            }
            .sheet(isPresented: $showCreateFolder) {
                CreateFolderView()
                    .environmentObject(knowledgeService)
            }
            .sheet(item: $selectedFolder) { folder in
                FolderDetailView(folder: folder)
                    .environmentObject(knowledgeService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateItems = true
                }
            }
        }
    }
    
    // MARK: - Clean Header (Following Main Page Pattern)
    
    private var cleanHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Organize your pet's important information in secure folders")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
                
                Image(systemName: "doc.text.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(.blue)
            }
        }
        .scaleEffect(animateItems ? 1.0 : 0.8)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateItems)
    }
    
    // MARK: - Search Section (Updated Design)
    
    private var searchSection: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
                .frame(width: 16, height: 16)
            
            TextField("Search folders and documents", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
        .scaleEffect(animateItems ? 1.0 : 0.8)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateItems)
    }
    
    // MARK: - Empty State (Updated Design)
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "folder.badge.plus")
                .font(.system(size: 64, weight: .light))
                .foregroundStyle(.blue)
            
            VStack(spacing: 12) {
                Text("Create Your Knowledge Base")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text("Organize your pet's important information in secure folders")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: { showCreateFolder = true }) {
                HStack(spacing: 8) {
                    Image(systemName: "folder.badge.plus")
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text("Create First Folder")
                        .font(.system(size: 16, weight: .semibold))
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(.blue)
                .foregroundColor(.white)
                .clipShape(RoundedRectangle(cornerRadius: 25))
            }
        }
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 20, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
        .scaleEffect(animateItems ? 1.0 : 0.8)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateItems)
    }
    
    // MARK: - Folders Grid (Following Main Page Pattern)
    
    private var foldersGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 16),
            GridItem(.flexible(), spacing: 16)
        ], spacing: 16) {
            ForEach(Array(filteredFolders.enumerated()), id: \.element.id) { index, folder in
                folderCard(folder: folder)
                    .scaleEffect(animateItems ? 1.0 : 0.8)
                    .opacity(animateItems ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateItems)
                    .onTapGesture {
                        selectedFolder = folder
                    }
            }
        }
    }
    
    private func folderCard(folder: KnowledgeFolder) -> some View {
        VStack(spacing: 12) {
            // Folder Icon
            ZStack {
                Circle()
                    .fill(folder.color.opacity(0.15))
                    .frame(width: 60, height: 60)
                
                Image(systemName: folder.icon)
                    .font(.title2)
                    .foregroundColor(folder.color)
            }
            
            // Folder Info
            VStack(spacing: 4) {
                HStack {
                    Text(folder.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                    
                    if folder.isSecure {
                        Image(systemName: "lock.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                Text("\(folder.documents.count) documents")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                if !folder.description.isEmpty {
                    Text(folder.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                }
            }
        }
        .padding(20)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
    }
}

// MARK: - Create Folder View

struct CreateFolderView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var knowledgeService: KnowledgeBaseService
    @State private var folderName = ""
    @State private var folderDescription = ""
    @State private var selectedIcon = "folder.fill"
    @State private var selectedColor = Color.blue
    @State private var isSecure = false
    @State private var isCreating = false
    
    private let folderIcons = [
        "folder.fill", "heart.text.square.fill", "stethoscope",
        "leaf.fill", "graduationcap.fill", "doc.text.fill",
        "photo.fill", "video.fill", "pill.fill"
    ]
    
    private let folderColors: [Color] = [
        .blue, .green, .orange, .red, .purple, .pink, .yellow, .indigo, .teal
    ]
    
    var isFormValid: Bool {
        !folderName.isEmpty && !folderDescription.isEmpty
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Folder Preview
                    VStack(spacing: 16) {
                        ZStack {
                            RoundedRectangle(cornerRadius: 16)
                                .fill(selectedColor.opacity(0.15))
                                .frame(width: 80, height: 80)
                            
                            Image(systemName: selectedIcon)
                                .font(.system(size: 32))
                                .foregroundColor(selectedColor)
                        }
                        
                        Text(folderName.isEmpty ? "New Folder" : folderName)
                            .font(.headline)
                            .fontWeight(.bold)
                    }
                    
                    // Form
                    VStack(spacing: 20) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Folder Name")
                                .font(.headline)
                            
                            TextField("Enter folder name", text: $folderName)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Description")
                                .font(.headline)
                            
                            TextField("Describe what you'll store here", text: $folderDescription, axis: .vertical)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .lineLimit(3...6)
                        }
                        
                        // Icon Selection
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Icon")
                                .font(.headline)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 12) {
                                ForEach(folderIcons, id: \.self) { icon in
                                    Button(action: { selectedIcon = icon }) {
                                        Image(systemName: icon)
                                            .font(.title2)
                                            .foregroundColor(selectedIcon == icon ? .white : .secondary)
                                            .frame(width: 44, height: 44)
                                            .background(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .fill(selectedIcon == icon ? selectedColor : Color(.systemGray5))
                                            )
                                    }
                                }
                            }
                        }
                        
                        // Color Selection
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Color")
                                .font(.headline)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 12) {
                                ForEach(folderColors, id: \.self) { color in
                                    Button(action: { selectedColor = color }) {
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(color)
                                            .frame(width: 44, height: 44)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(selectedColor == color ? Color.primary : Color.clear, lineWidth: 3)
                                            )
                                    }
                                }
                            }
                        }
                        
                        // Security Toggle
                        Toggle("Secure Folder", isOn: $isSecure)
                            .font(.headline)
                    }
                    
                    // Create Button
                    Button(action: createFolder) {
                        HStack {
                            if isCreating {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.white)
                            }
                            Text("Create Folder")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(isFormValid ? selectedColor : Color.gray.opacity(0.3))
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(!isFormValid || isCreating)
                }
                .padding()
            }
            .navigationTitle("New Folder")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func createFolder() {
        isCreating = true
        
        Task {
            await knowledgeService.createFolder(
                name: folderName,
                description: folderDescription,
                icon: selectedIcon,
                color: selectedColor,
                isSecure: isSecure
            )
            
            await MainActor.run {
                isCreating = false
                dismiss()
            }
        }
    }
}

// MARK: - Folder Detail View

struct FolderDetailView: View {
    let folder: KnowledgeFolder
    @EnvironmentObject var knowledgeService: KnowledgeBaseService
    @Environment(\.dismiss) private var dismiss
    @State private var showAddDocument = false
    @State private var animateItems = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Folder Header
                folderHeaderView

                // Documents List
                if folder.documents.isEmpty {
                    emptyDocumentsView
                } else {
                    documentsListView
                }
            }
            .navigationTitle(folder.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add Document") {
                        showAddDocument = true
                    }
                    .foregroundColor(folder.color)
                }
            }
            .sheet(isPresented: $showAddDocument) {
                AddDocumentView(folder: folder)
                    .environmentObject(knowledgeService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateItems = true
                }
            }
        }
    }

    private var folderHeaderView: some View {
        VStack(spacing: 16) {
            // Folder Icon and Info
            HStack(spacing: 16) {
                ZStack {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(folder.color.opacity(0.15))
                        .frame(width: 80, height: 80)

                    Image(systemName: folder.icon)
                        .font(.system(size: 32))
                        .foregroundColor(folder.color)
                }

                VStack(alignment: .leading, spacing: 8) {
                    Text(folder.name)
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(folder.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(3)

                    HStack {
                        Text("\(folder.documents.count) documents")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if folder.isSecure {
                            Image(systemName: "lock.fill")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                }

                Spacer()
            }
            .padding()
            .background(Color(.systemGray6))
        }
    }

    private var emptyDocumentsView: some View {
        VStack(spacing: 24) {
            Image(systemName: "doc.badge.plus")
                .font(.system(size: 64))
                .foregroundColor(folder.color)

            VStack(spacing: 12) {
                Text("No Documents Yet")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Add your first document to this folder")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }

            Button("Add Document") {
                showAddDocument = true
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(folder.color)
            .cornerRadius(12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var documentsListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(Array(folder.documents.enumerated()), id: \.element.id) { index, document in
                    documentCard(document: document)
                        .scaleEffect(animateItems ? 1.0 : 0.9)
                        .opacity(animateItems ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateItems)
                }
            }
            .padding()
        }
    }

    private func documentCard(document: KnowledgeDocument) -> some View {
        HStack(spacing: 16) {
            // Document Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(document.type.color.opacity(0.15))
                    .frame(width: 50, height: 50)

                Image(systemName: document.type.icon)
                    .font(.title3)
                    .foregroundColor(document.type.color)
            }

            // Document Info
            VStack(alignment: .leading, spacing: 4) {
                Text(document.title)
                    .font(.headline)
                    .fontWeight(.bold)
                    .lineLimit(2)

                Text(document.type.displayName)
                    .font(.caption)
                    .foregroundColor(.secondary)

                if !document.tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 4) {
                            ForEach(document.tags.prefix(3), id: \.self) { tag in
                                Text(tag)
                                    .font(.caption2)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(folder.color.opacity(0.1))
                                    .foregroundColor(folder.color)
                                    .cornerRadius(4)
                            }
                        }
                    }
                }
            }

            Spacer()

            // Action Button
            Button(action: {
                // Open document
            }) {
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Add Document View

struct AddDocumentView: View {
    let folder: KnowledgeFolder
    @EnvironmentObject var knowledgeService: KnowledgeBaseService
    @Environment(\.dismiss) private var dismiss
    @State private var title = ""
    @State private var content = ""
    @State private var selectedType = KnowledgeDocumentType.text
    @State private var tags = ""
    @State private var isAdding = false

    var isFormValid: Bool {
        !title.isEmpty && !content.isEmpty
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    VStack(spacing: 20) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Document Title")
                                .font(.headline)

                            TextField("Enter document title", text: $title)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Document Type")
                                .font(.headline)

                            Picker("Document Type", selection: $selectedType) {
                                ForEach(KnowledgeDocumentType.allCases, id: \.self) { type in
                                    HStack {
                                        Image(systemName: type.icon)
                                        Text(type.displayName)
                                    }
                                    .tag(type)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Content")
                                .font(.headline)

                            TextField("Enter document content", text: $content, axis: .vertical)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .lineLimit(5...15)
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Tags (Optional)")
                                .font(.headline)

                            TextField("Enter tags separated by commas", text: $tags)
                                .textFieldStyle(RoundedBorderTextFieldStyle())

                            Text("Example: recipe, chicken, healthy")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Button(action: addDocument) {
                        HStack {
                            if isAdding {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.white)
                            }
                            Text("Add Document")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(isFormValid ? folder.color : Color.gray.opacity(0.3))
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(!isFormValid || isAdding)
                }
                .padding()
            }
            .navigationTitle("Add Document")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func addDocument() {
        isAdding = true

        Task {
            let document = KnowledgeDocument(
                id: UUID(),
                title: title,
                content: content,
                type: selectedType,
                fileURL: nil,
                tags: tags.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }.filter { !$0.isEmpty },
                createdAt: Date(),
                updatedAt: Date()
            )

            await knowledgeService.addDocument(to: folder, document: document)

            await MainActor.run {
                isAdding = false
                dismiss()
            }
        }
    }
}

extension KnowledgeDocumentType {
    var color: Color {
        switch self {
        case .text: return .blue
        case .recipe: return .green
        case .medical: return .red
        case .training: return .orange
        case .photo: return .purple
        case .document: return .gray
        }
    }
}

#Preview {
    KnowledgeBaseView()
}
