//
//  MoreAppearanceSettingsView.swift
//  PetCapsule
//
//  Appearance settings for the Account & Settings section
//

import SwiftUI

struct MoreAppearanceSettingsView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Theme Selection
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Image(systemName: "paintbrush.fill")
                            .font(.title2)
                            .foregroundColor(.indigo)

                        Text("Appearance")
                            .font(.title2)
                            .fontWeight(.bold)
                    }
                    
                    VStack(spacing: 16) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Theme")
                                    .font(.headline)
                                    .fontWeight(.medium)

                                Text("Choose your preferred app appearance")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Menu {
                                ForEach(AppTheme.allCases, id: \.self) { theme in
                                    Button(action: {
                                        withAnimation(.easeInOut(duration: 0.3)) {
                                            themeManager.setTheme(theme)
                                        }
                                    }) {
                                        HStack {
                                            Image(systemName: theme.icon)
                                            Text(theme.rawValue)
                                            if themeManager.currentTheme == theme {
                                                Spacer()
                                                Image(systemName: "checkmark")
                                            }
                                        }
                                    }
                                }
                            } label: {
                                HStack(spacing: 8) {
                                    Image(systemName: themeManager.currentTheme.icon)
                                        .font(.caption)
                                        .foregroundColor(.purple)

                                    Text(themeManager.currentTheme.rawValue)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.purple)

                                    Image(systemName: "chevron.down")
                                        .font(.caption2)
                                        .foregroundColor(.purple)
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.purple.opacity(0.1))
                                )
                            }
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
                )
                
                Spacer()
            }
            .padding()
            .navigationTitle("Appearance")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.purple)
                }
            }
        }
    }
}

#Preview {
    MoreAppearanceSettingsView()
        .environmentObject(ThemeManager.shared)
} 