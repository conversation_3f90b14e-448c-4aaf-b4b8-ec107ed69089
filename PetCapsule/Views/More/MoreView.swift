//
//  MoreView.swift
//  PetCapsule
//
//  More page with Settings, Premium, and Pet Health
//
import SwiftUI
struct MoreView: View {
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var subscriptionService: SubscriptionService
    @StateObject private var premiumService = ComprehensivePremiumService.shared
    @EnvironmentObject private var themeManager: ThemeManager
    @EnvironmentObject private var realDataService: RealDataService
    // Sheet states
    @State private var showSettings = false
    // Removed showPremium - consolidated into unified subscription management
    // Removed showPetHealth - moved to Pet Support tab
    @State private var showProfile = false
    @State private var showHelp = false
    @State private var showRating = false
    @State private var showSignOutAlert = false
    @State private var showSharing = false
    @State private var showPrivacy = false
    @State private var showTerms = false
    @State private var showKnowledgeBase = false
    @State private var showVetSearch = false
    // Removed showEmergencyContacts - moved to Pet Support tab
    @State private var showPetInsurance = false
    @State private var showTrainingPlanner = false
    @State private var showNutritionPlanner = false
    @State private var showSubscriptions = false
    @State private var showBilling = false
    @State private var showPlanner = false
    @State private var showPetFriendlyPlaces = false
    @State private var showVaccinationPlanner = false
    // Individual settings sections (now moved to Account & Settings)
    @State private var showAppearanceSettings = false
    @State private var showNotificationSettings = false
    @State private var showPrivacySettings = false
    // Removed showAccountSettings since items are now in main Account & Settings
    @State private var showEditProfile = false
    @State private var showChangePassword = false
    @State private var showDeleteAccountView = false
    // Collapsible sections
    @State private var expandedSections: Set<String> = []
    @State private var animateItems = false
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Collapsible sections
                    collapsibleSection("Planner", icon: "calendar.badge.plus", items: plannerItems)
                    collapsibleSection("Resources", icon: "book.circle", items: resourcesItems)
                    // Account & Settings (consolidated)
                    collapsibleSection("Account & Settings", icon: "person.circle.fill", items: accountSettingsItems)
                    // Help & Support (moved outside as separate item)
                    collapsibleSection("Help & Support", icon: "questionmark.circle.fill", items: helpSupportItems)
                    collapsibleSection("About", icon: "info.circle", items: aboutItems)
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Everything Else")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.themeBackground)
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateItems = true
                }
            }
        }
        .sheet(isPresented: $showSettings) {
            AppSettingsView()
                .environmentObject(authService)
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showAppearanceSettings) {
            MoreAppearanceSettingsView()
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showNotificationSettings) {
            NotificationSettingsView()
        }
        .sheet(isPresented: $showPrivacySettings) {
            PrivacySettingsView()
        }
        .sheet(isPresented: $showEditProfile) {
            EditProfileView()
                .environmentObject(authService)
        }
        .sheet(isPresented: $showChangePassword) {
            ChangePasswordView()
                .environmentObject(authService)
        }
        .sheet(isPresented: $showDeleteAccountView) {
            DeleteAccountView()
                .environmentObject(authService)
        }
        // Removed PremiumHubView sheet - consolidated into unified SubscriptionView
        // Removed PetHealthView sheet - feature moved to Pet Support tab
        .sheet(isPresented: $showProfile) {
            UserProfileView()
                .environmentObject(authService)
        }
        .sheet(isPresented: $showHelp) {
            HelpSupportView()
        }
        .sheet(isPresented: $showRating) {
            AppRatingView()
        }
        .sheet(isPresented: $showSharing) {
            AppSharingView()
        }
        .sheet(isPresented: $showPrivacy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showTerms) {
            TermsOfServiceView()
        }
        .sheet(isPresented: $showKnowledgeBase) {
            KnowledgeBaseView()
        }
        .sheet(isPresented: $showVetSearch) {
            VetSearchView(selectedPet: realDataService.pets.first)
                .environmentObject(realDataService)
        }
        // Removed EmergencyContactsView sheet - feature integrated into Pet Support tab
        .sheet(isPresented: $showPetInsurance) {
            PetInsuranceView()
        }
        .sheet(isPresented: $showTrainingPlanner) {
            TrainingPlannerView()
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showNutritionPlanner) {
            NutritionPlannerView()
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showSubscriptions) {
            SubscriptionView()
                .environmentObject(subscriptionService)
        }
        .sheet(isPresented: $showPlanner) {
            PetPlannerView()
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showPetFriendlyPlaces) {
            PetFriendlyPlacesView()
                .environmentObject(realDataService)
        }
        .sheet(isPresented: $showVaccinationPlanner) {
            VaccinationPlannerView()
                .environmentObject(realDataService)
        }
        .alert("Sign Out", isPresented: $showSignOutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                Task {
                    await authService.signOut()
                }
            }
        } message: {
            Text("Are you sure you want to sign out? Your data will remain safe in the cloud.")
        }
    }
    // MARK: - Menu Items
    private var plannerItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "figure.walk", title: "Walk Planner", subtitle: "Plan walks, track routes, and save memories", color: .green, action: { showPlanner = true }),
            MoreMenuItem(icon: "leaf.fill", title: "Nutrition Planner", subtitle: "Create customized meal plans and track nutrition", color: .orange, action: { showNutritionPlanner = true }),
            MoreMenuItem(icon: "brain.head.profile", title: "Training Planner", subtitle: "Develop training schedules and track progress", color: .purple, action: { showTrainingPlanner = true }),
            MoreMenuItem(icon: "cross.case.fill", title: "Vaccination Planner", subtitle: "Track vaccination schedules and set reminders", color: .red, badge: getVaccinationUrgencyCount() > 0 ? "\(getVaccinationUrgencyCount())" : nil, action: { showVaccinationPlanner = true })
        ]
    }
    private var resourcesItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "location.fill", title: "Pet-Friendly Places", subtitle: "Discover parks, cafes, and pet-friendly locations", color: .purple, action: { showPetFriendlyPlaces = true }),
            MoreMenuItem(icon: "doc.fill", title: "Documents", subtitle: "Store recipes, medical data, and important documents", color: .blue, action: { showKnowledgeBase = true }),
            MoreMenuItem(icon: "stethoscope", title: "Find Veterinarians", subtitle: "Search and save vet contacts for emergencies", color: .green, action: { showVetSearch = true }),
            MoreMenuItem(icon: "shield.fill", title: "Pet Insurance", subtitle: "Compare plans and manage insurance policies", color: .indigo, action: { showPetInsurance = true })
        ]
    }
    // MARK: - Consolidated Account & Settings Items
    private var accountSettingsItems: [MoreMenuItem] {
        [
            // Profile (merged with Edit Profile functionality)
            MoreMenuItem(icon: "person.crop.circle", title: "Profile", subtitle: getUserProfileSubtitle(), color: .purple, action: { showEditProfile = true }),
            // Privacy & Security (with Change Password moved here)
            MoreMenuItem(icon: "lock.shield.fill", title: "Privacy & Security", subtitle: "Face ID, password, analytics, and data export", color: .green, action: { showPrivacySettings = true }),
            // Notifications
            MoreMenuItem(icon: "bell.fill", title: "Notifications", subtitle: "Push notifications and reminders", color: .blue, action: { showNotificationSettings = true }),
            // Appearance
            MoreMenuItem(icon: "paintbrush.fill", title: "Appearance", subtitle: "Theme and app appearance settings", color: .indigo, action: { showAppearanceSettings = true }),
            // Unified subscription management - shows different views based on user status
            MoreMenuItem(
                icon: "creditcard.and.123",
                title: "Manage Subscription",
                subtitle: !premiumService.isSubscribed ? "Unlock 4 AI agents and premium features" : "Manage subscription, billing, and payment history",
                color: .blue,
                badge: !premiumService.isSubscribed ? "Upgrade" : nil,
                action: { showSubscriptions = true }
            ),
            // Sign Out
            MoreMenuItem(icon: "rectangle.portrait.and.arrow.right.fill", title: "Sign Out", subtitle: "Sign out of your account", color: .orange, action: { showSignOutAlert = true }),
            // Delete Account (moved here from Account settings)
            MoreMenuItem(icon: "trash.circle.fill", title: "Delete Account", subtitle: "Permanently delete your account and all data", color: .red, action: { showDeleteAccountView = true })
        ]
    }
    // MARK: - Help & Support Items (now separate)
    private var helpSupportItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "questionmark.circle.fill", title: "Help & Support", subtitle: "Get help and contact support", color: .blue, action: { showHelp = true })
        ]
    }
    private var aboutItems: [MoreMenuItem] {
        [
            MoreMenuItem(icon: "doc.text.fill", title: "Privacy Policy", subtitle: "How we protect your data", color: .indigo, action: { showPrivacy = true }),
            MoreMenuItem(icon: "doc.plaintext.fill", title: "Terms of Service", subtitle: "Terms and conditions", color: .teal, action: { showTerms = true }),
            MoreMenuItem(icon: "star.fill", title: "Rate App", subtitle: "Share your experience on the App Store", color: .yellow, action: { showRating = true })
        ]
    }
    // MARK: - Collapsible Section View
    private func collapsibleSection(_ title: String, icon: String, items: [MoreMenuItem]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Button(action: {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    if expandedSections.contains(title) {
                        expandedSections.remove(title)
                    } else {
                        expandedSections.insert(title)
                    }
                }
            }) {
                HStack {
                    HStack(spacing: 12) {
                        Image(systemName: icon)
                            .font(.title3)
                            .foregroundColor(.purple)
                            .frame(width: 24)
                        Text(title)
                            .font(.petTitle3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                    }
                    Spacer()
                    Image(systemName: expandedSections.contains(title) ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .animation(.easeInOut(duration: 0.2), value: expandedSections.contains(title))
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 6, x: 0, y: 2)
                )
            }
            .buttonStyle(PlainButtonStyle())
            if expandedSections.contains(title) {
                VStack(spacing: 8) {
                    ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                        MoreMenuItemRow(item: item)
                            .scaleEffect(animateItems ? 1.0 : 0.9)
                            .opacity(animateItems ? 1.0 : 0.0)
                            .animation(
                                .spring(response: 0.6, dampingFraction: 0.8)
                                    .delay(Double(index) * 0.05),
                                value: animateItems
                            )
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
    }
    // MARK: - Helper Methods
    private func getUserProfileSubtitle() -> String {
        if let user = authService.currentUser {
            return user.email
        }
        return "Tap to edit your profile"
    }
    private func getVaccinationUrgencyCount() -> Int {
        // This would calculate urgent vaccinations from your pets data
        // For now, returning a placeholder
        return 0
    }
}
// MARK: - More Menu Item
struct MoreMenuItem: Identifiable {
    let id = UUID()
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let badge: String?
    let action: () -> Void
    init(icon: String, title: String, subtitle: String, color: Color, badge: String? = nil, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.color = color
        self.badge = badge
        self.action = action
    }
}
struct MoreMenuItemRow: View {
    let item: MoreMenuItem
    var body: some View {
        Button(action: item.action) {
            HStack(spacing: 16) {
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(item.color.opacity(0.15))
                        .frame(width: 32, height: 32)
                    Image(systemName: item.icon)
                        .font(.system(size: 14))
                        .foregroundColor(item.color)
                }
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Text(item.title)
                            .font(.petSubheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        if let badge = item.badge {
                            Text(badge)
                                .font(.petCaption)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.red)
                                .cornerRadius(8)
                        }
                        Spacer()
                    }
                    Text(item.subtitle)
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
// MARK: - Removed MoreAccountSettingsView since functionality is now integrated
#Preview {
    MoreView()
        .environmentObject(AuthenticationService())
        .environmentObject(SubscriptionService.shared)
        .environmentObject(ThemeManager.shared)
        .environmentObject(RealDataService())
}
