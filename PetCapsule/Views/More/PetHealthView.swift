//
//  PetHealthView.swift
//  PetCapsule
//
//  Pet Health - AI-powered health insights (moved from AI Health Center)
//

import SwiftUI

@available(iOS 18.0, *)
struct PetHealthView: View {
    @EnvironmentObject private var realDataService: RealDataService
    @EnvironmentObject private var aiSupportService: PetAISupportService
    @State private var selectedPet: Pet?
    @State private var selectedTab = 0
    @State private var animateContent = false

    private let tabs = ["Overview", "Nutrition", "Behavior", "Insights"]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Pet Selector
                petSelectorSection

                // Tab Selector
                tabSelectorSection

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    healthOverviewTab
                        .tag(0)

                    nutritionTab
                        .tag(1)

                    behaviorTab
                        .tag(2)

                    insightsTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Health Guardian")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Analyze") {
                        if let pet = selectedPet {
                            Task {
                                await aiSupportService.analyzeCompletePetHealth(for: pet)
                            }
                        }
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple)
                    )
                    .disabled(selectedPet == nil || aiSupportService.isAnalyzing)
                }
            }
            .onAppear {
                if selectedPet == nil && !realDataService.pets.isEmpty {
                    selectedPet = realDataService.pets.first
                }
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateContent = true
                }
            }
        }
    }

    // MARK: - Pet Selector Section

    private var petSelectorSection: some View {
        VStack(spacing: 16) {
            if realDataService.pets.isEmpty {
                Text("No pets added yet")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(realDataService.pets) { pet in
                            petSelectorCard(pet: pet)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
        .padding(.vertical)
        .background(Color(.systemGroupedBackground))
    }

    private func petSelectorCard(pet: Pet) -> some View {
        Button(action: { selectedPet = pet }) {
            VStack(spacing: 8) {
                // Pet Image
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(LinearGradient(
                            gradient: Gradient(colors: [.purple.opacity(0.3), .blue.opacity(0.3)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .overlay(
                            Image(systemName: pet.species == "dog" ? "dog.fill" : "cat.fill")
                                .font(.title2)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 60, height: 60)
                .clipShape(RoundedRectangle(cornerRadius: 12))

                Text(pet.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedPet?.id == pet.id ? .purple : .primary)
                    .lineLimit(1)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(selectedPet?.id == pet.id ? Color.purple.opacity(0.1) : Color(.systemBackground))
                    .stroke(selectedPet?.id == pet.id ? Color.purple : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Tab Selector Section

    private var tabSelectorSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 0) {
                ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                    Button(action: { selectedTab = index }) {
                        VStack(spacing: 8) {
                            Text(tab)
                                .font(.subheadline)
                                .fontWeight(selectedTab == index ? .semibold : .medium)
                                .foregroundColor(selectedTab == index ? .purple : .secondary)

                            Rectangle()
                                .fill(selectedTab == index ? Color.purple : Color.clear)
                                .frame(height: 2)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    // MARK: - Health Overview Tab

    private var healthOverviewTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                if let pet = selectedPet {
                    // Overall Health Score
                    healthScoreCard(pet: pet)

                    // Health Metrics
                    healthMetricsGrid(pet: pet)

                    // Recent Health Alerts
                    healthAlertsSection

                    // Vaccination Status
                    vaccinationStatusSection
                } else {
                    selectPetPrompt
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private func healthScoreCard(pet: Pet) -> some View {
        VStack(spacing: 16) {
            HStack {
                Text("Overall Health Score")
                    .font(.title3)
                    .fontWeight(.bold)

                Spacer()

                Text("Last updated: \(formatDate(pet.lastAIAnalysis ?? Date()))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // Health Score Circle
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.2), lineWidth: 12)
                    .frame(width: 120, height: 120)

                Circle()
                    .trim(from: 0, to: CGFloat(pet.healthScore))
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [.green, .yellow, .orange, .red]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ),
                        style: StrokeStyle(lineWidth: 12, lineCap: .round)
                    )
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: animateContent)

                VStack {
                    Text("\(Int(pet.healthScore * 100))")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("Health Score")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Text(getHealthScoreDescription(score: pet.healthScore))
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }

    private func healthMetricsGrid(pet: Pet) -> some View {
        LazyVGrid(
            columns: [GridItem(.flexible()), GridItem(.flexible())],
            spacing: 16
        ) {
            HealthMetricCard(
                icon: "scalemass.fill",
                label: "Weight",
                value: "\(String(format: "%.1f", pet.weight ?? 0)) kg",
                color: .blue
            )
            HealthMetricCard(
                icon: "flame.fill",
                label: "Calories",
                value: "\(pet.dailyCalories) kcal",
                color: .orange
            )
            HealthMetricCard(
                icon: "bolt.heart.fill",
                label: "Activity",
                value: "\(pet.activityLevel.capitalized)",
                color: .green
            )
            HealthMetricCard(
                icon: "pills.fill",
                label: "Meds",
                value: "\(pet.medications.count) active",
                color: .red
            )
        }
    }

    private var healthAlertsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Health Alerts")
                .font(.title3)
                .fontWeight(.bold)

            if let pet = selectedPet, !pet.healthAlerts.isEmpty {
                ForEach(pet.healthAlerts, id: \.id) { alert in
                    HealthAlertCard(alert: alert)
                }
            } else {
                noDataCard(message: "No current health alerts. Great job!")
            }
        }
    }

    private var vaccinationStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Vaccination Status")
                .font(.title3)
                .fontWeight(.bold)

            if let pet = selectedPet, !pet.vaccinations.isEmpty {
                VaccinationCard(
                    lastVaccine: pet.vaccinations.last ?? "N/A",
                    nextDue: "Next: Rabies (1 yr)"
                )
            } else {
                noDataCard(message: "No vaccination records found. Add them now!")
            }
        }
    }

    private var selectPetPrompt: some View {
        VStack(spacing: 20) {
            Image(systemName: "pawprint.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.purple)
            Text("Select a Pet")
                .font(.title2)
                .fontWeight(.bold)
            Text("Choose a pet to view their detailed health information and get AI-powered insights.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
    
    private func noDataCard(message: String) -> some View {
        Text(message)
            .font(.subheadline)
            .foregroundColor(.secondary)
            .frame(maxWidth: .infinity, alignment: .center)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
            )
    }

    // MARK: - Other Tabs (Placeholders)

    private var nutritionTab: some View {
        Text("Nutrition Tab")
            .font(.largeTitle)
    }

    private var behaviorTab: some View {
        Text("Behavior Tab")
            .font(.largeTitle)
    }

    private var insightsTab: some View {
        Text("Insights Tab")
            .font(.largeTitle)
    }

    // MARK: - Helper Methods

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    private func getHealthScoreDescription(score: Double) -> String {
        switch score {
        case 0.90...1.0:
            return "Excellent health! Your pet is thriving."
        case 0.80..<0.90:
            return "Good health, with minor areas for improvement."
        case 0.70..<0.80:
            return "Fair health. Consider focusing on diet and exercise."
        case 0.60..<0.70:
            return "Health needs attention. Monitor closely and consult a vet."
        default:
            return "Poor health. Veterinary consultation is highly recommended."
        }
    }
}

// MARK: - Reusable Components

struct HealthMetricCard: View {
    let icon: String
    let label: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)

            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
        }
        .padding()
        .frame(maxWidth: .infinity, minHeight: 100)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 1)
        )
    }
}

struct HealthAlertCard: View {
    let alert: HealthAlert

    var body: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.orange)
            Text(alert.description)
                .font(.subheadline)
                .foregroundColor(.primary)
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.orange.opacity(0.1))
        )
    }
}

struct VaccinationCard: View {
    let lastVaccine: String
    let nextDue: String

    var body: some View {
        HStack {
            Image(systemName: "syringe.fill")
                .foregroundColor(.blue)

            VStack(alignment: .leading) {
                Text("Last: \(lastVaccine)")
                    .font(.subheadline)
                Text(nextDue)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Button("View All") {}
                .font(.caption)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
        )
    }
}

#if DEBUG
@available(iOS 18.0, *)
struct PetHealthView_Previews: PreviewProvider {
    static var previews: some View {
        if #available(iOS 18.0, *) {
            PetHealthView()
                .environmentObject(RealDataService())
        } else {
            Text("iOS 18.0+ Required")
        }
    }
}
#endif
