//
//  PetInsuranceView.swift
//  PetCapsule
//
//  Pet insurance information and management
//

import SwiftUI

struct PetInsuranceView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var insuranceService = PetInsuranceService.shared
    @State private var selectedTab = 0
    @State private var animateItems = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Picker
                Picker("Insurance Tab", selection: $selectedTab) {
                    Text("Plans").tag(0)
                    Text("My Policy").tag(1)
                    Text("Claims").tag(2)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                // Content
                TabView(selection: $selectedTab) {
                    insurancePlansView
                        .tag(0)
                    
                    myPolicyView
                        .tag(1)
                    
                    claimsView
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Pet Insurance")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateItems = true
                }
            }
        }
    }
    
    // MARK: - Insurance Plans View
    
    private var insurancePlansView: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Header
                VStack(spacing: 12) {
                    Text("🛡️ Protect Your Pet")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Compare pet insurance plans to find the best coverage for your furry friend")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
                .padding(.top)
                
                // Insurance Plans
                ForEach(Array(insuranceService.availablePlans.enumerated()), id: \.element.id) { index, plan in
                    insurancePlanCard(plan: plan)
                        .scaleEffect(animateItems ? 1.0 : 0.9)
                        .opacity(animateItems ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateItems)
                }
                
                // Why Pet Insurance Section
                whyInsuranceSection
            }
            .padding()
        }
    }
    
    private func insurancePlanCard(plan: InsurancePlan) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(plan.name)
                        .font(.title3)
                        .fontWeight(.bold)
                    
                    Text(plan.provider)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("$\(Int(plan.monthlyPrice))")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    
                    Text("per month")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Coverage Details
            VStack(alignment: .leading, spacing: 8) {
                Text("Coverage")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(alignment: .leading, spacing: 4) {
                    coverageItem("Annual Limit", "$\(plan.annualLimit)")
                    coverageItem("Deductible", "$\(plan.deductible)")
                    coverageItem("Reimbursement", "\(Int(plan.reimbursementPercentage))%")
                }
            }
            
            // Features
            VStack(alignment: .leading, spacing: 8) {
                Text("What's Covered")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 4) {
                    ForEach(plan.features, id: \.self) { feature in
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                            Text(feature)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                    }
                }
            }
            
            // Action Button
            Button("Get Quote") {
                openInsuranceWebsite(plan.websiteURL)
            }
            .font(.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.blue)
            .cornerRadius(12)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
    
    private func coverageItem(_ title: String, _ value: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
    
    private var whyInsuranceSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Why Pet Insurance?")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 12) {
                benefitItem(
                    icon: "dollarsign.circle.fill",
                    title: "Save on Vet Bills",
                    description: "Get reimbursed for covered treatments and procedures"
                )
                
                benefitItem(
                    icon: "heart.circle.fill",
                    title: "Peace of Mind",
                    description: "Focus on your pet's health, not the cost"
                )
                
                benefitItem(
                    icon: "stethoscope",
                    title: "Better Care",
                    description: "Access to specialists and advanced treatments"
                )
                
                benefitItem(
                    icon: "clock.circle.fill",
                    title: "Emergency Ready",
                    description: "Be prepared for unexpected accidents and illnesses"
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.blue.opacity(0.1))
        )
    }
    
    private func benefitItem(icon: String, title: String, description: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
    
    // MARK: - My Policy View
    
    private var myPolicyView: some View {
        ScrollView {
            VStack(spacing: 24) {
                if let policy = insuranceService.currentPolicy {
                    // Current Policy
                    currentPolicyCard(policy: policy)
                    
                    // Quick Actions
                    policyActionsSection
                    
                    // Policy Documents
                    policyDocumentsSection
                } else {
                    // No Policy State
                    noPolicyView
                }
            }
            .padding()
        }
    }
    
    private func currentPolicyCard(policy: InsurancePolicy) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Current Policy")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Text(policy.status.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(policy.status.color)
                    .cornerRadius(8)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text(policy.planName)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(policy.provider)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Divider()
            
            VStack(spacing: 8) {
                policyDetailRow("Policy Number", policy.policyNumber)
                policyDetailRow("Monthly Premium", "$\(Int(policy.monthlyPremium))")
                policyDetailRow("Deductible", "$\(policy.deductible)")
                policyDetailRow("Annual Limit", "$\(policy.annualLimit)")
                policyDetailRow("Next Payment", formatDate(policy.nextPaymentDate))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
    
    private func policyDetailRow(_ title: String, _ value: String) -> some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
    
    private var policyActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 8) {
                actionButton("Submit Claim", icon: "doc.text.fill", color: .blue) {
                    // Open claim submission
                }
                
                actionButton("View Coverage", icon: "list.bullet", color: .green) {
                    // Show coverage details
                }
                
                actionButton("Contact Support", icon: "phone.fill", color: .orange) {
                    // Contact insurance support
                }
                
                actionButton("Update Policy", icon: "pencil", color: .purple) {
                    // Update policy details
                }
            }
        }
    }
    
    private func actionButton(_ title: String, icon: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }
    
    private var policyDocumentsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Documents")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 8) {
                documentItem("Policy Certificate", "PDF • 2.1 MB")
                documentItem("Coverage Summary", "PDF • 1.5 MB")
                documentItem("Claims History", "PDF • 890 KB")
            }
        }
    }
    
    private func documentItem(_ title: String, _ details: String) -> some View {
        HStack {
            Image(systemName: "doc.fill")
                .foregroundColor(.red)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text(details)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("View") {
                // Open document
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    private var noPolicyView: some View {
        VStack(spacing: 24) {
            Image(systemName: "shield.slash")
                .font(.system(size: 64))
                .foregroundColor(.gray)
            
            VStack(spacing: 12) {
                Text("No Active Policy")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Protect your pet with comprehensive insurance coverage")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            Button("Browse Insurance Plans") {
                selectedTab = 0
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(Color.blue)
            .cornerRadius(12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Claims View
    
    private var claimsView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if insuranceService.claims.isEmpty {
                    noClaimsView
                } else {
                    ForEach(insuranceService.claims) { claim in
                        claimCard(claim: claim)
                    }
                }
            }
            .padding()
        }
    }
    
    private var noClaimsView: some View {
        VStack(spacing: 24) {
            Image(systemName: "doc.text")
                .font(.system(size: 64))
                .foregroundColor(.gray)
            
            VStack(spacing: 12) {
                Text("No Claims Yet")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("When you need to submit a claim, it will appear here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            Button("Learn About Claims") {
                // Show claims information
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(Color.blue)
            .cornerRadius(12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func claimCard(claim: InsuranceClaim) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Claim #\(claim.claimNumber)")
                    .font(.headline)
                    .fontWeight(.bold)
                
                Spacer()
                
                Text(claim.status.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(claim.status.color)
                    .cornerRadius(8)
            }
            
            Text(claim.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack {
                Text("Amount: $\(Int(claim.amount))")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text(formatDate(claim.submittedDate))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
    
    // MARK: - Helper Methods
    
    private func openInsuranceWebsite(_ url: String) {
        if let url = URL(string: url) {
            UIApplication.shared.open(url)
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
}

#Preview {
    PetInsuranceView()
}
