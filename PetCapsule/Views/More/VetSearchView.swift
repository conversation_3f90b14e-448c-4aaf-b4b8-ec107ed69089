//
//  VetSearchView.swift
//  PetCapsule
//
//  Search and manage veterinarian contacts
//  ✅ Updated to match main page UX/UI design patterns
//

import SwiftUI
import MapKit
import CoreLocation

struct VetSearchView: View {
    let selectedPet: Pet?
    
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var vetSearchService = VetSearchService.shared
    @StateObject private var locationManager = LocationManager()
    @State private var searchText = ""
    @State private var selectedFilter: VetFilter = .all
    @State private var nearbyVets: [VeterinaryClinic] = []
    @State private var isLoading = false
    @State private var showingMap = false
    @State private var selectedVet: VeterinaryClinic?
    @State private var showingVetDetail = false
    @State private var animateCards = false
    @State private var region = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194),
        span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
    )
    
    var filteredVets: [VeterinaryClinic] {
        let filtered = nearbyVets.filter { vet in
            if !searchText.isEmpty {
                return vet.name.localizedCaseInsensitiveContains(searchText) ||
                       vet.address.localizedCaseInsensitiveContains(searchText)
            }
            return true
        }
        
        switch selectedFilter {
        case .all:
            return filtered
        case .emergency:
            return filtered.filter { $0.is24Hour }
        case .specialty:
            return filtered.filter { !$0.specialties.isEmpty }
        case .nearby:
            return filtered.filter { $0.distance <= 5.0 }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Clean Header
                    cleanHeader
                    
                    // Emergency Button
                    emergencyButton
                    
                    // Search Section
                    searchSection
                    
                    // Filter Chips
                    filterChips
                    
                    // View Toggle
                    viewToggleSection
                    
                    // Content
                    if showingMap {
                        mapView
                    } else {
                        listView
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .background(colorScheme == .dark ? Color.black : Color(.systemGroupedBackground))
            .navigationTitle("Find Veterinarian")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateCards = true
                }
                loadNearbyVets()
            }
            .sheet(isPresented: $showingVetDetail) {
                if let vet = selectedVet {
                    VetDetailView(vet: vet)
                }
            }
        }
    }
    
    // MARK: - Clean Header (Following Main Page Pattern)
    
    private var cleanHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    if let pet = selectedPet {
                        Text("Finding care for \(pet.name)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    } else {
                        Text("Find nearby veterinary clinics and emergency care")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                Spacer()
                
                Image(systemName: "stethoscope")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(.red)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    // MARK: - Emergency Button (Prominent)
    
    private var emergencyButton: some View {
        Button(action: call911) {
            HStack(spacing: 16) {
                // Emergency Icon
                ZStack {
                    Circle()
                        .fill(Color.red)
                        .frame(width: 50, height: 50)
                        .shadow(color: .black.opacity(0.2), radius: 6, x: 0, y: 3)
                    
                    Image(systemName: "phone.fill")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                // Emergency Text
                VStack(alignment: .leading, spacing: 4) {
                    Text("🚨 EMERGENCY")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                    
                    Text("Call 911 for immediate help")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Arrow indicator
                Image(systemName: "chevron.right")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.red)
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16, style: .continuous)
                    .fill(Color.red.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16, style: .continuous)
                            .stroke(Color.red.opacity(0.3), lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }
    
    // MARK: - Search Section (Updated Design)
    
    private var searchSection: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
                .frame(width: 16, height: 16)
            
            TextField("Search vets, clinics, or specialties", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button("Clear") {
                    searchText = ""
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)
    }
    
    // MARK: - Filter Chips (Updated Design)
    
    private var filterChips: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(VetFilter.allCases, id: \.self) { filter in
                    filterChip(filter)
                }
            }
            .padding(.horizontal, 4)
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateCards)
    }
    
    private func filterChip(_ filter: VetFilter) -> some View {
        Button(action: {
            selectedFilter = filter
        }) {
            HStack(spacing: 6) {
                Image(systemName: filter.icon)
                    .font(.caption)
                
                Text(filter.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                if filter == .emergency {
                    Text("\(nearbyVets.filter { $0.is24Hour }.count)")
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.red)
                        .cornerRadius(4)
                }
            }
            .foregroundColor(selectedFilter == filter ? .white : filter.color)
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 25, style: .continuous)
                    .fill(selectedFilter == filter ? filter.color : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 25, style: .continuous)
                            .stroke(selectedFilter == filter ? filter.color : Color(.systemGray4), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - View Toggle Section (Updated Design)
    
    private var viewToggleSection: some View {
        HStack(spacing: 0) {
            Button(action: { showingMap = false }) {
                HStack(spacing: 8) {
                    Image(systemName: "list.bullet")
                        .font(.subheadline)
                    Text("List")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(showingMap ? .secondary : .white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(showingMap ? Color(.systemGray6) : .blue)
                .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))
            }
            .buttonStyle(PlainButtonStyle())
            
            Button(action: { showingMap = true }) {
                HStack(spacing: 8) {
                    Image(systemName: "map")
                        .font(.subheadline)
                    Text("Map")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(!showingMap ? .secondary : .white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(!showingMap ? Color(.systemGray6) : .blue)
                .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))
            }
            .buttonStyle(PlainButtonStyle())
        }
        .background(
            RoundedRectangle(cornerRadius: 12, style: .continuous)
                .stroke(.quaternary, lineWidth: 1)
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateCards)
    }
    
    // MARK: - List View
    private var listView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                if isLoading {
                    ProgressView("Finding nearby vets...")
                        .padding()
                } else if filteredVets.isEmpty {
                    emptyStateView
                } else {
                    ForEach(filteredVets, id: \.id) { vet in
                        vetListCard(vet)
                    }
                }
            }
            .padding()
        }
    }
    
    private func vetListCard(_ vet: VeterinaryClinic) -> some View {
        Button(action: {
            selectedVet = vet
            showingVetDetail = true
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with name and status
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(vet.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                        
                        Text(vet.address)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        if vet.is24Hour {
                            Text("24/7")
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.green)
                                .cornerRadius(6)
                        }
                        
                        Text("\(String(format: "%.1f", vet.distance)) mi")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Specialties
                if !vet.specialties.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 6) {
                            ForEach(vet.specialties.prefix(3), id: \.self) { specialty in
                                Text(specialty)
                                    .font(.caption2)
                                    .foregroundColor(.blue)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }
                    }
                }
                
                // Actions row
                HStack {
                    // Call button
                    Button(action: {
                        callVet(vet)
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "phone.fill")
                                .font(.caption)
                            Text("Call")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue)
                        .cornerRadius(8)
                    }
                    
                    // Directions button
                    Button(action: {
                        openDirections(to: vet)
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "location.fill")
                                .font(.caption)
                            Text("Directions")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.blue)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                    }
                    
                    Spacer()
                    
                    // Rating
                    if vet.rating > 0 {
                        HStack(spacing: 4) {
                            Image(systemName: "star.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text("\(String(format: "%.1f", vet.rating))")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(radius: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Map View
    private var mapView: some View {
        Map(coordinateRegion: $region, annotationItems: filteredVets) { vet in
            MapAnnotation(coordinate: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194)) {
                VetSearchMapMarker(vet: vet) {
                    selectedVet = vet
                    showingVetDetail = true
                }
            }
        }
        .onAppear {
            if let location = locationManager.currentLocation {
                region.center = location.coordinate
            }
        }
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "location.magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("No Veterinarians Found")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Try adjusting your search or filters, or expand your search radius.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Search Wider Area") {
                // Expand search radius
                loadNearbyVets(radius: 50000) // 50km
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .padding(.vertical, 40)
    }
    
    // MARK: - Actions
    private func loadNearbyVets(radius: Double = 25000) {
        guard let location = locationManager.currentLocation else { return }
        
        isLoading = true
        
        Task {
            do {
                let vets = try await vetSearchService.findNearbyVets(
                    location: location.coordinate,
                    radius: radius,
                    emergencyOnly: false
                )
                
                await MainActor.run {
                    self.nearbyVets = vets
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    print("❌ Failed to load nearby vets: \(error)")
                }
            }
        }
    }
    
    private func callVet(_ vet: VeterinaryClinic) {
        let phoneNumber = vet.phoneNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        if let url = URL(string: "tel://\(phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openDirections(to vet: VeterinaryClinic) {
        let address = vet.address.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        if let url = URL(string: "http://maps.apple.com/?q=\(address)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func call911() {
        if let url = URL(string: "tel://911") {
            UIApplication.shared.open(url)
        }
    }
}

// MARK: - Supporting Views

struct VetSearchMapMarker: View {
    let vet: VeterinaryClinic
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                Image(systemName: vet.is24Hour ? "cross.circle.fill" : "cross.circle")
                    .font(.title2)
                    .foregroundColor(vet.is24Hour ? .red : .blue)
                    .background(Color.white)
                    .clipShape(Circle())
                
                Text(vet.name)
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.white.opacity(0.9))
                    .cornerRadius(4)
            }
        }
    }
}

struct VetDetailViewFromSearch: View {
    let vet: VeterinaryClinic
    let selectedPet: Pet?
    
    @Environment(\.dismiss) private var dismiss
    @State private var showingCallConfirmation = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header with main info
                    vetHeaderSection
                    
                    // Quick actions
                    quickActionsSection
                    
                    // Specialties
                    if !vet.specialties.isEmpty {
                        specialtiesSection
                    }
                    
                    // Pet information to share
                    if let pet = selectedPet {
                        petInfoSection(pet)
                    }
                    
                    // Additional info
                    additionalInfoSection
                }
                .padding()
            }
            .navigationTitle(vet.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
        .alert("Call \(vet.name)?", isPresented: $showingCallConfirmation) {
            Button("Call Now") {
                callVet()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This will call \(vet.phoneNumber)")
        }
    }
    
    private var vetHeaderSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(vet.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(vet.address)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    if vet.is24Hour {
                        Text("24/7 EMERGENCY")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.red)
                            .cornerRadius(6)
                    }
                    
                    Text("\(String(format: "%.1f", vet.distance)) miles away")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if vet.rating > 0 {
                        HStack(spacing: 2) {
                            Image(systemName: "star.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text("\(String(format: "%.1f", vet.rating))")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    private var quickActionsSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                // Call button
                Button(action: {
                    showingCallConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "phone.fill")
                            .font(.title3)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Call Now")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            Text(vet.phoneNumber)
                                .font(.caption)
                        }
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.red)
                    .cornerRadius(12)
                }
                
                // Directions button
                Button(action: {
                    openDirections()
                }) {
                    HStack {
                        Image(systemName: "location.fill")
                            .font(.title3)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Directions")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            Text("Get directions")
                                .font(.caption)
                        }
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
                }
            }
            
            // Share pet info button
            if selectedPet != nil {
                Button(action: {
                    sharePetInfo()
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                            .font(.title3)
                        
                        Text("Share Pet Information")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                }
            }
        }
    }
    
    private var specialtiesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Specialties")
                .font(.headline)
                .fontWeight(.bold)
            
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 8) {
                ForEach(vet.specialties, id: \.self) { specialty in
                    Text(specialty)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    private func petInfoSection(_ pet: Pet) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Pet Information to Share")
                .font(.headline)
                .fontWeight(.bold)
            
            Text(pet.quickMedicalSummary)
                .font(.subheadline)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    private var additionalInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Additional Information")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 8) {
                if vet.is24Hour {
                    infoRow("Hours", "24/7 Emergency Care")
                } else {
                    infoRow("Hours", "Call for current hours")
                }
                
                infoRow("Phone", vet.phoneNumber)
                infoRow("Distance", "\(String(format: "%.1f", vet.distance)) miles")
                
                if vet.rating > 0 {
                    infoRow("Rating", "\(String(format: "%.1f", vet.rating)) stars")
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    private func infoRow(_ label: String, _ value: String) -> some View {
        HStack {
            Text(label + ":")
                .font(.subheadline)
                .fontWeight(.medium)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
    
    private func callVet() {
        let phoneNumber = vet.phoneNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        if let url = URL(string: "tel://\(phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openDirections() {
        let address = vet.address.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        if let url = URL(string: "http://maps.apple.com/?q=\(address)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func sharePetInfo() {
        guard let pet = selectedPet else { return }
        
        let message = """
        Emergency Pet Information for \(vet.name):
        
        \(pet.quickMedicalSummary)
        
        Emergency Contact: \(pet.emergencyContactInfo)
        """
        
        if let url = URL(string: "sms:?body=\(message.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") {
            UIApplication.shared.open(url)
        }
    }
}

// MARK: - Supporting Types

enum VetFilter: CaseIterable {
    case all
    case emergency
    case specialty
    case nearby
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .emergency: return "24/7"
        case .specialty: return "Specialty"
        case .nearby: return "Nearby"
        }
    }
    
    var icon: String {
        switch self {
        case .all: return "list.bullet"
        case .emergency: return "cross.circle.fill"
        case .specialty: return "stethoscope"
        case .nearby: return "location.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .all: return .blue
        case .emergency: return .red
        case .specialty: return .purple
        case .nearby: return .green
        }
    }
}

#Preview {
    VetSearchView(selectedPet: Pet.samplePet)
}
