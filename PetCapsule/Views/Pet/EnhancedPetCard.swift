//
//  EnhancedPetCard.swift
//  PetCapsule
//
//  Created by Magic MCP Integration
//

import SwiftUI

// MARK: - Enhanced Pet Card with 21st.dev Magic MCP Design
struct EnhancedPetCard: View {
    let pet: Pet
    let onTap: () -> Void
    @State private var isPressed = false
    @State private var animateHealthScore = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 0) {
                // Header with gradient background
                headerSection
                
                // Content section
                contentSection
                
                // Footer with actions
                footerSection
            }
            .background(cardBackground)
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .shadow(color: .black.opacity(0.1), radius: 15, x: 0, y: 8)
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
        .onAppear {
            withAnimation(.spring(response: 1.0, dampingFraction: 0.7).delay(0.2)) {
                animateHealthScore = true
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color.purple.opacity(0.8),
                    Color.blue.opacity(0.6),
                    Color.cyan.opacity(0.4)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .frame(height: 120)
            
            // Floating elements
            HStack {
                // Pet image with enhanced styling
                petImageView
                
                Spacer()
                
                // Subscription badge
                if true { // Always show premium features
                    subscriptionBadge
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
        }
    }
    
    // MARK: - Pet Image View
    private var petImageView: some View {
        ZStack {
            // Glow effect
            Circle()
                .fill(
                    RadialGradient(
                        colors: [Color.white.opacity(0.3), Color.clear],
                        center: .center,
                        startRadius: 0,
                        endRadius: 50
                    )
                )
                .frame(width: 90, height: 90)
                .blur(radius: 10)
            
            // Main image container
            AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                ZStack {
                    Circle()
                        .fill(Color.white.opacity(0.9))
                    
                    VStack(spacing: 4) {
                        Text(petEmoji(for: pet.species))
                            .font(.system(size: 28))
                        
                        Text(pet.name.prefix(1).uppercased())
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.purple)
                    }
                }
            }
            .frame(width: 70, height: 70)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(Color.white, lineWidth: 3)
            )
            .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
        }
    }
    
    // MARK: - Subscription Badge
    private var subscriptionBadge: some View {
        HStack(spacing: 4) {
            Image(systemName: "crown.fill")
                .font(.caption2)
                .foregroundColor(.orange)
            
            Text("PREMIUM")
                .font(.caption2)
                .fontWeight(.bold)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(Color.black.opacity(0.3))
        )
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(spacing: 16) {
            // Pet name and basic info
            petInfoSection
            
            // Health score with animated progress
            healthScoreSection
            
            // Stats row
            statsSection
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Pet Info Section
    private var petInfoSection: some View {
        VStack(spacing: 8) {
            Text(pet.name)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            HStack {
                Text(pet.breed ?? "Mixed")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text("•")
                    .foregroundColor(.secondary)
                
                Text("\(pet.age) years old")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Health Score Section
    private var healthScoreSection: some View {
        VStack(spacing: 8) {
            Text("Health Score")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            ZStack {
                // Background circle
                Circle()
                    .stroke(Color.gray.opacity(0.2), lineWidth: 8)
                    .frame(width: 80, height: 80)
                
                // Progress circle
                Circle()
                    .trim(from: 0, to: animateHealthScore ? CGFloat(85) : 0) // Default health score
                    .stroke(
                        LinearGradient(
                            colors: [healthScoreColor(85), healthScoreColor(85).opacity(0.6)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(-90))
                    .animation(.spring(response: 1.5, dampingFraction: 0.8), value: animateHealthScore)
                
                // Score text
                VStack(spacing: 2) {
                    Text("\(Int(pet.healthScore * 100))")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(healthScoreColor(pet.healthScore))
                    
                    Text("%")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // MARK: - Stats Section
    private var statsSection: some View {
        HStack(spacing: 20) {
            statItem(
                icon: "photo.fill",
                value: "12", // Default memory count
                label: "Memories",
                color: .blue
            )
            
            statItem(
                icon: "heart.fill",
                value: "\(pet.healthAlerts.count)",
                label: "Alerts",
                color: pet.healthAlerts.isEmpty ? .green : .orange
            )
            
            statItem(
                icon: "brain.head.profile",
                value: "\(pet.aiRecommendations.count)",
                label: "AI Tips",
                color: .purple
            )
        }
    }
    
    private func statItem(icon: String, value: String, label: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(color)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - Footer Section
    private var footerSection: some View {
        HStack {
            if !pet.aiRecommendations.isEmpty {
                HStack(spacing: 6) {
                    Image(systemName: "sparkles")
                        .font(.caption)
                        .foregroundColor(.purple)
                    
                    Text("AI: \(pet.aiRecommendations.first!)")
                        .font(.caption)
                        .foregroundColor(.purple)
                        .lineLimit(1)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(Color.purple.opacity(0.1))
                )
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 16)
    }
    
    // MARK: - Card Background
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(Color(.systemBackground))
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(Color.gray.opacity(0.1), lineWidth: 1)
            )
    }
    
    // MARK: - Helper Functions
    private func petEmoji(for species: String) -> String {
        switch species.lowercased() {
        case "dog": return "🐕"
        case "cat": return "🐱"
        case "bird": return "🦜"
        case "rabbit": return "🐰"
        case "hamster": return "🐹"
        case "fish": return "🐠"
        case "reptile": return "🦎"
        default: return "🐾"
        }
    }
    
    private func healthScoreColor(_ score: Double) -> Color {
        switch score {
        case 0.9...1.0: return .green
        case 0.8..<0.9: return .mint
        case 0.7..<0.8: return .yellow
        case 0.6..<0.7: return .orange
        default: return .red
        }
    }
}

// MARK: - Preview
#Preview {
    EnhancedPetCard(
        pet: Pet(
            name: "Buddy",
            species: "dog",
            breed: "Golden Retriever",
            age: 3,
            // Remove ownerID parameter
        ),
        onTap: {}
    )
    .padding()
}
