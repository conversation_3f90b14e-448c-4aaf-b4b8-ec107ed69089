//
//  EnhancedPetCreationView.swift
//  PetCapsule
//
//  Comprehensive pet profile creation with health, nutrition, exercise, and training tabs
//
import SwiftUI
import PhotosUI
@available(iOS 18.0, *)
struct EnhancedPetCreationView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var authService: AuthenticationService
    @EnvironmentObject var realDataService: RealDataService
    // Tab selection
    @State private var selectedTab = 0
    // Basic Info
    @State private var name = ""
    @State private var species = "dog"
    @State private var breed = ""
    @State private var customBreed = ""
    @State private var age = 0
    @State private var dateOfBirth = Date()
    @State private var bio = ""
    @State private var gender = "unknown"
    @State private var isSpayedNeutered = false
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var profileImage: UIImage?
    @State private var useDateOfBirth = false
    // Health Info
    @State private var weight = ""
    @State private var microchipId = ""
    @State private var vetName = ""
    @State private var vetContact = ""
    @State private var emergencyVetName = ""
    @State private var emergencyVetContact = ""
    @State private var allergies: [String] = []
    @State private var medications: [String] = []
    @State private var medicalConditions: [String] = []
    @State private var vaccinations: [String] = []
    @State private var nextVaccinationDate = Date()
    @State private var lastCheckupDate = Date()
    @State private var bloodType = ""
    @State private var insuranceProvider = ""
    @State private var insurancePolicyNumber = ""
    // Nutrition Info
    @State private var currentFood = ""
    @State private var foodBrand = ""
    @State private var feedingSchedule = "twice_daily"
    @State private var foodAllergies: [String] = []
    @State private var dietaryRestrictions: [String] = []
    @State private var treatPreferences: [String] = []
    @State private var waterIntake = "normal"
    @State private var specialDiet = ""
    // Exercise Info
    @State private var activityLevel = "moderate"
    @State private var dailyExerciseMinutes = 30
    @State private var favoriteActivities: [String] = []
    @State private var walkingFrequency = "daily"
    @State private var exerciseRestrictions: [String] = []
    @State private var playPreferences: [String] = []
    // Training Info
    @State private var trainingLevel = "beginner"
    @State private var knownCommands: [String] = []
    @State private var behaviorIssues: [String] = []
    @State private var trainingGoals: [String] = []
    @State private var socialWithDogs = "friendly"
    @State private var socialWithCats = "friendly"
    @State private var socialWithPeople = "friendly"
    @State private var socialWithChildren = "friendly"
    // UI State
    @State private var isCreating = false
    @State private var showingError = false
    @State private var errorMessage = ""
    private let speciesOptions = ["dog", "cat", "bird", "rabbit", "hamster", "fish", "reptile", "other"]
    private let genderOptions = ["male", "female", "unknown"]
    private let activityLevels = ["low", "moderate", "high", "very_high"]
    private let feedingSchedules = ["once_daily", "twice_daily", "three_times_daily", "free_feeding"]
    private let walkingFrequencies = ["daily", "twice_daily", "every_other_day", "weekly"]
    private let trainingLevels = ["beginner", "intermediate", "advanced", "expert"]
    private let socialLevels = ["friendly", "cautious", "aggressive", "unknown"]
    private var speciesBreeds: [String] {
        PetBreedData.getBreeds(for: species)
    }
    private var tabs = ["Basic", "Health", "Nutrition", "Exercise", "Training"]
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Navigation
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(0..<tabs.count, id: \.self) { index in
                            Button(action: {
                                selectedTab = index
                            }) {
                                VStack(spacing: 8) {
                                    tabIcon(for: index)
                                        .font(.title2)
                                        .foregroundColor(selectedTab == index ? .white : .secondary)
                                    Text(tabs[index])
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(selectedTab == index ? .white : .secondary)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(selectedTab == index ? Color.blue : Color.clear)
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.horizontal)
                }
                .background(Color(.systemGroupedBackground))
                // Tab Content
                TabView(selection: $selectedTab) {
                    ForEach(0..<tabs.count, id: \.self) { index in
                        tabContent(for: index)
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Tell Us About Your Pet")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        createPetProfile()
                    }
                    .disabled(!canCreate || isCreating)
                }
            }
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .overlay {
            if isCreating {
                ZStack {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.5)
                        Text("Creating your pet profile...")
                            .font(.subheadline)
                            .foregroundColor(.white)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemBackground))
                            .shadow(radius: 10)
                    )
                }
            }
        }
    }
    private func tabIcon(for index: Int) -> Image {
        switch index {
        case 0: return Image(systemName: "heart.fill")
        case 1: return Image(systemName: "cross.fill")
        case 2: return Image(systemName: "leaf.fill")
        case 3: return Image(systemName: "figure.walk")
        case 4: return Image(systemName: "brain.head.profile")
        default: return Image(systemName: "questionmark")
        }
    }
    @ViewBuilder
    private func tabContent(for index: Int) -> some View {
        switch index {
        case 0: basicInfoTab
        case 1: healthInfoTab
        case 2: nutritionInfoTab
        case 3: exerciseInfoTab
        case 4: trainingInfoTab
        default: EmptyView()
        }
    }
    // MARK: - Basic Info Tab
    private var basicInfoTab: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Profile Photo Section
                profilePhotoSection
                // Basic Information
                basicInfoSection
                // Age Section
                ageSection
                // Gender & Spay/Neuter
                genderSection
                // Bio Section
                bioSection
                Spacer(minLength: 24)
            }
            .padding()
        }
    }
    private var profilePhotoSection: some View {
        VStack(spacing: 16) {
            PhotosPicker(
                selection: $selectedPhoto,
                matching: .images,
                photoLibrary: .shared()
            ) {
                if let profileImage = profileImage {
                    Image(uiImage: profileImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 120, height: 120)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.blue, lineWidth: 3)
                        )
                } else {
                    Circle()
                        .fill(Color(.systemGray6))
                        .frame(width: 120, height: 120)
                        .overlay(
                            VStack(spacing: 8) {
                                Image(systemName: "camera.fill")
                                    .font(.title)
                                    .foregroundColor(.blue)
                                Text("Add Photo")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        )
                        .overlay(
                            Circle()
                                .stroke(Color.blue.opacity(0.3), lineWidth: 2)
                        )
                }
            }
            .onChange(of: selectedPhoto) { _, newValue in
                Task {
                    if let newValue = newValue {
                        if let data = try? await newValue.loadTransferable(type: Data.self) {
                            profileImage = UIImage(data: data)
                        }
                    }
                }
            }
            Text("Tap to add a photo of your pet")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    private var basicInfoSection: some View {
        VStack(spacing: 16) {
            // Name Input
            VStack(alignment: .leading, spacing: 8) {
                Text("Pet Name *")
                    .font(.headline)
                    .foregroundColor(.primary)
                TextField("Enter your pet's name", text: $name)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            // Species Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("Species *")
                    .font(.headline)
                    .foregroundColor(.primary)
                Menu {
                    ForEach(speciesOptions, id: \.self) { speciesOption in
                        Button(action: {
                            species = speciesOption
                            breed = ""
                            customBreed = ""
                        }) {
                            HStack {
                                Text(PetBreedData.getSpeciesIcon(for: speciesOption))
                                Text(speciesOption.capitalized)
                            }
                        }
                    }
                } label: {
                    HStack {
                        Text(PetBreedData.getSpeciesIcon(for: species))
                        Text(species.capitalized)
                        Spacer()
                        Image(systemName: "chevron.down")
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
            }
            // Breed Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("Breed")
                    .font(.headline)
                    .foregroundColor(.primary)
                Menu {
                    ForEach(speciesBreeds, id: \.self) { breedOption in
                        Button(breedOption) {
                            breed = breedOption
                            if breedOption != "Other" {
                                customBreed = ""
                            }
                        }
                    }
                } label: {
                    HStack {
                        Text(breed.isEmpty ? "Select breed" : (breed == "Other" ? (customBreed.isEmpty ? "Other (enter custom breed)" : customBreed) : breed))
                            .foregroundColor(breed.isEmpty ? .secondary : .primary)
                        Spacer()
                        Image(systemName: "chevron.down")
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
                if breed == "Other" {
                    TextField("Enter custom breed", text: $customBreed)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
            }
        }
    }
    private var ageSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Age Information")
                .font(.headline)
                .foregroundColor(.primary)
            Toggle("Use date of birth", isOn: $useDateOfBirth)
                .toggleStyle(SwitchToggleStyle(tint: .blue))
            if useDateOfBirth {
                DatePicker(
                    "Date of Birth",
                    selection: $dateOfBirth,
                    in: ...Date(),
                    displayedComponents: .date
                )
                .datePickerStyle(CompactDatePickerStyle())
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Age (years)")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    Stepper(value: $age, in: 0...30) {
                        Text(age == 0 ? "Select age" : "\(age) year\(age == 1 ? "" : "s") old")
                            .font(.body)
                            .foregroundColor(age == 0 ? .secondary : .primary)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var genderSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Gender & Medical Status")
                .font(.headline)
                .foregroundColor(.primary)
            VStack(alignment: .leading, spacing: 8) {
                Text("Gender")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Picker("Gender", selection: $gender) {
                    ForEach(genderOptions, id: \.self) { option in
                        Text(option.capitalized).tag(option)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            Toggle("Spayed/Neutered", isOn: $isSpayedNeutered)
                .toggleStyle(SwitchToggleStyle(tint: .blue))
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var bioSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Bio (Optional)")
                .font(.headline)
                .foregroundColor(.primary)
            TextField("Tell us about your pet's personality, favorite activities, or special traits...", text: $bio, axis: .vertical)
                .lineLimit(3...6)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            Text("\(bio.count)/500")
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .trailing)
        }
    }
    // MARK: - Health Info Tab
    private var healthInfoTab: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Weight and Physical Info
                physicalInfoSection
                // Veterinary Information
                veterinaryInfoSection
                // Medical History
                medicalHistorySection
                // Vaccinations
                vaccinationsSection
                Spacer(minLength: 24)
            }
            .padding()
        }
    }
    private var physicalInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Physical Information")
                .font(.headline)
                .foregroundColor(.primary)
            VStack(alignment: .leading, spacing: 8) {
                Text("Weight (lbs)")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("Enter weight", text: $weight)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.decimalPad)
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Microchip ID")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("15-digit microchip number", text: $microchipId)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Blood Type")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("Blood type (if known)", text: $bloodType)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var veterinaryInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Veterinary Information")
                .font(.headline)
                .foregroundColor(.primary)
            VStack(alignment: .leading, spacing: 8) {
                Text("Primary Veterinarian")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("Vet name", text: $vetName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                TextField("Vet contact", text: $vetContact)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Emergency Veterinarian")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("Emergency vet name", text: $emergencyVetName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                TextField("Emergency vet contact", text: $emergencyVetContact)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Last Checkup Date")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                DatePicker("", selection: $lastCheckupDate, displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var medicalHistorySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Medical History")
                .font(.headline)
                .foregroundColor(.primary)
            // Allergies
            StringArrayInputSection(
                title: "Allergies",
                items: $allergies,
                placeholder: "Add allergy"
            )
            // Medications
            StringArrayInputSection(
                title: "Current Medications",
                items: $medications,
                placeholder: "Add medication"
            )
            // Medical Conditions
            StringArrayInputSection(
                title: "Medical Conditions",
                items: $medicalConditions,
                placeholder: "Add condition"
            )
            // Insurance Information
            VStack(alignment: .leading, spacing: 8) {
                Text("Insurance Information")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("Insurance provider", text: $insuranceProvider)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                TextField("Policy number", text: $insurancePolicyNumber)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var vaccinationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Vaccination Records")
                .font(.headline)
                .foregroundColor(.primary)
            StringArrayInputSection(
                title: "Received Vaccinations",
                items: $vaccinations,
                placeholder: "Add vaccination"
            )
            VStack(alignment: .leading, spacing: 8) {
                Text("Next Vaccination Due")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                DatePicker("", selection: $nextVaccinationDate, displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    // MARK: - Nutrition Info Tab
    private var nutritionInfoTab: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Current Food
                currentFoodSection
                // Feeding Schedule
                feedingScheduleSection
                // Dietary Restrictions
                dietaryRestrictionsSection
                Spacer(minLength: 24)
            }
            .padding()
        }
    }
    private var currentFoodSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Current Food")
                .font(.headline)
                .foregroundColor(.primary)
            VStack(alignment: .leading, spacing: 8) {
                Text("Food Type")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("e.g., Dry kibble, wet food, raw diet", text: $currentFood)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Brand")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("Food brand", text: $foodBrand)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Feeding Schedule")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Picker("Feeding Schedule", selection: $feedingSchedule) {
                    Text("Once daily").tag("once_daily")
                    Text("Twice daily").tag("twice_daily")
                    Text("Three times daily").tag("three_times_daily")
                    Text("Free feeding").tag("free_feeding")
                }
                .pickerStyle(MenuPickerStyle())
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var feedingScheduleSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Dietary Preferences")
                .font(.headline)
                .foregroundColor(.primary)
            VStack(alignment: .leading, spacing: 8) {
                Text("Water Intake")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Picker("Water Intake", selection: $waterIntake) {
                    Text("Low").tag("low")
                    Text("Normal").tag("normal")
                    Text("High").tag("high")
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Special Diet")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                TextField("Special dietary requirements", text: $specialDiet)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var dietaryRestrictionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Dietary Restrictions & Preferences")
                .font(.headline)
                .foregroundColor(.primary)
            StringArrayInputSection(
                title: "Food Allergies",
                items: $foodAllergies,
                placeholder: "Add food allergy"
            )
            StringArrayInputSection(
                title: "Dietary Restrictions",
                items: $dietaryRestrictions,
                placeholder: "Add restriction"
            )
            StringArrayInputSection(
                title: "Treat Preferences",
                items: $treatPreferences,
                placeholder: "Add treat preference"
            )
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    // MARK: - Exercise Info Tab
    private var exerciseInfoTab: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Activity Level
                activityLevelSection
                // Exercise Preferences
                exercisePreferencesSection
                // Exercise Restrictions
                exerciseRestrictionsSection
                Spacer(minLength: 24)
            }
            .padding()
        }
    }
    private var activityLevelSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Activity Level")
                .font(.headline)
                .foregroundColor(.primary)
            VStack(alignment: .leading, spacing: 8) {
                Text("Overall Activity Level")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Picker("Activity Level", selection: $activityLevel) {
                    Text("Low").tag("low")
                    Text("Moderate").tag("moderate")
                    Text("High").tag("high")
                    Text("Very High").tag("very_high")
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Daily Exercise Minutes")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Stepper(value: $dailyExerciseMinutes, in: 0...300, step: 15) {
                    Text("\(dailyExerciseMinutes) minutes")
                        .font(.body)
                }
            }
            VStack(alignment: .leading, spacing: 8) {
                Text("Walking Frequency")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Picker("Walking Frequency", selection: $walkingFrequency) {
                    Text("Daily").tag("daily")
                    Text("Twice daily").tag("twice_daily")
                    Text("Every other day").tag("every_other_day")
                    Text("Weekly").tag("weekly")
                }
                .pickerStyle(MenuPickerStyle())
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var exercisePreferencesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Exercise Preferences")
                .font(.headline)
                .foregroundColor(.primary)
            StringArrayInputSection(
                title: "Favorite Activities",
                items: $favoriteActivities,
                placeholder: "Add activity"
            )
            StringArrayInputSection(
                title: "Play Preferences",
                items: $playPreferences,
                placeholder: "Add play preference"
            )
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var exerciseRestrictionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Exercise Restrictions")
                .font(.headline)
                .foregroundColor(.primary)
            StringArrayInputSection(
                title: "Exercise Restrictions",
                items: $exerciseRestrictions,
                placeholder: "Add restriction"
            )
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    // MARK: - Training Info Tab
    private var trainingInfoTab: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Training Level
                trainingLevelSection
                // Commands and Behaviors
                commandsSection
                // Social Behavior
                socialBehaviorSection
                Spacer(minLength: 24)
            }
            .padding()
        }
    }
    private var trainingLevelSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Training Level")
                .font(.headline)
                .foregroundColor(.primary)
            VStack(alignment: .leading, spacing: 8) {
                Text("Current Training Level")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                Picker("Training Level", selection: $trainingLevel) {
                    Text("Beginner").tag("beginner")
                    Text("Intermediate").tag("intermediate")
                    Text("Advanced").tag("advanced")
                    Text("Expert").tag("expert")
                }
                .pickerStyle(SegmentedPickerStyle())
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var commandsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Commands & Behaviors")
                .font(.headline)
                .foregroundColor(.primary)
            StringArrayInputSection(
                title: "Known Commands",
                items: $knownCommands,
                placeholder: "Add command"
            )
            StringArrayInputSection(
                title: "Behavior Issues",
                items: $behaviorIssues,
                placeholder: "Add behavior issue"
            )
            StringArrayInputSection(
                title: "Training Goals",
                items: $trainingGoals,
                placeholder: "Add training goal"
            )
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private var socialBehaviorSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Social Behavior")
                .font(.headline)
                .foregroundColor(.primary)
            VStack(alignment: .leading, spacing: 12) {
                socialBehaviorPicker(title: "With Dogs", selection: $socialWithDogs)
                socialBehaviorPicker(title: "With Cats", selection: $socialWithCats)
                socialBehaviorPicker(title: "With People", selection: $socialWithPeople)
                socialBehaviorPicker(title: "With Children", selection: $socialWithChildren)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    private func socialBehaviorPicker(title: String, selection: Binding<String>) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)
            Picker(title, selection: selection) {
                Text("Friendly").tag("friendly")
                Text("Cautious").tag("cautious")
                Text("Aggressive").tag("aggressive")
                Text("Unknown").tag("unknown")
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    // MARK: - Validation and Creation
    private var canCreate: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !breed.isEmpty &&
        bio.count <= 500 &&
        (useDateOfBirth || age > 0)
    }
    private var finalBreedValue: String {
        return breed == "Other" ? customBreed : breed
    }
    private func createPetProfile() {
        guard let currentUser = authService.currentUser else {
            errorMessage = "You must be signed in to create a pet profile"
            showingError = true
            return
        }
        isCreating = true
        // Calculate birth date
        let calculatedBirthDate: Date
        if useDateOfBirth {
            calculatedBirthDate = dateOfBirth
        } else if age > 0 {
            calculatedBirthDate = Calendar.current.date(byAdding: .year, value: -age, to: Date()) ?? Date()
        } else {
            calculatedBirthDate = Date()
        }
        // Upload image if selected
        var profileImageURL: String? = nil
        if let profileImage = profileImage {
            if let imageData = profileImage.jpegData(compressionQuality: 0.8) {
                profileImageURL = "data:image/jpeg;base64," + imageData.base64EncodedString()
            }
        }
        // Create comprehensive pet profile
        let newPet = Pet(
            name: name.trimmingCharacters(in: .whitespacesAndNewlines),
            species: species,
            breed: finalBreedValue.isEmpty ? "" : finalBreedValue,
            age: useDateOfBirth ? Calendar.current.dateComponents([.month], from: calculatedBirthDate, to: Date()).month ?? 0 : age * 12,
            weight: Double(weight),
            profileImageURL: profileImageURL,
            dateOfBirth: calculatedBirthDate,
            adoptionDate: Date(),
            microchipId: microchipId.isEmpty ? nil : microchipId,
            isSpayedNeutered: isSpayedNeutered,
            sex: gender,
            emergencyContacts: [],
            primaryVet: vetName.isEmpty ? nil : VeterinarianInfo(
                clinicName: vetName.isEmpty ? nil : vetName,
                veterinarianName: vetName.isEmpty ? nil : vetName,
                phoneNumber: vetContact.isEmpty ? nil : vetContact,
                emergencyPhoneNumber: nil,
                address: nil,
                email: nil,
                website: nil,
                is24Hour: false,
                specialties: [],
                notes: nil
            ),
            emergencyVet: emergencyVetName.isEmpty ? nil : VeterinarianInfo(
                clinicName: emergencyVetName.isEmpty ? nil : emergencyVetName,
                veterinarianName: emergencyVetName.isEmpty ? nil : emergencyVetName,
                phoneNumber: emergencyVetContact.isEmpty ? nil : emergencyVetContact,
                emergencyPhoneNumber: nil,
                address: nil,
                email: nil,
                website: nil,
                is24Hour: true,
                specialties: [],
                notes: nil
            ),
            allergies: allergies + foodAllergies,
            medications: medications.compactMap { 
                Medication(
                    name: $0,
                    dosage: "",
                    frequency: "as needed",
                    prescribedBy: "",
                    startDate: Date(),
                    purpose: ""
                )
            },
            medicalConditions: medicalConditions.compactMap { 
                MedicalCondition(
                    name: $0,
                    diagnosedDate: Date()
                )
            },
            bloodType: bloodType.isEmpty ? nil : bloodType,
            insuranceInfo: insuranceProvider.isEmpty ? nil : InsuranceInfo(
                provider: insuranceProvider,
                policyNumber: insurancePolicyNumber,
                groupNumber: nil,
                memberID: insurancePolicyNumber,
                phoneNumber: "",
                website: nil,
                coverageDetails: nil,
                deductible: nil,
                copayAmount: nil
            ),
            healthAlerts: [],
            vaccinationRecords: vaccinations.compactMap { 
                VaccinationRecord(
                    vaccineName: $0,
                    dateAdministered: Date(),
                    veterinarian: "",
                    clinic: ""
                )
            },
            weightHistory: [],
            symptomsLog: [],
            feedingSchedule: [],
            exerciseNeeds: ExerciseNeeds(
                dailyMinutes: dailyExerciseMinutes,
                exerciseType: favoriteActivities.isEmpty ? ["walk"] : favoriteActivities,
                intensity: activityLevel,
                restrictions: exerciseRestrictions
            ),
            behaviorNotes: behaviorIssues.joined(separator: ", "),
            specialInstructions: specialDiet.isEmpty ? nil : specialDiet,
            lastKnownLocation: nil,
            isLost: false,
            collarInfo: nil,
            activityLevel: activityLevel,
            personalityTraits: [],
            vaccinations: vaccinations,
            aiRecommendations: [],
            lastCheckupDate: lastCheckupDate,
            chronicConditions: medicalConditions,
            foodAllergies: foodAllergies,
            healthScore: 85.0,
            dailyCalories: 1200,
            lastAIAnalysis: nil,
            storedMemoryCount: 0,
            subscriptionTier: "FREE",
            memories: [],
            gender: gender,
            vetName: vetName.isEmpty ? nil : vetName,
            vetContact: vetContact.isEmpty ? nil : vetContact,
            currentFood: currentFood.isEmpty ? nil : currentFood,
            foodBrand: foodBrand.isEmpty ? nil : foodBrand,
            createdAt: Date(),
            updatedAt: Date(),
            friendsCount: 0,
            bio: bio,
            achievementBadges: [],
            isFavorite: false,
            bondedWith: nil,
            isIndoor: true,
            careGroup: nil
        )
        // Add to local context
        modelContext.insert(newPet)
        currentUser.petIDs.append(newPet.id)
        // Save locally first
        do {
            try modelContext.save()
        } catch {
            errorMessage = "Failed to save pet profile locally: \(error.localizedDescription)"
            showingError = true
            isCreating = false
            return
        }
        Task {
            do {
                // Use RealDataService to create the pet instead
                await realDataService.addPet(newPet)
                // Refresh RealDataService to show the new pet
                await realDataService.refreshAllData()
                // Award gems for creating comprehensive pet profile
                let gemReward = GemRewardSystem.earnGems(for: .joinNetwork, user: currentUser)
                modelContext.insert(gemReward)
                try modelContext.save()
                await MainActor.run {
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    errorMessage = "Failed to sync pet profile: \(error.localizedDescription)"
                    showingError = true
                }
            }
            await MainActor.run {
                isCreating = false
            }
        }
    }
}
// MARK: - Helper Views
struct StringArrayInputSection: View {
    let title: String
    @Binding var items: [String]
    let placeholder: String
    @State private var newItem = ""
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)
            HStack {
                TextField(placeholder, text: $newItem)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                Button("Add") {
                    if !newItem.isEmpty {
                        items.append(newItem)
                        newItem = ""
                    }
                }
                .disabled(newItem.isEmpty)
            }
            if !items.isEmpty {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: 8) {
                    ForEach(items, id: \.self) { item in
                        HStack {
                            Text(item)
                                .font(.caption)
                            Spacer()
                            Button(action: {
                                items.removeAll { $0 == item }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.red)
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color(.systemGray5))
                        )
                    }
                }
            }
        }
    }
}
#Preview {
    if #available(iOS 18.0, *) {
        EnhancedPetCreationView()
            .environmentObject(AuthenticationService.shared)
            .environmentObject(RealDataService())
    } else {
        Text("iOS 18.0+ Required")
    }
} 