import SwiftUI

struct MyPetsListView: View {
    @EnvironmentObject var petService: RealDataService
    @State private var showingAddPet = false
    @State private var animateCards = false
    
    private let columns = [
        GridItem(.flexible(), spacing: 16),
        GridItem(.flexible(), spacing: 16)
    ]
    
    private let gradientColors: [[Color]] = [
        [Color.purple.opacity(0.8), Color.pink.opacity(0.8)],
        [Color.orange.opacity(0.8), Color.red.opacity(0.8)],
        [Color.blue.opacity(0.8), Color.cyan.opacity(0.8)],
        [Color.green.opacity(0.8), Color.mint.opacity(0.8)],
        [Color.indigo.opacity(0.8), Color.purple.opacity(0.8)],
        [Color.yellow.opacity(0.8), Color.orange.opacity(0.8)]
    ]
    
    // Test pets for when no real data is available (until authentication is set up)
    private var testPets: [Pet] {
        [
            Pet(name: "<PERSON>", species: "dog", breed: "Golden Retriever", age: 36, profileImageURL: nil),
            <PERSON>(name: "Whiskers", species: "cat", breed: "<PERSON>amese", age: 24, profileImageURL: nil),
            <PERSON>(name: "<PERSON>", species: "dog", breed: "Labrador", age: 48, profileImageURL: nil)
        ]
    }
    
    // Use test pets if no real pets are loaded
    private var displayPets: [Pet] {
        petService.pets.isEmpty ? testPets : petService.pets
    }
    
    var body: some View {
        ScrollView {
                LazyVGrid(columns: columns, spacing: 20) {
                    // Add Pet Card
                    AddPetCard(action: { showingAddPet = true })
                        .opacity(animateCards ? 1 : 0)
                        .animation(.easeInOut(duration: 0.3), value: animateCards)
                    
                    // Pet Cards
                    ForEach(Array(displayPets.enumerated()), id: \.element.id) { index, pet in
                        NavigationLink {
                            PetDetailView(pet: pet)
                                .environmentObject(petService)
                        } label: {
                            PetCard(
                                pet: pet, 
                                gradientColors: gradientColors[index % gradientColors.count]
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .opacity(animateCards ? 1 : 0)
                        .animation(.easeInOut(duration: 0.3).delay(Double(index) * 0.1), value: animateCards)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
            .background(Color(.systemBackground)) // White in light mode, black in dark mode
            .navigationTitle("My Pets ❤️")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                withAnimation(.easeInOut(duration: 0.5)) {
                    animateCards = true
                }
            }
            .sheet(isPresented: $showingAddPet) {
                AddPetView()
                    .environmentObject(petService)
            }
    }
}

// MARK: - Pet Card Component
struct PetCard: View {
    let pet: Pet
    let gradientColors: [Color]
    
    var body: some View {
        VStack(spacing: 0) {
            // Header Section
            ZStack {
                // Gradient Background
                LinearGradient(
                    gradient: Gradient(colors: gradientColors),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                
                // Pet Avatar and Info - Centered
                VStack(spacing: 4) {
                    // Avatar with proper centering
                    ZStack {
                        Circle()
                            .fill(.white.opacity(0.2))
                            .frame(width: 60, height: 60)
                        
                        if let profileImageURL = pet.profileImageURL,
                           let url = URL(string: profileImageURL) {
                            AsyncImage(url: url) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            } placeholder: {
                                Image(systemName: "pawprint.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                            }
                            .frame(width: 60, height: 60)
                            .clipShape(Circle())
                        } else {
                            Image(systemName: "pawprint.fill")
                                .font(.system(size: 24))
                                .foregroundColor(.white)
                                .frame(width: 60, height: 60)
                                .background(Circle().fill(.white.opacity(0.3)))
                        }
                        
                        // Glassmorphism overlay
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        .white.opacity(0.3),
                                        .clear
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 60, height: 60)
                    }
                    .padding(.top, 60)
                    
                    // Pet name and breed - properly aligned
                    VStack(spacing: 2) {
                        Text(pet.name)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .lineLimit(1)
                        

                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
            .frame(height: 140)
            
            // Stats Section - White background with proper spacing
            VStack(spacing: 12) {
                HStack(spacing: 12) {
                    // Health Badge
                    VStack(spacing: 2) {
                        Text("\(healthScore(for: pet))")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                        Text("Health")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                    
                    // Age Badge
                    VStack(spacing: 2) {
                        Text(petAge(for: pet))
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.blue)
                        Text("Age")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                
                // Health Status Badge - centered
                HStack(spacing: 6) {
                    Image(systemName: "heart.fill")
                        .font(.caption)
                        .foregroundColor(healthStatusColor(for: pet))
                    Text(healthStatus(for: pet))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(healthStatusColor(for: pet).opacity(0.1))
                .cornerRadius(12)
                
                // Last Checkup Badge
                HStack(spacing: 6) {
                    Image(systemName: "calendar")
                        .font(.caption)
                        .foregroundColor(.gray)
                    Text(lastCheckupText(for: pet))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
        }
        .frame(width: 160, height: 200)
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(color: Color.primary.opacity(0.1), radius: 15, x: 0, y: 8)
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.primary.opacity(0.1), lineWidth: 0.5)
        )
    }
    
    private func healthScore(for pet: Pet) -> Int {
        let baseScore = 85
        let conditionsPenalty = pet.medicalConditions.count * 5
        let medicationsPenalty = pet.medications.count * 3
        return max(50, baseScore - conditionsPenalty - medicationsPenalty)
    }
    
    private func petAge(for pet: Pet) -> String {
        guard let dateOfBirth = pet.dateOfBirth else {
            return "Unknown"
        }
        
        let calendar = Calendar.current
        let now = Date()
        let ageComponents = calendar.dateComponents([.year, .month], from: dateOfBirth, to: now)
        
        if let years = ageComponents.year, years > 0 {
            return "\(years)y"
        } else if let months = ageComponents.month, months > 0 {
            return "\(months)mo"
        } else {
            return "New"
        }
    }
    
    private func healthStatus(for pet: Pet) -> String {
        let score = healthScore(for: pet)
        if score >= 80 { return "Excellent" }
        else if score >= 70 { return "Very Good" }
        else if score >= 60 { return "Good" }
        else { return "Needs Care" }
    }
    
    private func healthStatusColor(for pet: Pet) -> Color {
        let score = healthScore(for: pet)
        if score >= 80 { return .green }
        else if score >= 70 { return .blue }
        else if score >= 60 { return .orange }
        else { return .red }
    }
    
    private func lastCheckupText(for pet: Pet) -> String {
        // This would typically come from the database
        return "No recent visit"
    }
}

// MARK: - Add Pet Card
struct AddPetCard: View {
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(.white.opacity(0.3))
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: "plus")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                }
                
                VStack(spacing: 4) {
                    Text("✨ Add")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("New Pet")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("0 Memories")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
        }
        .frame(width: 160, height: 200)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.green.opacity(0.8),
                    Color.mint.opacity(0.8)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .shadow(color: Color.primary.opacity(0.1), radius: 15, x: 0, y: 8)
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.primary.opacity(0.1), lineWidth: 0.5)
        )
    }
}

// No helper components needed - using inline SwiftUI

#Preview {
    MyPetsListView()
} 