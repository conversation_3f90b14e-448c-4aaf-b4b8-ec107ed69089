//
//  MyPetsView.swift
//  PetCapsule
//
//  Simple, Personal Pet Management for Pet Owners
//

import SwiftUI

struct MyPetsView: View {
    @EnvironmentObject var realDataService: RealDataService
    @Environment(\.colorScheme) var colorScheme
    @State private var showAddPet = false
    @State private var selectedPet: Pet?
    @State private var animateCards = false
    @State private var hapticFeedback = UIImpactFeedbackGenerator(style: .medium)
    @State private var showSampleGenerator = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Simple Welcome
                    welcomeHeader
                    
                    // Pet Grid
                    if realDataService.pets.isEmpty {
                        emptyStateView
                    } else {
                        petsGrid
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 8)
                .padding(.bottom, 100)
            }
            .refreshable {
                await realDataService.refreshAllData()
            }
            .background(colorScheme == .dark ? Color.black : Color(.systemGroupedBackground))
            .navigationTitle("My Furry Family")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: { 
                            showAddPet = true
                            hapticFeedback.impactOccurred()
                        }) {
                            Label("Add New Pet", systemImage: "plus.circle")
                        }
                        
                        if #available(iOS 18.0, *) {
                            Button(action: {
                                showSampleGenerator = true
                                hapticFeedback.impactOccurred()
                            }) {
                                Label("Generate Sample Pets", systemImage: "sparkles")
                            }
                        }
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundStyle(.blue)
                    }
                }
            }
            .sheet(isPresented: $showAddPet) {
                if #available(iOS 18.0, *) {
                    EnhancedPetCreationView()
                        .environmentObject(realDataService)
                } else {
                    AddPetView()
                        .environmentObject(realDataService)
                }
            }
            .sheet(isPresented: $showSampleGenerator) {
                if #available(iOS 18.0, *) {
                    SamplePetGeneratorView()
                } else {
                    Text("Sample generation requires iOS 18.0 or later")
                        .padding()
                }
            }
            .sheet(item: $selectedPet) { pet in
                PetDetailView(pet: pet)
                    .environmentObject(realDataService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1)) {
                    animateCards = true
                }
            }
        }
    }

    // MARK: - Simple Welcome Header
    private var welcomeHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    if realDataService.pets.count == 1 {
                        Text("Your beloved companion")
                    } else if realDataService.pets.count > 1 {
                        Text("Your \(realDataService.pets.count) beloved companions")
                    } else {
                        Text("Ready to add your first pet?")
                    }
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
                
                Spacer()
                
                Image(systemName: "heart.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(.red)
            }
        }
        .padding(.horizontal, 4)
    }

    // MARK: - Pets Grid
    private var petsGrid: some View {
        let columns = [
            GridItem(.flexible(), spacing: 16),
            GridItem(.flexible(), spacing: 16)
        ]
        return LazyVGrid(columns: columns, spacing: 16) {
            ForEach(realDataService.pets, id: \.id) { pet in
                PetCard(
                    pet: pet,
                    gradientColors: [.blue.opacity(0.8), .purple.opacity(0.6)]
                )
                .onTapGesture {
                    selectedPet = pet
                }
                .scaleEffect(animateCards ? 1.0 : 0.8)
                .opacity(animateCards ? 1.0 : 0.0)
                .animation(
                    .spring(response: 0.6, dampingFraction: 0.8).delay(Double(realDataService.pets.firstIndex(of: pet)!) * 0.1), 
                    value: animateCards
                )
            }
        }
    }

    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "pawprint.2")
                .font(.system(size: 64, weight: .light))
                .foregroundStyle(.secondary)
            
            VStack(spacing: 12) {
                Text("No pets yet")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text("Tap the + button to add your first furry friend")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 12) {
                Button(action: {
                    showAddPet = true
                    hapticFeedback.impactOccurred()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 16, weight: .semibold))
                        
                        Text("Add Your First Pet")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(.blue)
                    .foregroundColor(.white)
                    .clipShape(RoundedRectangle(cornerRadius: 25))
                }
                
                if #available(iOS 18.0, *) {
                    Button(action: {
                        showSampleGenerator = true
                        hapticFeedback.impactOccurred()
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "sparkles")
                                .font(.system(size: 16, weight: .semibold))
                            
                            Text("Generate Sample Pets")
                                .font(.system(size: 16, weight: .semibold))
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(
                            LinearGradient(colors: [.purple, .pink], startPoint: .leading, endPoint: .trailing)
                        )
                        .foregroundColor(.white)
                        .clipShape(RoundedRectangle(cornerRadius: 25))
                    }
                }
            }
        }
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 20, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
    }
}

// MARK: - Preview
struct MyPetsView_Previews: PreviewProvider {
    static var previews: some View {
        MyPetsView()
            .environmentObject(RealDataService())
    }
}
