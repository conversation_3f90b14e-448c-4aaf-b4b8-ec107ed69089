//
//  PetDetailView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//
import SwiftUI
import PhotosUI
struct PetDetailView: View {
    let pet: Pet
    @EnvironmentObject var realDataService: RealDataService
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    @State private var showEditPet = false
    @State private var animateCards = false
    // Add computed property to get memories from petID
    private var petMemories: [Memory] {
        // Get memories that belong to this pet using petID
        return realDataService.memories.filter { $0.petID == pet.id }
    }
    var body: some View {
        VStack(spacing: 0) {
                // Pet Header
                petHeaderSection
                // Tab Selector
                tabSelectorSection
                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    overviewTab
                        .tag(0)
                    healthTab
                        .tag(1)
                    memoriesTab
                        .tag(2)
                    aiInsightsTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle(pet.name)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Edit") {
                        showEditPet = true
                    }
                }
            }
            .sheet(isPresented: $showEditPet) {
                EditPetView(pet: pet)
                    .environmentObject(realDataService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
            }
    }
    // MARK: - Pet Header Section
    private var petHeaderSection: some View {
        VStack(spacing: 16) {
            // Pet Image and Basic Info
            HStack(spacing: 16) {
                // Pet Image
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    ZStack {
                        Circle()
                            .fill(Color.purple.opacity(0.2))
                        Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                            .font(.system(size: 40))
                    }
                }
                .frame(width: 100, height: 100)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.purple, lineWidth: 3)
                )
                // Pet Info
                VStack(alignment: .leading, spacing: 8) {
                    Text(pet.name)
                        .font(.petTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    Text("\(pet.breed ?? "Mixed") • \(pet.age) years old")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                    // Health Score
                    HStack(spacing: 8) {
                        Circle()
                            .fill(pet.healthScore > 0.8 ? .green : pet.healthScore > 0.6 ? .orange : .red)
                            .frame(width: 12, height: 12)
                        Text("\(Int(pet.healthScore * 100))% Health Score")
                            .font(.petSubheadline)
                            .foregroundColor(.secondary)
                    }
                    // Subscription Badge
                    if pet.subscriptionTier != "free" {
                        Text(pet.subscriptionTier.uppercased())
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.purple)
                            )
                    }
                }
                Spacer()
            }
            // Quick Stats
            HStack(spacing: 16) {
                quickStatCard(
                    icon: "photo.fill",
                    title: "Memories",
                    value: "\(pet.storedMemoryCount)",
                    color: .blue
                )
                quickStatCard(
                    icon: "heart.circle.fill",
                    title: "Friends",
                    value: "\(pet.friendsCount)",
                    color: .pink
                )
                quickStatCard(
                    icon: "trophy.fill",
                    title: "Badges",
                    value: "\(pet.achievementBadges.count)",
                    color: .yellow
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .padding()
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }
    private func quickStatCard(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            Text(value)
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
    // MARK: - Tab Selector Section
    private var tabSelectorSection: some View {
        HStack(spacing: 0) {
            tabButton(title: "Overview", icon: "info.circle.fill", index: 0)
            tabButton(title: "Health", icon: "heart.fill", index: 1)
            tabButton(title: "Memories", icon: "photo.fill", index: 2)
            tabButton(title: "AI Insights", icon: "brain.head.profile", index: 3)
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }
    private func tabButton(title: String, icon: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
                Text(title)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == index ? Color.purple.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    // MARK: - Overview Tab
    private var overviewTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Bio Section
                if !pet.bio.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("About \(pet.name)")
                            .font(.petTitle3)
                            .fontWeight(.bold)
                        Text(pet.bio)
                            .font(.petBody)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemGray6))
                    )
                }
                // Basic Info
                basicInfoSection
                // Personality Traits
                personalityTraitsSection
                // Achievement Badges
                achievementBadgesSection
            }
            .padding()
            .padding(.bottom, 100)
        }
    }
    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Basic Information")
                .font(.petTitle3)
                .fontWeight(.bold)
            VStack(spacing: 12) {
                infoRow(label: "Species", value: pet.species.capitalized)
                infoRow(label: "Breed", value: pet.breed ?? "Mixed")
                infoRow(label: "Age", value: "\(pet.age) years old")
                infoRow(label: "Gender", value: pet.gender?.capitalized ?? "Unknown")
                if let weight = pet.weight {
                    infoRow(label: "Weight", value: String(format: "%.1f kg", weight))
                }
                if let microchip = pet.microchipId {
                    infoRow(label: "Microchip", value: microchip)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private func infoRow(label: String, value: String) -> some View {
        HStack {
            Text(label)
                .font(.petSubheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.petSubheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }
    private var personalityTraitsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Personality Traits")
                .font(.petTitle3)
                .fontWeight(.bold)
            if pet.personalityTraits.isEmpty {
                Text("No personality traits recorded yet")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(pet.personalityTraits, id: \.self) { trait in
                        Text(trait)
                            .font(.petSubheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.purple)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.purple.opacity(0.1))
                            )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var achievementBadgesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Achievement Badges")
                .font(.petTitle3)
                .fontWeight(.bold)
            if pet.achievementBadges.isEmpty {
                Text("No badges earned yet")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                    ForEach(pet.achievementBadges, id: \.self) { badge in
                        VStack(spacing: 6) {
                            Image(systemName: "trophy.fill")
                                .font(.title2)
                                .foregroundColor(.yellow)
                            Text(badge)
                                .font(.petCaption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.yellow.opacity(0.1))
                        )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    // MARK: - Health Tab
    private var healthTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Health Score Section
                healthScoreSection
                // Medications Section
                if !pet.medications.isEmpty {
                    medicationsSection
                }
                // Vaccinations Section  
                if !pet.vaccinations.isEmpty {
                    vaccinationsSection
                }
                // Health Alerts Section
                if !pet.healthAlerts.isEmpty {
                    healthAlertsSection
                }
                // Veterinarian Info Section
                if let vetInfo = pet.veterinarianInfo {
                    veterinarianInfoSection(vetInfo)
                }
                // Insurance Info Section
                if let insuranceInfo = pet.insuranceInfo {
                    insuranceInfoSection(insuranceInfo)
                }
                // If no health data
                if pet.medications.isEmpty && pet.vaccinations.isEmpty && 
                   pet.healthAlerts.isEmpty && pet.veterinarianInfo == nil {
                    noHealthDataView
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }
    private var healthScoreSection: some View {
        VStack(spacing: 16) {
            Text("Health Overview")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            HStack(spacing: 20) {
                // Health Score Circle
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                        .frame(width: 80, height: 80)
                    Circle()
                        .trim(from: 0, to: CGFloat(pet.healthScore))
                        .stroke(healthScoreColor(pet.healthScore), lineWidth: 8)
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(-90))
                    VStack {
                        Text("\(Int(pet.healthScore * 100))")
                            .font(.petTitle2)
                            .fontWeight(.bold)
                            .foregroundColor(healthScoreColor(pet.healthScore))
                        Text("%")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }
                VStack(alignment: .leading, spacing: 8) {
                    Text("Health Score")
                        .font(.petSubheadline)
                        .fontWeight(.semibold)
                    Text(healthScoreText(pet.healthScore))
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                    if let weight = pet.weight {
                        Text("Weight: \(String(format: "%.1f", weight)) kg")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                    if let lastCheckup = pet.lastCheckupDate {
                        Text("Last checkup: \(lastCheckup, style: .date)")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var medicationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "pills.fill")
                    .foregroundColor(.blue)
                Text("Medications")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            ForEach(pet.medications, id: \.name) { medication in
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(medication.name)
                            .font(.petSubheadline)
                            .fontWeight(.semibold)
                        if !medication.dosage.isEmpty {
                            Text("Dosage: \(medication.dosage)")
                                .font(.petCaption)
                                .foregroundColor(.secondary)
                        }
                        if !medication.frequency.isEmpty {
                            Text("Frequency: \(medication.frequency)")
                                .font(.petCaption)
                                .foregroundColor(.secondary)
                        }
                    }
                    Spacer()
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.1))
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var vaccinationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "cross.fill")
                    .foregroundColor(.green)
                Text("Vaccinations")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(pet.vaccinations, id: \.self) { vaccination in
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text(vaccination)
                            .font(.petCaption)
                            .fontWeight(.medium)
                        Spacer()
                    }
                    .padding(8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.green.opacity(0.1))
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var healthAlertsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                Text("Health Alerts")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            ForEach(pet.healthAlerts, id: \.id) { alert in
                HStack {
                    Image(systemName: "exclamationmark.circle.fill")
                        .foregroundColor(.orange)
                    Text(alert.description)
                        .font(.petSubheadline)
                    Spacer()
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange.opacity(0.1))
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private func veterinarianInfoSection(_ vetInfo: VeterinarianInfo) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "stethoscope")
                    .foregroundColor(.purple)
                Text("Veterinarian")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            VStack(alignment: .leading, spacing: 8) {
                                    Text(vetInfo.displayName)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                Text("Dr. \(vetInfo.veterinarianName)")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                if let phoneNumber = vetInfo.phoneNumber, !phoneNumber.isEmpty {
                    HStack {
                        Image(systemName: "phone.fill")
                            .foregroundColor(.blue)
                        Text(phoneNumber)
                            .font(.petCaption)
                    }
                }
                if let address = vetInfo.address, !address.isEmpty {
                    HStack {
                        Image(systemName: "location.fill")
                            .foregroundColor(.red)
                        Text(address)
                            .font(.petCaption)
                    }
                }
                if vetInfo.is24Hour == true {
                    HStack {
                        Image(systemName: "clock.fill")
                            .foregroundColor(.green)
                        Text("24 Hour Emergency")
                            .font(.petCaption)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private func insuranceInfoSection(_ insuranceInfo: InsuranceInfo) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "shield.fill")
                    .foregroundColor(.blue)
                Text("Insurance")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            VStack(alignment: .leading, spacing: 8) {
                Text(insuranceInfo.displayProvider)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                Text("Policy: \(insuranceInfo.displayPolicyNumber)")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                if let memberID = insuranceInfo.memberID, !memberID.isEmpty {
                    Text("Member ID: \(memberID)")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                if let deductible = insuranceInfo.deductible {
                    Text("Deductible: $\(String(format: "%.2f", deductible))")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                if let coverage = insuranceInfo.coverageDetails, !coverage.isEmpty {
                    Text("Coverage: \(coverage)")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var noHealthDataView: some View {
        VStack(spacing: 16) {
            Image(systemName: "heart.text.square")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            Text("No Health Data")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            Text("Add health information, medications, and vet details to track \(pet.name)'s wellbeing.")
                .font(.petSubheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    private func healthScoreColor(_ score: Double) -> Color {
        switch score {
        case 0.9...1.0: return .green
        case 0.8..<0.9: return .mint
        case 0.7..<0.8: return .yellow
        case 0.6..<0.7: return .orange
        default: return .red
        }
    }
    private func healthScoreText(_ score: Double) -> String {
        switch score {
        case 0.9...1.0: return "Excellent health"
        case 0.8..<0.9: return "Very good health"
        case 0.7..<0.8: return "Good health"
        case 0.6..<0.7: return "Fair health"
        default: return "Needs attention"
        }
    }
    // MARK: - Memories Tab
    private var memoriesTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Memories Header
                memoriesHeaderSection
                // Memory Stats
                memoryStatsSection
                // Recent Memories
                if !petMemories.isEmpty {
                    recentMemoriesSection
                } else {
                    noMemoriesView
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }
    private var memoriesHeaderSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "photo.on.rectangle.angled")
                    .font(.title2)
                    .foregroundColor(.blue)
                VStack(alignment: .leading) {
                    Text("Memories of \(pet.name)")
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    Text("Cherished moments and milestones")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                Spacer()
                Button(action: {
                    // TODO: Add new memory action
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(LinearGradient(
                    colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
        )
    }
    private var memoryStatsSection: some View {
        HStack(spacing: 16) {
            memoryStatCard(
                icon: "photo.fill",
                title: "Photos",
                count: petMemories.filter { $0.type == .photo }.count,
                color: .blue
            )
            memoryStatCard(
                icon: "video.fill",
                title: "Videos", 
                count: petMemories.filter { $0.type == .video }.count,
                color: .purple
            )
            memoryStatCard(
                icon: "star.fill",
                title: "Milestones",
                count: petMemories.filter { $0.type == .milestone }.count,
                color: .yellow
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private func memoryStatCard(icon: String, title: String, count: Int, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            Text("\(count)")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
    private var recentMemoriesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Memories")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
                Button("View All") {
                    // TODO: Show all memories
                }
                .font(.petCaption)
                .foregroundColor(.blue)
            }
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(Array(petMemories.prefix(6)), id: \.id) { memory in
                    memoryCard(memory)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private func memoryCard(_ memory: Memory) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            // Memory Image/Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(
                        colors: [getMemoryColor(memory.type), getMemoryColor(memory.type).opacity(0.7)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(height: 100)
                if let mediaURL = memory.mediaURL, memory.type == .photo {
                    AsyncImage(url: URL(string: mediaURL)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Image(systemName: memory.type.systemImage)
                            .font(.title)
                            .foregroundColor(.white)
                    }
                    .frame(height: 100)
                    .clipped()
                    .cornerRadius(12)
                } else {
                    Image(systemName: memory.type.systemImage)
                        .font(.title)
                        .foregroundColor(.white)
                }
                // Memory Type Badge
                VStack {
                    HStack {
                        Text(memory.type.displayName)
                            .font(.petCaption)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color.black.opacity(0.3))
                            )
                        Spacer()
                    }
                    Spacer()
                }
                .padding(8)
                // Duration for videos
                if memory.type == .video, let _ = memory.duration {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Text(memory.formattedDuration ?? "")
                                .font(.petCaption)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 3)
                                .background(
                                    Capsule()
                                        .fill(Color.black.opacity(0.7))
                                )
                        }
                    }
                    .padding(8)
                }
            }
            // Memory Details
            VStack(alignment: .leading, spacing: 4) {
                Text(memory.title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .lineLimit(2)
                    .foregroundColor(.primary)
                if !memory.content.isEmpty {
                    Text(memory.content)
                        .font(.petCaption)
                        .lineLimit(3)
                        .foregroundColor(.secondary)
                }
                HStack {
                    Text(memory.createdAt, style: .date)
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                    Spacer()
                    if memory.isFavorite {
                        Image(systemName: "heart.fill")
                            .font(.petCaption)
                            .foregroundColor(.red)
                    }
                    if memory.milestone != nil {
                        Image(systemName: "star.fill")
                            .font(.petCaption)
                            .foregroundColor(.yellow)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .onTapGesture {
            // TODO: Show memory detail
        }
    }
    private var noMemoriesView: some View {
        VStack(spacing: 16) {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            Text("No Memories Yet")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            Text("Start capturing special moments with \(pet.name)! Take photos, record videos, and create lasting memories.")
                .font(.petSubheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            Button(action: {
                // TODO: Add first memory action
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Add First Memory")
                }
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue)
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    private func getMemoryColor(_ type: MemoryType) -> Color {
        switch type {
        case .photo: return .blue
        case .video: return .purple
        case .audio: return .green
        case .text: return .orange
        case .milestone: return .yellow
        }
    }
    // MARK: - AI Insights Tab
    private var aiInsightsTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // AI Insights Header
                aiInsightsHeaderSection
                // AI Recommendations Section
                if !pet.aiRecommendations.isEmpty {
                    aiRecommendationsSection
                }
                // Behavioral Insights Section
                if !pet.behaviorIssues.isEmpty {
                    behaviorInsightsSection
                }
                // Training Suggestions Section
                if !pet.knownCommands.isEmpty || !pet.behaviorIssues.isEmpty {
                    trainingInsightsSection
                }
                // Health Insights Section
                healthInsightsSection
                // If no AI data
                if pet.aiRecommendations.isEmpty && pet.behaviorIssues.isEmpty && pet.knownCommands.isEmpty {
                    noAIDataView
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }
    private var aiInsightsHeaderSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .font(.title2)
                    .foregroundColor(.purple)
                VStack(alignment: .leading) {
                    Text("AI Insights for \(pet.name)")
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    Text("Personalized recommendations based on your pet's profile")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(LinearGradient(
                    colors: [Color.purple.opacity(0.1), Color.blue.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
        )
    }
    private var aiRecommendationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                Text("AI Recommendations")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            ForEach(Array(pet.aiRecommendations.enumerated()), id: \.offset) { index, recommendation in
                HStack(alignment: .top, spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(Color.yellow.opacity(0.2))
                            .frame(width: 32, height: 32)
                        Text("\(index + 1)")
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.yellow)
                    }
                    VStack(alignment: .leading, spacing: 4) {
                        Text(recommendation)
                            .font(.petSubheadline)
                            .foregroundColor(.primary)
                        Text("AI-generated recommendation")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.yellow.opacity(0.1))
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var behaviorInsightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "pawprint.fill")
                    .foregroundColor(.orange)
                Text("Behavior Analysis")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            ForEach(pet.behaviorIssues, id: \.self) { issue in
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.circle.fill")
                            .foregroundColor(.orange)
                        Text(issue)
                            .font(.petSubheadline)
                            .fontWeight(.semibold)
                        Spacer()
                    }
                    Text(getAIBehaviorSuggestion(for: issue))
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                        .padding(.leading, 24)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange.opacity(0.1))
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var trainingInsightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "graduationcap.fill")
                    .foregroundColor(.green)
                Text("Training Insights")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            VStack(alignment: .leading, spacing: 12) {
                // Known Commands
                if !pet.knownCommands.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Known Commands (\(pet.knownCommands.count))")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                            ForEach(pet.knownCommands, id: \.self) { command in
                                Text(command)
                                    .font(.petCaption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        Capsule()
                                            .fill(Color.green.opacity(0.2))
                                    )
                                    .foregroundColor(.green)
                            }
                        }
                    }
                }
                // AI Training Suggestions
                VStack(alignment: .leading, spacing: 8) {
                    Text("AI Training Suggestions")
                        .font(.petSubheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                    Text(getAITrainingSuggestion())
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var healthInsightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "heart.text.square.fill")
                    .foregroundColor(.red)
                Text("Health Insights")
                    .font(.petTitle3)
                    .fontWeight(.bold)
                Spacer()
            }
            VStack(alignment: .leading, spacing: 12) {
                // Health Score Analysis
                HStack {
                    Image(systemName: "chart.line.uptrend.xyaxis.circle.fill")
                        .foregroundColor(healthScoreColor(pet.healthScore))
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Health Score Analysis")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)
                        Text(getHealthScoreAnalysis())
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }
                // Age-based insights
                HStack {
                    Image(systemName: "calendar.circle.fill")
                        .foregroundColor(.blue)
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Age-Based Care")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)
                        Text(getAgeBasedInsight())
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }
                // Species-specific insights
                HStack {
                    Image(systemName: "pawprint.circle.fill")
                        .foregroundColor(.purple)
                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(pet.species.capitalized) Care Tips")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)
                        Text(getSpeciesSpecificInsight())
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    private var noAIDataView: some View {
        VStack(spacing: 16) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            Text("Learning About \(pet.name)")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            Text("As you add more information about your pet, our AI will provide personalized insights and recommendations.")
                .font(.petSubheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    // MARK: - AI Helper Functions
    private func getAIBehaviorSuggestion(for issue: String) -> String {
        switch issue.lowercased() {
        case let x where x.contains("anxiety"):
            return "Create a calm environment with consistent routines. Consider anxiety-reducing activities."
        case let x where x.contains("aggression"):
            return "Consult with a professional trainer. Avoid punishment-based training methods."
        case let x where x.contains("barking"):
            return "Identify triggers and use positive reinforcement to reward quiet behavior."
        case let x where x.contains("separation"):
            return "Gradually increase alone time and provide mental stimulation when you're away."
        default:
            return "Monitor the behavior and consider consulting with a veterinary behaviorist for personalized guidance."
        }
    }
    private func getAITrainingSuggestion() -> String {
        let commandCount = pet.knownCommands.count
        switch commandCount {
        case 0...2:
            return "Start with basic commands like 'sit' and 'stay'. Use positive reinforcement with treats and praise."
        case 3...5:
            return "Great foundation! Try intermediate commands like 'down', 'come', and 'leave it'."
        case 6...10:
            return "Excellent progress! Consider advanced training like 'heel', 'wait', and distance commands."
        default:
            return "Outstanding training! Focus on maintaining skills and consider specialized training activities."
        }
    }
    private func getHealthScoreAnalysis() -> String {
        let score = pet.healthScore
        switch score {
        case 0.9...1.0:
            return "Your pet is in excellent health! Maintain current care routines and regular checkups."
        case 0.8..<0.9:
            return "Very good health status. Minor areas for improvement in diet or exercise may help."
        case 0.7..<0.8:
            return "Good overall health. Consider reviewing nutrition and activity levels."
        case 0.6..<0.7:
            return "Fair health status. Schedule a vet checkup to address any concerns."
        default:
            return "Health needs attention. Please consult with your veterinarian soon."
        }
    }
    private func getAgeBasedInsight() -> String {
        let ageInYears = pet.ageInYears
        switch ageInYears {
        case 0...1:
            return "Puppy/kitten stage: Focus on socialization, vaccination schedule, and basic training."
        case 2...6:
            return "Adult stage: Maintain regular exercise, balanced nutrition, and preventive care."
        case 7...10:
            return "Senior stage: Monitor for age-related changes, consider senior-specific nutrition."
        default:
            return "Senior+ stage: Increase vet checkup frequency and watch for mobility issues."
        }
    }
    private func getSpeciesSpecificInsight() -> String {
        switch pet.species.lowercased() {
        case "dog":
            return "Regular walks, social interaction, and mental stimulation are essential for dogs."
        case "cat":
            return "Provide vertical spaces, interactive toys, and respect their independent nature."
        case "bird":
            return "Ensure proper lighting, social interaction, and a varied diet with fresh foods."
        case "rabbit":
            return "Provide hay-based diet, safe chewing materials, and gentle handling."
        default:
            return "Research species-specific care requirements for optimal health and happiness."
        }
    }
}
// MARK: - Edit Pet View
struct EditPetView: View {
    let pet: Pet
    @EnvironmentObject var realDataService: RealDataService
    @Environment(\.dismiss) private var dismiss
    // Basic Information
    @State private var name: String
    @State private var species: String
    @State private var breed: String
    @State private var age: Int
    @State private var dateOfBirth: Date
    @State private var useDateOfBirth: Bool
    @State private var weight: String
    @State private var gender: String
    @State private var bio: String
    @State private var activityLevel: String
    @State private var microchipId: String
    @State private var isSpayedNeutered: Bool
    @State private var isIndoor: Bool
    @State private var allergies: [String]
    // Health Information
    @State private var medications: [Medication]
    @State private var vaccinations: [VaccinationRecord]
    @State private var medicalConditions: [MedicalCondition]
    @State private var weightRecords: [WeightRecord]
    @State private var healthScore: Double
    @State private var bloodType: String
    @State private var lastCheckupDate: Date?
    @State private var nextVaccinationDate: Date?
    // Veterinarian Information
    @State private var primaryVet: VeterinarianInfo?
    @State private var emergencyVet: VeterinarianInfo?
    @State private var vetName: String
    @State private var vetContact: String
    @State private var emergencyVetName: String
    @State private var emergencyVetContact: String
    // Insurance Information
    @State private var insuranceInfo: InsuranceInfo?
    @State private var insuranceProvider: String
    @State private var insurancePolicyNumber: String
    // Nutrition Information
    @State private var feedingTimes: [FeedingTime]
    @State private var currentFood: String
    @State private var foodBrand: String
    @State private var feedingSchedule: String
    @State private var foodAllergies: [String]
    @State private var dietaryRestrictions: [String]
    @State private var waterIntakeML: String
    @State private var specialDiet: String
    @State private var treatPreferences: [String]
    // Exercise Information
    @State private var dailyExerciseMinutes: Int
    @State private var favoriteActivities: [String]
    @State private var walkingFrequency: String
    @State private var exerciseRestrictions: [String]
    @State private var playPreferences: [String]
    // Training Information
    @State private var knownCommands: [String]
    @State private var behaviorIssues: [String]
    @State private var trainingLevel: String
    @State private var trainingGoals: [String]
    @State private var socialBehavior: String
    @State private var socialWithDogs: String
    @State private var socialWithCats: String
    @State private var socialWithPeople: String
    @State private var socialWithChildren: String
    // Emergency Contacts
    @State private var emergencyContacts: [EmergencyContact]
    // AI Recommendations
    @State private var aiRecommendations: [String]
    // UI state
    @State private var selectedTab = 0
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    @State private var showingImagePicker = false
    @State private var selectedImage: UIImage?
    @State private var profileImage: UIImage?
    // Input fields for adding new items
    @State private var newAllergy = ""
    @State private var newFoodAllergy = ""
    @State private var newDietaryRestriction = ""
    @State private var newCommand = ""
    @State private var newBehaviorIssue = ""
    @State private var newActivity = ""
    @State private var newExerciseRestriction = ""
    @State private var newPlayPreference = ""
    @State private var newTrainingGoal = ""
    @State private var newTreatPreference = ""
    @State private var newEmergencyContact = EmergencyContact(
        name: "",
        phoneNumber: "",
        type: .custom
    )
    // Delete functionality
    @State private var showingDeleteAlert = false
    @State private var deleteConfirmationText = ""
    @State private var isDeleting = false
    // Constants for dropdowns
    private let activityLevels = ["low", "moderate", "high", "very_high"]
    private let feedingSchedules = ["once_daily", "twice_daily", "three_times_daily", "free_feeding"]
    private let walkingFrequencies = ["daily", "twice_daily", "every_other_day", "weekly"]
    private let trainingLevels = ["beginner", "intermediate", "advanced", "expert"]
    private let socialLevels = ["friendly", "cautious", "aggressive", "unknown"]
    init(pet: Pet) {
        self.pet = pet
        _name = State(initialValue: pet.name)
        _species = State(initialValue: pet.species)
        _breed = State(initialValue: pet.breed ?? "Mixed")
        _age = State(initialValue: pet.age)
        _dateOfBirth = State(initialValue: pet.dateOfBirth ?? Date())
        _useDateOfBirth = State(initialValue: pet.dateOfBirth != nil)
        _weight = State(initialValue: String(pet.weight ?? 0))
        _gender = State(initialValue: pet.gender ?? "")
        _bio = State(initialValue: pet.bio)
        _activityLevel = State(initialValue: pet.activityLevel)
        _microchipId = State(initialValue: pet.microchipId ?? "")
        _isSpayedNeutered = State(initialValue: pet.isSpayedNeutered)
        _isIndoor = State(initialValue: pet.isIndoor)
        _allergies = State(initialValue: pet.allergies)
        // Health
        _medications = State(initialValue: pet.medications)
        _vaccinations = State(initialValue: pet.vaccinationRecords)
        _medicalConditions = State(initialValue: pet.medicalConditions)
        _weightRecords = State(initialValue: pet.weightHistory)
        _healthScore = State(initialValue: pet.healthScore)
        _bloodType = State(initialValue: pet.bloodType ?? "")
        _lastCheckupDate = State(initialValue: pet.lastCheckupDate)
        _nextVaccinationDate = State(initialValue: nil)
        // Veterinarian
        _primaryVet = State(initialValue: pet.primaryVet)
        _emergencyVet = State(initialValue: pet.emergencyVet)
        _vetName = State(initialValue: pet.vetName ?? "")
        _vetContact = State(initialValue: pet.vetContact ?? "")
        _emergencyVetName = State(initialValue: pet.emergencyVet?.displayName ?? "")
        _emergencyVetContact = State(initialValue: pet.emergencyVet?.phoneNumber ?? "")
        // Insurance
        _insuranceInfo = State(initialValue: pet.insuranceInfo)
        _insuranceProvider = State(initialValue: pet.insuranceInfo?.provider ?? "")
        _insurancePolicyNumber = State(initialValue: pet.insuranceInfo?.policyNumber ?? "")
        // Nutrition
        _feedingTimes = State(initialValue: pet.feedingSchedule)
        _currentFood = State(initialValue: pet.currentFood ?? "")
        _foodBrand = State(initialValue: pet.foodBrand ?? "")
        _feedingSchedule = State(initialValue: "twice_daily")
        _foodAllergies = State(initialValue: pet.foodAllergies)
        _dietaryRestrictions = State(initialValue: pet.dietaryRestrictions)
        _waterIntakeML = State(initialValue: String(pet.waterIntakeML ?? 0))
        _specialDiet = State(initialValue: pet.specialInstructions ?? "")
        _treatPreferences = State(initialValue: [])
        // Exercise
        _dailyExerciseMinutes = State(initialValue: pet.exerciseMinutesDaily ?? 30)
        _favoriteActivities = State(initialValue: pet.favoriteActivities)
        _walkingFrequency = State(initialValue: pet.walkingFrequency ?? "daily")
        _exerciseRestrictions = State(initialValue: [])
        _playPreferences = State(initialValue: [])
        // Training
        _knownCommands = State(initialValue: pet.knownCommands)
        _behaviorIssues = State(initialValue: pet.behaviorIssues)
        _trainingLevel = State(initialValue: pet.trainingLevel ?? "beginner")
        _trainingGoals = State(initialValue: [])
        _socialBehavior = State(initialValue: pet.socialBehavior ?? "friendly")
        _socialWithDogs = State(initialValue: "friendly")
        _socialWithCats = State(initialValue: "friendly")
        _socialWithPeople = State(initialValue: "friendly")
        _socialWithChildren = State(initialValue: "friendly")
        // Emergency Contacts
        _emergencyContacts = State(initialValue: pet.emergencyContacts)
        // AI
        _aiRecommendations = State(initialValue: pet.aiRecommendations)
    }
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Selector
                editTabSelectorSection
                // Tabbed Content
                TabView(selection: $selectedTab) {
                    basicInfoEditTab
                        .tag(0)
                    healthEditTab
                        .tag(1)
                    nutritionEditTab
                        .tag(2)
                    exerciseEditTab
                        .tag(3)
                    trainingEditTab
                        .tag(4)
                    veterinarianEditTab
                        .tag(5)
                    emergencyEditTab
                        .tag(6)
                    deleteTab
                        .tag(7)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Edit \(pet.name)")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        savePet()
                    }
                    .fontWeight(.semibold)
                    .disabled(isLoading || name.isEmpty)
                }
            }
            .sheet(isPresented: $showingImagePicker) {
                PetDetailImagePicker(selectedImage: $selectedImage)
            }
            .onChange(of: selectedImage) { _, newImage in
                profileImage = newImage
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
            .alert("Delete Pet", isPresented: $showingDeleteAlert) {
                TextField("Type 'delete permanently' to confirm", text: $deleteConfirmationText)
                Button("Cancel", role: .cancel) {
                    deleteConfirmationText = ""
                }
                Button("Delete", role: .destructive) {
                    deletePet()
                }
                .disabled(deleteConfirmationText.lowercased() != "delete permanently")
            } message: {
                Text("This action cannot be undone. Type 'delete permanently' to confirm deletion of \(pet.name) and all associated memories.")
            }
            .overlay {
                if isLoading || isDeleting {
                    ZStack {
                        Color.black.opacity(0.3)
                            .ignoresSafeArea()
                        ProgressView(isDeleting ? "Deleting..." : "Saving...")
                            .padding()
                            .background(Color(.systemBackground))
                            .cornerRadius(10)
                    }
                }
            }
        }
    }
    // MARK: - Edit Tab Selector
    private var editTabSelectorSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                editTabButton("Basic", systemImage: "heart.fill", tag: 0)
                editTabButton("Health", systemImage: "cross.fill", tag: 1)
                editTabButton("Nutrition", systemImage: "leaf.fill", tag: 2)
                editTabButton("Exercise", systemImage: "figure.walk", tag: 3)
                editTabButton("Training", systemImage: "brain.head.profile", tag: 4)
                editTabButton("Vet Info", systemImage: "stethoscope", tag: 5)
                editTabButton("Emergency", systemImage: "phone.fill", tag: 6)
                editTabButton("Delete", systemImage: "trash.fill", tag: 7)
            }
            .padding(.horizontal)
        }
        .background(Color(.systemGroupedBackground))
        .padding(.vertical, 8)
    }
    private func editTabButton(_ title: String, systemImage: String, tag: Int) -> some View {
        Button(action: {
            selectedTab = tag
        }) {
            VStack(spacing: 4) {
                Image(systemName: systemImage)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(selectedTab == tag ? .white : .secondary)
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == tag ? .white : .secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == tag ? Color.blue : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    // MARK: - Basic Info Edit Tab
    private var basicInfoEditTab: some View {
        Form {
            Section("Pet Information") {
                profileImageSection
                TextField("Pet Name", text: $name)
                Picker("Species", selection: $species) {
                    Text("Dog").tag("dog")
                    Text("Cat").tag("cat")
                    Text("Bird").tag("bird")
                    Text("Rabbit").tag("rabbit")
                    Text("Other").tag("other")
                }
                TextField("Breed", text: $breed)
                // Age or Date of Birth
                Toggle("Use Date of Birth", isOn: $useDateOfBirth)
                if useDateOfBirth {
                    DatePicker("Date of Birth", selection: $dateOfBirth, displayedComponents: .date)
                        .datePickerStyle(.compact)
                } else {
                    Stepper("Age: \(age) years", value: $age, in: 0...30)
                }
                TextField("Weight (kg)", text: $weight)
                    .keyboardType(.decimalPad)
                Picker("Gender", selection: $gender) {
                    Text("Select").tag("")
                    Text("Male").tag("male")
                    Text("Female").tag("female")
                    Text("Unknown").tag("unknown")
                }
                Toggle("Spayed/Neutered", isOn: $isSpayedNeutered)
                Toggle("Indoor Pet", isOn: $isIndoor)
            }
            Section("About") {
                TextField("Bio", text: $bio, axis: .vertical)
                    .lineLimit(3...6)
                Picker("Activity Level", selection: $activityLevel) {
                    Text("Low").tag("low")
                    Text("Moderate").tag("moderate")
                    Text("High").tag("high")
                    Text("Very High").tag("very_high")
                }
                TextField("Microchip ID", text: $microchipId)
            }
            Section("Allergies") {
                ForEach(allergies, id: \.self) { allergy in
                    HStack {
                        Text(allergy)
                        Spacer()
                        Button("Remove") {
                            allergies.removeAll { $0 == allergy }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add allergy", text: $newAllergy)
                    Button("Add") {
                        if !newAllergy.isEmpty {
                            allergies.append(newAllergy)
                            newAllergy = ""
                        }
                    }
                    .disabled(newAllergy.isEmpty)
                }
            }
        }
    }
    // MARK: - Profile Image Section
    private var profileImageSection: some View {
        HStack {
            if let profileImage = profileImage {
                Image(uiImage: profileImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 80, height: 80)
                    .clipShape(Circle())
            } else if let url = pet.profileImageURL, !url.isEmpty {
                AsyncImage(url: URL(string: url)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(Color(.systemGray5))
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 80, height: 80)
                .clipShape(Circle())
            } else {
                Circle()
                    .fill(Color(.systemGray5))
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(.gray)
                    )
            }
            VStack(alignment: .leading) {
                Text("Profile Photo")
                    .font(.headline)
                Button("Change Photo") {
                    showingImagePicker = true
                }
                .font(.caption)
            }
            Spacer()
        }
        .padding(.vertical, 8)
    }
    // MARK: - Health Edit Tab
    private var healthEditTab: some View {
        Form {
            Section("Health Score") {
                HStack {
                    Text("Health Score")
                    Spacer()
                    Text("\(Int(healthScore * 100))%")
                        .fontWeight(.semibold)
                }
                Slider(value: $healthScore, in: 0...1, step: 0.01)
            }
            Section("Medications") {
                ForEach(medications.indices, id: \.self) { index in
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(medications[index].name)
                                .fontWeight(.semibold)
                            Spacer()
                            Button("Remove") {
                                medications.remove(at: index)
                            }
                            .foregroundColor(.red)
                        }
                        Text("\(medications[index].dosage) - \(medications[index].frequency)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
                Button("Add Medication") {
                    let medication = Medication(
                        name: "New Medication",
                        dosage: "As directed",
                        frequency: "Daily",
                        prescribedBy: "Veterinarian",
                        startDate: Date(),
                        purpose: "Treatment"
                    )
                    medications.append(medication)
                }
            }
            Section("Vaccinations") {
                ForEach(vaccinations.indices, id: \.self) { index in
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(vaccinations[index].vaccineName)
                                .fontWeight(.semibold)
                            Spacer()
                            Button("Remove") {
                                vaccinations.remove(at: index)
                            }
                            .foregroundColor(.red)
                        }
                        Text("Administered: \(vaccinations[index].dateAdministered, style: .date)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        if let nextDue = vaccinations[index].nextDueDate {
                            Text("Next due: \(nextDue, style: .date)")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                    .padding(.vertical, 4)
                }
                Button("Add Vaccination") {
                    let vaccination = VaccinationRecord(
                        vaccineName: "New Vaccine",
                        dateAdministered: Date(),
                        nextDueDate: Calendar.current.date(byAdding: .year, value: 1, to: Date()),
                        veterinarian: "Dr. Veterinarian",
                        clinic: "Clinic Name"
                    )
                    vaccinations.append(vaccination)
                }
            }
            Section("Medical Conditions") {
                ForEach(medicalConditions.indices, id: \.self) { index in
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(medicalConditions[index].name)
                                .fontWeight(.semibold)
                            Spacer()
                            Button("Remove") {
                                medicalConditions.remove(at: index)
                            }
                            .foregroundColor(.red)
                        }
                        Text("Severity: \(medicalConditions[index].severity.capitalized)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("Status: \(medicalConditions[index].status.capitalized)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
                Button("Add Medical Condition") {
                    let condition = MedicalCondition(
                        name: "New Condition",
                        diagnosedDate: Date(),
                        severity: "mild",
                        status: "active"
                    )
                    medicalConditions.append(condition)
                }
            }
        }
    }
    // MARK: - Nutrition Edit Tab
    private var nutritionEditTab: some View {
        Form {
            Section("Basic Nutrition") {
                TextField("Current Food", text: $currentFood)
                TextField("Food Brand", text: $foodBrand)
                TextField("Daily Water Intake (ml)", text: $waterIntakeML)
                    .keyboardType(.numberPad)
                Picker("Feeding Schedule", selection: $feedingSchedule) {
                    Text("Once Daily").tag("once_daily")
                    Text("Twice Daily").tag("twice_daily")
                    Text("Three Times Daily").tag("three_times_daily")
                    Text("Free Feeding").tag("free_feeding")
                }
                .pickerStyle(MenuPickerStyle())
                TextField("Special Diet Notes", text: $specialDiet, axis: .vertical)
                    .lineLimit(2...4)
            }
            Section("Food Allergies") {
                ForEach(foodAllergies, id: \.self) { allergy in
                    HStack {
                        Text(allergy)
                        Spacer()
                        Button("Remove") {
                            foodAllergies.removeAll { $0 == allergy }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add food allergy", text: $newFoodAllergy)
                    Button("Add") {
                        if !newFoodAllergy.isEmpty {
                            foodAllergies.append(newFoodAllergy)
                            newFoodAllergy = ""
                        }
                    }
                    .disabled(newFoodAllergy.isEmpty)
                }
            }
            Section("Dietary Restrictions") {
                ForEach(dietaryRestrictions, id: \.self) { restriction in
                    HStack {
                        Text(restriction)
                        Spacer()
                        Button("Remove") {
                            dietaryRestrictions.removeAll { $0 == restriction }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add dietary restriction", text: $newDietaryRestriction)
                    Button("Add") {
                        if !newDietaryRestriction.isEmpty {
                            dietaryRestrictions.append(newDietaryRestriction)
                            newDietaryRestriction = ""
                        }
                    }
                    .disabled(newDietaryRestriction.isEmpty)
                }
            }
            Section("Treat Preferences") {
                ForEach(treatPreferences, id: \.self) { treat in
                    HStack {
                        Text(treat)
                        Spacer()
                        Button("Remove") {
                            treatPreferences.removeAll { $0 == treat }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add treat preference", text: $newTreatPreference)
                    Button("Add") {
                        if !newTreatPreference.isEmpty {
                            treatPreferences.append(newTreatPreference)
                            newTreatPreference = ""
                        }
                    }
                    .disabled(newTreatPreference.isEmpty)
                }
            }
            Section("Feeding Schedule") {
                ForEach(feedingTimes.indices, id: \.self) { index in
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Feeding \(index + 1)")
                                .fontWeight(.semibold)
                            Spacer()
                            Button("Remove") {
                                feedingTimes.remove(at: index)
                            }
                            .foregroundColor(.red)
                        }
                        Text("Time: \(feedingTimes[index].time, style: .time)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("Food: \(feedingTimes[index].foodType)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("Amount: \(feedingTimes[index].amount)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
                Button("Add Feeding Time") {
                    let feedingTime = FeedingTime(
                        time: Date(),
                        foodType: "Dry Food",
                        amount: "1 cup",
                        isCompleted: false
                    )
                    feedingTimes.append(feedingTime)
                }
            }
        }
    }
    // MARK: - Exercise Edit Tab
    private var exerciseEditTab: some View {
        Form {
            Section("Exercise Requirements") {
                Stepper("Daily Exercise: \(dailyExerciseMinutes) minutes", value: $dailyExerciseMinutes, in: 0...300, step: 15)
                Picker("Walking Frequency", selection: $walkingFrequency) {
                    Text("Daily").tag("daily")
                    Text("Twice Daily").tag("twice_daily")
                    Text("Every Other Day").tag("every_other_day")
                    Text("Weekly").tag("weekly")
                }
                .pickerStyle(MenuPickerStyle())
            }
            Section("Favorite Activities") {
                ForEach(favoriteActivities, id: \.self) { activity in
                    HStack {
                        Text(activity)
                        Spacer()
                        Button("Remove") {
                            favoriteActivities.removeAll { $0 == activity }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add activity", text: $newActivity)
                    Button("Add") {
                        if !newActivity.isEmpty {
                            favoriteActivities.append(newActivity)
                            newActivity = ""
                        }
                    }
                    .disabled(newActivity.isEmpty)
                }
            }
            Section("Exercise Restrictions") {
                ForEach(exerciseRestrictions, id: \.self) { restriction in
                    HStack {
                        Text(restriction)
                        Spacer()
                        Button("Remove") {
                            exerciseRestrictions.removeAll { $0 == restriction }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add exercise restriction", text: $newExerciseRestriction)
                    Button("Add") {
                        if !newExerciseRestriction.isEmpty {
                            exerciseRestrictions.append(newExerciseRestriction)
                            newExerciseRestriction = ""
                        }
                    }
                    .disabled(newExerciseRestriction.isEmpty)
                }
            }
            Section("Play Preferences") {
                ForEach(playPreferences, id: \.self) { preference in
                    HStack {
                        Text(preference)
                        Spacer()
                        Button("Remove") {
                            playPreferences.removeAll { $0 == preference }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add play preference", text: $newPlayPreference)
                    Button("Add") {
                        if !newPlayPreference.isEmpty {
                            playPreferences.append(newPlayPreference)
                            newPlayPreference = ""
                        }
                    }
                    .disabled(newPlayPreference.isEmpty)
                }
            }
        }
    }
    // MARK: - Training Edit Tab
    private var trainingEditTab: some View {
        Form {
            Section("Training Level") {
                Picker("Training Level", selection: $trainingLevel) {
                    Text("Beginner").tag("beginner")
                    Text("Basic").tag("basic")
                    Text("Intermediate").tag("intermediate")
                    Text("Advanced").tag("advanced")
                    Text("Expert").tag("expert")
                }
            }
            Section("Known Commands") {
                ForEach(knownCommands, id: \.self) { command in
                    HStack {
                        Text(command)
                        Spacer()
                        Button("Remove") {
                            knownCommands.removeAll { $0 == command }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add command", text: $newCommand)
                    Button("Add") {
                        if !newCommand.isEmpty {
                            knownCommands.append(newCommand)
                            newCommand = ""
                        }
                    }
                    .disabled(newCommand.isEmpty)
                }
            }
            Section("Behavior Issues") {
                ForEach(behaviorIssues, id: \.self) { issue in
                    HStack {
                        Text(issue)
                        Spacer()
                        Button("Remove") {
                            behaviorIssues.removeAll { $0 == issue }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add behavior issue", text: $newBehaviorIssue)
                    Button("Add") {
                        if !newBehaviorIssue.isEmpty {
                            behaviorIssues.append(newBehaviorIssue)
                            newBehaviorIssue = ""
                        }
                    }
                    .disabled(newBehaviorIssue.isEmpty)
                }
            }
            Section("Training Goals") {
                ForEach(trainingGoals, id: \.self) { goal in
                    HStack {
                        Text(goal)
                        Spacer()
                        Button("Remove") {
                            trainingGoals.removeAll { $0 == goal }
                        }
                        .foregroundColor(.red)
                    }
                }
                HStack {
                    TextField("Add training goal", text: $newTrainingGoal)
                    Button("Add") {
                        if !newTrainingGoal.isEmpty {
                            trainingGoals.append(newTrainingGoal)
                            newTrainingGoal = ""
                        }
                    }
                    .disabled(newTrainingGoal.isEmpty)
                }
            }
            Section("Social Behavior") {
                Picker("General Social Behavior", selection: $socialBehavior) {
                    Text("Friendly").tag("friendly")
                    Text("Cautious").tag("cautious")
                    Text("Aggressive").tag("aggressive")
                    Text("Unknown").tag("unknown")
                }
                .pickerStyle(MenuPickerStyle())
                Group {
                    Picker("With Dogs", selection: $socialWithDogs) {
                        Text("Friendly").tag("friendly")
                        Text("Cautious").tag("cautious")
                        Text("Aggressive").tag("aggressive")
                        Text("Unknown").tag("unknown")
                    }
                    .pickerStyle(MenuPickerStyle())
                    Picker("With Cats", selection: $socialWithCats) {
                        Text("Friendly").tag("friendly")
                        Text("Cautious").tag("cautious")
                        Text("Aggressive").tag("aggressive")
                        Text("Unknown").tag("unknown")
                    }
                    .pickerStyle(MenuPickerStyle())
                    Picker("With People", selection: $socialWithPeople) {
                        Text("Friendly").tag("friendly")
                        Text("Cautious").tag("cautious")
                        Text("Aggressive").tag("aggressive")
                        Text("Unknown").tag("unknown")
                    }
                    .pickerStyle(MenuPickerStyle())
                    Picker("With Children", selection: $socialWithChildren) {
                        Text("Friendly").tag("friendly")
                        Text("Cautious").tag("cautious")
                        Text("Aggressive").tag("aggressive")
                        Text("Unknown").tag("unknown")
                    }
                    .pickerStyle(MenuPickerStyle())
                }
            }
        }
    }
    // MARK: - Veterinarian Edit Tab
    private var veterinarianEditTab: some View {
        Form {
            Section("Primary Veterinarian") {
                TextField("Veterinarian Name", text: $vetName)
                TextField("Contact Number", text: $vetContact)
                    .keyboardType(.phonePad)
            }
            Section("Emergency Veterinarian") {
                TextField("Emergency Vet Name", text: $emergencyVetName)
                TextField("Emergency Contact", text: $emergencyVetContact)
                    .keyboardType(.phonePad)
            }
            Section("Insurance Information") {
                TextField("Insurance Provider", text: $insuranceProvider)
                TextField("Policy Number", text: $insurancePolicyNumber)
            }
            Section("Health Records") {
                if let lastCheckup = lastCheckupDate {
                    DatePicker("Last Checkup", selection: Binding(
                        get: { lastCheckup },
                        set: { lastCheckupDate = $0 }
                    ), displayedComponents: .date)
                } else {
                    Button("Set Last Checkup Date") {
                        lastCheckupDate = Date()
                    }
                }
                if let nextVac = nextVaccinationDate {
                    DatePicker("Next Vaccination", selection: Binding(
                        get: { nextVac },
                        set: { nextVaccinationDate = $0 }
                    ), displayedComponents: .date)
                } else {
                    Button("Set Next Vaccination Date") {
                        nextVaccinationDate = Date()
                    }
                }
            }
        }
    }
        // MARK: - Emergency Edit Tab
    private var emergencyEditTab: some View {
        List {
            Section("Emergency Contacts") {
                if emergencyContacts.isEmpty {
                    Text("No emergency contacts added yet")
                        .foregroundColor(.secondary)
                        .italic()
                } else {
                    ForEach(emergencyContacts.indices, id: \.self) { index in
                        let contact = emergencyContacts[index]
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text(contact.name)
                                    .fontWeight(.semibold)
                                Spacer()
                                Button("Remove") {
                                    emergencyContacts.remove(at: index)
                                }
                                .foregroundColor(.red)
                            }
                            HStack {
                                Text("Phone: \(contact.phoneNumber)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Spacer()
                                Text("Type: \(contact.type.rawValue.capitalized)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
            Section("Add Emergency Contact") {
                VStack(spacing: 12) {
                    TextField("Name", text: $newEmergencyContact.name)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    TextField("Phone", text: $newEmergencyContact.phoneNumber)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.phonePad)
                    Picker("Contact Type", selection: $newEmergencyContact.type) {
                        Text("Emergency").tag(EmergencyContactType.emergency)
                        Text("Poison Control").tag(EmergencyContactType.emergency)
                        Text("Veterinarian").tag(EmergencyContactType.veterinarian)
                        Text("Animal Hospital").tag(EmergencyContactType.veterinarian)
                        Text("Custom").tag(EmergencyContactType.custom)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    Button("Add Contact") {
                        if !newEmergencyContact.name.isEmpty && !newEmergencyContact.phoneNumber.isEmpty {
                            let contact = EmergencyContact(
                                id: UUID().uuidString,
                                name: newEmergencyContact.name,
                                phoneNumber: newEmergencyContact.phoneNumber,
                                type: newEmergencyContact.type,
                                country: newEmergencyContact.country
                            )
                                                    emergencyContacts.append(contact)
                        newEmergencyContact = EmergencyContact(id: UUID().uuidString, name: "", phoneNumber: "", type: .custom, country: "US", isEditable: true)
                        }
                    }
                    .disabled(newEmergencyContact.name.isEmpty || newEmergencyContact.phoneNumber.isEmpty)
                    .buttonStyle(.borderedProminent)
                }
                .listRowBackground(Color.clear)
            }
        }
    }
    // MARK: - Delete Tab
    private var deleteTab: some View {
        Form {
            Section {
                Button(action: {
                    showingDeleteAlert = true
                }) {
                    HStack {
                        Image(systemName: "trash.fill")
                            .foregroundColor(.white)
                        Text("Delete Pet")
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.red)
                    .cornerRadius(10)
                }
                .disabled(isDeleting)
            } footer: {
                Text("This action cannot be undone. All memories and data associated with this pet will be permanently deleted.")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    private func savePet() {
        isLoading = true
        Task {
            do {
                // Create updated pet object
                let updatedPet = pet
                // Basic Information
                updatedPet.name = name
                updatedPet.species = species
                updatedPet.breed = breed.isEmpty ? "Mixed" : breed
                updatedPet.age = age
                updatedPet.weight = Double(weight)
                updatedPet.gender = gender.isEmpty ? nil : gender
                updatedPet.bio = bio
                updatedPet.activityLevel = activityLevel
                updatedPet.microchipId = microchipId.isEmpty ? nil : microchipId
                updatedPet.isSpayedNeutered = isSpayedNeutered
                updatedPet.isIndoor = isIndoor
                updatedPet.allergies = allergies
                // Date of birth handling
                if useDateOfBirth {
                    updatedPet.dateOfBirth = dateOfBirth
                }
                // Health Information
                updatedPet.healthScore = healthScore
                updatedPet.medications = medications
                updatedPet.vaccinationRecords = vaccinations
                updatedPet.medicalConditions = medicalConditions
                updatedPet.weightHistory = weightRecords
                updatedPet.bloodType = bloodType.isEmpty ? nil : bloodType
                updatedPet.lastCheckupDate = lastCheckupDate
                // Veterinarian Information
                updatedPet.primaryVet = primaryVet
                updatedPet.emergencyVet = emergencyVet
                updatedPet.vetName = vetName.isEmpty ? nil : vetName
                updatedPet.vetContact = vetContact.isEmpty ? nil : vetContact
                // Update existing veterinarian info or create new ones
                if !vetName.isEmpty || !vetContact.isEmpty {
                    if updatedPet.primaryVet == nil {
                        updatedPet.primaryVet = VeterinarianInfo(
                            clinicName: vetName.isEmpty ? nil : vetName,
                            veterinarianName: vetName.isEmpty ? nil : vetName,
                            phoneNumber: vetContact.isEmpty ? nil : vetContact,
                            emergencyPhoneNumber: nil,
                            address: nil,
                            email: nil,
                            website: nil,
                            is24Hour: false,
                            specialties: [],
                            notes: nil
                        )
                    } else {
                        updatedPet.primaryVet?.clinicName = vetName.isEmpty ? nil : vetName
                        updatedPet.primaryVet?.veterinarianName = vetName.isEmpty ? nil : vetName
                        updatedPet.primaryVet?.phoneNumber = vetContact.isEmpty ? nil : vetContact
                    }
                }
                if !emergencyVetName.isEmpty || !emergencyVetContact.isEmpty {
                    if updatedPet.emergencyVet == nil {
                        updatedPet.emergencyVet = VeterinarianInfo(
                            clinicName: emergencyVetName.isEmpty ? nil : emergencyVetName,
                            veterinarianName: emergencyVetName.isEmpty ? nil : emergencyVetName,
                            phoneNumber: emergencyVetContact.isEmpty ? nil : emergencyVetContact,
                            emergencyPhoneNumber: nil,
                            address: nil,
                            email: nil,
                            website: nil,
                            is24Hour: true,
                            specialties: [],
                            notes: nil
                        )
                    } else {
                        updatedPet.emergencyVet?.clinicName = emergencyVetName.isEmpty ? nil : emergencyVetName
                        updatedPet.emergencyVet?.veterinarianName = emergencyVetName.isEmpty ? nil : emergencyVetName
                        updatedPet.emergencyVet?.phoneNumber = emergencyVetContact.isEmpty ? nil : emergencyVetContact
                    }
                }
                // Insurance Information
                if !insuranceProvider.isEmpty || !insurancePolicyNumber.isEmpty {
                    if updatedPet.insuranceInfo == nil {
                        updatedPet.insuranceInfo = InsuranceInfo(
                            provider: insuranceProvider.isEmpty ? nil : insuranceProvider,
                            policyNumber: insurancePolicyNumber.isEmpty ? nil : insurancePolicyNumber,
                            groupNumber: nil,
                            memberID: insurancePolicyNumber.isEmpty ? nil : insurancePolicyNumber,
                            phoneNumber: nil,
                            website: nil,
                            coverageDetails: nil,
                            deductible: nil,
                            copayAmount: nil
                        )
                    } else {
                        updatedPet.insuranceInfo?.provider = insuranceProvider.isEmpty ? nil : insuranceProvider
                        updatedPet.insuranceInfo?.policyNumber = insurancePolicyNumber.isEmpty ? nil : insurancePolicyNumber
                        updatedPet.insuranceInfo?.memberID = insurancePolicyNumber.isEmpty ? nil : insurancePolicyNumber
                    }
                }
                // Nutrition Information
                updatedPet.currentFood = currentFood.isEmpty ? nil : currentFood
                updatedPet.foodBrand = foodBrand.isEmpty ? nil : foodBrand
                updatedPet.waterIntakeML = Int(waterIntakeML)
                updatedPet.feedingSchedule = feedingTimes
                updatedPet.foodAllergies = foodAllergies
                updatedPet.dietaryRestrictions = dietaryRestrictions
                updatedPet.specialInstructions = specialDiet.isEmpty ? nil : specialDiet
                // Exercise Information
                updatedPet.exerciseMinutesDaily = dailyExerciseMinutes
                updatedPet.favoriteActivities = favoriteActivities
                updatedPet.walkingFrequency = walkingFrequency.isEmpty ? nil : walkingFrequency
                // Training Information
                updatedPet.knownCommands = knownCommands
                updatedPet.behaviorIssues = behaviorIssues
                updatedPet.trainingLevel = trainingLevel.isEmpty ? nil : trainingLevel
                updatedPet.socialBehavior = socialBehavior.isEmpty ? nil : socialBehavior
                // Emergency Contacts
                updatedPet.emergencyContacts = emergencyContacts
                // AI Recommendations
                updatedPet.aiRecommendations = aiRecommendations
                // Update modification timestamp
                updatedPet.updatedAt = Date()
                // Upload new image if selected
                if let newImage = profileImage {
                    let imageURL = try await uploadPetImage(newImage)
                    updatedPet.profileImageURL = imageURL
                }
                // Update pet in database
                guard let userId = realDataService.getCurrentUserId() else {
                    print("❌ No authenticated user found for pet update")
                    return
                }
                let success = await realDataService.updatePet(updatedPet)
                await MainActor.run {
                    isLoading = false
                    if success {
                        dismiss()
                    } else {
                        errorMessage = "Failed to update pet. Please try again."
                        showingError = true
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "Error updating pet: \(error.localizedDescription)"
                    showingError = true
                }
            }
        }
    }
    private func deletePet() {
        isDeleting = true
        Task {
            let success = await realDataService.deletePet(UUID(uuidString: pet.id) ?? UUID())
            await MainActor.run {
                isDeleting = false
                deleteConfirmationText = ""
                if success {
                    // Close the edit view and the detail view
                    dismiss()
                } else {
                    errorMessage = "Failed to delete pet. Please try again."
                    showingError = true
                }
            }
        }
    }
    private func uploadPetImage(_ image: UIImage) async throws -> String? {
        // Use the same upload logic as AddPetView
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw NSError(domain: "ImageError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert image"])
        }
        guard imageData.count <= 10 * 1024 * 1024 else {
            throw NSError(domain: "ImageError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Image too large"])
        }
        let fileName = "pet_\(pet.id)_edit_\(Date().timeIntervalSince1970).jpg"
        do {
            // Using Apple native storage instead of external services
            print("Uploading pet photo with Apple native services: \(fileName)")
            // TODO: Implement proper photo upload with Apple services
            let publicURL = URL(string: "https://placeholder.com/\(fileName)")!
            return publicURL.absoluteString
        } catch {
            // Fallback to local storage in development mode
            if Config.Features.skipAuthentication {
                return try await storeImageLocally(image, fileName: fileName)
            } else {
                throw error
            }
        }
    }
    private func storeImageLocally(_ image: UIImage, fileName: String) async throws -> String {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw NSError(domain: "FileError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not access documents directory"])
        }
        let imageURL = documentsPath.appendingPathComponent(fileName)
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw NSError(domain: "ImageError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert image"])
        }
        try imageData.write(to: imageURL)
        return imageURL.absoluteString
    }
}
// MARK: - Pet Detail Image Picker
struct PetDetailImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.dismiss) private var dismiss
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = true
        return picker
    }
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: PetDetailImagePicker
        init(_ parent: PetDetailImagePicker) {
            self.parent = parent
        }
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.selectedImage = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.selectedImage = originalImage
            }
            parent.dismiss()
        }
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}
// MARK: - Edit Tab Button
struct EditTabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.petSubheadline)
                .fontWeight(isSelected ? .bold : .medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.purple : Color.clear)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.purple, lineWidth: isSelected ? 0 : 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
// MARK: - Helper Views for Adding Data
// Note: Using simplified inline forms instead of separate views to avoid duplication