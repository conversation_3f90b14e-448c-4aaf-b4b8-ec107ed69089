//
//  PetProfileCreationView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//
import SwiftUI
import PhotosUI
struct PetProfileCreationView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var realDataService: RealDataService
    @State private var name = ""
    @State private var species = "dog"
    @State private var breed = ""
    @State private var customBreed = "" // Separate variable for custom breed input
    @State private var age = 0
    @State private var dateOfBirth = Date()
    @State private var bio = ""
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var profileImage: UIImage?
    @State private var useDateOfBirth = false
    @State private var isCreating = false
    @State private var showingError = false
    @State private var errorMessage = ""
    private let speciesOptions = ["dog", "cat", "bird", "rabbit", "hamster", "fish", "reptile", "other"]
    private var speciesBreeds: [String] {
        PetBreedData.getBreeds(for: species)
    }
    private func getSpeciesIcon(for species: String) -> String {
        PetBreedData.getSpeciesIcon(for: species)
    }
    private var displayBreedText: String {
        if breed.isEmpty {
            return "Select breed"
        } else if breed == "Other" {
            return customBreed.isEmpty ? "Other (enter custom breed)" : customBreed
        } else {
            return breed
        }
    }
    private var finalBreedValue: String {
        return breed == "Other" ? customBreed : breed
    }
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.lg) {
                    // Profile Photo Section
                    profilePhotoSection
                    // Basic Information
                    basicInfoSection
                    // Age Section
                    ageSection
                    // Bio Section
                    bioSection
                    Spacer(minLength: Spacing.xl)
                }
                .padding(Spacing.lg)
            }
            .navigationTitle("Create Pet Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createPetProfile()
                    }
                    .disabled(!canCreate || isCreating)
                }
            }
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }
    private var profilePhotoSection: some View {
        VStack(spacing: Spacing.md) {
            PhotosPicker(
                selection: $selectedPhoto,
                matching: .images,
                photoLibrary: .shared()
            ) {
                if let profileImage = profileImage {
                    Image(uiImage: profileImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 120, height: 120)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.petAccent, lineWidth: 3)
                        )
                } else {
                    Circle()
                        .fill(Color.petSecondaryBackground)
                        .frame(width: 120, height: 120)
                        .overlay(
                            VStack(spacing: Spacing.sm) {
                                Image(systemName: "camera.fill")
                                    .font(.title)
                                    .foregroundColor(.petAccent)
                                Text("Add Photo")
                                    .font(.petCaption)
                                    .foregroundColor(.petSecondaryText)
                            }
                        )
                        .overlay(
                            Circle()
                                .stroke(Color.petAccent.opacity(0.3), lineWidth: 2)
                        )
                }
            }
            .onChange(of: selectedPhoto) { _, newValue in
                Task {
                    if let newValue = newValue {
                        if let data = try? await newValue.loadTransferable(type: Data.self) {
                            profileImage = UIImage(data: data)
                        }
                    }
                }
            }
            Text("Tap to add a photo of your pet")
                .font(.petCaption)
                .foregroundColor(.petSecondaryText)
        }
    }
    private var basicInfoSection: some View {
        VStack(spacing: Spacing.md) {
            // Name Input
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Pet Name")
                    .font(.petHeadline)
                    .foregroundColor(.petText)
                TextField("Enter your pet's name", text: $name)
                    .petTextFieldStyle()
            }
            // Species Selection
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Species")
                    .font(.petHeadline)
                    .foregroundColor(.petText)
                Menu {
                    ForEach(speciesOptions, id: \.self) { speciesOption in
                        Button(action: {
                            species = speciesOption
                            // Reset breed selection when species changes
                            breed = ""
                            customBreed = ""
                        }) {
                            HStack {
                                Text(getSpeciesIcon(for: speciesOption))
                                Text(speciesOption.capitalized)
                            }
                        }
                    }
                } label: {
                    HStack {
                        Text(getSpeciesIcon(for: species))
                        Text(species.capitalized)
                        Spacer()
                        Image(systemName: "chevron.down")
                            .foregroundColor(.petSecondaryText)
                    }
                    .padding(Spacing.md)
                    .background(Color.petSecondaryBackground)
                    .cornerRadius(CornerRadius.md)
                    .overlay(
                        RoundedRectangle(cornerRadius: CornerRadius.md)
                            .stroke(Color.petTertiaryText.opacity(0.3), lineWidth: 1)
                    )
                }
            }
            // Breed Selection
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("Breed")
                    .font(.petHeadline)
                    .foregroundColor(.petText)
                Menu {
                    ForEach(speciesBreeds, id: \.self) { breedOption in
                        Button(breedOption) {
                            breed = breedOption
                            if breedOption != "Other" {
                                customBreed = "" // Clear custom breed when selecting a predefined breed
                            }
                        }
                    }
                } label: {
                    HStack {
                        Text(displayBreedText)
                            .foregroundColor(breed.isEmpty ? .petSecondaryText : .petText)
                        Spacer()
                        Image(systemName: "chevron.down")
                            .foregroundColor(.petSecondaryText)
                    }
                    .padding(Spacing.md)
                    .background(Color.petSecondaryBackground)
                    .cornerRadius(CornerRadius.md)
                    .overlay(
                        RoundedRectangle(cornerRadius: CornerRadius.md)
                            .stroke(Color.petTertiaryText.opacity(0.3), lineWidth: 1)
                    )
                }
                if breed == "Other" {
                    TextField("Enter custom breed", text: $customBreed)
                        .petTextFieldStyle()
                        .onChange(of: customBreed) {
                            // Optional: You could auto-update as they type, or wait for them to finish
                        }
                }
            }
        }
    }
    private var ageSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Age Information")
                .font(.petHeadline)
                .foregroundColor(.petText)
            Toggle("Use date of birth", isOn: $useDateOfBirth)
                .toggleStyle(SwitchToggleStyle(tint: .petAccent))
            if useDateOfBirth {
                DatePicker(
                    "Date of Birth",
                    selection: $dateOfBirth,
                    in: ...Date(),
                    displayedComponents: .date
                )
                .datePickerStyle(CompactDatePickerStyle())
            } else {
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    Text("Age (years)")
                        .font(.petSubheadline)
                        .foregroundColor(.petText)
                    Stepper(value: $age, in: 0...30) {
                        Text(age == 0 ? "Select age" : "\(age) year\(age == 1 ? "" : "s") old")
                            .font(.petBody)
                            .foregroundColor(age == 0 ? .petSecondaryText : .petText)
                    }
                }
            }
        }
        .padding(Spacing.md)
        .background(Color.petSecondaryBackground)
        .cornerRadius(CornerRadius.md)
    }
    private var bioSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Bio (Optional)")
                .font(.petHeadline)
                .foregroundColor(.petText)
            TextField("Tell us about your pet's personality, favorite activities, or special traits...", text: $bio, axis: .vertical)
                .lineLimit(3...6)
                .petTextFieldStyle()
            Text("\(bio.count)/500")
                .font(.petCaption)
                .foregroundColor(.petSecondaryText)
                .frame(maxWidth: .infinity, alignment: .trailing)
        }
    }
    private var canCreate: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !finalBreedValue.isEmpty &&
        bio.count <= 500 &&
        (useDateOfBirth || age > 0)
    }
    private func createPetProfile() {
        guard let currentUser = AuthenticationService.shared.currentUser else {
            errorMessage = "You must be signed in to create a pet profile"
            showingError = true
            return
        }
        isCreating = true
        // Calculate birth date
        let calculatedBirthDate: Date
        if useDateOfBirth {
            calculatedBirthDate = dateOfBirth
        } else if age > 0 {
            calculatedBirthDate = Calendar.current.date(byAdding: .year, value: -age, to: Date()) ?? Date()
        } else {
            calculatedBirthDate = Date() // Default to current date if no age is provided
        }
        // Upload image if selected
        var profileImageURL: String? = nil
        if let profileImage = profileImage {
            // TODO: Implement image upload to storage service
            // For now, convert to base64 data URL for temporary storage
            if let imageData = profileImage.jpegData(compressionQuality: 0.8) {
                profileImageURL = "data:image/jpeg;base64," + imageData.base64EncodedString()
            }
        }
        // Use the convenience initializer which is more appropriate for pet profile creation
        let newPet = Pet(
            name: name.trimmingCharacters(in: .whitespacesAndNewlines),
            species: species,
            breed: finalBreedValue.isEmpty ? "" : finalBreedValue,
            birthDate: calculatedBirthDate,
            adoptionDate: Date(),
            weight: nil,
            activityLevel: "moderate",
            personalityTraits: [],
            healthConditions: [],
            medications: [],
            vaccinations: [],
            healthAlerts: [],
            aiRecommendations: [],
            profileImageURL: profileImageURL,
            lastCheckupDate: nil
        )
        // Add to local context
        modelContext
        currentUser.petIDs.append(newPet.id)
        // Save locally first
        do {
            try modelContext.save()
        } catch {
            errorMessage = "Failed to save pet profile locally: \(error.localizedDescription)"
            showingError = true
            isCreating = false
            return
        }
        Task {
            do {
                try await 
                // Refresh RealDataService to show the new pet in MyPetsView
                await realDataService.refreshAllData()
                // Award gems for creating first pet
                let gemReward = GemRewardSystem.earnGems(for: .joinNetwork, user: currentUser)
                modelContext
                try modelContext.save()
                await MainActor.run {
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    errorMessage = "Failed to sync pet profile: \(error.localizedDescription)"
                    showingError = true
                }
            }
            await MainActor.run {
                isCreating = false
            }
        }
    }
    private func calculateAge(from dateOfBirth: Date) -> Int {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: dateOfBirth, to: Date())
        return ageComponents.year ?? 0
    }
}
#Preview {
    PetProfileCreationView()
        .environmentObject(RealDataService())
}
