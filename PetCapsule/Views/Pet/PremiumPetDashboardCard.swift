//
//  PremiumPetDashboardCard.swift
//  PetCapsule
//
//  Premium Pet Dashboard Card - Variant 4 Implementation
//  Monetization-focused design with health alerts and marketplace
//
import SwiftUI
@available(iOS 18.0, *)
struct PremiumPetDashboardCard: View {
    let pet: Pet
    let onUpgrade: () -> Void
    let onAlertAction: (String) -> Void
    let onMarketplaceClick: (String) -> Void
    let onTap: () -> Void
    @State private var selectedTab: DashboardTab = .health
    @State private var favoriteItems: Set<String> = []
    @State private var animateHealth = false
    @State private var pulseAnimation = false
    enum DashboardTab: String, CaseIterable {
        case health = "Health"
        case alerts = "Alerts"
        case marketplace = "Shop"
        var icon: String {
            switch self {
            case .health: return "heart.circle.fill"
            case .alerts: return "bell.fill"
            case .marketplace: return "cart.fill"
            }
        }
    }
    var isPremium: Bool {
        pet.subscriptionTier != "free"
    }
    var criticalAlerts: [HealthAlert] {
        pet.healthAlerts.filter { alert in
            return alert.severity == .critical || alert.severity == .high
        }
    }
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 0) {
                // Premium Upgrade Banner
                if !isPremium {
                    premiumUpgradeBanner
                }
                // Pet Header
                petHeaderSection
                // Tab Navigation
                tabNavigationSection
                // Tab Content
                tabContentSection
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.9),
                            Color.blue.opacity(0.05),
                            Color.purple.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 24)
                .stroke(Color.gray.opacity(0.1), lineWidth: 1)
        )
        .overlay(alignment: .bottomTrailing) {
            floatingActionButton
        }
        .frame(maxWidth: 380)
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6)) {
                animateHealth = true
            }
            withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: true)) {
                pulseAnimation = true
            }
        }
    }
    // MARK: - Premium Upgrade Banner
    private var premiumUpgradeBanner: some View {
        ZStack {
            LinearGradient(
                colors: [Color.purple, Color.pink],
                startPoint: .leading,
                endPoint: .trailing
            )
            .overlay(
                // Animated background pattern
                LinearGradient(
                    colors: [Color.purple.opacity(0.3), Color.pink.opacity(0.3)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .scaleEffect(pulseAnimation ? 1.2 : 1.0)
                .animation(.easeInOut(duration: 3).repeatForever(autoreverses: true), value: pulseAnimation)
            )
            HStack(spacing: 16) {
                HStack(spacing: 8) {
                    Image(systemName: "crown.fill")
                        .font(.title3)
                        .foregroundColor(.white)
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Upgrade to Premium")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.white)
                        Text("Unlock advanced health insights")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white.opacity(0.9))
                    }
                }
                Spacer()
                Button(action: onUpgrade) {
                    Text("Upgrade")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.white.opacity(0.2))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .clipShape(UnevenRoundedRectangle(topLeadingRadius: 24, topTrailingRadius: 24))
    }
    // MARK: - Pet Header Section
    private var petHeaderSection: some View {
        VStack(spacing: 20) {
            HStack(spacing: 16) {
                // Pet Image with Status
                ZStack {
                    AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.gray.opacity(0.2))
                            .overlay(
                                Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                                    .font(.title)
                            )
                    }
                    .frame(width: 64, height: 64)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 3)
                            .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
                    )
                    .scaleEffect(animateHealth ? 1.0 : 0.8)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateHealth)
                    // Health Status Indicator
                    Circle()
                        .fill(Color.green)
                        .frame(width: 20, height: 20)
                        .overlay(
                            Image(systemName: "heart.fill")
                                .font(.system(size: 10))
                                .foregroundColor(.white)
                        )
                        .overlay(
                            Circle()
                                .stroke(Color.white, lineWidth: 2)
                        )
                        .offset(x: 20, y: -20)
                        .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: pulseAnimation)
                }
                // Pet Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(pet.name)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.primary)
                    Text(pet.breed ?? "Mixed Breed")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                    HStack(spacing: 8) {
                        // Activity Badge
                        HStack(spacing: 4) {
                            Image(systemName: "bolt.fill")
                                .font(.system(size: 10))
                                .foregroundColor(.blue)
                            Text("Active")
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(.blue)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.blue.opacity(0.1))
                        )
                        // Premium Badge
                        if isPremium {
                            HStack(spacing: 4) {
                                Image(systemName: "crown.fill")
                                    .font(.system(size: 10))
                                    .foregroundColor(.white)
                                Text("Premium")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                LinearGradient(
                                    colors: [Color.purple, Color.pink],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                            )
                        }
                    }
                }
                Spacer()
                // Critical Alerts Bell
                if !criticalAlerts.isEmpty {
                    ZStack {
                        Circle()
                            .fill(Color.red.opacity(0.1))
                            .frame(width: 40, height: 40)
                        Image(systemName: "bell.fill")
                            .font(.system(size: 18))
                            .foregroundColor(.red)
                        Circle()
                            .fill(Color.red)
                            .frame(width: 12, height: 12)
                            .offset(x: 12, y: -12)
                            .scaleEffect(pulseAnimation ? 1.2 : 1.0)
                            .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: pulseAnimation)
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    // MARK: - Tab Navigation Section
    private var tabNavigationSection: some View {
        HStack(spacing: 0) {
            ForEach(DashboardTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        selectedTab = tab
                    }
                }) {
                    VStack(spacing: 6) {
                        HStack(spacing: 6) {
                            Image(systemName: tab.icon)
                                .font(.system(size: 14))
                                .foregroundColor(selectedTab == tab ? .purple : .secondary)
                            Text(tab.rawValue)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(selectedTab == tab ? .purple : .secondary)
                            if tab == .alerts && !criticalAlerts.isEmpty {
                                Circle()
                                    .fill(Color.red)
                                    .frame(width: 8, height: 8)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedTab == tab ? Color.purple.opacity(0.1) : Color.clear)
                        )
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 8)
        .background(Color.gray.opacity(0.05))
    }
    // MARK: - Tab Content Section
    private var tabContentSection: some View {
        Group {
            switch selectedTab {
            case .health:
                healthTabContent
            case .alerts:
                alertsTabContent
            case .marketplace:
                marketplaceTabContent
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
        .frame(minHeight: 300)
    }
    // MARK: - Health Tab Content
    private var healthTabContent: some View {
        VStack(spacing: 16) {
            // Health Metrics Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                healthMetricCard(
                    label: "Weight",
                    value: String(format: "%.1f", pet.weight ?? 0),
                    unit: "kg",
                    status: .good,
                    trend: 2
                )
                healthMetricCard(
                    label: "Activity",
                    value: String(Int(pet.healthScore * 100)),
                    unit: "%",
                    status: .good,
                    trend: 15
                )
                healthMetricCard(
                    label: "Health",
                    value: String(Int(pet.healthScore * 100)),
                    unit: "%",
                    status: pet.healthScore > 0.8 ? .good : pet.healthScore > 0.6 ? .concerning : .abnormal,
                    trend: -5
                )
            }
            // Premium Feature Teaser
            if !isPremium {
                premiumFeatureTeaser
            } else {
                // Premium AI Insights
                aiInsightsCard
            }
        }
        .transition(.asymmetric(insertion: .move(edge: .leading), removal: .move(edge: .trailing)))
    }
    private func healthMetricCard(label: String, value: String, unit: String, status: HealthStatus, trend: Int) -> some View {
        VStack(spacing: 8) {
            // Circular Progress
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.2), lineWidth: 3)
                    .frame(width: 48, height: 48)
                Circle()
                    .trim(from: 0, to: animateHealth ? CGFloat(abs(trend)) / 100 : 0)
                    .stroke(
                        status == .good ? Color.green :
                        status == .concerning ? Color.orange : Color.red,
                        style: StrokeStyle(lineWidth: 3, lineCap: .round)
                    )
                    .frame(width: 48, height: 48)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1).delay(0.5), value: animateHealth)
                Text(value)
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.primary)
            }
            Text(label)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.primary)
            Text("\(trend > 0 ? "+" : "")\(trend)%")
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.secondary)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.6))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gray.opacity(0.1), lineWidth: 1)
                )
        )
    }
    private var premiumFeatureTeaser: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [Color.purple, Color.pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 48, height: 48)
                    .overlay(
                        Image(systemName: "sparkles")
                            .font(.system(size: 20))
                            .foregroundColor(.white)
                    )
                VStack(alignment: .leading, spacing: 4) {
                    Text("AI Health Insights")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.purple)
                    Text("Get personalized recommendations")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }
                Spacer()
                Button(action: onUpgrade) {
                    HStack(spacing: 4) {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 10))
                        Text("Unlock")
                            .font(.system(size: 12, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        LinearGradient(
                            colors: [Color.purple, Color.pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.purple.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.purple.opacity(0.2), lineWidth: 1)
                )
        )
    }
    private var aiInsightsCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .font(.system(size: 18))
                    .foregroundColor(.purple)
                Text("AI Health Analysis")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 16))
                    .foregroundColor(.green)
            }
            Text("Your pet's health is excellent! Regular exercise and balanced nutrition are keeping \(pet.name) in optimal condition.")
                .font(.system(size: 12))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.green.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.2), lineWidth: 1)
                )
        )
    }
    // MARK: - Alerts Tab Content
    private var alertsTabContent: some View {
        VStack(spacing: 12) {
            if pet.healthAlerts.isEmpty {
                // No alerts state
                VStack(spacing: 16) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.green)
                    Text("All caught up!")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                    Text("No health alerts at this time")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(.vertical, 40)
            } else {
                // Health alerts list
                ForEach(pet.healthAlerts, id: \.id) { alert in
                    healthAlertCard(alert: alert)
                }
            }
        }
        .transition(.asymmetric(insertion: .move(edge: .leading), removal: .move(edge: .trailing)))
    }
    private func healthAlertCard(alert: HealthAlert) -> some View {
        let alertColor = alertPriorityColor(for: alert.severity)
        return HStack(spacing: 12) {
            // Alert Icon
            RoundedRectangle(cornerRadius: 12)
                .fill(alertColor)
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: alertIcon(for: alert.type))
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                )
            VStack(alignment: .leading, spacing: 4) {
                Text(alert.message)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                Text("Tap for more information")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                HStack(spacing: 4) {
                    Image(systemName: "calendar")
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                    Text("Triggered: \(alert.triggeredAt, formatter: dateFormatter)")
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                }
            }
            Spacer()
            Button(action: {
                onAlertAction(alert.id)
            }) {
                Image(systemName: "arrow.right")
                    .font(.system(size: 12))
                    .foregroundColor(.white)
                    .frame(width: 24, height: 24)
                    .background(Color.blue)
                    .clipShape(Circle())
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(alertColor.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(alertColor.opacity(0.2), lineWidth: 1)
                )
        )
    }
    // MARK: - Marketplace Tab Content
    private var marketplaceTabContent: some View {
        VStack(spacing: 16) {
            // Recommended Products Header
            HStack {
                Text("Recommended for \(pet.name)")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                Spacer()
                HStack(spacing: 4) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.system(size: 10))
                        .foregroundColor(.blue)
                    Text("AI Picked")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.blue)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.blue.opacity(0.1))
                )
            }
            // Marketplace Items
            VStack(spacing: 12) {
                ForEach(generateMarketplaceItems(), id: \.id) { item in
                    marketplaceItemCard(item: item)
                }
            }
            // Premium Delivery Prompt
            premiumDeliveryPrompt
        }
        .transition(.asymmetric(insertion: .move(edge: .leading), removal: .move(edge: .trailing)))
    }
    private func marketplaceItemCard(item: MarketplaceItem) -> some View {
        HStack(spacing: 12) {
            // Product Image
            AsyncImage(url: URL(string: item.imageURL)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.2))
                    .overlay(
                        Image(systemName: "photo")
                            .font(.system(size: 16))
                            .foregroundColor(.gray)
                    )
            }
            .frame(width: 48, height: 48)
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .overlay(
                // Discount Badge
                Group {
                    if item.discount > 0 {
                        Text("-\(item.discount)%")
                            .font(.system(size: 8, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 2)
                            .background(Color.red)
                            .clipShape(RoundedRectangle(cornerRadius: 4))
                            .offset(x: -4, y: -4)
                    }
                }
                , alignment: .topTrailing
            )
            VStack(alignment: .leading, spacing: 4) {
                Text(item.name)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .font(.system(size: 10))
                        .foregroundColor(.yellow)
                    Text(String(format: "%.1f", item.rating))
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                }
                HStack(spacing: 8) {
                    Text(item.formattedPrice)
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.green)
                    if item.originalPrice > item.price {
                        Text("$\(String(format: "%.0f", item.originalPrice))")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                            .strikethrough()
                    }
                }
            }
            Spacer()
            VStack(spacing: 8) {
                // Favorite Button
                Button(action: {
                    toggleFavorite(item.id)
                }) {
                    Image(systemName: favoriteItems.contains(item.id) ? "heart.fill" : "heart")
                        .font(.system(size: 12))
                        .foregroundColor(favoriteItems.contains(item.id) ? .red : .gray)
                        .frame(width: 24, height: 24)
                        .background(Color.gray.opacity(0.1))
                        .clipShape(RoundedRectangle(cornerRadius: 6))
                }
                .buttonStyle(PlainButtonStyle())
                // Add to Cart Button
                Button(action: {
                    onMarketplaceClick(item.id)
                }) {
                    Image(systemName: "cart")
                        .font(.system(size: 12))
                        .foregroundColor(.white)
                        .frame(width: 24, height: 24)
                        .background(Color.blue)
                        .clipShape(RoundedRectangle(cornerRadius: 6))
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.6))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.gray.opacity(0.1), lineWidth: 1)
                )
        )
        .onTapGesture {
            onMarketplaceClick(item.id)
        }
    }
    private var premiumDeliveryPrompt: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [Color.blue, Color.cyan],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 48, height: 48)
                    .overlay(
                        Image(systemName: "award")
                            .font(.system(size: 20))
                            .foregroundColor(.white)
                    )
                VStack(alignment: .leading, spacing: 4) {
                    Text("Premium Delivery")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.blue)
                    Text("Free shipping + 20% off all orders")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }
                Spacer()
                Button(action: onUpgrade) {
                    Text("Subscribe")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            LinearGradient(
                                colors: [Color.blue, Color.cyan],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.blue.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                )
        )
    }
    // MARK: - Floating Action Button
    private var floatingActionButton: some View {
        Button(action: {
            // Handle add action
        }) {
            Image(systemName: "plus")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 48, height: 48)
                .background(
                    LinearGradient(
                        colors: [Color.purple, Color.pink],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    .clipShape(Circle())
                    .shadow(color: Color.purple.opacity(0.4), radius: 8, x: 0, y: 4)
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(pulseAnimation ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: pulseAnimation)
        .padding(.trailing, 16)
        .padding(.bottom, 16)
    }
    // MARK: - Helper Methods
    private func toggleFavorite(_ itemId: String) {
        if favoriteItems.contains(itemId) {
            favoriteItems.remove(itemId)
        } else {
            favoriteItems
        }
    }
    private func alertIcon(for type: String) -> String {
        switch type {
        case "vaccination": return "shield.fill"
        case "checkup": return "heart.circle.fill"
        case "medication": return "pills.fill"
        case "emergency": return "exclamationmark.triangle.fill"
        default: return "bell.fill"
        }
    }
    private func alertPriorityColor(for severity: AlertSeverity) -> Color {
        return severity.color
    }
    private func generateMarketplaceItems() -> [MarketplaceItem] {
        [
            MarketplaceItem(
                id: "1",
                name: "Premium Dog Food",
                price: 45.0,
                originalPrice: 60.0,
                imageURL: "https://via.placeholder.com/200x200",
                rating: 4.8,
                category: "food",
                discount: 25
            ),
            MarketplaceItem(
                id: "2",
                name: "Interactive Puzzle Toy",
                price: 25.0,
                originalPrice: 25.0,
                imageURL: "https://via.placeholder.com/200x200",
                rating: 4.6,
                category: "toy",
                discount: 0
            )
        ]
    }
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter
    }
}
// MARK: - Supporting Data Models
struct MarketplaceItem: Identifiable {
    let id: String
    let name: String
    let price: Double
    let originalPrice: Double
    let imageURL: String
    let rating: Double
    let category: String
    let discount: Int
    var formattedPrice: String {
        return "$\(String(format: "%.0f", price))"
    }
}
// MARK: - Preview
#Preview {
    if #available(iOS 18.0, *) {
        PremiumPetDashboardCard(
            pet: Pet(
                name: "Buddy",
                species: "dog",
                breed: "Golden Retriever",
                age: 36,
                weight: 25.0,
                healthScore: 0.85,
                subscriptionTier: "free"
            ),
            onUpgrade: { print("Upgrade tapped") },
            onAlertAction: { alertId in print("Alert action: \(alertId)") },
            onMarketplaceClick: { itemId in print("Marketplace item: \(itemId)") },
            onTap: { print("Pet card tapped") }
        )
        .padding()
        .background(Color.gray.opacity(0.1))
    } else {
        Text("Premium Dashboard requires iOS 18.0+")
            .foregroundColor(.secondary)
    }
} 