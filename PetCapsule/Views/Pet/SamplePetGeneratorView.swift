//
//  SamplePetGeneratorView.swift
//  PetCapsule
//
//  UI for generating sample pets with comprehensive data
//

import SwiftUI

@available(iOS 18.0, *)
struct SamplePetGeneratorView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var generator = SamplePetDataGenerator.shared
    @State private var showingSuccess = false
    @State private var animateButton = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 32) {
                        // Header
                        headerSection
                        
                        // Features
                        featuresSection
                        
                        // Generation Status
                        if generator.isGenerating {
                            generationStatusSection
                        }
                        
                        // Generate Button
                        generateButton
                        
                        // Success Message
                        if showingSuccess {
                            successMessage
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 32)
                }
            }
            .navigationTitle("Sample Pet Generator")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .disabled(generator.isGenerating)
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Icon
            ZStack {
                Circle()
                    .fill(Color.purple.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                Image(systemName: "sparkles")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.purple)
            }
            .scaleEffect(animateButton ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: animateButton)
            
            // Title and Description
            VStack(spacing: 8) {
                Text("Generate Sample Pets")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text("Create 3 realistic pet profiles with comprehensive AI-generated data to explore all app features")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .onAppear {
            animateButton = true
        }
    }
    
    // MARK: - Features Section
    
    private var featuresSection: some View {
        VStack(spacing: 16) {
            Text("What You'll Get")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            // Features List
            VStack(spacing: 12) {
                SampleFeatureRow(icon: "pawprint.fill", title: "Diverse Pet Profiles", description: "Dogs, cats with different ages, breeds, and personalities")
                SampleFeatureRow(icon: "brain.head.profile", title: "AI-Generated Insights", description: "Health recommendations and personalized care tips")
                SampleFeatureRow(icon: "heart.text.square", title: "Complete Health Records", description: "Medical history, vaccinations, and medications")
                SampleFeatureRow(icon: "fork.knife.circle", title: "Nutrition & Exercise Plans", description: "Feeding schedules and activity recommendations")
                SampleFeatureRow(icon: "photo.on.rectangle", title: "Profile Photos", description: "High-quality images from Unsplash")
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Generation Status Section
    
    private var generationStatusSection: some View {
        VStack(spacing: 16) {
            // Progress Bar
            VStack(spacing: 8) {
                HStack {
                    Text("Progress")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text("\(Int(generator.progress * 100))%")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                }
                
                ProgressView(value: generator.progress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    .scaleEffect(y: 2)
            }
            
            // Status Message
            if !generator.statusMessage.isEmpty {
                HStack {
                    Image(systemName: "gear")
                        .foregroundColor(.blue)
                        .rotationEffect(.degrees(animateButton ? 360 : 0))
                        .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: animateButton)
                    
                    Text(generator.statusMessage)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
        }
        .padding(20)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Generate Button
    
    private var generateButton: some View {
        Button(action: {
            Task {
                do {
                    try await generator.generateSamplePets()
                    showingSuccess = true
                    // Auto-dismiss after showing success
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        dismiss()
                    }
                } catch {
                    print("Error generating sample pets: \(error)")
                }
            }
        }) {
            HStack(spacing: 12) {
                if generator.isGenerating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "sparkles")
                        .font(.system(size: 18, weight: .semibold))
                }
                
                Text(generator.isGenerating ? "Generating Pets..." : "Generate Sample Pets")
                    .font(.system(size: 18, weight: .semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                LinearGradient(
                    colors: generator.isGenerating ? [.gray, .gray.opacity(0.8)] : [.blue, .purple],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
        }
        .disabled(generator.isGenerating)
        .scaleEffect(generator.isGenerating ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: generator.isGenerating)
    }
    
    // MARK: - Success Message
    
    private var successMessage: some View {
        VStack(spacing: 12) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 32))
                .foregroundColor(.green)
            
            Text("Sample pets created successfully!")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text("Check out your new pets in the My Pets section")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(20)
        .background(Color.green.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .transition(.scale.combined(with: .opacity))
    }
}

// MARK: - Sample Feature Row

struct SampleFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        SamplePetGeneratorView()
    } else {
        Text("Requires iOS 18.0+")
    }
} 