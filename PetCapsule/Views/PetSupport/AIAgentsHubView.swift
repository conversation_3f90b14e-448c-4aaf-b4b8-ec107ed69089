//
//  AIAgentsHubView.swift
//  PetCapsule
//
//  Clean, Modern AI Agents Hub Interface
//  ✅ Consistent sizing, uncluttered design
//

import SwiftUI

@available(iOS 18.0, *)
struct AIAgentsHubView: View {
    @EnvironmentObject var realDataService: RealDataService
    @StateObject private var aiService = EnhancedAIAgentService.shared
    @StateObject private var emergencyService = EmergencyContactsService.shared
    @State private var selectedAgent: AIAgent?
    @State private var selectedPet: Pet?
    @State private var showingPetMaster = false
    @State private var showingConversationHistory = false
    @State private var searchText = ""
    @State private var selectedCategory: AgentCategory = .all
    @State private var animateCards = false
    @State private var showingPetSelector = false
    
    enum AgentCategory: String, CaseIterable {
        case all = "All"
        case health = "Health"
        case lifestyle = "Care"
        case premium = "Premium"
        
        var icon: String {
            switch self {
            case .all: return "sparkles"
            case .health: return "heart.fill"
            case .lifestyle: return "house.fill"
            case .premium: return "crown.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .all: return .purple
            case .health: return .red
            case .lifestyle: return .green
            case .premium: return .yellow
            }
        }
    }
    
    var filteredAgents: [AIAgent] {
        let agents = selectedCategory == .all ? aiService.availableAgents : 
                    aiService.availableAgents.filter { categorizeAgent($0) == selectedCategory }
        
        if searchText.isEmpty {
            return agents
        } else {
            return agents.filter { agent in
                agent.name.localizedCaseInsensitiveContains(searchText) ||
                agent.description.localizedCaseInsensitiveContains(searchText) ||
                agent.specialty.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // CLEAN HEADER
                cleanHeader
                
                // SIMPLIFIED CATEGORY SELECTOR
                categorySelector
                
                // CONSISTENT AGENTS GRID
                agentsGrid
            }
            .navigationBarHidden(true)
            .background(Color(.systemGroupedBackground))
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2)) {
                    animateCards = true
                }
            }
        }
        .sheet(item: $selectedAgent) { agent in
            PetAIAgentChatView(agent: agent, selectedPet: selectedPet)
        }
        .sheet(isPresented: $showingPetMaster) {
            PetMasterChatView()
        }
        .sheet(isPresented: $showingConversationHistory) {
            ConversationHistoryView()
        }
        .sheet(isPresented: $showingPetSelector) {
            PetSelectorView(selectedPet: $selectedPet)
        }
    }
    
    // MARK: - Clean Header
    
    private var cleanHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("AI Assistants")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    if let pet = selectedPet {
                        Text("Helping with \(pet.name)")
                            .font(.subheadline)
                            .foregroundStyle(.blue)
                    } else {
                        Text("Choose an expert for your pet")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }
                }
                
                Spacer()
                
                // Action buttons
                HStack(spacing: 12) {
                    Button(action: { showingConversationHistory = true }) {
                        Image(systemName: "clock")
                            .font(.title2)
                            .foregroundStyle(.secondary)
                    }
                    
                    Button(action: { showingPetSelector = true }) {
                        Circle()
                            .fill(.blue.opacity(0.1))
                            .frame(width: 40, height: 40)
                            .overlay(
                                Text(selectedPet?.name.prefix(1).uppercased() ?? "🐾")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundStyle(.blue)
                            )
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    // MARK: - Simplified Category Selector
    
    private var categorySelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(AgentCategory.allCases, id: \.self) { category in
                    Button(action: {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            selectedCategory = category
                        }
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: category.icon)
                                .font(.caption)
                            
                            Text(category.rawValue)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundStyle(selectedCategory == category ? .white : .primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            Capsule()
                                .fill(selectedCategory == category ? .blue : Color(.systemGray5))
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - Consistent Agents Grid
    
    private var agentsGrid: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 16),
                GridItem(.flexible(), spacing: 16)
            ], spacing: 20) {
                ForEach(Array(filteredAgents.enumerated()), id: \.element.id) { index, agent in
                    CleanAgentCard(agent: agent) {
                        selectedAgent = agent
                    }
                    .scaleEffect(animateCards ? 1.0 : 0.8)
                    .opacity(animateCards ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateCards)
                    .accessibilityIdentifier("ai_agent_card_\(agent.name.replacingOccurrences(of: " ", with: "_").lowercased())")
                    .accessibilityLabel("\(agent.name) AI Agent")
                    .accessibilityHint("Tap to chat with \(agent.name)")
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 100)
        }
    }
    
    // MARK: - Helper Functions
    
    private func categorizeAgent(_ agent: AIAgent) -> AgentCategory {
        let name = agent.name.lowercased()
        let specialty = agent.specialty.lowercased()
        
        if name.contains("nutrition") || name.contains("health") || name.contains("wellness") || name.contains("guardian") ||
           specialty.contains("health") || specialty.contains("nutrition") || specialty.contains("wellness") {
            return .health
        } else if name.contains("trainer") || specialty.contains("training") || specialty.contains("behavior") {
            return .lifestyle
        } else if agent.isPremium {
            return .premium
        } else {
            return .lifestyle
        }
    }
}

// MARK: - Clean Agent Card

struct CleanAgentCard: View {
    let agent: AIAgent
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                // Agent Avatar - Consistent Size
                ZStack {
                    Circle()
                                                 .fill(
                             LinearGradient(
                                 colors: [.blue, .purple], // Fixed colors for consistency
                                 startPoint: .topLeading,
                                 endPoint: .bottomTrailing
                             )
                         )
                        .frame(width: 60, height: 60)
                    
                    Text(agent.iconName)
                        .font(.system(size: 28))
                }
                
                // Agent Info - Fixed Height
                VStack(spacing: 8) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)
                    
                    Text(agent.specialty)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .lineLimit(1)
                    
                    // Status indicator
                    HStack(spacing: 4) {
                        Circle()
                            .fill(.green)
                            .frame(width: 6, height: 6)
                        
                        Text("Available")
                            .font(.caption2)
                            .foregroundStyle(.secondary)
                    }
                }
                .frame(height: 60) // Fixed height for consistency
            }
            .frame(maxWidth: .infinity)
            .frame(height: 160) // Fixed total card height
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(ScaleButtonStyle())
    }
}

// MARK: - Custom Button Style

struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Simple Pet Selector

struct PetSelectorView: View {
    @Binding var selectedPet: Pet?
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realDataService: RealDataService
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Select Pet")
                    .font(.title)
                    .padding()
                
                List {
                    Button("All Pets") {
                        selectedPet = nil
                        dismiss()
                    }
                    
                    ForEach(realDataService.pets, id: \.id) { pet in
                        Button(pet.name) {
                            selectedPet = pet
                            dismiss()
                        }
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        AIAgentsHubView()
    } else {
        Text("iOS 18+ Required")
    }
}
