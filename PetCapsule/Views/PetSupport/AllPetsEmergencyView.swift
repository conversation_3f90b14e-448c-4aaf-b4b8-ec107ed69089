import SwiftUI
import CoreLocation
import AVFoundation
struct AllPetsEmergencyView: View {
    let pets: [Pet]
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) var colorScheme
    @State private var selectedEmergencyType: EmergencyType = .veterinary
    @State private var affectedPets: Set<String> = []
    @State private var emergencyNotes = ""
    @State private var currentLocation: CLLocation?
    @State private var isCallingEmergency = false
    @State private var emergencyHaptic = UINotificationFeedbackGenerator()
    var body: some View {
        NavigationView {
            ZStack {
                // Glassmorphic background
                LinearGradient(
                    colors: colorScheme == .dark ? 
                        [Color.black, Color.red.opacity(0.2)] :
                        [Color.red.opacity(0.1), Color.white],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                ScrollView {
                    VStack(spacing: 24) {
                        // Emergency Header
                        emergencyHeader
                        // Pet Selection
                        petSelection
                        // Emergency Type Selection
                        emergencyTypeSelection
                        // Emergency Notes
                        emergencyNotesSection
                        // Emergency Actions
                        emergencyActions
                    }
                    .padding(20)
                }
            }
            .navigationTitle("Multi-Pet Emergency")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    // MARK: - Emergency Header
    private var emergencyHeader: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.red, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 100, height: 100)
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.white)
            }
            VStack(spacing: 8) {
                Text("Emergency Situation")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.red)
                Text("Select affected pets and emergency type")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(24)
        .background(glassmorphicCardBackground)
    }
    // MARK: - Pet Selection
    private var petSelection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("🐾 Affected Pets")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
                Button(affectedPets.count == pets.count ? "Deselect All" : "Select All") {
                    if affectedPets.count == pets.count {
                        affectedPets.removeAll()
                    } else {
                        affectedPets = Set(pets.map { $0.id })
                    }
                    emergencyHaptic.notificationOccurred(.success)
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
            }
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(pets) { pet in
                    petSelectionCard(pet)
                }
            }
        }
        .padding(20)
        .background(glassmorphicCardBackground)
    }
    private func petSelectionCard(_ pet: Pet) -> some View {
        Button(action: {
            if affectedPets.contains(pet.id) {
                affectedPets.remove(pet.id)
            } else {
                affectedPets
            }
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
        }) {
            VStack(spacing: 12) {
                ZStack(alignment: .topTrailing) {
                    AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.3))
                            .overlay(
                                Text(pet.species == "dog" ? "🐕" : "🐱")
                                    .font(.title)
                            )
                    }
                    .frame(width: 60, height: 60)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    // Selection indicator
                    if affectedPets.contains(pet.id) {
                        Circle()
                            .fill(.red)
                            .frame(width: 24, height: 24)
                            .overlay(
                                Image(systemName: "checkmark")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            )
                            .offset(x: 8, y: -8)
                    }
                }
                VStack(spacing: 4) {
                    Text(pet.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(affectedPets.contains(pet.id) ? .red : .primary)
                    Text(pet.breed ?? pet.species.capitalized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.regularMaterial)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                affectedPets.contains(pet.id) ? 
                                    Color.red.opacity(0.1) : 
                                    Color.clear
                            )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        affectedPets.contains(pet.id) ? 
                            Color.red.opacity(0.5) : 
                            Color.clear,
                        lineWidth: 2
                    )
            )
        }
    }
    // MARK: - Emergency Type Selection
    private var emergencyTypeSelection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🚨 Emergency Type")
                .font(.headline)
                .fontWeight(.bold)
            VStack(spacing: 12) {
                emergencyTypeButton(.veterinary, "Medical Emergency", "heart.fill")
                emergencyTypeButton(.poison, "Poisoning", "exclamationmark.triangle.fill")
                emergencyTypeButton(.injury, "Injury/Trauma", "bandage.fill")
                emergencyTypeButton(.lost, "Lost Pet", "location.fill")
            }
        }
        .padding(20)
        .background(glassmorphicCardBackground)
    }
    private func emergencyTypeButton(_ type: EmergencyType, _ title: String, _ icon: String) -> some View {
        Button(action: {
            selectedEmergencyType = type
            emergencyHaptic.notificationOccurred(.success)
        }) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(selectedEmergencyType == type ? .white : .red)
                    .frame(width: 32)
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(selectedEmergencyType == type ? .white : .primary)
                Spacer()
                if selectedEmergencyType == type {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.white)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        selectedEmergencyType == type ? 
                            LinearGradient(colors: [.red, .orange], startPoint: .leading, endPoint: .trailing) :
                            LinearGradient(colors: [.clear, .clear], startPoint: .leading, endPoint: .trailing)
                    )
                    .background(.regularMaterial)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        selectedEmergencyType == type ? Color.clear : Color.red.opacity(0.3),
                        lineWidth: 1
                    )
            )
        }
    }
    // MARK: - Emergency Notes
    private var emergencyNotesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📝 Emergency Details")
                .font(.headline)
                .fontWeight(.bold)
            VStack(alignment: .leading, spacing: 8) {
                Text("Describe the situation (optional)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                TextEditor(text: $emergencyNotes)
                    .frame(minHeight: 100)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.regularMaterial)
                    )
                    .overlay(
                        Group {
                            if emergencyNotes.isEmpty {
                                Text("What happened? Any symptoms or details...")
                                    .foregroundColor(.secondary)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 20)
                                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                    .allowsHitTesting(false)
                            }
                        }
                    )
            }
        }
        .padding(20)
        .background(glassmorphicCardBackground)
    }
    // MARK: - Emergency Actions
    private var emergencyActions: some View {
        VStack(spacing: 16) {
            // Main Emergency Call Button
            Button(action: {
                callEmergencyServices()
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "phone.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                    Text("CALL EMERGENCY VET NOW")
                        .font(.headline)
                        .fontWeight(.black)
                        .foregroundColor(.white)
                    Spacer()
                    if isCallingEmergency {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Image(systemName: "arrow.right.circle.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 64)
                .padding(.horizontal, 24)
                .background(
                    LinearGradient(
                        colors: [.red, Color(red: 0.8, green: 0.1, blue: 0.1)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(20)
                .shadow(color: .red.opacity(0.4), radius: 8, x: 0, y: 4)
            }
            .disabled(affectedPets.isEmpty || isCallingEmergency)
            .opacity(affectedPets.isEmpty ? 0.6 : 1.0)
            // Secondary Actions
            HStack(spacing: 12) {
                secondaryActionButton("Find Nearest Vet", "location.fill", .blue) {
                    // Find nearest vet
                }
                secondaryActionButton("Call Poison Control", "cross.circle.fill", .purple) {
                    // Call poison control
                }
            }
        }
        .padding(20)
        .background(glassmorphicCardBackground)
    }
    private func secondaryActionButton(_ title: String, _ icon: String, _ color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.regularMaterial)
                    .background(color.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(color.opacity(0.3), lineWidth: 1)
            )
        }
    }
    // MARK: - Helper Views
    private var glassmorphicCardBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.regularMaterial)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: colorScheme == .dark ? 
                                [Color.white.opacity(0.12), Color.white.opacity(0.04)] :
                                [Color.white.opacity(0.8), Color.white.opacity(0.4)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            colors: [Color.white.opacity(0.3), Color.clear],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(
                color: colorScheme == .dark ? .black.opacity(0.3) : .gray.opacity(0.2),
                radius: 10,
                x: 0,
                y: 5
            )
    }
    // MARK: - Actions
    private func callEmergencyServices() {
        isCallingEmergency = true
        emergencyHaptic.notificationOccurred(.error)
        // Voice announcement
        let utterance = AVSpeechUtterance(string: "Calling emergency veterinary services for \(affectedPets.count) pets")
        let synthesizer = AVSpeechSynthesizer()
        synthesizer.speak(utterance)
        // Simulate emergency call process
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            isCallingEmergency = false
            // In real implementation, this would integrate with phone app
            if let phoneURL = URL(string: "tel://911") {
                UIApplication.shared.open(phoneURL)
            }
            dismiss()
        }
    }
}
#Preview {
    AllPetsEmergencyView(pets: [
        Pet(name: "Buddy", species: "dog", breed: "Golden Retriever", age: 36),
        Pet(name: "Whiskers", species: "cat", breed: "Persian", age: 24)
    ])
} 