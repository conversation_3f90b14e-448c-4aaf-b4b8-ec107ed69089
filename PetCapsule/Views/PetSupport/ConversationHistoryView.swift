//
//  ConversationHistoryView.swift
//  PetCapsule
//
//  AI Conversation History View
//  🤖 View and manage AI agent conversation history
//

import SwiftUI

struct ConversationHistoryView: View {
    @EnvironmentObject private var conversationService: AIConversationService
    @EnvironmentObject private var realDataService: RealDataService
    @Environment(\.dismiss) private var dismiss
    
    @State private var searchText = ""
    @State private var selectedAgent: String = "All"
    @State private var showDeleteAlert = false
    @State private var conversationToDelete: AIConversation?
    
    private let agentNames = ["All", "Dr. Nutrition", "Health Guardian", "Trainer Pro", "Style Guru", "Shopping Assistant", "Wellness Coach", "Pet Master"]
    
    var filteredConversations: [AIConversation] {
        var conversations = conversationService.conversations
        
        // Filter by agent
        if selectedAgent != "All" {
            conversations = conversations.filter { $0.agentName == selectedAgent }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            conversations = conversations.filter { conversation in
                conversation.displayTitle.localizedCaseInsensitiveContains(searchText) ||
                conversation.agentName.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return conversations
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Search and Filter
                searchAndFilterView
                
                // Conversations List
                conversationsList
            }
            .navigationBarHidden(true)
            .background(Color(.systemGroupedBackground))
        }
        .alert("Delete Conversation", isPresented: $showDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                if let conversation = conversationToDelete {
                    Task {
                        await deleteConversation(conversation)
                    }
                }
            }
        } message: {
            Text("Are you sure you want to delete this conversation? This action cannot be undone.")
        }
        .onAppear {
            Task {
                await conversationService.loadUserConversations()
            }
        }
    }
    
    // MARK: - Header View

    private var headerView: some View {
        VStack(spacing: 16) {
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(spacing: 4) {
                    Text("Conversation History")
                        .font(.petTitle2)
                        .fontWeight(.bold)

                    Text("\(filteredConversations.count) conversations")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Stats badge
                VStack(spacing: 2) {
                    Text("\(conversationService.conversations.count)")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    Text("Total")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }

            // Quick stats row
            if !conversationService.conversations.isEmpty {
                HStack(spacing: 20) {
                    StatItem(icon: "bubble.left.and.bubble.right.fill", value: "\(getTotalMessages())", label: "Messages")
                    StatItem(icon: "person.2.fill", value: "\(getUniqueAgents())", label: "Agents")
                    StatItem(icon: "calendar.badge.clock", value: "Today", label: "Last Chat")
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color(.systemBackground), Color(.systemGray6).opacity(0.3)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Search and Filter View

    private var searchAndFilterView: some View {
        VStack(spacing: 16) {
            // Enhanced Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16))

                TextField("Search conversations...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            searchText = ""
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(searchText.isEmpty ? Color.clear : Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )

            // Enhanced Agent Filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(agentNames, id: \.self) { agentName in
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                selectedAgent = agentName
                            }
                        }) {
                            HStack(spacing: 6) {
                                if agentName != "All" {
                                    Text(getAgentEmojiForFilter(agentName))
                                        .font(.caption)
                                }
                                Text(agentName)
                                    .font(.petCaption)
                                    .fontWeight(.medium)
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedAgent == agentName ?
                                         LinearGradient(colors: [Color.blue, Color.blue.opacity(0.8)], startPoint: .leading, endPoint: .trailing) :
                                         LinearGradient(colors: [Color(.systemGray5)], startPoint: .leading, endPoint: .trailing)
                                    )
                            )
                            .foregroundColor(selectedAgent == agentName ? .white : .primary)
                            .scaleEffect(selectedAgent == agentName ? 1.05 : 1.0)
                            .shadow(color: selectedAgent == agentName ? .blue.opacity(0.3) : .clear, radius: 4, x: 0, y: 2)
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private func getAgentEmojiForFilter(_ agentName: String) -> String {
        switch agentName {
        case "Dr. Nutrition": return "🥗"
        case "Health Guardian": return "🏥"
        case "Style Guru": return "✂️"
        case "Trainer Pro": return "🎾"
        case "Shopping Assistant": return "🛍️"
        case "Wellness Coach": return "🧘‍♀️"
        case "Pet Master": return "👑"
        default: return "🤖"
        }
    }
    
    // MARK: - Conversations List
    
    private var conversationsList: some View {
        Group {
            if conversationService.isLoading {
                loadingView
            } else if filteredConversations.isEmpty {
                emptyStateView
            } else {
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(filteredConversations) { conversation in
                            ConversationRowView(
                                conversation: conversation,
                                onTap: {
                                    Task {
                                        await conversationService.setCurrentConversation(conversation)
                                        // Navigate to chat view
                                    }
                                },
                                onDelete: {
                                    conversationToDelete = conversation
                                    showDeleteAlert = true
                                }
                            )
                        }
                    }
                    .padding()
                }
            }
        }
    }
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading conversations...")
                .font(.petBody)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 32) {
            // Animated chat bubbles
            ZStack {
                Circle()
                    .fill(LinearGradient(colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(width: 120, height: 120)

                VStack(spacing: 8) {
                    HStack(spacing: 4) {
                        Circle().fill(Color.blue).frame(width: 8, height: 8)
                        Circle().fill(Color.purple).frame(width: 8, height: 8)
                        Circle().fill(Color.orange).frame(width: 8, height: 8)
                    }

                    Image(systemName: "bubble.left.and.bubble.right.fill")
                        .font(.system(size: 40))
                        .foregroundStyle(
                            LinearGradient(colors: [Color.blue, Color.purple], startPoint: .topLeading, endPoint: .bottomTrailing)
                        )
                }
            }

            VStack(spacing: 16) {
                Text("No Conversations Yet")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                VStack(spacing: 8) {
                    Text("Start chatting with AI agents to see your")
                        .font(.petBody)
                        .foregroundColor(.secondary)
                    Text("conversation history here.")
                        .font(.petBody)
                        .foregroundColor(.secondary)
                }
                .multilineTextAlignment(.center)

                // Feature highlights
                VStack(spacing: 12) {
                    ConversationFeatureRow(icon: "🤖", title: "AI Specialists", description: "Chat with expert AI agents")
                    ConversationFeatureRow(icon: "💾", title: "Auto-Save", description: "All conversations saved automatically")
                    ConversationFeatureRow(icon: "🔍", title: "Search & Filter", description: "Find conversations easily")
                }
                .padding(.top, 8)
            }

            Button(action: { dismiss() }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus.bubble.fill")
                        .font(.system(size: 16))
                    Text("Start Chatting")
                        .font(.petBody)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(colors: [Color.blue, Color.purple], startPoint: .leading, endPoint: .trailing)
                )
                .cornerRadius(25)
                .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, 32)
    }
    
    // MARK: - Helper Functions

    private func getTotalMessages() -> Int {
        return conversationService.conversations.reduce(0) { $0 + $1.messageCount }
    }

    private func getUniqueAgents() -> Int {
        return Set(conversationService.conversations.map { $0.agentName }).count
    }

    // MARK: - Actions

    private func deleteConversation(_ conversation: AIConversation) async {
        let success = await conversationService.deleteConversation(conversation.id)
        if !success {
            // Show error message
            print("Failed to delete conversation")
        }
    }
}

// MARK: - Stat Item Component

struct StatItem: View {
    let icon: String
    let value: String
    let label: String

    var body: some View {
        VStack(spacing: 4) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(.blue)
                Text(value)
                    .font(.petCaption)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }

            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Conversation Feature Row Component

struct ConversationFeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Text(icon)
                .font(.title2)
                .frame(width: 40, height: 40)
                .background(Color(.systemGray6))
                .cornerRadius(10)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.petBody)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.horizontal, 16)
    }
}

// MARK: - Conversation Row View

struct ConversationRowView: View {
    let conversation: AIConversation
    let onTap: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // Enhanced Agent Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: getAgentColors(conversation.agentName),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 56, height: 56)

                    Text(getAgentEmoji(conversation.agentName))
                        .font(.title2)
                }
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)

                // Enhanced Conversation Info
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(conversation.displayTitle)
                            .font(.petBody)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(1)

                        Spacer()

                        Text(conversation.formattedLastMessageTime)
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        // Agent badge
                        HStack(spacing: 4) {
                            Text(getAgentEmoji(conversation.agentName))
                                .font(.caption2)
                            Text(conversation.agentName)
                                .font(.petCaption)
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)

                        Spacer()

                        // Message count badge
                        HStack(spacing: 4) {
                            Image(systemName: "bubble.left.and.bubble.right.fill")
                                .font(.caption2)
                            Text("\(conversation.messageCount)")
                                .font(.petCaption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.secondary)
                    }
                }

                // Enhanced Delete Button
                Button(action: onDelete) {
                    Image(systemName: "trash.fill")
                        .font(.caption)
                        .foregroundColor(.white)
                        .frame(width: 32, height: 32)
                        .background(Color.red.opacity(0.8))
                        .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.08), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func getAgentEmoji(_ agentName: String) -> String {
        switch agentName {
        case "Dr. Nutrition": return "🥗"
        case "Health Guardian": return "🏥"
        case "Style Guru": return "✂️"
        case "Trainer Pro": return "🎾"
        case "Shopping Assistant": return "🛍️"
        case "Wellness Coach": return "🧘‍♀️"
        case "Pet Master": return "👑"
        default: return "🤖"
        }
    }

    private func getAgentColors(_ agentName: String) -> [Color] {
        switch agentName {
        case "Dr. Nutrition": return [Color.green, Color.green.opacity(0.7)]
        case "Health Guardian": return [Color.red, Color.pink]
        case "Style Guru": return [Color.purple, Color.blue]
        case "Trainer Pro": return [Color.orange, Color.yellow]
        case "Shopping Assistant": return [Color.blue, Color.cyan]
        case "Wellness Coach": return [Color.teal, Color.mint]
        case "Pet Master": return [Color.orange, Color.red]
        default: return [Color.gray, Color.gray.opacity(0.7)]
        }
    }
}

#Preview {
    ConversationHistoryView()
        .environmentObject(AIConversationService.shared)
        .environmentObject(RealDataService())
}
