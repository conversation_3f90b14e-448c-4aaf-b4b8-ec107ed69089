//
//  EmergencyVetDetailSheet.swift
//  PetCapsule
//
//  Emergency Vet Detail Sheet with Reviews, Contact Info, and Hours
//

import SwiftUI
import MapKit

struct EmergencyVetDetailSheet: View {
    let vet: EmergencyVet
    @Environment(\.dismiss) private var dismiss
    @State private var showingDirections = false
    @State private var showingCallConfirmation = false
    @State private var showingReviews = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header Image
                    headerImage
                    
                    // Vet Info Section
                    vetInfoSection
                    
                    // Status and Hours Section
                    statusAndHoursSection
                    
                    // Services Section
                    servicesSection
                    
                    // Reviews Section
                    reviewsSection
                    
                    // Emergency Info Section
                    emergencyInfoSection
                    
                    // Action Buttons
                    actionButtonsSection
                }
                .padding()
            }
            .navigationTitle(vet.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundStyle(.secondary)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        shareVet()
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                            .font(.title2)
                            .foregroundStyle(.blue)
                    }
                }
            }
        }
        .alert("Call Emergency Vet?", isPresented: $showingCallConfirmation) {
            Button("Call \(vet.name)") {
                callVet()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This will call \(vet.name) at \(vet.phoneNumber)")
        }
        .sheet(isPresented: $showingReviews) {
            VetReviewsView(vet: vet)
        }
    }
    
    // MARK: - Header Image
    
    private var headerImage: some View {
        AsyncImage(url: URL(string: vet.imageURL ?? "")) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Rectangle()
                .fill(LinearGradient(
                    colors: [Color.red.opacity(0.3), Color.red.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .overlay(
                    Image(systemName: "cross.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.red)
                )
        }
        .frame(height: 200)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .overlay(
            // 24-Hour Badge
            VStack {
                HStack {
                    Spacer()
                    if vet.is24Hour {
                        Text("24/7")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.red)
                            .clipShape(Capsule())
                    }
                }
                .padding()
                Spacer()
            }
        )
    }
    
    // MARK: - Vet Info Section
    
    private var vetInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(vet.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Emergency Veterinary Hospital")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                        
                        Text(String(format: "%.1f", vet.rating))
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    Button(action: {
                        showingReviews = true
                    }) {
                        Text("\(vet.reviewCount) reviews")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }
                    
                    Text("\(String(format: "%.1f", vet.distance)) mi away")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Address
            HStack {
                Image(systemName: "location")
                    .foregroundColor(.secondary)
                
                Text(vet.address)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Phone
            HStack {
                Image(systemName: "phone")
                    .foregroundColor(.secondary)
                
                Text(vet.phoneNumber)
                    .font(.subheadline)
                    .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Status and Hours Section
    
    private var statusAndHoursSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Hours & Availability")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Current Status
            HStack {
                Circle()
                    .fill(vet.isOpen ? .green : .red)
                    .frame(width: 12, height: 12)
                
                if vet.isOpen {
                    Text(vet.is24Hour ? "Open 24 Hours" : "Open Now")
                        .font(.subheadline)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                } else {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Closed")
                            .font(.subheadline)
                            .foregroundColor(.red)
                            .fontWeight(.medium)
                        
                        if let nextOpen = vet.nextOpenTime {
                            Text("Opens \(nextOpen)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
                
                if vet.acceptsWalkIns {
                    Text("Walk-ins Welcome")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.green.opacity(0.1))
                        .clipShape(Capsule())
                }
            }
            
            // Hours
            if !vet.hours.isEmpty {
                VStack(alignment: .leading, spacing: 6) {
                    ForEach(Array(vet.hours.sorted(by: { dayOrder($0.key) < dayOrder($1.key) })), id: \.key) { day, hours in
                        HStack {
                            Text(day)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .frame(width: 80, alignment: .leading)
                            
                            Text(hours)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Category Section
    
    private var servicesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Facility Type")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 8) {
                Image(systemName: vet.category.icon)
                    .foregroundColor(vet.category.color)
                    .font(.system(size: 16, weight: .semibold))
                    .frame(width: 24)
                
                Text(vet.category.displayName)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(vet.category.color.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .padding()
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Reviews Section
    
    private var reviewsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Reviews")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("See All") {
                    showingReviews = true
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            // Rating Summary
            HStack(spacing: 16) {
                // Overall Rating
                VStack(spacing: 4) {
                    Text(String(format: "%.1f", vet.rating))
                        .font(.title)
                        .fontWeight(.bold)
                    
                    HStack(spacing: 2) {
                        ForEach(1...5, id: \.self) { star in
                            Image(systemName: star <= Int(vet.rating) ? "star.fill" : "star")
                                .foregroundColor(.yellow)
                                .font(.caption)
                        }
                    }
                    
                    Text("\(vet.reviewCount) reviews")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Quick Review Stats
                VStack(alignment: .trailing, spacing: 4) {
                    reviewStat("Excellent Care", percentage: 92)
                    reviewStat("Staff Friendly", percentage: 89)
                    reviewStat("Quick Response", percentage: 95)
                }
            }
            
            // Sample Reviews
            VStack(alignment: .leading, spacing: 12) {
                sampleReview(
                    reviewer: "Sarah M.",
                    rating: 5,
                    text: "Amazing emergency care! They saved my dog's life when he ate chocolate. Professional staff and quick response time.",
                    timeAgo: "2 days ago"
                )
                
                sampleReview(
                    reviewer: "Mike T.",
                    rating: 4,
                    text: "Great facility with modern equipment. Wait time was reasonable for emergency care.",
                    timeAgo: "1 week ago"
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Emergency Info Section
    
    private var emergencyInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Emergency Information")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 12) {
                if let fee = vet.emergencyFee {
                    infoRow(icon: "dollarsign.circle", title: "Emergency Fee", value: fee, color: .orange)
                }
                
                infoRow(
                    icon: "clock.circle",
                    title: "Response Time",
                    value: "Usually within 10 minutes",
                    color: .blue
                )
                
                infoRow(
                    icon: vet.acceptsWalkIns ? "checkmark.circle" : "calendar",
                    title: "Walk-ins",
                    value: vet.acceptsWalkIns ? "Accepted" : "Appointment Required",
                    color: vet.acceptsWalkIns ? .green : .orange
                )
                
                infoRow(
                    icon: "cross.circle",
                    title: "Emergency Services",
                    value: vet.is24Hour ? "24/7 Emergency Care" : "Extended Hours",
                    color: .red
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Action Buttons Section
    
    private var actionButtonsSection: some View {
        VStack(spacing: 16) {
            // Primary Actions
            HStack(spacing: 12) {
                // Call Button
                Button(action: {
                    showingCallConfirmation = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "phone.fill")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Call Now")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        LinearGradient(
                            colors: [.green, .green.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        in: RoundedRectangle(cornerRadius: 14)
                    )
                    .foregroundColor(.white)
                    .shadow(color: .green.opacity(0.3), radius: 6, x: 0, y: 3)
                }
                
                // Directions Button
                Button(action: getDirections) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.triangle.turn.up.right.diamond")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Directions")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        LinearGradient(
                            colors: [.blue, .blue.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        in: RoundedRectangle(cornerRadius: 14)
                    )
                    .foregroundColor(.white)
                    .shadow(color: .blue.opacity(0.3), radius: 6, x: 0, y: 3)
                }
            }
            
            // Secondary Actions
            HStack(spacing: 12) {
                // Website Button
                if let _ = vet.website {
                    Button(action: openWebsite) {
                        HStack(spacing: 8) {
                            Image(systemName: "safari.fill")
                                .font(.system(size: 14, weight: .semibold))
                            Text("Website")
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 14)
                        .background(
                            LinearGradient(
                                colors: [.orange, .orange.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            in: RoundedRectangle(cornerRadius: 14)
                        )
                        .foregroundColor(.white)
                        .shadow(color: .orange.opacity(0.3), radius: 6, x: 0, y: 3)
                    }
                }
                
                // Save/Favorite Button
                Button(action: saveToFavorites) {
                    HStack(spacing: 8) {
                        Image(systemName: "heart.fill")
                            .font(.system(size: 14, weight: .semibold))
                        Text("Save")
                            .font(.system(size: 14, weight: .semibold))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(
                        LinearGradient(
                            colors: [.purple, .purple.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        in: RoundedRectangle(cornerRadius: 14)
                    )
                    .foregroundColor(.white)
                    .shadow(color: .purple.opacity(0.3), radius: 6, x: 0, y: 3)
                }
            }
        }
    }
    
    // MARK: - Helper Views
    
    private func reviewStat(_ title: String, percentage: Int) -> some View {
        HStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(percentage)%")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.green)
        }
    }
    
    private func sampleReview(reviewer: String, rating: Int, text: String, timeAgo: String) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text(reviewer)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                HStack(spacing: 2) {
                    ForEach(1...5, id: \.self) { star in
                        Image(systemName: star <= rating ? "star.fill" : "star")
                            .foregroundColor(.yellow)
                            .font(.caption2)
                    }
                }
                
                Text(timeAgo)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(2)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    private func infoRow(icon: String, title: String, value: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.system(size: 16, weight: .semibold))
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Helper Methods
    
    private func dayOrder(_ day: String) -> Int {
        switch day {
        case "Monday": return 1
        case "Tuesday": return 2
        case "Wednesday": return 3
        case "Thursday": return 4
        case "Friday": return 5
        case "Saturday": return 6
        case "Sunday": return 7
        default: return 8
        }
    }
    
    private func callVet() {
        let cleanedNumber = vet.phoneNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        if let url = URL(string: "tel://\(cleanedNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func getDirections() {
        let placemark = MKPlacemark(coordinate: vet.coordinate)
        let mapItem = MKMapItem(placemark: placemark)
        mapItem.name = vet.name
        mapItem.openInMaps(launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving])
    }
    
    private func openWebsite() {
        if let website = vet.website,
           let url = URL(string: website) {
            UIApplication.shared.open(url)
        }
    }
    
    private func saveToFavorites() {
        // Implement save to favorites functionality
        print("Saved \(vet.name) to favorites")
    }
    
    private func shareVet() {
        let shareText = """
        \(vet.name)
        \(vet.address)
        Phone: \(vet.phoneNumber)
        
        Rating: \(String(format: "%.1f", vet.rating)) ⭐ (\(vet.reviewCount) reviews)
        \(vet.is24Hour ? "24/7 Emergency Care" : "Extended Hours")
        
        Shared from PetCapsule
        """
        
        let activityController = UIActivityViewController(
            activityItems: [shareText],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.present(activityController, animated: true)
        }
    }
}

// MARK: - Vet Reviews View

struct VetReviewsView: View {
    let vet: EmergencyVet
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Overall Rating Summary
                    ratingsSummary
                    
                    // Individual Reviews
                    ForEach(sampleReviews, id: \.id) { review in
                        reviewCard(review)
                    }
                }
                .padding()
            }
            .navigationTitle("Reviews")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var ratingsSummary: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(spacing: 8) {
                    Text(String(format: "%.1f", vet.rating))
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    HStack(spacing: 4) {
                        ForEach(1...5, id: \.self) { star in
                            Image(systemName: star <= Int(vet.rating) ? "star.fill" : "star")
                                .foregroundColor(.yellow)
                                .font(.title3)
                        }
                    }
                    
                    Text("\(vet.reviewCount) reviews")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    ratingBar(stars: 5, percentage: 78)
                    ratingBar(stars: 4, percentage: 15)
                    ratingBar(stars: 3, percentage: 5)
                    ratingBar(stars: 2, percentage: 1)
                    ratingBar(stars: 1, percentage: 1)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private func ratingBar(stars: Int, percentage: Int) -> some View {
        HStack(spacing: 8) {
            Text("\(stars)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Image(systemName: "star.fill")
                .foregroundColor(.yellow)
                .font(.caption2)
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray4))
                        .frame(height: 6)
                    
                    Rectangle()
                        .fill(Color.yellow)
                        .frame(width: geometry.size.width * CGFloat(percentage) / 100, height: 6)
                }
            }
            .frame(height: 6)
            
            Text("\(percentage)%")
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 30, alignment: .trailing)
        }
    }
    
    private func reviewCard(_ review: VetReview) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(review.reviewerName)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text(review.timeAgo)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                HStack(spacing: 2) {
                    ForEach(1...5, id: \.self) { star in
                        Image(systemName: star <= review.rating ? "star.fill" : "star")
                            .foregroundColor(.yellow)
                            .font(.caption)
                    }
                }
            }
            
            Text(review.reviewText)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            if !review.vetResponse.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Divider()
                    
                    HStack {
                        Image(systemName: "checkmark.seal.fill")
                            .foregroundColor(.blue)
                            .font(.caption)
                        
                        Text("Response from \(vet.name)")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    }
                    
                    Text(review.vetResponse)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    private var sampleReviews: [VetReview] {
        [
            VetReview(
                id: "1",
                reviewerName: "Sarah M.",
                rating: 5,
                reviewText: "Amazing emergency care! They saved my dog's life when he ate chocolate. The staff was professional, caring, and worked quickly to treat him. Dr. Johnson explained everything clearly and kept us updated throughout the process. Highly recommend!",
                timeAgo: "2 days ago",
                vetResponse: "Thank you for trusting us with your pet's care, Sarah. We're so glad your dog is feeling better!"
            ),
            VetReview(
                id: "2",
                reviewerName: "Mike T.",
                rating: 4,
                reviewText: "Great facility with modern equipment. The wait time was reasonable considering it was an emergency. Staff was knowledgeable and my cat received excellent care for her injury.",
                timeAgo: "1 week ago",
                vetResponse: ""
            ),
            VetReview(
                id: "3",
                reviewerName: "Jessica R.",
                rating: 5,
                reviewText: "Called at 2 AM when my puppy was having breathing problems. They were ready for us when we arrived and immediately got to work. The veterinarian was experienced and compassionate. Worth every penny for peace of mind.",
                timeAgo: "2 weeks ago",
                vetResponse: "We're here 24/7 for exactly these situations. So happy your puppy is doing well!"
            ),
            VetReview(
                id: "4",
                reviewerName: "David L.",
                rating: 4,
                reviewText: "Professional service and clean facility. The emergency fee was explained upfront with no surprises. My dog's surgery went well and recovery was smooth.",
                timeAgo: "3 weeks ago",
                vetResponse: ""
            ),
            VetReview(
                id: "5",
                reviewerName: "Amanda K.",
                rating: 5,
                reviewText: "This place is a lifesaver! My elderly cat had a sudden health emergency on a Sunday. The team here provided exceptional care and helped us through a very scary time. Thank you!",
                timeAgo: "1 month ago",
                vetResponse: "Thank you Amanda. Senior pets require special attention and we're honored you trusted us with your cat's care."
            )
        ]
    }
}

struct VetReview {
    let id: String
    let reviewerName: String
    let rating: Int
    let reviewText: String
    let timeAgo: String
    let vetResponse: String
}

#Preview {
    EmergencyVetDetailSheet(vet: EmergencyVet(
        name: "24/7 Emergency Animal Hospital",
        address: "123 Emergency Ave, Your City, CA 94568",
        coordinate: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194),
        phoneNumber: "(555) 911-PETS",
        website: "https://emergency-animal-hospital.com",
        rating: 4.8,
        reviewCount: 342,
        distance: 2.3,
        is24Hour: true,
        category: .hospital,
        hours: ["Monday": "24 Hours", "Tuesday": "24 Hours", "Wednesday": "24 Hours", "Thursday": "24 Hours", "Friday": "24 Hours", "Saturday": "24 Hours", "Sunday": "24 Hours"],
        isOpen: true,
        emergencyFee: "$150 consultation",
        acceptsWalkIns: true
    ))
} 