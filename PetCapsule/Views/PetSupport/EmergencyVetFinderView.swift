//
//  EmergencyVetFinderView.swift
//  PetCapsule
//
//  Enhanced Emergency Vet Finder with Real MapKit Data & Clean Design
//

import SwiftUI
import MapKit
import CoreLocation

struct EmergencyVetFinderView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var locationService = EmergencyVetLocationService()
    @State private var searchText = ""
    @State private var selectedVet: EmergencyVet?
    @State private var showingVetDetails = false
    @State private var searchTask: Task<Void, Never>?
    @State private var cameraPosition: MapCameraPosition = .automatic
    @State private var showingLocationPermissionAlert = false
    @State private var hasRequestedPermission = false
    @State private var show24HourOnly = false
    @State private var selectedCategory: VetCategory? = nil
    @State private var forceUpdate = false
    
    var body: some View {
        ZStack {
            // Clean background
            Color(.systemGroupedBackground)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Custom Navigation Header
                navigationHeader
                
                // Search and Filter Section
                searchAndFilterSection
                    .padding(.horizontal, 20)
                    .padding(.bottom, 16)
                
                // Main Content
                if locationService.authorizationStatus == .notDetermined && !hasRequestedPermission {
                    locationPermissionPrompt
                } else if locationService.authorizationStatus == .denied || locationService.authorizationStatus == .restricted {
                    locationDeniedView
                } else {
                    mapContentView
                        .overlay(searchStatusOverlay, alignment: .top)
                }
            }
        }
        .sheet(isPresented: $showingVetDetails) {
            if let vet = selectedVet {
                EmergencyVetDetailSheet(vet: vet)
            }
        }
        .alert("Location Permission Required", isPresented: $showingLocationPermissionAlert) {
            Button("Enable in Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("PetTime Capsule needs location access to find nearby veterinary clinics for your pets.")
        }
        .onAppear {
            requestLocationPermissionIfNeeded()
        }
        .onDisappear {
            searchTask?.cancel()
        }
        .onChange(of: searchText) { _, newValue in
            searchTask?.cancel()
            searchTask = Task {
                try? await Task.sleep(nanoseconds: 300_000_000) // 0.3 second debounce
                if !Task.isCancelled {
                    await MainActor.run {
                        forceUpdate.toggle()
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredVets: [EmergencyVet] {
        var vets = locationService.nearbyVets
        
        // Filter by 24-hour availability
        if show24HourOnly {
            vets = vets.filter { $0.is24Hour }
        }
        
        // Filter by category
        if let category = selectedCategory {
            vets = vets.filter { $0.category == category }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            vets = vets.filter { vet in
                vet.name.localizedCaseInsensitiveContains(searchText) ||
                vet.address.localizedCaseInsensitiveContains(searchText) ||
                vet.category.displayName.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return vets.sorted { $0.distance < $1.distance }
    }
    
    // MARK: - Navigation Header
    
    private var navigationHeader: some View {
        HStack {
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "stethoscope")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
                    .frame(width: 44, height: 44)
                    .background(Color(.systemBackground))
                    .clipShape(Circle())
                    .shadow(radius: 2)
            }
            
            Spacer()
            
            VStack(spacing: 2) {
                Text("Nearest Vets")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                if !locationService.nearbyVets.isEmpty {
                    Text("\(filteredVets.count) found")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .frame(width: 44, height: 44)
                    .background(Color(.systemBackground))
                    .clipShape(Circle())
                    .shadow(radius: 2)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
        .padding(.bottom, 16)
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Search and Filter Section
    
    private var searchAndFilterSection: some View {
        VStack(spacing: 12) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search emergency vets...", text: $searchText)
                    .foregroundColor(.primary)
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(radius: 2)
            
            // Filter Options
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // 24-Hour Filter
                    FilterButton(
                        title: "24 Hours",
                        icon: "clock.fill",
                        isSelected: show24HourOnly,
                        color: .red
                    ) {
                        show24HourOnly.toggle()
                    }
                    
                    // Category Filters
                    ForEach(VetCategory.allCases, id: \.self) { category in
                        FilterButton(
                            title: category.displayName,
                            icon: category.icon,
                            isSelected: selectedCategory == category,
                            color: category.color
                        ) {
                            if selectedCategory == category {
                                selectedCategory = nil
                            } else {
                                selectedCategory = category
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
    }
    
    // MARK: - Location Permission Views
    
    private var locationPermissionPrompt: some View {
        VStack(spacing: 24) {
            Spacer()
            
            VStack(spacing: 20) {
                // Icon
                ZStack {
                    Circle()
                        .fill(.blue.gradient)
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: "cross.circle.fill")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(.white)
                }
                
                // Content
                VStack(spacing: 12) {
                    Text("Find Emergency Vets Near You")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                    
                    Text("We need your location to show nearby veterinary clinics and emergency animal hospitals.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Action Button
                Button(action: {
                    requestLocationPermission()
                }) {
                    Text("Enable Location Access")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(.blue.gradient)
                        .cornerRadius(12)
                }
                .padding(.horizontal, 40)
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
    
    private var locationDeniedView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            VStack(spacing: 20) {
                // Icon
                ZStack {
                    Circle()
                        .fill(Color.red.opacity(0.8))
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: "location.slash")
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(.white)
                }
                
                // Content
                VStack(spacing: 12) {
                    Text("Location Access Needed")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                    
                    Text("To find emergency vets near you, please enable location access in Settings.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Action Buttons
                VStack(spacing: 12) {
                    Button(action: {
                        showingLocationPermissionAlert = true
                    }) {
                        Text("Open Settings")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(.blue.gradient)
                            .cornerRadius(12)
                    }
                    
                    Button(action: {
                        dismiss()
                    }) {
                        Text("Cancel")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 40)
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Map Content View
    
    private var mapContentView: some View {
        VStack(spacing: 0) {
            Map(position: $cameraPosition) {
                // User location
                if let userLocation = locationService.userLocation {
                    Annotation("Your Location", coordinate: userLocation.coordinate) {
                        ZStack {
                            Circle()
                                .fill(.blue.gradient)
                                .frame(width: 20, height: 20)
                                .shadow(color: .blue.opacity(0.5), radius: 8)
                            
                            Circle()
                                .stroke(Color.white, lineWidth: 3)
                                .frame(width: 20, height: 20)
                        }
                    }
                }
                
                // Emergency vets
                ForEach(filteredVets, id: \.id) { vet in
                    Annotation(vet.name, coordinate: vet.coordinate) {
                        VetMarker(vet: vet)
                            .onTapGesture {
                                selectedVet = vet
                                showingVetDetails = true
                            }
                    }
                }
            }
            .mapStyle(.standard)
            .mapControls {
                MapUserLocationButton()
                MapCompass()
            }
        }
    }
    
    // MARK: - Search Status Overlay
    
    private var searchStatusOverlay: some View {
        VStack {
            if locationService.isSearching {
                HStack(spacing: 12) {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(.primary)
                    
                    Text("Finding emergency vets...")
                        .font(.caption)
                        .foregroundColor(.primary)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(.regularMaterial)
                .cornerRadius(20)
                .padding(.top, 20)
            } else if let error = locationService.searchError {
                HStack(spacing: 12) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.primary)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(.regularMaterial)
                .cornerRadius(20)
                .padding(.top, 20)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func requestLocationPermissionIfNeeded() {
        guard !hasRequestedPermission else { return }
        hasRequestedPermission = true
        locationService.requestLocationPermission()
    }
    
    private func requestLocationPermission() {
        locationService.requestLocationPermission()
    }
}

// MARK: - Supporting Views

struct FilterButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .fontWeight(.semibold)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            .foregroundColor(isSelected ? .white : color)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? color : Color(.systemBackground))
                    .stroke(color, lineWidth: 1.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct VetMarker: View {
    let vet: EmergencyVet
    
    var body: some View {
        ZStack {
            Circle()
                .fill(vet.category.color.gradient)
                .frame(width: 30, height: 30)
                .shadow(radius: 4)
            
            Image(systemName: vet.category.icon)
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(.white)
        }
    }
}

// MARK: - Data Models

struct EmergencyVet: Identifiable, Codable {
    let id = UUID().uuidString
    let name: String
    let address: String
    let coordinate: CLLocationCoordinate2D
    let phoneNumber: String
    let website: String?
    let rating: Double
    let reviewCount: Int
    let distance: Double // in miles
    let is24Hour: Bool
    let category: VetCategory
    let hours: [String: String] // Day: Hours
    let isOpen: Bool
    let nextOpenTime: String?
    let emergencyFee: String?
    let acceptsWalkIns: Bool
    let imageURL: String?
    
    enum CodingKeys: CodingKey {
        case name, address, phoneNumber, website, rating, reviewCount, distance, is24Hour, category, hours, isOpen, nextOpenTime, emergencyFee, acceptsWalkIns, imageURL
    }
    
    init(name: String, address: String, coordinate: CLLocationCoordinate2D, phoneNumber: String, website: String? = nil, rating: Double = 0.0, reviewCount: Int = 0, distance: Double = 0.0, is24Hour: Bool = false, category: VetCategory = .clinic, hours: [String: String] = [:], isOpen: Bool = true, nextOpenTime: String? = nil, emergencyFee: String? = nil, acceptsWalkIns: Bool = true, imageURL: String? = nil) {
        self.name = name
        self.address = address
        self.coordinate = coordinate
        self.phoneNumber = phoneNumber
        self.website = website
        self.rating = rating
        self.reviewCount = reviewCount
        self.distance = distance
        self.is24Hour = is24Hour
        self.category = category
        self.hours = hours
        self.isOpen = isOpen
        self.nextOpenTime = nextOpenTime
        self.emergencyFee = emergencyFee
        self.acceptsWalkIns = acceptsWalkIns
        self.imageURL = imageURL
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(address, forKey: .address)
        try container.encode(phoneNumber, forKey: .phoneNumber)
        try container.encode(website, forKey: .website)
        try container.encode(rating, forKey: .rating)
        try container.encode(reviewCount, forKey: .reviewCount)
        try container.encode(distance, forKey: .distance)
        try container.encode(is24Hour, forKey: .is24Hour)
        try container.encode(category, forKey: .category)
        try container.encode(hours, forKey: .hours)
        try container.encode(isOpen, forKey: .isOpen)
        try container.encode(nextOpenTime, forKey: .nextOpenTime)
        try container.encode(emergencyFee, forKey: .emergencyFee)
        try container.encode(acceptsWalkIns, forKey: .acceptsWalkIns)
        try container.encode(imageURL, forKey: .imageURL)
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        address = try container.decode(String.self, forKey: .address)
        coordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0) // Default value
        phoneNumber = try container.decode(String.self, forKey: .phoneNumber)
        website = try container.decodeIfPresent(String.self, forKey: .website)
        rating = try container.decode(Double.self, forKey: .rating)
        reviewCount = try container.decode(Int.self, forKey: .reviewCount)
        distance = try container.decode(Double.self, forKey: .distance)
        is24Hour = try container.decode(Bool.self, forKey: .is24Hour)
        category = try container.decode(VetCategory.self, forKey: .category)
        hours = try container.decode([String: String].self, forKey: .hours)
        isOpen = try container.decode(Bool.self, forKey: .isOpen)
        nextOpenTime = try container.decodeIfPresent(String.self, forKey: .nextOpenTime)
        emergencyFee = try container.decodeIfPresent(String.self, forKey: .emergencyFee)
        acceptsWalkIns = try container.decode(Bool.self, forKey: .acceptsWalkIns)
        imageURL = try container.decodeIfPresent(String.self, forKey: .imageURL)
    }
}

enum VetCategory: String, CaseIterable, Codable {
    case nearby = "nearby"
    case hospital = "hospital"
    case clinic = "clinic"
    case specialist = "specialist"
    
    var displayName: String {
        switch self {
        case .nearby: return "Nearby"
        case .hospital: return "Hospitals"
        case .clinic: return "Clinics"
        case .specialist: return "Specialists"
        }
    }
    
    var icon: String {
        switch self {
        case .nearby: return "location.fill"
        case .hospital: return "cross.fill"
        case .clinic: return "building.2.fill"
        case .specialist: return "stethoscope"
        }
    }
    
    var color: Color {
        switch self {
        case .nearby: return .blue
        case .hospital: return .red
        case .clinic: return .green
        case .specialist: return .purple
        }
    }
}

// MARK: - Location Service

class EmergencyVetLocationService: NSObject, ObservableObject, CLLocationManagerDelegate {
    @Published var nearbyVets: [EmergencyVet] = []
    @Published var userLocation: CLLocation?
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined
    @Published var isSearching = false
    @Published var searchError: String?
    
    private let locationManager = CLLocationManager()
    
    override init() {
        super.init()
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        authorizationStatus = locationManager.authorizationStatus
    }
    
    func requestLocationPermission() {
        locationManager.requestWhenInUseAuthorization()
    }
    
    func startLocationUpdates() {
        guard authorizationStatus == .authorizedWhenInUse || authorizationStatus == .authorizedAlways else {
            return
        }
        locationManager.startUpdatingLocation()
    }
    
    // MARK: - CLLocationManagerDelegate
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        userLocation = location
        searchNearbyVets()
        locationManager.stopUpdatingLocation()
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        authorizationStatus = status
        if status == .authorizedWhenInUse || status == .authorizedAlways {
            startLocationUpdates()
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("Location error: \(error)")
        searchError = "Unable to get your location"
        isSearching = false
    }
    
    // MARK: - Search Methods
    
    private func searchNearbyVets() {
        guard let userLocation = userLocation else { return }
        
        isSearching = true
        searchError = nil
        
        let searchTerms = [
            "veterinary hospital",
            "emergency vet",
            "animal hospital", 
            "veterinary clinic",
            "pet emergency"
        ]
        
        let group = DispatchGroup()
        var allResults: [MKMapItem] = []
        
        for searchTerm in searchTerms {
            group.enter()
            
            let request = MKLocalSearch.Request()
            request.naturalLanguageQuery = searchTerm
            request.region = MKCoordinateRegion(
                center: userLocation.coordinate,
                latitudinalMeters: 25000, // 25km radius
                longitudinalMeters: 25000
            )
            
            let search = MKLocalSearch(request: request)
            search.start { [weak self] response, error in
                defer { group.leave() }
                
                if let mapItems = response?.mapItems {
                    allResults.append(contentsOf: mapItems)
                }
            }
        }
        
        group.notify(queue: .main) { [weak self] in
            self?.processSearchResults(allResults)
        }
    }
    
    private func processSearchResults(_ mapItems: [MKMapItem]) {
        // Remove duplicates based on location proximity
        var uniqueResults: [MKMapItem] = []
        
        for item in mapItems {
            let isDuplicate = uniqueResults.contains { existing in
                let distance = item.placemark.location?.distance(from: existing.placemark.location ?? CLLocation()) ?? 0
                return distance < 100 // Within 100 meters
            }
            
            if !isDuplicate {
                uniqueResults.append(item)
            }
        }
        
        // Convert to EmergencyVet objects
        nearbyVets = uniqueResults.compactMap { mapItem in
            guard let location = mapItem.placemark.location else { return nil }
            
            let distance = userLocation?.distance(from: location) ?? 0
            let distanceInMiles = distance / 1609.34 // Convert meters to miles
            
            // Determine category based on name
            let name = mapItem.name ?? "Veterinary Clinic"
            let category: VetCategory
            
            if name.lowercased().contains("emergency") || name.lowercased().contains("hospital") {
                category = .hospital
            } else if name.lowercased().contains("specialist") || name.lowercased().contains("specialty") {
                category = .specialist
            } else {
                category = .clinic
            }
            
            // Extract phone number
            let phoneNumber = mapItem.phoneNumber ?? "No phone available"
            
            // Build address
            let placemark = mapItem.placemark
            var addressComponents: [String] = []
            
            if let streetNumber = placemark.subThoroughfare {
                addressComponents.append(streetNumber)
            }
            if let streetName = placemark.thoroughfare {
                addressComponents.append(streetName)
            }
            if let city = placemark.locality {
                addressComponents.append(city)
            }
            if let state = placemark.administrativeArea {
                addressComponents.append(state)
            }
            if let zipCode = placemark.postalCode {
                addressComponents.append(zipCode)
            }
            
            let address = addressComponents.joined(separator: " ")
            
            return EmergencyVet(
                name: name,
                address: address.isEmpty ? "Address not available" : address,
                coordinate: location.coordinate,
                phoneNumber: phoneNumber,
                website: mapItem.url?.absoluteString,
                rating: 0.0, // MapKit doesn't provide ratings
                reviewCount: 0,
                distance: distanceInMiles,
                is24Hour: name.lowercased().contains("24") || name.lowercased().contains("emergency"),
                category: category,
                hours: [:], // Would need additional API for hours
                isOpen: true, // Assume open for now
                emergencyFee: nil,
                acceptsWalkIns: true
            )
        }
        
        // Sort by distance
        nearbyVets.sort { $0.distance < $1.distance }
        
        isSearching = false
        
        if nearbyVets.isEmpty {
            searchError = "No veterinary clinics found in your area"
        }
    }
}

#Preview {
    EmergencyVetFinderView()
} 