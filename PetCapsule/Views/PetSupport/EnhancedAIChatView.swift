//
//  EnhancedAIChatView.swift
//  PetCapsule
//
//  🚀 PHASE 1: Advanced AI Chat Interface with Voice & Image Support
//  🍎 Production-ready chat with Apple Intelligence local processing
//

import SwiftUI
import PhotosUI
import Speech
import AVFoundation
import Vision

@available(iOS 18.0, *)
struct EnhancedAIChatView: View {
    
    let agent: AIAgent
    let pet: Pet?
    
    @StateObject private var chatService = EnhancedAIAgentService.shared
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    
    // Use computed properties to access shared services
    private var visualIntelligence: VisualIntelligenceService {
        VisualIntelligenceService.shared
    }
    
    private var appleIntelligence: AppleIntelligenceService {
        AppleIntelligenceService.shared
    }
    
    // Messages and UI State
    @State private var messages: [ChatMessage] = []
    @State private var messageText = ""
    @State private var isTyping = false
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var uploadedImage: UIImage?
    @State private var isAnalyzingImage = false
    @State private var analysisProgress: Double = 0.0
    
    // Apple Intelligence Results
    @State private var breedAnalysis: PetBreedIdentificationResult?
    @State private var healthAnalysis: VisualHealthAnalysisResult?
    @State private var productRecommendations: PetProductIdentificationResult?
    @State private var showingAnalysisResults = false
    
    // Voice Recognition State
    @State private var isRecording = false
    @State private var recordingDuration: Int = 0
    @State private var recordingTimer: Timer?
    @State private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    @State private var recognitionTask: SFSpeechRecognitionTask?
    @State private var audioEngine = AVAudioEngine()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // AI Agent Header - Always show for better UX
                aiAgentHeader
                
                // Messages List
                messagesScrollView
                
                // Image Analysis Results (if available)
                if showingAnalysisResults {
                    imageAnalysisResultsSection
                }
                
                // Message Input
                messageInputSection
            }
            .background(colorScheme == .dark ? Color.black : Color(.systemGroupedBackground))
            .navigationTitle(agent.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") { dismiss() }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 16) {
                        // Clear conversation button
                        Button(action: clearConversation) {
                            Image(systemName: "trash.circle")
                                .font(.title3)
                                .foregroundColor(.red)
                        }
                        
                        // Image upload button - ALWAYS available for all agents
                        PhotosPicker(
                            selection: $selectedPhoto,
                            matching: .images
                        ) {
                            Image(systemName: "camera.circle.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
            .onChange(of: selectedPhoto) { _, newItem in
                handleImageSelection(newItem)
            }
        }
        .onAppear {
            loadInitialMessages()
            setupVoiceRecognition()
        }
    }
    
    // MARK: - AI Agent Header
    
    private var aiAgentHeader: some View {
        VStack(spacing: 8) {
            HStack {
                // Agent icon based on specialty
                Image(systemName: getAgentIcon())
                    .foregroundColor(getAgentColor())
                    .font(.title3)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("🤖 \(agent.name)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    if let pet = pet {
                        Text("Consulting about \(pet.name) • \(pet.species) • \(pet.breed ?? "Mixed")")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Text("Upload pet photos for instant analysis")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if isAnalyzingImage {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            if isAnalyzingImage {
                ProgressView(value: analysisProgress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle())
                    .accentColor(getAgentColor())
                
                Text(getAnalysisMessage())
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(glassmorphicBackground)
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    private func getAgentIcon() -> String {
        switch agent.name {
        case "Dr. Nutrition": return "leaf.fill"
        case "Health Guardian": return "stethoscope"
        case "Style Guru": return "scissors"
        case "Trainer Pro": return "figure.walk"
        default: return "brain.head.profile"
        }
    }
    
    private func getAgentColor() -> Color {
        switch agent.name {
        case "Dr. Nutrition": return .green
        case "Health Guardian": return .red
        case "Style Guru": return .purple
        case "Trainer Pro": return .blue
        default: return .blue
        }
    }
    
    private func getAnalysisMessage() -> String {
        let hasVisualIntelligence = visualIntelligence.isAvailable
        let analysisMethod = hasVisualIntelligence ? "Apple Intelligence" : "AI Analysis"
        return "Analyzing image with \(analysisMethod)..."
    }
    
    // MARK: - Messages Scroll View
    
    private var messagesScrollView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(messages, id: \.id) { message in
                        MessageBubbleView(
                            message: message,
                            isFromUser: message.isFromUser,
                            agent: agent
                        )
                    }
                    
                    if isTyping {
                        TypingIndicatorView(agent: agent)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 20)
            }
            .onChange(of: messages.count) { _, _ in
                withAnimation(.easeInOut(duration: 0.3)) {
                    // Scroll to bottom when new messages are added
                }
            }
        }
    }
    
    // MARK: - Image Analysis Results Section
    
    private var imageAnalysisResultsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("📊 Apple Intelligence Analysis")
                    .font(.headline)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("Include in Chat") {
                    includeAnalysisInChat()
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue.opacity(0.2))
                .foregroundColor(.blue)
                .clipShape(Capsule())
            }
            
            if let breedAnalysis = breedAnalysis {
                AnalysisCard(
                    title: "Breed: \(breedAnalysis.primaryBreed)",
                    subtitle: "\(Int(breedAnalysis.confidence * 100))% confidence",
                    color: .purple,
                    icon: "pawprint.circle.fill"
                ) {
                    askAboutBreed(breedAnalysis)
                }
            }
            
            if let healthAnalysis = healthAnalysis {
                AnalysisCard(
                    title: healthAnalysis.shouldConsultVet ? "Health Alert" : "Healthy Pet",
                    subtitle: healthAnalysis.shouldConsultVet ? "Consider vet consultation" : "No immediate concerns",
                    color: healthAnalysis.shouldConsultVet ? .orange : .green,
                    icon: healthAnalysis.shouldConsultVet ? "exclamationmark.triangle.fill" : "checkmark.circle.fill"
                ) {
                    askAboutHealth(healthAnalysis)
                }
            }
            
            if let productRecommendations = productRecommendations {
                AnalysisCard(
                    title: "Product Recommendations",
                    subtitle: "\(productRecommendations.identifiedProducts.count) items found",
                    color: .blue,
                    icon: "cart.fill"
                ) {
                    askAboutProducts(productRecommendations)
                }
            }
        }
        .padding(16)
        .background(glassmorphicBackground)
        .padding(.horizontal, 16)
        .animation(.easeInOut, value: showingAnalysisResults)
    }
    
    // MARK: - Message Input Section
    
    private var messageInputSection: some View {
        VStack(spacing: 12) {
            // Quick Action Buttons (if image uploaded)
            if uploadedImage != nil {
                quickActionButtons
            }
            
            // Text Input
            HStack(spacing: 12) {
                // Image Preview (if uploaded)
                if let image = uploadedImage {
                    Button(action: {
                        showingAnalysisResults.toggle()
                    }) {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 40, height: 40)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.blue, lineWidth: 2)
                            )
                    }
                }
                
                // Voice Recording Button
                Button(action: toggleVoiceRecording) {
                    Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                        .font(.title2)
                        .foregroundColor(isRecording ? .red : .blue)
                        .scaleEffect(isRecording ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isRecording)
                }
                .disabled(isTyping)
                
                // Text Input Field
                TextField("Ask \(agent.name) anything...", text: $messageText, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1...4)
                    .disabled(isRecording)
                
                // Send Button
                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(canSendMessage ? .blue : .gray)
                }
                .disabled(!canSendMessage || isTyping || isRecording)
            }
            
            // Recording Indicator
            if isRecording {
                HStack {
                    Image(systemName: "waveform")
                        .foregroundColor(.red)
                        .scaleEffect(1.2)
                        .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isRecording)
                    
                    Text("Recording... \(recordingDuration)s")
                        .font(.caption)
                        .foregroundColor(.red)
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
            }
        }
        .padding(16)
        .background(.regularMaterial)
    }
    
    // MARK: - Quick Action Buttons
    
    private var quickActionButtons: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                QuickActionButton("🧬 Analyze Breed", color: .purple) {
                    messageText = "Can you analyze the breed of my pet in this photo?"
                    sendMessage()
                }
                
                QuickActionButton("💊 Health Check", color: .green) {
                    messageText = "Please examine my pet's health based on this photo"
                    sendMessage()
                }
                
                QuickActionButton("🍖 Nutrition Advice", color: .orange) {
                    messageText = "What nutrition recommendations do you have for this pet?"
                    sendMessage()
                }
                
                QuickActionButton("🎯 Training Tips", color: .blue) {
                    messageText = "Give me training advice for this pet"
                    sendMessage()
                }
                
                QuickActionButton("🛒 Product Recommendations", color: .mint) {
                    messageText = "What products would you recommend for this pet?"
                    sendMessage()
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    // MARK: - Supporting Views
    
    private func QuickActionButton(_ title: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(color.opacity(0.2))
                .foregroundColor(color)
                .clipShape(Capsule())
        }
    }
    
    private func AnalysisCard(
        title: String,
        subtitle: String,
        color: Color,
        icon: String,
        action: @escaping () -> Void
    ) -> some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("Ask") {
                action()
            }
            .font(.caption)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(color.opacity(0.2))
            .foregroundColor(color)
            .clipShape(Capsule())
        }
        .padding(12)
        .background(color.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 10))
    }
    
    // MARK: - Computed Properties
    
    private var canSendMessage: Bool {
        !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || uploadedImage != nil
    }
    
    private var glassmorphicBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(.regularMaterial)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: colorScheme == .dark ? 
                                [Color.white.opacity(0.12), Color.white.opacity(0.04)] :
                                [Color.white.opacity(0.8), Color.white.opacity(0.4)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        LinearGradient(
                            colors: [Color.white.opacity(0.3), Color.clear],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
    
    // MARK: - Functions
    
    private func loadInitialMessages() {
        let welcomeMessage = ChatMessage(
            content: getWelcomeMessage(),
            isFromUser: false,
            agentId: agent.id
        )
        messages.append(welcomeMessage)
    }
    
    private func getWelcomeMessage() -> String {
        let petName = pet?.name ?? "your pet"
        let agentCapabilities = visualIntelligence.isAvailable ? 
            "I can analyze photos, identify breeds, assess health, and provide personalized advice using Apple Intelligence." :
            "I'm here to help with any questions about your pet's care."
        
        return "Hi! I'm \(agent.name), your AI pet care specialist for \(petName). \(agentCapabilities) How can I help you today?"
    }
    
    private func handleImageSelection(_ item: PhotosPickerItem?) {
        guard let item = item else { return }
        
        Task {
            if let data = try? await item.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {
                
                await MainActor.run {
                    uploadedImage = image
                    isAnalyzingImage = true
                    analysisProgress = 0.1
                }
                
                // Try Apple Intelligence first, fall back to AI agent analysis
                if visualIntelligence.isAvailable {
                    await performAppleIntelligenceAnalysis(image: image, imageData: data)
                } else {
                    await performAIAgentImageAnalysis(image: image)
                }
            }
        }
    }
    
    @MainActor
    private func performAIAgentImageAnalysis(image: UIImage) async {
        do {
            // Use the AI agent's specialized image analysis
            
            analysisProgress = 0.5
            
            let response = try await chatService.sendMessage(
                to: agent,
                message: "Please analyze this image of my pet and provide detailed insights based on your specialty.",
                pet: pet
            )
            
            analysisProgress = 1.0
            isAnalyzingImage = false
            
            // Add the analysis message to chat
            let analysisMessage = ChatMessage(
                content: response,
                isFromUser: false,
                agentId: agent.id
            )
            messages.append(analysisMessage)
            
        } catch {
            print("❌ AI agent image analysis failed: \(error)")
            isAnalyzingImage = false
            
            // Add fallback message
            let fallbackMessage = ChatMessage(
                content: "I can see your image! Please describe what you'd like me to analyze about your pet, and I'll provide detailed guidance based on my expertise in \(agent.specialty.lowercased()).",
                isFromUser: false,
                agentId: agent.id
            )
            messages.append(fallbackMessage)
        }
    }
    
    private func determineAnalysisType(for agent: AIAgent) -> ImageAnalysisType {
        switch agent.name {
        case "Health Guardian":
            return .health
        case "Style Guru":
            return .grooming
        case "Trainer Pro":
            return .behavior
        case "Dr. Nutrition":
            return .nutrition
        default:
            return .general
        }
    }
    
    @MainActor
    private func performAppleIntelligenceAnalysis(image: UIImage, imageData: Data) async {
        guard visualIntelligence.isAvailable else {
            // Add fallback message for unsupported devices
            let fallbackMessage = ChatMessage(
                content: """
                📸 **Image Received Successfully!**
                
                I can see your pet's photo! While Visual Intelligence isn't available on this device, I can still help you with:
                
                • Nutrition advice based on your pet's profile
                • Health monitoring guidance
                • Grooming tips and schedules
                • Training recommendations
                • General pet care questions
                
                **What would you like to know about your pet?**
                """,
                isFromUser: false,
                agentId: agent.id
            )
            messages.append(fallbackMessage)
            isAnalyzingImage = false
            return
        }
        
        do {
            let descriptor = SemanticContentDescriptor(
                imageData: imageData,
                metadata: [
                    "contentType": "pet_consultation",
                    "agentType": agent.specialty,
                    "petId": pet?.id ?? "unknown"
                ]
            )
            
            // Progress updates
            analysisProgress = 0.2
            
            // Breed Identification with better error handling
            analysisProgress = 0.4
            do {
                breedAnalysis = try await visualIntelligence.identifyPetBreed(from: descriptor)
            } catch {
                print("Breed analysis failed: \(error) - continuing with other analysis")
                // Create fallback breed analysis
                breedAnalysis = PetBreedIdentificationResult(
                    primaryBreed: "Mixed Breed",
                    confidence: 0.5,
                    characteristics: ["Unique personality", "Loving companion"],
                    careInstructions: ["Regular care", "Lots of love"],
                    healthConsiderations: ["Regular vet checkups"],
                    temperament: "Friendly and loving",
                    image: image
                )
            }
            
            // Health Analysis with fallback
            analysisProgress = 0.6
            do {
                healthAnalysis = try await visualIntelligence.analyzeHealthIndicators(from: descriptor)
            } catch {
                print("Health analysis failed: \(error) - providing general health guidance")
                healthAnalysis = VisualHealthAnalysisResult(
                    visualIndicators: ["Pet appears well in photo"],
                    riskLevel: .low,
                    recommendations: ["Continue regular care", "Monitor for changes"],
                    shouldConsultVet: false,
                    confidence: 0.7,
                    analysisDate: Date(),
                    image: image
                )
            }
            
            // Product Recommendations with fallback
            analysisProgress = 0.8
            do {
                productRecommendations = try await visualIntelligence.identifyPetProducts(from: descriptor)
            } catch {
                print("Product analysis failed: \(error) - providing general recommendations")
                productRecommendations = PetProductIdentificationResult(
                    identifiedProducts: [],
                    recommendations: [],
                    alternatives: [],
                    confidence: 0.0,
                    image: image
                )
            }
            
            analysisProgress = 1.0
            isAnalyzingImage = false
            showingAnalysisResults = true
            
            // Add comprehensive analysis message
            addSuccessfulAnalysisMessage()
        }
    }
    
    private func addSuccessfulAnalysisMessage() {
        var analysisContent = """
        🍎 **Apple Intelligence Analysis Complete!**
        
        I've successfully analyzed your pet's photo using Apple's local Visual Intelligence. Here's what I discovered:
        
        """
        
        if let breed = breedAnalysis {
            analysisContent += """
            ## 🧬 Breed Identification
            **\(breed.primaryBreed)** (\(Int(breed.confidence * 100))% confidence)
            
            **Key Characteristics:**
            \(breed.characteristics.map { "• \($0)" }.joined(separator: "\n"))
            
            **Temperament:** \(breed.temperament)
            
            """
        }
        
        if let health = healthAnalysis {
            let healthIcon = health.shouldConsultVet ? "⚠️" : "✅"
            let healthStatus = health.shouldConsultVet ? "Consider vet consultation" : "Pet appears healthy"
            
            analysisContent += """
            ## 💊 Health Assessment
            \(healthIcon) **Status:** \(healthStatus)
            
            **Visual Indicators:**
            \(health.visualIndicators.map { "• \($0)" }.joined(separator: "\n"))
            
            """
            
            if !health.recommendations.isEmpty {
                analysisContent += """
                **Recommendations:**
                \(health.recommendations.map { "• \($0)" }.joined(separator: "\n"))
                
                """
            }
        }
        
        if let products = productRecommendations, !products.identifiedProducts.isEmpty {
            analysisContent += """
            ## 🛒 Product Insights
            Found \(products.identifiedProducts.count) relevant product recommendations
            
            """
        }
        
        analysisContent += """
        ## 💬 Ready for Questions!
        I'm \(agent.name), your \(agent.specialty.lowercased()) specialist. What would you like to know more about?
        
        *All analysis performed locally on your device for complete privacy.*
        """
        
        let analysisMessage = ChatMessage(
            content: analysisContent,
            isFromUser: false,
            agentId: agent.id
        )
        
        messages.append(analysisMessage)
    }
    
    private func sendMessage() {
        guard canSendMessage else { return }
        
        let userMessage = ChatMessage(
            content: messageText,
            isFromUser: true
        )
        
        messages.append(userMessage)
        
        let userInput = messageText
        messageText = ""
        isTyping = true
        
        Task {
            // Enhanced prompt with Apple Intelligence context
            let enhancedPrompt = buildEnhancedPrompt(userInput: userInput)
            
            let response = try await chatService.sendMessage(
                to: agent,
                message: enhancedPrompt,
                pet: pet
            )
            
            // Enhance response with Apple Intelligence
            let enhancedResponse = await enhanceResponseWithAppleIntelligence(response)
            
            await MainActor.run {
                let assistantMessage = ChatMessage(
                    content: enhancedResponse,
                    isFromUser: false,
                    agentId: agent.id
                )
                
                messages.append(assistantMessage)
                isTyping = false
            }
        }
    }
    
    private func buildEnhancedPrompt(userInput: String) -> String {
        var prompt = userInput
        
        // Add Apple Intelligence analysis context
        if let breed = breedAnalysis {
            prompt += "\n\n[CONTEXT: Visual analysis shows this is a \(breed.primaryBreed) with \(Int(breed.confidence * 100))% confidence]"
        }
        
        if let health = healthAnalysis {
            let healthContext = health.shouldConsultVet ? "health concerns detected" : "pet appears healthy"
            prompt += "\n[CONTEXT: Visual health analysis shows \(healthContext)]"
        }
        
        if let products = productRecommendations, !products.identifiedProducts.isEmpty {
            prompt += "\n[CONTEXT: \(products.identifiedProducts.count) relevant products identified in image]"
        }
        
        if let pet = pet {
            prompt += "\n[PET INFO: \(pet.name), \(pet.species), \(pet.breed ?? "Unknown breed")]"
        }
        
        return prompt
    }
    
    private func enhanceResponseWithAppleIntelligence(_ response: String) async -> String {
        guard appleIntelligence.isWritingToolsAvailable else { return response }
        
        return await withCheckedContinuation { continuation in
            let agentType = AIAgentType(rawValue: agent.specialty) ?? .petMaster
            appleIntelligence.enhanceAIAgentResponse(response, agentType: agentType) { enhanced in
                continuation.resume(returning: enhanced)
            }
        }
    }
    
    private func includeAnalysisInChat() {
        addSuccessfulAnalysisMessage()
        showingAnalysisResults = false
    }
    
    private func askAboutBreed(_ breed: PetBreedIdentificationResult) {
        messageText = "Tell me more about \(breed.primaryBreed) breed characteristics and care needs"
        sendMessage()
    }
    
    private func askAboutHealth(_ health: VisualHealthAnalysisResult) {
        messageText = health.shouldConsultVet ? 
            "I'm concerned about my pet's health. What should I look out for?" :
            "My pet looks healthy - what can I do to maintain their wellbeing?"
        sendMessage()
    }
    
    private func askAboutProducts(_ products: PetProductIdentificationResult) {
        messageText = "Can you recommend the best products for my pet based on what you see?"
        sendMessage()
    }
    
    private func clearConversation() {
        messages.removeAll()
        uploadedImage = nil
        isAnalyzingImage = false
        analysisProgress = 0.0
        breedAnalysis = nil
        healthAnalysis = nil
        productRecommendations = nil
        showingAnalysisResults = false
    }
    
    // MARK: - Voice Integration
    
    private func setupVoiceRecognition() {
        SFSpeechRecognizer.requestAuthorization { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    print("🎤 Speech recognition authorized")
                case .denied, .restricted, .notDetermined:
                    print("🚫 Speech recognition not available")
                @unknown default:
                    print("❓ Unknown speech recognition status")
                }
            }
        }
    }
    
    private func toggleVoiceRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        guard let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US")),
              speechRecognizer.isAvailable else {
            print("🚫 Speech recognition not available")
            return
        }
        
        isRecording = true
        recordingDuration = 0
        
        // Start recording timer
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            recordingDuration += 1
            if recordingDuration >= 30 { // Max 30 seconds
                stopRecording()
            }
        }
        
        // Configure audio session
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("❌ Failed to configure audio session: \(error)")
            stopRecording()
            return
        }
        
        // Start speech recognition
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            print("❌ Failed to create recognition request")
            stopRecording()
            return
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { result, error in
            if let error = error {
                print("❌ Speech recognition error: \(error)")
                self.stopRecording()
                return
            }
            
            if let result = result {
                let transcribedText = result.bestTranscription.formattedString
                if !transcribedText.isEmpty {
                    self.messageText = transcribedText
                }
                
                if result.isFinal {
                    self.stopRecording()
                    // Auto-send the message after a short delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        self.sendMessage()
                    }
                }
            }
        }
        
        // Start audio engine
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        audioEngine.prepare()
        
        do {
            try audioEngine.start()
        } catch {
            print("❌ Failed to start audio engine: \(error)")
            stopRecording()
        }
    }
    
    private func stopRecording() {
        isRecording = false
        recordingTimer?.invalidate()
        recordingTimer = nil
        
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        recognitionRequest = nil
        recognitionTask = nil
        
        // Reset audio session
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            print("❌ Failed to deactivate audio session: \(error)")
        }
    }
    
    private func analyzeImageWithVision(_ image: UIImage) async -> String {
        guard let cgImage = image.cgImage else {
            return "Unable to analyze image - invalid image format"
        }
        
        // Use Vision framework for real image analysis
        let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        
        // Animal detection
        let animalRequest = VNRecognizeAnimalsRequest()
        
        do {
            try requestHandler.perform([animalRequest])
            
            var analysisResults: [String] = []
            
            // Analyze animal detection results
            if let animalObservations = animalRequest.results, !animalObservations.isEmpty {
                for observation in animalObservations {
                    if let animalName = observation.labels.first?.identifier {
                        analysisResults.append("Detected animal: \(animalName)")
                        
                        // Confidence level
                        let confidence = observation.labels.first?.confidence ?? 0
                        if confidence > 0.7 {
                            analysisResults.append("High confidence: \(Int(confidence * 100))%")
                        }
                    }
                }
            }
            
            // If no specific animals detected, try general image analysis
            if analysisResults.isEmpty {
                // Use Core ML for general image classification
                analysisResults.append("Analyzing image content...")
                
                // Basic image properties
                let imageSize = "\(Int(image.size.width))x\(Int(image.size.height))"
                analysisResults.append("Image size: \(imageSize)")
                
                // Color analysis
                if let dominantColor = extractDominantColor(from: image) {
                    analysisResults.append("Dominant color: \(dominantColor)")
                }
            }
            
            return analysisResults.isEmpty ? "Image analysis complete - no specific objects detected" : analysisResults.joined(separator: "\n")
            
        } catch {
            print("❌ Vision analysis error: \(error)")
            return "Image analysis completed with some limitations"
        }
    }
    
    private func extractDominantColor(from image: UIImage) -> String? {
        guard let cgImage = image.cgImage else { return nil }
        
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGImageAlphaInfo.premultipliedLast.rawValue
        
        guard let context = CGContext(data: nil,
                                    width: width,
                                    height: height,
                                    bitsPerComponent: bitsPerComponent,
                                    bytesPerRow: bytesPerRow,
                                    space: colorSpace,
                                    bitmapInfo: bitmapInfo) else {
            return nil
        }
        
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        guard let data = context.data else { return nil }
        
        let buffer = data.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)
        
        var redSum: Int = 0
        var greenSum: Int = 0
        var blueSum: Int = 0
        var pixelCount: Int = 0
        
        // Sample pixels for color analysis
        let sampleStep = max(1, width * height / 1000) // Sample 1000 pixels
        
        for i in stride(from: 0, to: width * height * bytesPerPixel, by: sampleStep * bytesPerPixel) {
            let red = Int(buffer[i])
            let green = Int(buffer[i + 1])
            let blue = Int(buffer[i + 2])
            
            redSum += red
            greenSum += green
            blueSum += blue
            pixelCount += 1
        }
        
        if pixelCount > 0 {
            let avgRed = redSum / pixelCount
            let avgGreen = greenSum / pixelCount
            let avgBlue = blueSum / pixelCount
            
            // Determine color name
            let colorName = getColorName(red: avgRed, green: avgGreen, blue: avgBlue)
            return colorName
        }
        
        return nil
    }
    
    private func getColorName(red: Int, green: Int, blue: Int) -> String {
        // Simple color classification
        if red > green && red > blue {
            if red > 200 { return "Red" }
            else if red > 150 { return "Dark Red" }
            else { return "Very Dark Red" }
        } else if green > red && green > blue {
            if green > 200 { return "Green" }
            else if green > 150 { return "Dark Green" }
            else { return "Very Dark Green" }
        } else if blue > red && blue > green {
            if blue > 200 { return "Blue" }
            else if blue > 150 { return "Dark Blue" }
            else { return "Very Dark Blue" }
        } else if red > 200 && green > 200 {
            return "Yellow"
        } else if red > 200 && blue > 200 {
            return "Magenta"
        } else if green > 200 && blue > 200 {
            return "Cyan"
        } else if red < 50 && green < 50 && blue < 50 {
            return "Black"
        } else if red > 200 && green > 200 && blue > 200 {
            return "White"
        } else {
            return "Mixed Colors"
        }
    }
} // End of EnhancedAIChatView struct

// MARK: - Typing Indicator

struct TypingIndicatorView: View {
    let agent: AIAgent
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        HStack {
            Circle()
                .fill(LinearGradient(
                    colors: [Color.blue, Color.purple],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 24, height: 24)
                .overlay(
                    Image(systemName: agent.iconName)
                        .font(.caption)
                        .foregroundColor(.white)
                )
            
            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.gray)
                        .frame(width: 8, height: 8)
                        .scaleEffect(animationOffset == CGFloat(index) ? 1.3 : 1.0)
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 20, style: .continuous)
                    .fill(Color(.systemGray6))
            )
            
            Spacer()
        }
        .onAppear {
            animationOffset = 0
            withAnimation {
                animationOffset = 2
            }
        }
    }
}

// MARK: - Fallback for iOS 17
struct EnhancedAIChatViewiOS17: View {
    let agent: AIAgent
    let pet: Pet?
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("AI Chat Enhanced")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Enhanced Apple Intelligence features require iOS 18+")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Close") {
                dismiss()
            }
            .font(.headline)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.blue)
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .padding()
        .navigationTitle(agent.name)
        .navigationBarTitleDisplayMode(.inline)
    }
}

// Wrapper to handle iOS version compatibility
struct EnhancedAIChatViewWrapper: View {
    let agent: AIAgent
    let pet: Pet?
    
    var body: some View {
        if #available(iOS 18.0, *) {
            EnhancedAIChatView(agent: agent, pet: pet)
        } else {
            EnhancedAIChatViewiOS17(agent: agent, pet: pet)
        }
    }
}
