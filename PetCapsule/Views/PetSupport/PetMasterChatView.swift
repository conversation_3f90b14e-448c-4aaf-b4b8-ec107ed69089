//
//  PetMasterChatView.swift
//  PetCapsule
//
//  Pet Master Chat Interface - Ultimate AI Agent
//  Comprehensive chat interface with all data sources
//

import SwiftUI

@available(iOS 18.0, *)
struct PetMasterChatView: View {
    @StateObject private var aiService = EnhancedAIAgentService.shared
    @State private var messageText = ""
    @State private var isLoading = false
    @State private var showingVoiceInput = false
    @State private var animateMessages = false
    @Environment(\.dismiss) private var dismiss
    
    // Pet Master Agent Configuration
    private var petMasterAgent: AIAgent {
        AIAgent(
            id: UUID(),
            name: "Pet Master",
            iconName: "🎯",
            description: "Ultimate AI companion with comprehensive pet care knowledge",
            specialty: "Comprehensive Pet Care",
            specialties: ["All Pet Care", "Expert Coordination", "24/7 Support"],
            gradientColors: ["#8B5CF6", "#3B82F6", "#10B981"],
            isPremium: false,
            systemPrompt: "You are Pet Master, the ultimate AI pet care companion with access to all specialized knowledge areas.",
            conversationStarters: [
                "Analyze all my pets' health status",
                "Create a comprehensive care plan",
                "Help with a complex pet situation",
                "Coordinate multiple pet needs"
            ],
            responseConfig: AIResponseConfig(
                maxTokens: 1000,
                temperature: 0.7,
                tone: "expert",
                responseStyle: "comprehensive",
                expertise: "master"
            )
        )
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Messages
                messagesSection
                
                // Input Section
                inputSection
            }
            .navigationBarHidden(true)
            .background(Color(.systemGroupedBackground))
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateMessages = true
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 0) {
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 2) {
                    Text("Pet Master")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 6, height: 6)
                        
                        Text("Ultimate AI • Always Available")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }
                
                Spacer()
                
                Button(action: { showingVoiceInput = true }) {
                    Image(systemName: "mic.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
            
            // Pet Master Avatar and Info
            VStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(LinearGradient(
                            colors: [.purple, .blue, .green],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 80, height: 80)
                    
                    Text("🎯")
                        .font(.system(size: 40))
                }
                
                VStack(spacing: 4) {
                    Text("Pet Master AI")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Comprehensive pet care with access to all knowledge sources")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Capabilities Pills
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(["All Pet Data", "Knowledge Base", "Agent Insights", "Web Search"], id: \.self) { capability in
                            Text(capability)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    LinearGradient(
                                        colors: [.purple.opacity(0.8), .blue.opacity(0.8)],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
            .padding(.vertical, 20)
            .background(Color(.systemBackground))
        }
        .scaleEffect(animateMessages ? 1.0 : 0.9)
        .opacity(animateMessages ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateMessages)
    }
    
    // MARK: - Messages Section
    
    private var messagesSection: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Welcome Message
                welcomeMessage
                
                // Conversation Starters
                conversationStarters
                
                // Chat Messages
                if let messages = aiService.conversationHistory[petMasterAgent.id.uuidString] {
                    ForEach(Array(messages.enumerated()), id: \.element.id) { index, message in
                        ChatMessageView(message: message)
                            .scaleEffect(animateMessages ? 1.0 : 0.8)
                            .opacity(animateMessages ? 1.0 : 0.0)
                            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateMessages)
                    }
                }
                
                // Loading Indicator
                if isLoading {
                    HStack {
                        TypingIndicator()
                        Spacer()
                    }
                    .padding(.horizontal, 20)
                }
            }
            .padding(.vertical, 20)
        }
    }
    
    // MARK: - Welcome Message
    
    private var welcomeMessage: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "sparkles")
                    .foregroundColor(.purple)
                Text("Welcome to Pet Master")
                    .font(.headline)
                    .fontWeight(.bold)
            }
            
            Text("I'm your ultimate pet care companion with access to all your pet data, knowledge base, specialist insights, and real-time information. How can I help you today?")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .padding(.horizontal, 20)
    }
    
    // MARK: - Conversation Starters
    
    private var conversationStarters: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Start")
                .font(.headline)
                .fontWeight(.semibold)
                .padding(.horizontal, 20)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(petMasterAgent.conversationStarters, id: \.self) { starter in
                        Button(action: {
                            messageText = starter
                            sendMessage()
                        }) {
                            Text(starter)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(Color(.systemGray6))
                                .cornerRadius(20)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 20)
            }
        }
    }
    
    // MARK: - Input Section
    
    private var inputSection: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 12) {
                // Text Input
                HStack {
                    TextField("Ask Pet Master anything...", text: $messageText, axis: .vertical)
                        .textFieldStyle(PlainTextFieldStyle())
                        .lineLimit(1...4)
                    
                    if !messageText.isEmpty {
                        Button(action: { messageText = "" }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(20)
                
                // Send Button
                Button(action: sendMessage) {
                    Image(systemName: isLoading ? "stop.circle.fill" : "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(messageText.isEmpty && !isLoading ? .secondary : .blue)
                }
                .disabled(messageText.isEmpty && !isLoading)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
        }
    }
    
    // MARK: - Actions
    
    private func sendMessage() {
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let userMessage = messageText
        messageText = ""
        isLoading = true
        
        Task {
            do {
                _ = try await aiService.sendMessage(to: petMasterAgent, message: userMessage, pet: nil)
            } catch {
                print("Failed to send message: \(error)")
            }
            await MainActor.run {
                isLoading = false
            }
        }
    }
}

// MARK: - Supporting Views

struct ChatMessageView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .font(.body)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .cornerRadius(18)
                    
                    Text(message.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            } else {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Text("🎯")
                            .font(.caption)
                        
                        Text("Pet Master")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.purple)
                    }
                    
                    Text(message.content)
                        .font(.body)
                        .foregroundColor(.primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color(.systemGray6))
                        .cornerRadius(18)
                    
                    Text(message.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
        .padding(.horizontal, 20)
    }
}

struct TypingIndicator: View {
    @State private var animating = false
    
    var body: some View {
        HStack(spacing: 4) {
            Text("🎯")
                .font(.caption)
            
            Text("Pet Master is thinking")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(spacing: 2) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.secondary)
                        .frame(width: 4, height: 4)
                        .scaleEffect(animating ? 1.0 : 0.5)
                        .animation(
                            .easeInOut(duration: 0.6)
                            .repeatForever()
                            .delay(Double(index) * 0.2),
                            value: animating
                        )
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(18)
        .onAppear {
            animating = true
        }
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        PetMasterChatView()
    } else {
        Text("iOS 18.0+ Required")
    }
}
