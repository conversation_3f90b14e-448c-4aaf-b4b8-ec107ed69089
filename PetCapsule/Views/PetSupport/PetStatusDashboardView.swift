import SwiftUI

struct PetStatusDashboardView: View {
    let urgentPets: [Pet]
    let attentionPets: [Pet]
    let upcomingCarePets: [Pet]
    let allGoodPets: [Pet]
    
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) var colorScheme
    @State private var selectedPet: Pet?
    @State private var showingPetDetail = false
    @State private var animateCards = false
    
    var totalPets: Int {
        urgentPets.count + attentionPets.count + upcomingCarePets.count + allGoodPets.count
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Glassmorphic background
                LinearGradient(
                    colors: colorScheme == .dark ? 
                        [Color.black, Color(red: 0.05, green: 0.05, blue: 0.15)] :
                        [Color(red: 0.98, green: 0.99, blue: 1.0), Color(red: 0.94, green: 0.97, blue: 1.0)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Dashboard Header
                        dashboardHeader
                        
                        // Priority Sections
                        if !urgentPets.isEmpty {
                            prioritySection(
                                "🚨 Needs Immediate Attention",
                                pets: urgentPets,
                                color: .red,
                                backgroundColor: Color.red.opacity(0.1)
                            )
                        }
                        
                        if !attentionPets.isEmpty {
                            prioritySection(
                                "⚠️ Needs Attention Today",
                                pets: attentionPets,
                                color: .orange,
                                backgroundColor: Color.orange.opacity(0.1)
                            )
                        }
                        
                        if !upcomingCarePets.isEmpty {
                            prioritySection(
                                "📅 Upcoming Care",
                                pets: upcomingCarePets,
                                color: .yellow,
                                backgroundColor: Color.yellow.opacity(0.1)
                            )
                        }
                        
                        if !allGoodPets.isEmpty {
                            prioritySection(
                                "✅ All Good",
                                pets: allGoodPets,
                                color: .green,
                                backgroundColor: Color.green.opacity(0.1)
                            )
                        }
                        
                        // Empty state
                        if totalPets == 0 {
                            emptyStateView
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationTitle("Pet Status Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2)) {
                    animateCards = true
                }
            }
        }
        .sheet(isPresented: $showingPetDetail) {
            if let pet = selectedPet {
                PetDetailDashboardView(pet: pet)
            }
        }
    }
    
    // MARK: - Dashboard Header
    
    private var dashboardHeader: some View {
        VStack(spacing: 20) {
            // Status Overview
            HStack(spacing: 16) {
                statusCard("Urgent", count: urgentPets.count, color: .red, icon: "exclamationmark.triangle.fill")
                statusCard("Today", count: attentionPets.count, color: .orange, icon: "clock.fill")
                statusCard("Upcoming", count: upcomingCarePets.count, color: .yellow, icon: "calendar.fill")
                statusCard("Good", count: allGoodPets.count, color: .green, icon: "checkmark.circle.fill")
            }
            
            // Summary Text
            VStack(spacing: 8) {
                Text(getStatusSummary())
                    .font(.headline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                
                Text("Tap any pet for detailed care information")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(20)
        .background(glassmorphicCardBackground)
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    private func statusCard(_ title: String, count: Int, color: Color, icon: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .background(color.opacity(0.1))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(color.opacity(0.3), lineWidth: 1)
        )
    }
    
    // MARK: - Priority Section
    
    private func prioritySection(_ title: String, pets: [Pet], color: Color, backgroundColor: Color) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section Header
            HStack {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(color)
                
                Spacer()
                
                Text("\(pets.count) pet\(pets.count == 1 ? "" : "s")")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(color.opacity(0.2))
                    .cornerRadius(12)
            }
            
            // Pet Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(pets) { pet in
                    detailedPetCard(pet, priorityColor: color)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.regularMaterial)
                .background(backgroundColor)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(color.opacity(0.3), lineWidth: 1)
        )
        .shadow(
            color: colorScheme == .dark ? .black.opacity(0.2) : color.opacity(0.1),
            radius: 8,
            x: 0,
            y: 4
        )
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(pets.count) * 0.1), value: animateCards)
    }
    
    private func detailedPetCard(_ pet: Pet, priorityColor: Color) -> some View {
        Button(action: {
            selectedPet = pet
            showingPetDetail = true
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
        }) {
            VStack(spacing: 12) {
                // Pet Photo with Status Indicator
                ZStack(alignment: .topTrailing) {
                    AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.gray.opacity(0.3))
                            .overlay(
                                Text(pet.species == "dog" ? "🐕" : "🐱")
                                    .font(.title)
                            )
                    }
                    .frame(width: 80, height: 80)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    
                    // Priority Badge
                    Circle()
                        .fill(priorityColor)
                        .frame(width: 20, height: 20)
                        .overlay(
                            Circle()
                                .stroke(Color.white, lineWidth: 2)
                        )
                        .offset(x: 6, y: -6)
                }
                
                VStack(spacing: 6) {
                    Text(pet.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    Text(pet.breed ?? pet.species.capitalized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    // Age and Health Info
                    HStack(spacing: 4) {
                        Text(formatAge(pet.age))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Text(getHealthStatus(pet))
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(priorityColor)
                    }
                }
                
                // Quick Status Indicators
                statusIndicators(for: pet, color: priorityColor)
            }
            .padding(16)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.regularMaterial)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                LinearGradient(
                                    colors: colorScheme == .dark ? 
                                        [Color.white.opacity(0.12), Color.white.opacity(0.04)] :
                                        [Color.white.opacity(0.9), Color.white.opacity(0.6)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(priorityColor.opacity(0.3), lineWidth: 1)
            )
            .shadow(
                color: priorityColor.opacity(0.2),
                radius: 4,
                x: 0,
                y: 2
            )
        }
    }
    
    private func statusIndicators(for pet: Pet, color: Color) -> some View {
        HStack(spacing: 8) {
            // Health Alerts
            if !pet.healthAlerts.isEmpty {
                statusIndicator(
                    icon: "heart.fill",
                    count: pet.healthAlerts.count,
                    color: .red,
                    label: "alert\(pet.healthAlerts.count == 1 ? "" : "s")"
                )
            }
            
            // Medications
            if !pet.medications.isEmpty {
                statusIndicator(
                    icon: "pills.fill",
                    count: pet.medications.count,
                    color: .blue,
                    label: "med\(pet.medications.count == 1 ? "" : "s")"
                )
            }
            
            // If no specific indicators, show general status
            if pet.healthAlerts.isEmpty && pet.medications.isEmpty {
                Text("All good")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.green)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.2))
                    .cornerRadius(8)
            }
        }
    }
    
    private func statusIndicator(icon: String, count: Int, color: Color, label: String) -> some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption2)
                .foregroundColor(color)
            
            Text("\(count) \(label)")
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(color.opacity(0.2))
        .cornerRadius(8)
    }
    
    // MARK: - Empty State
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "pawprint.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            VStack(spacing: 8) {
                Text("No Pets Added")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Add your pets to see their status dashboard")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(40)
        .background(glassmorphicCardBackground)
    }
    
    // MARK: - Helper Views
    
    private var glassmorphicCardBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.regularMaterial)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: colorScheme == .dark ? 
                                [Color.white.opacity(0.12), Color.white.opacity(0.04)] :
                                [Color.white.opacity(0.8), Color.white.opacity(0.4)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            colors: [Color.white.opacity(0.3), Color.clear],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(
                color: colorScheme == .dark ? .black.opacity(0.3) : .gray.opacity(0.2),
                radius: 10,
                x: 0,
                y: 5
            )
    }
    
    // MARK: - Helper Functions
    
    private func getStatusSummary() -> String {
        if urgentPets.count > 0 {
            return "\(urgentPets.count) pet\(urgentPets.count == 1 ? "" : "s") need immediate attention"
        } else if attentionPets.count > 0 {
            return "\(attentionPets.count) pet\(attentionPets.count == 1 ? "" : "s") need attention today"
        } else if totalPets > 0 {
            return "All \(totalPets) pet\(totalPets == 1 ? "" : "s") are doing great!"
        } else {
            return "No pets added yet"
        }
    }
    
    private func formatAge(_ ageInMonths: Int) -> String {
        let years = ageInMonths / 12
        let months = ageInMonths % 12
        
        if years == 0 {
            return "\(months)mo"
        } else if months == 0 {
            return "\(years)yr"
        } else {
            return "\(years)yr \(months)mo"
        }
    }
    
    private func getHealthStatus(_ pet: Pet) -> String {
        if !pet.healthAlerts.isEmpty {
            return "Health issues"
        } else if !pet.medications.isEmpty {
            return "On medication"
        } else {
            return "Healthy"
        }
    }
}

// MARK: - Pet Detail Dashboard View

struct PetDetailDashboardView: View {
    let pet: Pet
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                LinearGradient(
                    colors: colorScheme == .dark ? 
                        [Color.black, Color(red: 0.05, green: 0.05, blue: 0.15)] :
                        [Color(red: 0.98, green: 0.99, blue: 1.0), Color(red: 0.94, green: 0.97, blue: 1.0)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Pet Header
                        petHeader
                        
                        // Health Status
                        healthStatusSection
                        
                        // Medications
                        medicationsSection
                        
                        // Daily Care
                        dailyCareSection
                        
                        // Recent Activity
                        recentActivitySection
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationTitle(pet.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var petHeader: some View {
        VStack(spacing: 16) {
            AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        Text(pet.species == "dog" ? "🐕" : "🐱")
                            .font(.system(size: 40))
                    )
            }
            .frame(width: 120, height: 120)
            .clipShape(RoundedRectangle(cornerRadius: 20))
            
            VStack(spacing: 8) {
                Text(pet.name)
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("\(pet.breed ?? pet.species.capitalized) • \(formatAge(pet.age))")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(24)
        .background(glassmorphicCardBackground)
    }
    
    private var healthStatusSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🏥 Health Status")
                .font(.headline)
                .fontWeight(.bold)
            
            if pet.healthAlerts.isEmpty {
                healthStatusRow("Overall Health", value: "Good", color: .green, icon: "checkmark.circle.fill")
            } else {
                ForEach(pet.healthAlerts, id: \.message) { alert in
                    healthStatusRow("Health Alert", value: alert.message, color: .red, icon: "exclamationmark.triangle.fill")
                }
            }
            
            healthStatusRow("Last Checkup", value: "2 months ago", color: .blue, icon: "calendar.circle.fill")
            healthStatusRow("Next Checkup", value: "Due in 4 months", color: .orange, icon: "clock.circle.fill")
        }
        .padding(20)
        .background(glassmorphicCardBackground)
    }
    
    private func healthStatusRow(_ title: String, value: String, color: Color, icon: String) -> some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(value)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
    }
    
    private var medicationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("💊 Medications")
                .font(.headline)
                .fontWeight(.bold)
            
            if pet.medications.isEmpty {
                Text("No active medications")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 16)
            } else {
                ForEach(pet.medications, id: \.name) { medication in
                    medicationRow(medication)
                }
            }
        }
        .padding(20)
        .background(glassmorphicCardBackground)
    }
    
    private func medicationRow(_ medication: Medication) -> some View {
        HStack(spacing: 16) {
            Image(systemName: "pills.fill")
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(medication.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("\(medication.dosage) • \(medication.frequency)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text("Next: 6:00 PM")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.blue.opacity(0.2))
                .cornerRadius(6)
        }
        .padding(.vertical, 8)
    }
    
    private var dailyCareSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("📋 Daily Care")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                careTaskRow("Morning Feed", isCompleted: true, time: "8:00 AM")
                careTaskRow("Walk", isCompleted: true, time: "9:30 AM")
                careTaskRow("Evening Feed", isCompleted: false, time: "6:00 PM")
                careTaskRow("Medication", isCompleted: false, time: "6:30 PM")
            }
        }
        .padding(20)
        .background(glassmorphicCardBackground)
    }
    
    private func careTaskRow(_ task: String, isCompleted: Bool, time: String) -> some View {
        HStack(spacing: 16) {
            Image(systemName: isCompleted ? "checkmark.circle.fill" : "circle")
                .font(.title3)
                .foregroundColor(isCompleted ? .green : .secondary)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(task)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .strikethrough(isCompleted)
                    .foregroundColor(isCompleted ? .secondary : .primary)
                
                Text(time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if !isCompleted {
                Text("Pending")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.2))
                    .cornerRadius(6)
            }
        }
        .padding(.vertical, 8)
    }
    
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("📊 Recent Activity")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                activityRow("Walk completed", time: "2 hours ago", icon: "figure.walk", color: .green)
                activityRow("Fed breakfast", time: "4 hours ago", icon: "fork.knife", color: .blue)
                activityRow("Medication given", time: "Yesterday", icon: "pills.fill", color: .purple)
            }
        }
        .padding(20)
        .background(glassmorphicCardBackground)
    }
    
    private func activityRow(_ activity: String, time: String, icon: String, color: Color) -> some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(activity)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
    }
    
    private var glassmorphicCardBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.regularMaterial)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: colorScheme == .dark ? 
                                [Color.white.opacity(0.12), Color.white.opacity(0.04)] :
                                [Color.white.opacity(0.8), Color.white.opacity(0.4)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        LinearGradient(
                            colors: [Color.white.opacity(0.3), Color.clear],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(
                color: colorScheme == .dark ? .black.opacity(0.3) : .gray.opacity(0.2),
                radius: 10,
                x: 0,
                y: 5
            )
    }
    
    private func formatAge(_ ageInMonths: Int) -> String {
        let years = ageInMonths / 12
        let months = ageInMonths % 12
        
        if years == 0 {
            return "\(months) month\(months == 1 ? "" : "s") old"
        } else if months == 0 {
            return "\(years) year\(years == 1 ? "" : "s") old"
        } else {
            return "\(years) year\(years == 1 ? "" : "s"), \(months) month\(months == 1 ? "" : "s") old"
        }
    }
}

#Preview {
    PetStatusDashboardView(
        urgentPets: [
            Pet(id: "1", name: "Buddy", species: "dog", breed: "Golden Retriever", age: 36)
        ],
        attentionPets: [
            Pet(id: "2", name: "Whiskers", species: "cat", breed: "Persian", age: 24)
        ],
        upcomingCarePets: [],
        allGoodPets: [
            Pet(id: "3", name: "Max", species: "dog", breed: "Labrador", age: 48)
        ]
    )
} 