import SwiftUI
import CoreLocation
import AVFoundation
import MapKit

@available(iOS 18.0, *)
struct PetSupportView: View {
    @EnvironmentObject private var realDataService: RealDataService
    @StateObject private var emergencyService = EmergencyContactsService.shared
    @StateObject private var vetFinder = VetFinderService()
    @StateObject private var aiService = EnhancedAIAgentService.shared
    @State private var selectedCrisisType: CrisisType?
    @State private var selectedAgent: AIAgent?
    @State private var showingAIAgentsHub = false
    @State private var showingEmergencyProtocol = false
    @State private var showingVetMap = false
    @State private var showingEmergencyContacts = false
    @State private var emergencyMode = false
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // CLEAN HEADER
                    cleanHeader
                    
                    // PROMINENT EMERGENCY BUTTON
                    prominentEmergencyButton
                    
                    // IMMEDIATE EMERGENCY SECTION
                    if emergencyMode {
                        activeEmergencyProtocol
                    } else {
                        cleanEmergencyActionGrid
                        
                        // Quick Emergency Contacts Access
                        quickEmergencyContactsSection
                    }
                    
                    // AI EXPERT CONSULTATION SECTION
                    cleanExpertConsultationSection
                        .accessibilityIdentifier("ai_expert_consultation_section")
                    
                    // WHEN TO SEEK HELP GUIDE
                    cleanWhenToSeekHelpSection
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .background(colorScheme == .dark ? Color.black : Color(.systemGroupedBackground))
            .navigationTitle("Your Care Team")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(item: $selectedAgent) { agent in
                if #available(iOS 18.0, *) {
                    EnhancedAIChatView(agent: agent, pet: nil)
                } else {
                    PetAIAgentChatView(agent: agent, selectedPet: nil)
                }
            }
            .sheet(isPresented: $showingAIAgentsHub) {
                AIAgentsHubView()
            }
            .sheet(isPresented: $showingEmergencyProtocol) {
                if let crisisType = selectedCrisisType {
                    EmergencyProtocolView(crisisType: crisisType)
                }
            }
            .sheet(isPresented: $showingVetMap) {
                VetMapView()
            }
            .sheet(isPresented: $showingEmergencyContacts) {
                EmergencyContactsView()
            }
        }
    }
    
    // MARK: - Clean Header
    
    private var cleanHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Emergency protocols & expert consultation")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
            
            // Optional: Subtle accent line
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [
                            .red.opacity(0.3),
                            .orange.opacity(0.2),
                            .clear
                        ],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(height: 2)
                .cornerRadius(1)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Prominent Emergency Button
    
    private var prominentEmergencyButton: some View {
        Button(action: {
            withAnimation(.spring()) {
                emergencyMode.toggle()
                if emergencyMode {
                    // Auto-trigger vet search when entering emergency mode
                    Task {
                        await vetFinder.searchNearbyVets()
                    }
                }
            }
        }) {
            HStack(spacing: 16) {
                // Emergency Icon
                ZStack {
                    Circle()
                        .fill(emergencyMode ? Color.orange : Color.red)
                        .frame(width: 50, height: 50)
                        .shadow(color: .black.opacity(0.2), radius: 6, x: 0, y: 3)
                    
                    Image(systemName: emergencyMode ? "xmark" : "exclamationmark.triangle.fill")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                // Emergency Text
                VStack(alignment: .leading, spacing: 4) {
                    Text(emergencyMode ? "EXIT EMERGENCY MODE" : "🚨 EMERGENCY")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(emergencyMode ? .orange : .red)
                    
                    Text(emergencyMode ? "Tap to exit emergency mode" : "Tap for immediate help")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Arrow indicator
                Image(systemName: "chevron.right")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(emergencyMode ? .orange : .red)
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20, style: .continuous)
                    .fill(emergencyMode ? Color.orange.opacity(0.1) : Color.red.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20, style: .continuous)
                            .stroke(emergencyMode ? Color.orange.opacity(0.3) : Color.red.opacity(0.3), lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(emergencyMode ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: emergencyMode)
    }
    
    // MARK: - Clean Emergency Actions Grid
    
    private var cleanEmergencyActionGrid: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🚨 Emergency Actions")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundStyle(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                emergencyActionCard(
                    icon: "phone.fill",
                    title: "Call Emergency Vet",
                    subtitle: "Find 24/7 emergency clinic",
                    color: .red,
                    crisisType: .veterinaryEmergency
                )
                
                emergencyActionCard(
                    icon: "exclamationmark.triangle.fill",
                    title: "Poison Emergency",
                    subtitle: "Toxic ingestion protocol",
                    color: .orange,
                    crisisType: .poisoning
                )
                
                emergencyActionCard(
                    icon: "bandage.fill",
                    title: "Injury Protocol",
                    subtitle: "First aid & stabilization",
                    color: .blue,
                    crisisType: .injury
                )
                
                emergencyActionCard(
                    icon: "questionmark.circle.fill",
                    title: "Pet Lost",
                    subtitle: "Search & recovery plan",
                    color: .purple,
                    crisisType: .lostPet
                )
            }
        }
        .padding(20)
        .background(cleanBackground)
    }
    
    private func emergencyActionCard(icon: String, title: String, subtitle: String, color: Color, crisisType: CrisisType) -> some View {
        Button(action: {
            selectedCrisisType = crisisType
            showingEmergencyProtocol = true
        }) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 48, height: 48)
                    .background(color.opacity(0.2))
                    .cornerRadius(12)
                
                VStack(spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.regularMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Active Emergency Protocol
    
    private var activeEmergencyProtocol: some View {
        VStack(spacing: 20) {
            // Emergency status banner
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
                    .font(.title3)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("EMERGENCY MODE ACTIVE")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                    
                    Text("All pets being monitored for immediate attention")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding()
            .background(Color.red.opacity(0.1))
            .cornerRadius(12)
            
            // Quick emergency contacts
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("📞 Emergency Contacts")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Button("Manage") {
                        showingEmergencyContacts = true
                    }
                    .font(.subheadline)
                    .foregroundColor(.blue)
                    
                    Text("US")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.secondary.opacity(0.1))
                        .cornerRadius(8)
                }
                
                VStack(spacing: 8) {
                    // TODO: Add emergency contacts display
                    Text("Emergency contacts will appear here")
                        .foregroundColor(.secondary)
                    
                    // Vet Finder Button
                    vetFinderButton()
                }
            }
        }
        .padding(20)
        .background(cleanBackground)
    }
    
    private func emergencyContactButton(_ contact: EmergencyContact) -> some View {
        Button(action: {
            // emergencyService.callEmergencyContact(contact) // TODO: Implement call functionality
        }) {
            HStack {
                Image(systemName: contact.type.icon)
                    .foregroundColor(Color(contact.type.color))
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(contact.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    HStack {
                        Text(contact.phoneNumber)
                            .font(.caption)
                            .foregroundColor(Color(contact.type.color))
                            .fontWeight(.semibold)
                        
                        // Cost display removed for now
                        // if let cost = contact.cost {
                        //     Text("• \(cost)")
                        //         .font(.caption2)
                        //         .foregroundColor(.secondary)
                        // }
                    }
                }
                
                Spacer()
                
                if emergencyService.isSimulator {
                    Image(systemName: "doc.on.doc")
                        .foregroundColor(.secondary)
                        .font(.caption)
                } else {
                    Image(systemName: "phone.fill")
                        .foregroundColor(Color(contact.type.color))
                }
            }
            .padding()
            .background(Color(contact.type.color).opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func vetFinderButton() -> some View {
        Button(action: {
            Task {
                await vetFinder.getCurrentLocationAndSearchVets()
                showingVetMap = true
            }
        }) {
            HStack {
                Image(systemName: "cross.case.fill")
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Find Emergency Vet")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    if vetFinder.isSearching {
                        Text("Getting location...")
                            .font(.caption)
                            .foregroundColor(.blue)
                    } else if let error = vetFinder.searchError {
                        Text(error)
                            .font(.caption)
                            .foregroundColor(.red)
                            .lineLimit(2)
                    } else if let nearestVet = vetFinder.findNearestEmergencyVet() {
                        Text("\(nearestVet.name) • \(nearestVet.distanceString)")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .fontWeight(.semibold)
                    } else {
                        Text("Tap to find nearby vets")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .fontWeight(.semibold)
                    }
                }
                
                Spacer()
                
                if vetFinder.isSearching {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "map.fill")
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(Color.blue.opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Clean Expert Consultation Section
    
    private var cleanExpertConsultationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("🤖 AI Expert Consultation")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundStyle(.primary)
                
                Spacer()
                
                Button("View All") {
                    showingAIAgentsHub = true
                }
                .font(.subheadline)
                .foregroundStyle(.blue)
            }
            
            Text("Get instant expert advice from specialized AI agents")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // AI Agents Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                ForEach(aiService.availableAgents.prefix(4), id: \.id) { agent in
                    cleanAgentCard(agent)
                }
            }
        }
        .padding(20)
        .background(cleanBackground)
    }
    
    private func cleanAgentCard(_ agent: AIAgent) -> some View {
        Button(action: {
            selectedAgent = agent
        }) {
            VStack(spacing: 12) {
                // Clean Agent Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)
                    
                    Text(agent.iconName)
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                }
                
                VStack(spacing: 4) {
                    Text(agent.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text(agent.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                }
                
                // Clean availability indicator
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 4, height: 4)
                    
                    Text("Available")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .padding(.horizontal, 8)
            .background(
                RoundedRectangle(cornerRadius: 16, style: .continuous)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16, style: .continuous)
                            .stroke(.quaternary, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Clean When to Seek Help Guide
    
    private var cleanWhenToSeekHelpSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("⚠️ When to Seek Immediate Help")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundStyle(.primary)
            
            VStack(alignment: .leading, spacing: 12) {
                warningSignItem("Difficulty breathing or choking", .red)
                warningSignItem("Severe bleeding or trauma", .red)
                warningSignItem("Unconsciousness or seizures", .red)
                warningSignItem("Suspected poisoning", .orange)
                warningSignItem("Persistent vomiting or diarrhea", .orange)
                warningSignItem("Extreme lethargy or behavior changes", .yellow)
            }
            
            Text("Trust your instincts - when in doubt, consult a professional")
                .font(.caption)
                .foregroundColor(.secondary)
                .italic()
                .padding(.top, 8)
        }
        .padding(20)
        .background(cleanBackground)
    }
    
    private func warningSignItem(_ text: String, _ severity: Color) -> some View {
        HStack(spacing: 12) {
            Circle()
                .fill(severity)
                .frame(width: 8, height: 8)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
    
    // MARK: - Clean Background
    
    private var cleanBackground: some View {
        RoundedRectangle(cornerRadius: 20, style: .continuous)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 20, style: .continuous)
                    .stroke(.quaternary, lineWidth: 1)
            )
    }
    
    // MARK: - Quick Emergency Contacts Access
    
    private var quickEmergencyContactsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("📞 Emergency Contacts")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundStyle(.primary)
                
                Spacer()
                
                Button("Manage") {
                    showingEmergencyContacts = true
                }
                .font(.subheadline)
                .foregroundStyle(.blue)
            }
            
            if true { // emergencyService.customContacts.isEmpty
                // No custom contacts - encourage setup
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "person.badge.plus")
                            .foregroundColor(.blue)
                            .font(.title3)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Set up emergency contacts")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text("Add your vet, family, and emergency services")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Button("Add Now") {
                            showingEmergencyContacts = true
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.small)
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                }
            } else {
                // Show first few contacts + country contacts
                VStack(spacing: 8) {
                    // Show user's custom contacts first
                    // TODO: Add custom contacts display
                    
                    // Show one country emergency contact
                    if let countryContact = emergencyService.currentCountryContacts.first {
                        emergencyContactButton(emergencyService.convertToEmergencyContact(countryContact))
                    }
                    
                    if emergencyService.customContacts.count > 2 || emergencyService.currentCountryContacts.count > 1 {
                        Button("View All Contacts (\(emergencyService.allEmergencyContacts.count))") {
                            showingEmergencyContacts = true
                        }
                        .font(.subheadline)
                        .foregroundColor(.blue)
                        .padding(.vertical, 8)
                    }
                }
            }
        }
        .padding(20)
        .background(cleanBackground)
    }
}

// MARK: - Supporting Types

enum CrisisType: String, CaseIterable {
    case veterinaryEmergency = "veterinary_emergency"
    case poisoning = "poisoning"
    case injury = "injury"
    case lostPet = "lost_pet"
    
    var displayName: String {
        switch self {
        case .veterinaryEmergency: return "Emergency Vet"
        case .poisoning: return "Poison Emergency"
        case .injury: return "Injury Protocol"
        case .lostPet: return "Pet Lost"
        }
    }
    
    var color: Color {
        switch self {
        case .veterinaryEmergency: return .red
        case .poisoning: return .orange
        case .injury: return .blue
        case .lostPet: return .purple
        }
    }
    
    var iconName: String {
        switch self {
        case .veterinaryEmergency: return "phone.fill"
        case .poisoning: return "exclamationmark.triangle.fill"
        case .injury: return "bandage.fill"
        case .lostPet: return "questionmark.circle.fill"
        }
    }
    
    var description: String {
        switch self {
        case .veterinaryEmergency: return "Find 24/7 emergency clinic and get immediate veterinary care"
        case .poisoning: return "Toxic ingestion protocol and poison control assistance"
        case .injury: return "First aid & stabilization for pet injuries"
        case .lostPet: return "Search & recovery plan for missing pets"
        }
    }
    
    var protocolSteps: [String] {
        switch self {
        case .veterinaryEmergency:
            return [
                "Assess if situation is life-threatening (call 911 if needed)",
                "Keep your pet calm and still",
                "Call nearest 24/7 emergency veterinary clinic",
                "Prepare to transport safely - use carrier or stretcher",
                "Bring pet's medical records and current medications",
                "Stay calm and provide clear information to vet staff"
            ]
        case .poisoning:
            return [
                "DO NOT induce vomiting unless instructed by poison control",
                "Call Pet Poison Control Hotline: (*************",
                "Have product packaging ready for reference",
                "Note exact time of ingestion and amount consumed",
                "Follow poison control instructions exactly",
                "Get to emergency vet immediately if instructed"
            ]
        case .injury:
            return [
                "Ensure your safety first - injured pets may bite",
                "Keep your pet calm and still to prevent further injury",
                "Control bleeding with clean cloth and direct pressure",
                "Do not remove embedded objects from wounds",
                "Support broken limbs with makeshift splints if needed",
                "Transport carefully to emergency veterinarian"
            ]
        case .lostPet:
            return [
                "Don't panic - act quickly but systematically",
                "Search immediate area calling your pet's name",
                "Contact local animal shelters and rescue groups",
                "Post on social media with clear photos and details",
                "Put out familiar scents (your clothing, their bed)",
                "Check with neighbors and ask them to check their property"
            ]
        }
    }
    
    var additionalResources: [ResourceItem] {
        switch self {
        case .veterinaryEmergency:
            return [
                ResourceItem(title: "ASPCA Emergency Care Guide", url: "https://www.aspca.org/pet-care/general-pet-care/emergency-care"),
                ResourceItem(title: "Pet Emergency Preparedness", url: "https://www.ready.gov/pets"),
                ResourceItem(title: "When to Call the Emergency Vet", url: "https://www.petmd.com/dog/emergency/common-emergencies")
            ]
        case .poisoning:
            return [
                ResourceItem(title: "ASPCA Poison Control Center", url: "https://www.aspca.org/pet-care/animal-poison-control"),
                ResourceItem(title: "Common Pet Toxins List", url: "https://www.aspca.org/pet-care/animal-poison-control/people-foods-avoid-feeding-pets"),
                ResourceItem(title: "Pet Poison Prevention", url: "https://www.petpoisonhelpline.com/pet-owners/")
            ]
        case .injury:
            return [
                ResourceItem(title: "Pet First Aid Basics", url: "https://www.redcross.org/get-help/how-to-prepare-for-emergencies/types-of-emergencies/pet-emergency"),
                ResourceItem(title: "Wound Care for Pets", url: "https://www.petmd.com/dog/emergency/common-emergencies/e_dg_wounds"),
                ResourceItem(title: "Pet CPR Instructions", url: "https://www.redcross.org/take-a-class/cpr/performing-cpr/pet-cpr")
            ]
        case .lostPet:
            return [
                ResourceItem(title: "Finding Lost Pets", url: "https://www.aspca.org/pet-care/general-pet-care/lost-pet-guide"),
                ResourceItem(title: "Lost Pet Recovery Tips", url: "https://www.humanesociety.org/resources/what-do-if-you-lose-your-pet"),
                ResourceItem(title: "Microchip Your Pet", url: "https://www.avma.org/resources/pet-owners/petcare/microchipping-animals")
            ]
        }
    }
}

struct ResourceItem {
    let title: String
    let url: String
}



struct FirstAidInstructionsView: View {
    let crisisType: CrisisType
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Crisis-specific instructions
                    instructionSection
                    
                    // General tips
                    generalTipsSection
                    
                    // Emergency contacts
                    emergencyContactsSection
                }
                .padding()
            }
            .navigationTitle("First Aid Guide")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
    
    private var instructionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🚨 \(crisisType.displayName) Instructions")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(crisisType.color)
            
            ForEach(Array(getInstructions().enumerated()), id: \.offset) { index, instruction in
                HStack(alignment: .top, spacing: 12) {
                    Text("\(index + 1)")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(width: 24, height: 24)
                        .background(Circle().fill(crisisType.color))
                    
                    Text(instruction)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
        )
    }
    
    private var generalTipsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("💡 General Tips")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 8) {
                tipItem("Stay calm - your pet can sense your stress")
                tipItem("Have emergency vet contact info readily available")
                tipItem("Keep a pet first aid kit in your home and car")
                tipItem("Know your pet's normal vital signs")
                tipItem("Have your pet's medical records easily accessible")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
        )
    }
    
    private var emergencyContactsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("📞 Emergency Contacts")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                contactButton("🚨 Emergency Services", "911", .red) {
                    call911()
                }
                
                contactButton("🐾 Pet Poison Control", "(*************", .purple) {
                    callPoisonControl()
                }
                
                contactButton("🩺 ASPCA Poison Control", "(888) 426-4435", .orange) {
                    callASPCAPoisonControl()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
        )
    }
    
    private func tipItem(_ text: String) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Text("•")
                .foregroundColor(crisisType.color)
                .fontWeight(.bold)
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
        }
    }
    
    private func contactButton(_ title: String, _ number: String, _ color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    Text(number)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "phone.fill")
                    .foregroundColor(color)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func getInstructions() -> [String] {
        switch crisisType {
        case .poisoning:
            return [
                "Do NOT induce vomiting unless specifically told to by poison control",
                "Remove your pet from the source of poison if safe to do so",
                "Call Pet Poison Control: (************* immediately",
                "Have the product packaging ready with ingredient list",
                "Note the time and amount consumed if known",
                "Follow the exact instructions given by poison control experts",
                "Get to emergency vet if directed - bring the poison container"
            ]
        case .injury:
            return [
                "Approach slowly - injured pets may bite out of fear",
                "Muzzle if necessary, but ensure breathing isn't obstructed",
                "For bleeding: apply direct pressure with clean cloth",
                "For fractures: support the limb, don't try to set the bone",
                "For objects in wounds: DO NOT remove, stabilize instead",
                "Keep your pet warm with blankets",
                "Transport on a flat surface if spinal injury is suspected"
            ]
        case .lostPet:
            return [
                "Search immediately in a 1-2 block radius calling their name",
                "Leave out items with your scent (worn clothing, shoes)",
                "Contact all local animal shelters, rescues, and veterinarians",
                "Post clear photos on social media and lost pet websites",
                "Create and distribute flyers in the area they went missing",
                "Check animal control facilities daily in person",
                "Consider hiring a pet tracker or search dog team"
            ]
        case .veterinaryEmergency:
            return [
                "Check breathing and pulse - clear airway if needed",
                "Control any bleeding with direct pressure",
                "Keep your pet calm and still to prevent shock",
                "Maintain body temperature with blankets",
                "Call ahead to emergency vet to prepare for your arrival",
                "Transport carefully - use a carrier for small pets",
                "Bring all medications and medical records if possible"
            ]
        }
    }
    
    private func call911() {
        if let url = URL(string: "tel://911") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callPoisonControl() {
        if let url = URL(string: "tel://**********") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callASPCAPoisonControl() {
        if let url = URL(string: "tel://**********") {
            UIApplication.shared.open(url)
        }
    }
}

struct EmergencyProtocolView: View {
    let crisisType: CrisisType
    @Environment(\.dismiss) private var dismiss
    @StateObject private var locationManager = LocationManager()
    @StateObject private var vetFinder = VetFinderService()
    @State private var nearbyVets: [VeterinaryClinic] = []
    @State private var isLoadingVets = false
    @State private var showingCallConfirmation = false
    @State private var selectedVet: VeterinaryClinic?
    @State private var showingFirstAid = false
    @State private var lostPetReportSent = false
    @State private var showingVetFinder = false
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // Header Section
                    headerSection
                    
                    // Main Action Section
                    mainActionSection
                    
                    // Protocol Steps
                    protocolStepsSection
                    
                    // Additional Resources
                    additionalResourcesSection
                }
                .padding()
            }
            .background(colorScheme == .dark ? Color.black : Color(.systemGroupedBackground))
            .navigationTitle(crisisType.displayName)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
        .onAppear {
            if crisisType == .veterinaryEmergency {
                loadNearbyVets()
            }
        }
        .alert("Call Emergency Vet?", isPresented: $showingCallConfirmation) {
            if let vet = selectedVet {
                Button("Call \(vet.name)") {
                    callVet(vet)
                }
                Button("Cancel", role: .cancel) { }
            }
        } message: {
            if let vet = selectedVet {
                Text("This will call \(vet.name) at \(vet.phoneNumber)")
            }
        }
        .sheet(isPresented: $showingFirstAid) {
            FirstAidInstructionsView(crisisType: crisisType)
        }
        .sheet(isPresented: $showingVetFinder) {
            EmergencyVetFinderView()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Crisis Icon
            ZStack {
                Circle()
                    .fill(crisisType.color.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                Image(systemName: crisisType.iconName)
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(crisisType.color)
            }
            
            // Crisis Title and Description
            VStack(spacing: 8) {
                Text(crisisType.displayName)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(crisisType.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
        )
    }
    
    // MARK: - Main Action Section
    private var mainActionSection: some View {
        VStack(spacing: 12) {
            switch crisisType {
            case .veterinaryEmergency:
                emergencyVetActions
            case .poisoning:
                poisonEmergencyActions
            case .injury:
                injuryProtocolActions
            case .lostPet:
                lostPetActions
            }
        }
    }
    
    // MARK: - Emergency Vet Actions
    private var emergencyVetActions: some View {
        VStack(spacing: 12) {
            // Call 911 for Life-Threatening
            emergencyActionButton(
                title: "🚨 Call 911",
                subtitle: "For life-threatening emergencies",
                color: .red,
                isPrimary: true
            ) {
                call911()
            }
            
            if isLoadingVets {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Finding nearby emergency vets...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
            } else if !nearbyVets.isEmpty {
                VStack(spacing: 8) {
                    Text("Nearest Emergency Vets")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    ForEach(nearbyVets.prefix(3), id: \.id) { vet in
                        vetCardButton(vet)
                    }
                }
            }
            
            // General Vet Call
            emergencyActionButton(
                title: "📞 Call Emergency Vet",
                subtitle: "24/7 veterinary emergency line",
                color: .orange
            ) {
                callGeneralEmergencyVet()
            }
        }
    }
    
    // MARK: - Poison Emergency Actions
    private var poisonEmergencyActions: some View {
        VStack(spacing: 12) {
            emergencyActionButton(
                title: "☎️ Pet Poison Control",
                subtitle: "(************* - Available 24/7",
                color: .purple,
                isPrimary: true
            ) {
                callPoisonControl()
            }
            
            emergencyActionButton(
                title: "🩺 ASPCA Poison Control", 
                subtitle: "(888) 426-4435 - $75 consultation fee",
                color: .orange
            ) {
                callASPCAPoisonControl()
            }
            
            emergencyActionButton(
                title: "📋 First Aid Instructions",
                subtitle: "Immediate poisoning response steps",
                color: .blue
            ) {
                showingFirstAid = true
            }
        }
    }
    
    // MARK: - Injury Protocol Actions
    private var injuryProtocolActions: some View {
        VStack(spacing: 12) {
            emergencyActionButton(
                title: "🚑 Call Emergency Vet",
                subtitle: "For serious injuries requiring immediate care",
                color: .red,
                isPrimary: true
            ) {
                callGeneralEmergencyVet()
            }
            
            emergencyActionButton(
                title: "🩹 First Aid Guide",
                subtitle: "Step-by-step injury treatment",
                color: .green
            ) {
                showingFirstAid = true
            }
            
            emergencyActionButton(
                title: "📍 Find Nearest Vet",
                subtitle: "Locate closest emergency clinic",
                color: .blue
            ) {
                showingVetFinder = true
            }
        }
    }
    
    // MARK: - Lost Pet Actions
    private var lostPetActions: some View {
        VStack(spacing: 12) {
            if !lostPetReportSent {
                emergencyActionButton(
                    title: "📢 Report Lost Pet",
                    subtitle: "Alert local shelters and community",
                    color: .red,
                    isPrimary: true
                ) {
                    reportLostPet()
                }
            } else {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Lost pet report sent!")
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(12)
            }
            
            emergencyActionButton(
                title: "📱 Post on Social Media",
                subtitle: "Share on Facebook, Nextdoor, etc.",
                color: .blue
            ) {
                shareOnSocialMedia()
            }
            
            emergencyActionButton(
                title: "🔍 Search Tips",
                subtitle: "Effective strategies for finding pets",
                color: .purple
            ) {
                showingFirstAid = true
            }
        }
    }
    
    // MARK: - Protocol Steps Section
    private var protocolStepsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("📋 Protocol Steps")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 12) {
                ForEach(Array(crisisType.protocolSteps.enumerated()), id: \.offset) { index, step in
                    protocolStepView(number: index + 1, step: step)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
        )
    }
    
    // MARK: - Additional Resources Section
    private var additionalResourcesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("📚 Additional Resources")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                ForEach(crisisType.additionalResources, id: \.title) { resource in
                    resourceButton(resource)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
        )
    }
    
    // MARK: - Helper Views
    private func emergencyActionButton(
        title: String,
        subtitle: String,
        color: Color,
        isPrimary: Bool = false,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(isPrimary ? .headline : .subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(isPrimary ? .white : .primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(isPrimary ? .white.opacity(0.9) : .secondary)
                }
                
                Spacer()
                
                Image(systemName: "arrow.right.circle.fill")
                    .font(.title2)
                    .foregroundColor(isPrimary ? .white : color)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isPrimary ? color : color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func vetCardButton(_ vet: VeterinaryClinic) -> some View {
        Button(action: {
            selectedVet = vet
            showingCallConfirmation = true
        }) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(vet.name)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(vet.address)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("\(String(format: "%.1f", vet.distance)) mi")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                        
                        if vet.is24Hour {
                            Text("24/7")
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.green.opacity(0.2))
                                .foregroundColor(.green)
                                .cornerRadius(4)
                        }
                    }
                }
                
                Text(vet.phoneNumber)
                    .font(.caption)
                    .foregroundColor(.blue)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.regularMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func protocolStepView(number: Int, step: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Text("\(number)")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Circle().fill(crisisType.color))
            
            Text(step)
                .font(.subheadline)
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)
            
            Spacer()
        }
    }
    
    private func resourceButton(_ resource: ResourceItem) -> some View {
        Button(action: {
            if let url = URL(string: resource.url) {
                UIApplication.shared.open(url)
            }
        }) {
            HStack {
                Text(resource.title)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "arrow.up.right")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Actions
    private func loadNearbyVets() {
        guard let location = locationManager.currentLocation else { return }
        
        isLoadingVets = true
        
        // Create sample nearby vets (in real app, this would be an API call)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            self.nearbyVets = [
                VeterinaryClinic(
                    name: "24/7 Emergency Animal Hospital",
                    address: "123 Emergency Ave, Your City",
                    phoneNumber: "(555) 911-PETS",
                    is24Hour: true,
                    distance: 2.3,
                    rating: 4.8,
                    coordinate: location.coordinate
                ),
                VeterinaryClinic(
                    name: "Downtown Veterinary Emergency",
                    address: "456 Main St, Your City",
                    phoneNumber: "(*************",
                    is24Hour: true,
                    distance: 3.7,
                    rating: 4.6,
                    coordinate: location.coordinate
                ),
                VeterinaryClinic(
                    name: "Westside Animal Emergency",
                    address: "789 West Ave, Your City", 
                    phoneNumber: "(*************",
                    is24Hour: false,
                    distance: 5.1,
                    rating: 4.4,
                    coordinate: location.coordinate
                )
            ]
            self.isLoadingVets = false
        }
    }
    
    private func call911() {
        if let url = URL(string: "tel://911") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callPoisonControl() {
        if let url = URL(string: "tel://**********") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callASPCAPoisonControl() {
        if let url = URL(string: "tel://**********") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callGeneralEmergencyVet() {
        // In real app, would find nearest emergency vet
        if let url = URL(string: "tel://5559111111") {
            UIApplication.shared.open(url)
        }
    }
    
    private func callVet(_ vet: VeterinaryClinic) {
        let phoneNumber = vet.phoneNumber.replacingOccurrences(of: "[^0-9]", with: "", options: .regularExpression)
        if let url = URL(string: "tel://\(phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func reportLostPet() {
        // In real app, would integrate with lost pet services
        lostPetReportSent = true
        
        // Simulate posting to multiple platforms
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // Show success message
        }
    }
    
    private func shareOnSocialMedia() {
        let message = "🚨 LOST PET ALERT 🚨\nPlease help me find my beloved pet! If you see them, please contact me immediately. #LostPet #PetAlert"
        
        if let url = URL(string: "https://www.facebook.com/sharer/sharer.php?u=\(message.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") {
            UIApplication.shared.open(url)
        }
    }
}

#Preview {
    if #available(iOS 18.0, *) {
        PetSupportView()
            .environmentObject(RealDataService())
    } else {
        Text("iOS 18.0+ Required")
    }
}
