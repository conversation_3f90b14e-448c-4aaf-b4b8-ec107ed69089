//
//  VetFinderView.swift
//  PetCapsule
//
//  Find nearby veterinarians and emergency animal hospitals
//

import SwiftUI
import MapKit

struct VetFinderView: View {
    @EnvironmentObject var vetFinder: VetFinderService
    @Environment(\.dismiss) private var dismiss
    @State private var selectedVet: VeterinaryLocation?
    @State private var showingVetDetails = false
    @State private var region = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194),
        span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
    )
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Map View
                Map(coordinateRegion: $region, showsUserLocation: true, annotationItems: allVets) { vet in
                    MapAnnotation(coordinate: vet.coordinate) {
                        VetMapMarker(vet: vet) {
                            selectedVet = vet
                            showingVetDetails = true
                        }
                    }
                }
                .frame(height: 300)
                .onAppear {
                    if let userLocation = vetFinder.userLocation {
                        region.center = userLocation.coordinate
                    }
                    Task {
                        await vetFinder.searchNearbyVets()
                    }
                }
                
                // Search Status
                if vetFinder.isSearching {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Searching for nearby veterinarians...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGroupedBackground))
                }
                
                // Vet List
                List {
                    if !vetFinder.emergencyVets.isEmpty {
                        Section("🚨 Emergency Veterinarians") {
                            ForEach(vetFinder.emergencyVets) { vet in
                                VetListRow(vet: vet) {
                                    selectedVet = vet
                                    showingVetDetails = true
                                }
                            }
                        }
                    }
                    
                    if !vetFinder.nearbyVets.isEmpty {
                        Section("🏥 Nearby Veterinarians") {
                            ForEach(vetFinder.nearbyVets) { vet in
                                VetListRow(vet: vet) {
                                    selectedVet = vet
                                    showingVetDetails = true
                                }
                            }
                        }
                    }
                    
                    if vetFinder.nearbyVets.isEmpty && vetFinder.emergencyVets.isEmpty && !vetFinder.isSearching {
                        Section {
                            VStack(spacing: 12) {
                                Image(systemName: "cross.case")
                                    .font(.system(size: 48))
                                    .foregroundColor(.secondary)
                                
                                Text("No veterinarians found")
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                
                                Text("Try allowing location access or search in a different area")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                                
                                Button("Search Again") {
                                    Task {
                                        await vetFinder.searchNearbyVets()
                                    }
                                }
                                .buttonStyle(.borderedProminent)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 40)
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
            .navigationTitle("Find Veterinarian")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") { dismiss() }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Refresh") {
                        Task {
                            await vetFinder.searchNearbyVets()
                        }
                    }
                    .disabled(vetFinder.isSearching)
                }
            }
            .sheet(isPresented: $showingVetDetails) {
                if let vet = selectedVet {
                    VetDetailsView(vet: vet)
                        .environmentObject(vetFinder)
                }
            }
        }
    }
    
    private var allVets: [VeterinaryLocation] {
        return vetFinder.emergencyVets + vetFinder.nearbyVets
    }
}

struct VetMapMarker: View {
    let vet: VeterinaryLocation
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                Circle()
                    .fill(vet.isEmergency ? Color.red : Color.blue)
                    .frame(width: 30, height: 30)
                    .shadow(radius: 3)
                
                Image(systemName: vet.isEmergency ? "cross.fill" : "stethoscope")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)
            }
        }
    }
}

struct VetListRow: View {
    let vet: VeterinaryLocation
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Vet Icon
                ZStack {
                    Circle()
                        .fill(vet.isEmergency ? Color.red.opacity(0.2) : Color.blue.opacity(0.2))
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: vet.isEmergency ? "cross.fill" : "stethoscope")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(vet.isEmergency ? .red : .blue)
                }
                
                // Vet Info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(vet.name)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Text(vet.distanceString)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                    }
                    
                    Text(vet.address)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    HStack {
                        if vet.isEmergency {
                            Text("24/7 Emergency")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color.red)
                                .cornerRadius(8)
                        }
                        
                        if vet.phoneNumber != nil {
                            Text("Phone available")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                        }
                        
                        Spacer()
                    }
                }
                
                // Arrow
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct VetDetailsView: View {
    let vet: VeterinaryLocation
    @EnvironmentObject var vetFinder: VetFinderService
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text(vet.name)
                                .font(.largeTitle)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            if vet.isEmergency {
                                Text("24/7")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.red)
                                    .cornerRadius(8)
                            }
                        }
                        
                        Text(vet.address)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("\(vet.distanceString) away")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                    .padding()
                    .background(Color(.systemGroupedBackground))
                    .cornerRadius(12)
                    
                    // Action Buttons
                    VStack(spacing: 12) {
                        if vet.phoneNumber != nil {
                            Button(action: {
                                vetFinder.callVet(vet)
                            }) {
                                HStack {
                                    Image(systemName: "phone.fill")
                                    Text("Call Now")
                                        .fontWeight(.semibold)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green)
                                .foregroundColor(.white)
                                .cornerRadius(12)
                            }
                        }
                        
                        Button(action: {
                            vetFinder.openInMaps(vet)
                        }) {
                            HStack {
                                Image(systemName: "location.fill")
                                Text("Get Directions")
                                    .fontWeight(.semibold)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                    }
                    .padding()
                    .background(Color(.systemGroupedBackground))
                    .cornerRadius(12)
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Veterinarian Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    VetFinderView()
        .environmentObject(VetFinderService())
} 