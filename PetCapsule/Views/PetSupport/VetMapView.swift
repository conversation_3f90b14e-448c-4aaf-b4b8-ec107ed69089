import SwiftUI
import MapKit
import CoreLocation

struct VetMapView: View {
    @StateObject private var vetFinder = VetFinderService()
    @State private var showVetDetails: VeterinaryLocation?
    
    var body: some View {
        NavigationView {
            ZStack {
                // Map View
                VetMapViewRepresentable(
                    vets: vetFinder.selectedVets,
                    userLocation: vetFinder.userLocation,
                    selectedVet: $showVetDetails
                )
                .ignoresSafeArea()
                
                // Loading Overlay
                if vetFinder.isSearching {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()
                    
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.5)
                        
                        Text("Finding nearby vets...")
                            .foregroundColor(.white)
                            .font(.headline)
                            .padding(.top)
                    }
                }
                
                // Error Message
                if let error = vetFinder.searchError {
                    VStack {
                        Spacer()
                        
                        Text(error)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.red)
                            .cornerRadius(12)
                            .padding()
                    }
                }
                
                // Vet List Bottom Sheet
                if !vetFinder.selectedVets.isEmpty {
                    VStack {
                        Spacer()
                        
                        VetListBottomSheet(
                            vets: vetFinder.selectedVets,
                            selectedVet: $showVetDetails
                        )
                    }
                }
            }
            .navigationTitle("Nearby Vets")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                Task {
                    await vetFinder.showNearbyVetsMapPopup()
                }
            }
        }
    }
}

// MARK: - Map View Representable

struct VetMapViewRepresentable: UIViewRepresentable {
    let vets: [VeterinaryLocation]
    let userLocation: CLLocation?
    @Binding var selectedVet: VeterinaryLocation?
    
    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .none
        return mapView
    }
    
    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Remove existing annotations
        mapView.removeAnnotations(mapView.annotations.filter { !($0 is MKUserLocation) })
        
        // Add vet annotations
        let annotations = vets.map { vet in
            VetAnnotation(vet: vet)
        }
        mapView.addAnnotations(annotations)
        
        // Update region to show all vets
        if !vets.isEmpty {
            let coordinates = vets.map { $0.coordinate }
            let region = regionToFit(coordinates: coordinates, userLocation: userLocation)
            mapView.setRegion(region, animated: true)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    private func regionToFit(coordinates: [CLLocationCoordinate2D], userLocation: CLLocation?) -> MKCoordinateRegion {
        var allCoordinates = coordinates
        
        // Include user location if available
        if let userCoord = userLocation?.coordinate {
            allCoordinates.append(userCoord)
        }
        
        guard !allCoordinates.isEmpty else {
            return MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194),
                span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
            )
        }
        
        let minLat = allCoordinates.map { $0.latitude }.min()!
        let maxLat = allCoordinates.map { $0.latitude }.max()!
        let minLon = allCoordinates.map { $0.longitude }.min()!
        let maxLon = allCoordinates.map { $0.longitude }.max()!
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: max(0.01, (maxLat - minLat) * 1.3),
            longitudeDelta: max(0.01, (maxLon - minLon) * 1.3)
        )
        
        return MKCoordinateRegion(center: center, span: span)
    }
    
    class Coordinator: NSObject, MKMapViewDelegate {
        let parent: VetMapViewRepresentable
        
        init(_ parent: VetMapViewRepresentable) {
            self.parent = parent
        }
        
        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            guard let vetAnnotation = annotation as? VetAnnotation else { return nil }
            
            let identifier = "VetPin"
            let annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier) as? MKMarkerAnnotationView ?? MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            
            annotationView.annotation = annotation
            annotationView.glyphImage = UIImage(systemName: vetAnnotation.vet.isEmergency ? "cross.circle.fill" : "stethoscope")
            annotationView.markerTintColor = vetAnnotation.vet.isEmergency ? .systemRed : .systemBlue
            annotationView.canShowCallout = true
            
            // Add call and directions buttons
            let callButton = UIButton(type: .system)
            callButton.setImage(UIImage(systemName: "phone.fill"), for: .normal)
            callButton.tintColor = .systemGreen
            callButton.addTarget(self, action: #selector(callVet), for: .touchUpInside)
            
            let directionsButton = UIButton(type: .system)
            directionsButton.setImage(UIImage(systemName: "map.fill"), for: .normal)
            directionsButton.tintColor = .systemBlue
            directionsButton.addTarget(self, action: #selector(getDirections), for: .touchUpInside)
            
            let stackView = UIStackView(arrangedSubviews: [callButton, directionsButton])
            stackView.axis = .horizontal
            stackView.spacing = 16
            
            annotationView.rightCalloutAccessoryView = stackView
            
            return annotationView
        }
        
        func mapView(_ mapView: MKMapView, didSelect view: MKAnnotationView) {
            if let vetAnnotation = view.annotation as? VetAnnotation {
                parent.selectedVet = vetAnnotation.vet
            }
        }
        
        @objc private func callVet() {
            guard let selectedVet = parent.selectedVet,
                  let phoneNumber = selectedVet.phoneNumber else { return }
            
            let cleanNumber = phoneNumber.replacingOccurrences(of: "[^0-9+]", with: "", options: .regularExpression)
            
            if let url = URL(string: "tel:\(cleanNumber)") {
                UIApplication.shared.open(url)
            }
        }
        
        @objc private func getDirections() {
            guard let selectedVet = parent.selectedVet else { return }
            
            selectedVet.mapItem.openInMaps(launchOptions: [
                MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving
            ])
        }
    }
}

// MARK: - Vet Annotation

class VetAnnotation: NSObject, MKAnnotation {
    let vet: VeterinaryLocation
    
    var coordinate: CLLocationCoordinate2D {
        return vet.coordinate
    }
    
    var title: String? {
        return vet.name
    }
    
    var subtitle: String? {
        let distance = vet.distanceString
        let emergency = vet.isEmergency ? " • 24/7 Emergency" : ""
        return "\(distance)\(emergency)"
    }
    
    init(vet: VeterinaryLocation) {
        self.vet = vet
    }
}

// MARK: - Bottom Sheet

struct VetListBottomSheet: View {
    let vets: [VeterinaryLocation]
    @Binding var selectedVet: VeterinaryLocation?
    @State private var isExpanded = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Handle bar
            RoundedRectangle(cornerRadius: 3)
                .fill(Color.secondary)
                .frame(width: 40, height: 6)
                .padding(.top, 8)
                .onTapGesture {
                    withAnimation(.spring()) {
                        isExpanded.toggle()
                    }
                }
            
            // Content
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Text("Nearby Vets (\(vets.count))")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Button(action: {
                        withAnimation(.spring()) {
                            isExpanded.toggle()
                        }
                    }) {
                        Image(systemName: isExpanded ? "chevron.down" : "chevron.up")
                            .font(.title3)
                            .foregroundColor(.blue)
                    }
                }
                
                if isExpanded {
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(vets) { vet in
                                VetRowView(
                                    vet: vet,
                                    isSelected: selectedVet?.id == vet.id
                                ) {
                                    selectedVet = vet
                                }
                            }
                        }
                    }
                    .frame(maxHeight: 300)
                } else {
                    // Show just the nearest vet when collapsed
                    if let nearestVet = vets.first {
                        VetRowView(
                            vet: nearestVet,
                            isSelected: selectedVet?.id == nearestVet.id
                        ) {
                            selectedVet = nearestVet
                        }
                    }
                }
            }
            .padding()
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(radius: 10)
        )
        .padding()
    }
}

// MARK: - Vet Row View

struct VetRowView: View {
    let vet: VeterinaryLocation
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Vet Type Icon
                ZStack {
                    Circle()
                        .fill(vet.isEmergency ? Color.red.opacity(0.15) : Color.blue.opacity(0.15))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: vet.isEmergency ? "cross.circle.fill" : "stethoscope")
                        .foregroundColor(vet.isEmergency ? .red : .blue)
                        .font(.title3)
                }
                
                // Vet Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(vet.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                    
                    HStack {
                        Text(vet.distanceString)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                        
                        if vet.isEmergency {
                            Text("• 24/7 Emergency")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                        
                        Spacer()
                    }
                    
                    Text(vet.address)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                // Action Buttons
                HStack(spacing: 8) {
                    if vet.phoneNumber != nil {
                        Button(action: { callVet(vet) }) {
                            Image(systemName: "phone.fill")
                                .font(.caption)
                                .foregroundColor(.green)
                                .padding(8)
                                .background(Color.green.opacity(0.1))
                                .clipShape(Circle())
                        }
                    }
                    
                    Button(action: { openInMaps(vet) }) {
                        Image(systemName: "map.fill")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .padding(8)
                            .background(Color.blue.opacity(0.1))
                            .clipShape(Circle())
                    }
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func callVet(_ vet: VeterinaryLocation) {
        guard let phoneNumber = vet.phoneNumber else { return }
        let cleanNumber = phoneNumber.replacingOccurrences(of: "[^0-9+]", with: "", options: .regularExpression)
        
        if let url = URL(string: "tel:\(cleanNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openInMaps(_ vet: VeterinaryLocation) {
        vet.mapItem.openInMaps(launchOptions: [
            MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving
        ])
    }
}

#Preview {
    VetMapView()
} 