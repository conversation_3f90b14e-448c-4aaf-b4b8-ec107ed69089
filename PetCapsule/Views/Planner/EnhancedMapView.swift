//
//  EnhancedMapView.swift
//  PetCapsule
//
//  Enhanced Apple MapKit integration for iOS 18 Award Consideration
//  Interactive maps with custom annotations and smooth animations
//

import SwiftUI
import MapKit
import CoreLocation

struct EnhancedMapView: View {
    @StateObject private var mapService = AppleMapService.shared
    @StateObject private var locationManager = LocationManager()
    
    @State private var cameraPosition: MapCameraPosition = .automatic
    @State private var selectedLocation: PetFriendlyLocation?
    @State private var showingLocationDetails = false
    @State private var mapStyle: MapStyle = .standard
    @State private var showingRouteOptions = false
    @State private var selectedRoute: WalkRoute?
    
    let locations: [PetFriendlyLocation]
    let showUserLocation: Bool
    let allowRouteSelection: Bool
    
    init(locations: [PetFriendlyLocation] = [], showUserLocation: Bool = true, allowRouteSelection: Bool = true) {
        self.locations = locations
        self.showUserLocation = showUserLocation
        self.allowRouteSelection = allowRouteSelection
    }
    
    var body: some View {
        ZStack {
            // Main Map View
            Map(position: $cameraPosition) {
                // User location
                if showUserLocation, let userLocation = locationManager.currentLocation {
                    Annotation("Your Location", coordinate: userLocation.coordinate) {
                        EnhancedUserLocationMarker()
                    }
                }
                
                // Pet-friendly locations
                ForEach(displayedLocations, id: \.id) { location in
                    Annotation(location.name, coordinate: location.coordinate) {
                        EnhancedPetLocationMarker(location: location)
                            .onTapGesture {
                                selectedLocation = location
                                showingLocationDetails = true
                            }
                    }
                }
                
                // Walk route overlay
                if let route = selectedRoute {
                    MapPolyline(coordinates: route.coordinates)
                        .stroke(.blue, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                }
            }
            .mapStyle(mapStyle)
            .mapControls {
                MapUserLocationButton()
                MapCompass()
                MapScaleView()
            }
            .onMapCameraChange { context in
                // Handle camera changes if needed
            }
            
            // Map Style Selector
            VStack {
                HStack {
                    Spacer()
                    mapStyleSelector
                }
                Spacer()
            }
            .padding()
            
            // Route Options Button
            if allowRouteSelection && selectedLocation != nil {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        routeOptionsButton
                    }
                }
                .padding()
            }
        }
        .sheet(isPresented: $showingLocationDetails) {
            if let location = selectedLocation {
                LocationDetailSheet(location: location)
            }
        }
        .sheet(isPresented: $showingRouteOptions) {
            if let location = selectedLocation {
                RouteOptionsSheet(destination: location, onRouteSelected: handleRouteSelection)
            }
        }
        .onAppear {
            setupInitialCamera()
        }
        .onChange(of: locationManager.currentLocation) { _, newLocation in
            if let location = newLocation {
                updateCameraPosition(for: location.coordinate)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var displayedLocations: [PetFriendlyLocation] {
        if locations.isEmpty {
            return mapService.nearbyPetFriendlyLocations
        } else {
            return locations
        }
    }
    
    // MARK: - UI Components
    
    private var mapStyleSelector: some View {
        Menu {
            Button("Standard") { mapStyle = .standard }
            Button("Satellite") { mapStyle = .imagery }
            Button("Hybrid") { mapStyle = .hybrid }
        } label: {
            Image(systemName: "map")
                .font(.title2)
                .foregroundColor(.primary)
                .padding(12)
                .background(.regularMaterial, in: Circle())
                .shadow(radius: 2)
        }
    }
    
    private var routeOptionsButton: some View {
        Button(action: {
            showingRouteOptions = true
        }) {
            Image(systemName: "point.topleft.down.curvedto.point.bottomright.up")
                .font(.title2)
                .foregroundColor(.white)
                .padding(12)
                .background(.blue, in: Circle())
                .shadow(radius: 4)
        }
    }
    
    // MARK: - Helper Methods
    
    private func setupInitialCamera() {
        if let userLocation = locationManager.currentLocation {
            cameraPosition = .region(MKCoordinateRegion(
                center: userLocation.coordinate,
                latitudinalMeters: 5000,
                longitudinalMeters: 5000
            ))
        } else if let firstLocation = displayedLocations.first {
            cameraPosition = .region(MKCoordinateRegion(
                center: firstLocation.coordinate,
                latitudinalMeters: 5000,
                longitudinalMeters: 5000
            ))
        }
    }
    
    private func updateCameraPosition(for coordinate: CLLocationCoordinate2D) {
        withAnimation(.easeInOut(duration: 1.0)) {
            cameraPosition = .region(MKCoordinateRegion(
                center: coordinate,
                latitudinalMeters: 5000,
                longitudinalMeters: 5000
            ))
        }
    }
    
    private func handleRouteRequest(to location: PetFriendlyLocation) {
        selectedLocation = location
        showingRouteOptions = true
        showingLocationDetails = false
    }
    
    private func handleRouteSelection(_ route: WalkRoute) {
        selectedRoute = route
        showingRouteOptions = false
        
        // Animate to show the full route
        if let polyline = route.getMKPolyline() {
            let routeRegion = MKCoordinateRegion(polyline.boundingMapRect)
            withAnimation(.easeInOut(duration: 1.5)) {
                cameraPosition = .region(routeRegion)
            }
        }
    }
}

// MARK: - Custom Markers

struct EnhancedUserLocationMarker: View {
    var body: some View {
        ZStack {
            Circle()
                .fill(.blue.opacity(0.3))
                .frame(width: 32, height: 32)
            
            Circle()
                .fill(.blue)
                .frame(width: 16, height: 16)
                .overlay(
                    Circle()
                        .stroke(.white, lineWidth: 2)
                )
        }
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: true)
    }
}

struct EnhancedPetLocationMarker: View {
    let location: PetFriendlyLocation

    var body: some View {
        VStack(spacing: 0) {
            Image(systemName: location.category.icon)
                .font(.title3)
                .foregroundColor(.white)
                .padding(8)
                .background(categoryColor)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(.white, lineWidth: 2)
                )
                .shadow(radius: 3)

            // Pointer
            Triangle()
                .fill(categoryColor)
                .frame(width: 12, height: 8)
                .offset(y: -2)
        }
        .scaleEffect(1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: location.id)
    }
    
    private var categoryColor: Color {
        switch location.category {
        case .park:
            return .green
        case .veterinary:
            return .red
        case .petStore:
            return .orange
        case .restaurant:
            return .purple
        case .grooming:
            return .pink
        case .beach:
            return .cyan
        case .trail:
            return .brown
        case .other:
            return .gray
        }
    }
}

struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.minY))
        path.closeSubpath()
        return path
    }
}

// MARK: - Location Manager

class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    private let locationManager = CLLocationManager()
    
    @Published var currentLocation: CLLocation?
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined
    
    override init() {
        super.init()
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        requestLocationPermission()
    }
    
    func requestLocationPermission() {
        switch authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            // Handle denied access
            break
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        @unknown default:
            break
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        currentLocation = locations.last
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        authorizationStatus = status
        
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        case .denied, .restricted:
            locationManager.stopUpdatingLocation()
        default:
            break
        }
    }
}

// MARK: - Extensions

extension MKCoordinateRegion {
    init(_ boundingMapRect: MKMapRect) {
        let topLeft = MKMapPoint(x: boundingMapRect.minX, y: boundingMapRect.minY)
        let bottomRight = MKMapPoint(x: boundingMapRect.maxX, y: boundingMapRect.maxY)
        
        let center = CLLocationCoordinate2D(
            latitude: (topLeft.coordinate.latitude + bottomRight.coordinate.latitude) / 2,
            longitude: (topLeft.coordinate.longitude + bottomRight.coordinate.longitude) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: abs(topLeft.coordinate.latitude - bottomRight.coordinate.latitude) * 1.3,
            longitudeDelta: abs(topLeft.coordinate.longitude - bottomRight.coordinate.longitude) * 1.3
        )
        
        self.init(center: center, span: span)
    }
}

// MARK: - Pollen Indicator Component

struct PollenIndicator: View {
    let type: String
    let level: Int
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 12, height: 12)

            Text(type)
                .font(.caption2)
                .foregroundColor(.primary)

            Text(levelText)
                .font(.caption2)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity)
    }

    private var levelText: String {
        switch level {
        case 0...1:
            return "Low"
        case 2...3:
            return "Moderate"
        case 4...5:
            return "High"
        default:
            return "Very High"
        }
    }
}

#Preview {
    EnhancedMapView()
}
