//
//  FoodDatabaseView.swift
//  PetCapsule
//
//  Browse and search pet food database
//

import SwiftUI

struct FoodDatabaseView: View {
    @StateObject private var nutritionService = NutritionPlannerService.shared
    @State private var searchText = ""
    @State private var selectedCategory: FoodCategory = .dryFood
    @State private var selectedSpecies: PetSpecies = .dog
    @State private var sortBy: SortOption = .rating
    @State private var showingFilters = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Search and Filters
            searchAndFiltersSection
            
            // Category Tabs
            categoryTabsSection
            
            // Food List
            foodListSection
        }
        .sheet(isPresented: $showingFilters) {
            FoodFiltersView(
                selectedSpecies: $selectedSpecies,
                sortBy: $sortBy
            )
        }
    }
    
    // MARK: - Search and Filters Section
    
    private var searchAndFiltersSection: some View {
        VStack(spacing: 12) {
            HStack {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search foods...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color(.systemGray6))
                .cornerRadius(8)
                
                Button("Filters") {
                    showingFilters = true
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            // Quick Stats
            HStack {
                Text("\(filteredFoods.count) foods")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("Sorted by \(sortBy.displayName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    // MARK: - Category Tabs Section
    
    private var categoryTabsSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                ForEach(FoodCategory.allCases, id: \.self) { category in
                    CategoryTab(
                        category: category,
                        isSelected: selectedCategory == category,
                        count: foodCount(for: category)
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
    
    // MARK: - Food List Section
    
    private var foodListSection: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredFoods) { food in
                    FoodItemCard(food: food)
                }
            }
            .padding()
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredFoods: [FoodItem] {
        var foods = nutritionService.foodDatabase
        
        // Filter by category
        foods = foods.filter { $0.category == selectedCategory }
        
        // Filter by species
        foods = foods.filter { $0.species == selectedSpecies }
        
        // Filter by search text
        if !searchText.isEmpty {
            foods = foods.filter { food in
                food.name.localizedCaseInsensitiveContains(searchText) ||
                food.brand.localizedCaseInsensitiveContains(searchText) ||
                food.ingredients.joined().localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Sort
        switch sortBy {
        case .rating:
            foods = foods.sorted { $0.rating > $1.rating }
        case .price:
            foods = foods.sorted { $0.price < $1.price }
        case .calories:
            foods = foods.sorted { $0.caloriesPerCup < $1.caloriesPerCup }
        case .protein:
            foods = foods.sorted { $0.macros.protein > $1.macros.protein }
        case .name:
            foods = foods.sorted { $0.name < $1.name }
        }
        
        return foods
    }
    
    private func foodCount(for category: FoodCategory) -> Int {
        nutritionService.foodDatabase.filter { $0.category == category && $0.species == selectedSpecies }.count
    }
}

// MARK: - Supporting Views

struct CategoryTab: View {
    let category: FoodCategory
    let isSelected: Bool
    let count: Int
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                HStack(spacing: 6) {
                    Image(systemName: category.icon)
                        .font(.caption)
                    
                    Text(category.displayName)
                        .font(.subheadline)
                        .fontWeight(isSelected ? .semibold : .regular)
                }
                .foregroundColor(isSelected ? .white : .primary)
                
                Text("\(count)")
                    .font(.caption2)
                    .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? Color.blue : Color(.systemBackground))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct FoodItemCard: View {
    let food: FoodItem
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(food.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(food.brand)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                        
                        Text(String(format: "%.1f", food.rating))
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    Text("$\(String(format: "%.2f", food.price))")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
            }
            
            // Nutrition Info
            HStack(spacing: 16) {
                NutritionBadge(
                    label: "Calories",
                    value: "\(food.caloriesPerCup)",
                    unit: "/cup",
                    color: .blue
                )
                
                NutritionBadge(
                    label: "Protein",
                    value: "\(food.macros.protein)",
                    unit: "g",
                    color: .green
                )
                
                NutritionBadge(
                    label: "Fat",
                    value: "\(food.macros.fat)",
                    unit: "g",
                    color: .orange
                )
                
                Spacer()
            }
            
            // Ingredients Preview
            VStack(alignment: .leading, spacing: 4) {
                Text("Main Ingredients")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                Text(food.ingredients.prefix(3).joined(separator: ", "))
                    .font(.caption)
                    .foregroundColor(.primary)
                    .lineLimit(1)
            }
            
            // Allergens (if any)
            if !food.allergens.isEmpty {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    
                    Text("Contains: \(food.allergens.joined(separator: ", "))")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct NutritionBadge: View {
    let label: String
    let value: String
    let unit: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 2) {
            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
            
            HStack(spacing: 1) {
                Text(value)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
                
                Text(unit)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct FoodFiltersView: View {
    @Binding var selectedSpecies: PetSpecies
    @Binding var sortBy: SortOption
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            Form {
                Section("Pet Type") {
                    Picker("Species", selection: $selectedSpecies) {
                        ForEach(PetSpecies.allCases, id: \.self) { species in
                            Text(species.displayName).tag(species)
                        }
                    }
                    .pickerStyle(.segmented)
                }
                
                Section("Sort By") {
                    Picker("Sort Option", selection: $sortBy) {
                        ForEach(SortOption.allCases, id: \.self) { option in
                            Text(option.displayName).tag(option)
                        }
                    }
                    .pickerStyle(.navigationLink)
                }
            }
            .navigationTitle("Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Enums

enum SortOption: String, CaseIterable {
    case rating = "rating"
    case price = "price"
    case calories = "calories"
    case protein = "protein"
    case name = "name"
    
    var displayName: String {
        switch self {
        case .rating: return "Rating"
        case .price: return "Price"
        case .calories: return "Calories"
        case .protein: return "Protein"
        case .name: return "Name"
        }
    }
}

#Preview {
    FoodDatabaseView()
}
