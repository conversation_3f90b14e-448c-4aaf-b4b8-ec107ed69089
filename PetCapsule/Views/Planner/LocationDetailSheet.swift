//
//  LocationDetailSheet.swift
//  PetCapsule
//
//  Location details sheet for enhanced MapKit integration
//

import SwiftUI
import MapKit

struct PlannerLocationDetailSheet: View {
    let location: PetFriendlyLocation
    let onRouteRequest: (PetFriendlyLocation) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var showingFullMap = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header Image/Map Preview
                    mapPreview
                    
                    // Location Info
                    locationInfo
                    
                    // Action Buttons
                    actionButtons
                    
                    // Additional Details
                    additionalDetails
                }
                .padding()
            }
            .navigationTitle(location.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Directions") {
                        onRouteRequest(location)
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingFullMap) {
            EnhancedMapView(locations: [location], showUserLocation: true, allowRouteSelection: false)
        }
    }
    
    // MARK: - UI Components
    
    private var mapPreview: some View {
        Button(action: {
            showingFullMap = true
        }) {
            Map(bounds: MapCameraBounds(
                minimumDistance: 500,
                maximumDistance: 2000
            )) {
                Annotation(location.name, coordinate: CLLocationCoordinate2D(latitude: 40.7829, longitude: -73.9654)) {
                    PetLocationMarker(location: location)
                }
            }
            .frame(height: 200)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var locationInfo: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Category and Rating
            HStack {
                Label(location.type.rawValue, systemImage: location.type.icon)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if location.rating > 0 {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                        Text(String(format: "%.1f", location.rating))
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
            }
            
            // Address
            if !location.address.isEmpty {
                Label(location.address, systemImage: "location")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Distance
            if location.distance > 0 {
                Label(formatDistance(location.distance), systemImage: "figure.walk")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Status
            HStack {
                Circle()
                    .fill(location.isOpen ? .green : .red)
                    .frame(width: 8, height: 8)
                Text(location.isOpen ? "Open" : "Closed")
                    .font(.subheadline)
                    .foregroundColor(location.isOpen ? .green : .red)
                    .fontWeight(.medium)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            // Primary Actions
            HStack(spacing: 12) {
                // Directions Button
                Button(action: {
                    onRouteRequest(location)
                }) {
                    Label("Directions", systemImage: "arrow.triangle.turn.up.right.diamond")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        .fontWeight(.semibold)
                }
                
                // Call Button (placeholder - no phone number in current model)
                Button(action: {
                    callLocation("************")
                }) {
                    Label("Call", systemImage: "phone")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        .fontWeight(.semibold)
                }
            }
            
            // Secondary Actions
            HStack(spacing: 12) {
                // Website Button (placeholder - no website in current model)
                Button(action: {
                    openWebsite("https://example.com")
                }) {
                    Label("Website", systemImage: "safari")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.orange)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        .fontWeight(.semibold)
                }
                
                // Share Button
                Button(action: {
                    shareLocation()
                }) {
                    Label("Share", systemImage: "square.and.arrow.up")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.purple)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        .fontWeight(.semibold)
                }
            }
        }
    }
    
    private var additionalDetails: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("About This Location")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                DetailRow(title: "Category", value: location.type.rawValue)
                
                if location.rating > 0 {
                    DetailRow(title: "Rating", value: String(format: "%.1f stars", location.rating))
                }
                
                if location.distance > 0 {
                    DetailRow(title: "Distance", value: formatDistance(location.distance))
                }
                
                DetailRow(title: "Status", value: location.isOpen ? "Currently Open" : "Currently Closed")
                
                DetailRow(title: "Phone", value: "************")
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Helper Methods
    
    private func formatDistance(_ distance: Double) -> String {
        let formatter = MKDistanceFormatter()
        formatter.unitStyle = .abbreviated
        return formatter.string(fromDistance: distance)
    }
    
    private func callLocation(_ phoneNumber: String) {
        if let url = URL(string: "tel:\(phoneNumber)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openWebsite(_ website: String) {
        if let url = URL(string: website) {
            UIApplication.shared.open(url)
        }
    }
    
    private func shareLocation() {
        let activityController = UIActivityViewController(
            activityItems: [
                "\(location.name)\n\(location.address)\n\nShared from PetCapsule"
            ],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityController, animated: true)
        }
    }
}

// MARK: - Supporting Views

struct DetailRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

#Preview {
    PlannerLocationDetailSheet(
        location: PetFriendlyLocation(
            name: "Central Dog Park",
            type: .park,
            rating: 4.5,
            distance: 1200,
            address: "123 Park Avenue, New York, NY",
            imageURL: "https://example.com/park.jpg",
            amenities: ["Off-leash area", "Water fountains", "Waste bags"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 40.7829, longitude: -73.9654),
            phoneNumber: "(*************",
            website: "https://www.centralparknyc.org",
            hours: "6:00 AM - 1:00 AM"
        ),
        onRouteRequest: { _ in }
    )
}
