//
//  MealScheduleView.swift
//  PetCapsule
//
//  Detailed meal scheduling and tracking interface
//

import SwiftUI

struct MealScheduleView: View {
    let petId: String
    @StateObject private var nutritionService = NutritionPlannerService.shared
    @State private var selectedDate = Date()
    @State private var showingMealDetail = false
    @State private var selectedMeal: ScheduledMeal?
    
    var body: some View {
        VStack(spacing: 16) {
            // Date Picker
            datePickerSection
            
            // Schedule Overview
            scheduleOverviewCard
            
            // Meals List
            mealsListSection
            
            Spacer()
        }
        .sheet(isPresented: $showingMealDetail) {
            if selectedMeal != nil {
                Text("Meal details temporarily unavailable")
            }
        }
    }
    
    // MARK: - Date Picker Section
    
    private var datePickerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Schedule")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(dateRange, id: \.self) { date in
                        DateCard(
                            date: date,
                            isSelected: Calendar.current.isDate(date, inSameDayAs: selectedDate),
                            completionRate: getCompletionRate(for: date)
                        ) {
                            selectedDate = date
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Schedule Overview Card
    
    private var scheduleOverviewCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Today's Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if let schedule = activeSchedule {
                    Text("\(Int(schedule.completionRate * 100))% Complete")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                        .fontWeight(.medium)
                }
            }
            
            if let schedule = activeSchedule {
                ProgressView(value: schedule.completionRate)
                    .tint(.blue)
                
                HStack {
                    Text("\(schedule.todaysMeals.filter { $0.isCompleted }.count) of \(schedule.todaysMeals.count) meals completed")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    if schedule.completionRate == 1.0 {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("All done!")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                }
            } else {
                Text("No meal schedule found")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Meals List Section
    
    private var mealsListSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Meals")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let schedule = activeSchedule {
                ForEach(mealsForSelectedDate) { meal in
                    MealCard(
                        meal: meal,
                        scheduleId: schedule.id,
                        onTap: {
                            selectedMeal = meal
                            showingMealDetail = true
                        }
                    )
                }
            } else {
                EmptyMealsView()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Computed Properties
    
    private var dateRange: [Date] {
        let calendar = Calendar.current
        let today = Date()
        return (-3...3).compactMap { offset in
            calendar.date(byAdding: .day, value: offset, to: today)
        }
    }
    
    private var activeSchedule: MealSchedule? {
        nutritionService.mealSchedules.first { $0.petId == petId && $0.isActive }
    }
    
    private var mealsForSelectedDate: [ScheduledMeal] {
        guard let schedule = activeSchedule else { return [] }
        let calendar = Calendar.current
        return schedule.meals.filter { meal in
            calendar.isDate(meal.scheduledTime, inSameDayAs: selectedDate)
        }.sorted { $0.scheduledTime < $1.scheduledTime }
    }
    
    // MARK: - Helper Methods
    
    private func getCompletionRate(for date: Date) -> Double {
        guard let schedule = activeSchedule else { return 0 }
        let calendar = Calendar.current
        let mealsForDate = schedule.meals.filter { meal in
            calendar.isDate(meal.scheduledTime, inSameDayAs: date)
        }
        
        let completedMeals = mealsForDate.filter { $0.isCompleted }.count
        return mealsForDate.count > 0 ? Double(completedMeals) / Double(mealsForDate.count) : 0
    }
}

// MARK: - Supporting Views

struct DateCard: View {
    let date: Date
    let isSelected: Bool
    let completionRate: Double
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(dayFormatter.string(from: date))
                    .font(.caption)
                    .foregroundColor(isSelected ? .white : .secondary)
                
                Text(dateFormatter.string(from: date))
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(isSelected ? .white : .primary)
                
                // Completion indicator
                Circle()
                    .stroke(isSelected ? Color.white.opacity(0.5) : Color.gray.opacity(0.3), lineWidth: 2)
                    .background(
                        Circle()
                            .trim(from: 0, to: completionRate)
                            .stroke(isSelected ? .white : .blue, lineWidth: 2)
                            .rotationEffect(.degrees(-90))
                    )
                    .frame(width: 20, height: 20)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(isSelected ? Color.blue : Color(.systemBackground))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private let dayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEE"
        return formatter
    }()
    
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter
    }()
}

struct MealCard: View {
    let meal: ScheduledMeal
    let scheduleId: String
    let onTap: () -> Void
    @StateObject private var nutritionService = NutritionPlannerService.shared
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // Completion Button
                Button(action: {
                    nutritionService.updateMealCompletion(
                        scheduleId: scheduleId,
                        mealId: meal.id,
                        completed: !meal.isCompleted,
                        actualTime: meal.isCompleted ? nil : Date()
                    )
                }) {
                    Image(systemName: meal.isCompleted ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(meal.isCompleted ? .green : .gray)
                        .font(.title2)
                }
                .buttonStyle(PlainButtonStyle())
                
                // Meal Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(mealName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(formatTime(meal.scheduledTime))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let notes = meal.notes, !notes.isEmpty {
                        Text(notes)
                            .font(.caption)
                            .foregroundColor(.blue)
                            .lineLimit(1)
                    }
                }
                
                Spacer()
                
                // Status Indicator
                VStack(alignment: .trailing, spacing: 4) {
                    statusIndicator
                    
                    if meal.isCompleted, let actualTime = meal.actualTime {
                        Text("at \(formatTime(actualTime))")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var mealName: String {
        let hour = Calendar.current.component(.hour, from: meal.scheduledTime)
        switch hour {
        case 6...10: return "Breakfast"
        case 11...14: return "Lunch"
        case 15...18: return "Dinner"
        case 19...21: return "Evening Meal"
        default: return "Meal"
        }
    }
    
    private var statusIndicator: some View {
        Group {
            if meal.isCompleted {
                Text("Completed")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.1))
                    .foregroundColor(.green)
                    .cornerRadius(8)
            } else if meal.isOverdue {
                Text("Overdue")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.1))
                    .foregroundColor(.red)
                    .cornerRadius(8)
            } else if Date() > meal.scheduledTime {
                Text("Due Now")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.1))
                    .foregroundColor(.orange)
                    .cornerRadius(8)
            } else {
                Text("Scheduled")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(8)
            }
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        return formatter.string(from: date)
    }
}

struct EmptyMealsView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "fork.knife.circle")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("No Meals Scheduled")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("Create a nutrition plan to get started with meal scheduling")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

#Preview {
    MealScheduleView(petId: "sample-pet-id")
}
