//
//  NutritionPlanCreationView.swift
//  PetCapsule
//
//  Create personalized nutrition plans for pets
//
import SwiftUI
struct NutritionPlanCreationView: View {
    let pet: Pet
    @Environment(\.dismiss) private var dismiss
    @StateObject private var nutritionService = NutritionPlannerService.shared
    @State private var selectedGoals: Set<GoalType> = [.weight, .hydration]
    @State private var activityLevel: ActivityLevel = .moderate
    @State private var dietaryRestrictions: Set<String> = []
    @State private var preferredBrands: Set<String> = []
    @State private var budget: BudgetRange = .moderate
    @State private var mealsPerDay = 2
    @State private var isCreating = false
    var body: some View {
        NavigationView {
            Form {
                // Pet Information Section
                Section("Pet Information") {
                    HStack {
                        Circle()
                            .fill(Color.blue)
                            .frame(width: 50, height: 50)
                            .overlay(
                                Text(String(pet.name.prefix(1)))
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                            )
                        VStack(alignment: .leading) {
                            Text(pet.name)
                                .font(.headline)
                                .fontWeight(.semibold)
                            Text("\(pet.breed ?? "Mixed") • \(pet.age) years old")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        Spacer()
                    }
                    .padding(.vertical, 4)
                }
                // Activity Level Section
                Section("Activity Level") {
                    Picker("Activity Level", selection: $activityLevel) {
                        ForEach(ActivityLevel.allCases, id: \.self) { level in
                            VStack(alignment: .leading) {
                                Text(level.displayName)
                                    .font(.subheadline)
                                Text(level.description)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .tag(level)
                        }
                    }
                    .pickerStyle(.navigationLink)
                }
                // Nutrition Goals Section
                Section("Nutrition Goals") {
                    ForEach(GoalType.allCases, id: \.self) { goalType in
                        HStack {
                            Image(systemName: goalType.icon)
                                .foregroundColor(goalType.color)
                                .frame(width: 20)
                            VStack(alignment: .leading) {
                                Text(goalType.displayName)
                                    .font(.subheadline)
                                Text(goalDescription(for: goalType))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                            Toggle("", isOn: Binding(
                                get: { selectedGoals.contains(goalType) },
                                set: { isSelected in
                                    if isSelected {
                                        selectedGoals
                                    } else {
                                        selectedGoals.remove(goalType)
                                    }
                                }
                            ))
                        }
                    }
                }
                // Feeding Schedule Section
                Section("Feeding Schedule") {
                    Stepper("Meals per day: \(mealsPerDay)", value: $mealsPerDay, in: 1...4)
                    Text(mealScheduleDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                // Dietary Restrictions Section
                Section("Dietary Restrictions") {
                    ForEach(commonAllergens, id: \.self) { allergen in
                        HStack {
                            Text(allergen)
                            Spacer()
                            Toggle("", isOn: Binding(
                                get: { dietaryRestrictions.contains(allergen) },
                                set: { isSelected in
                                    if isSelected {
                                        dietaryRestrictions
                                    } else {
                                        dietaryRestrictions.remove(allergen)
                                    }
                                }
                            ))
                        }
                    }
                }
                // Budget Section
                Section("Budget") {
                    Picker("Monthly Budget", selection: $budget) {
                        ForEach(BudgetRange.allCases, id: \.self) { range in
                            VStack(alignment: .leading) {
                                Text(range.displayName)
                                    .font(.subheadline)
                                Text(range.description)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .tag(range)
                        }
                    }
                    .pickerStyle(.navigationLink)
                }
                // Preferred Brands Section
                Section("Preferred Brands (Optional)") {
                    ForEach(popularBrands, id: \.self) { brand in
                        HStack {
                            Text(brand)
                            Spacer()
                            Toggle("", isOn: Binding(
                                get: { preferredBrands.contains(brand) },
                                set: { isSelected in
                                    if isSelected {
                                        preferredBrands
                                    } else {
                                        preferredBrands.remove(brand)
                                    }
                                }
                            ))
                        }
                    }
                }
            }
            .navigationTitle("Create Nutrition Plan")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createPlan()
                    }
                    .disabled(isCreating || selectedGoals.isEmpty)
                }
            }
        }
    }
    // MARK: - Computed Properties
    private var mealScheduleDescription: String {
        switch mealsPerDay {
        case 1:
            return "Single daily meal (not recommended for most pets)"
        case 2:
            return "Morning and evening meals (recommended for adult pets)"
        case 3:
            return "Morning, afternoon, and evening meals (good for puppies/kittens)"
        case 4:
            return "Four meals throughout the day (ideal for very young pets)"
        default:
            return "Custom feeding schedule"
        }
    }
    private let commonAllergens = [
        "Chicken", "Beef", "Dairy", "Wheat", "Corn", "Soy", "Fish", "Eggs"
    ]
    private let popularBrands = [
        "Royal Canin", "Hill's Science Diet", "Blue Buffalo", "Purina Pro Plan",
        "Wellness", "Orijen", "Acana", "Taste of the Wild"
    ]
    // MARK: - Methods
    private func goalDescription(for goalType: GoalType) -> String {
        switch goalType {
        case .weight:
            return "Maintain optimal body weight"
        case .hydration:
            return "Ensure adequate daily water intake"
        case .activity:
            return "Support energy levels for daily activities"
        case .supplements:
            return "Include beneficial supplements when needed"
        case .treats:
            return "Moderate treat consumption"
        }
    }
    private func createPlan() {
        isCreating = true
        Task {
            let plan = await nutritionService.generateNutritionPlan(for: pet)
            let schedule = nutritionService.createMealSchedule(for: pet, plan: plan)
            await MainActor.run {
                isCreating = false
                dismiss()
            }
        }
    }
}
// MARK: - Supporting Enums
enum ActivityLevel: String, CaseIterable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case veryHigh = "very_high"
    var displayName: String {
        switch self {
        case .low: return "Low Activity"
        case .moderate: return "Moderate Activity"
        case .high: return "High Activity"
        case .veryHigh: return "Very High Activity"
        }
    }
    var description: String {
        switch self {
        case .low: return "Mostly indoor, minimal exercise"
        case .moderate: return "Daily walks, some playtime"
        case .high: return "Regular exercise, active lifestyle"
        case .veryHigh: return "Working dog, intense daily exercise"
        }
    }
    var multiplier: Double {
        switch self {
        case .low: return 0.8
        case .moderate: return 1.0
        case .high: return 1.3
        case .veryHigh: return 1.6
        }
    }
}
enum BudgetRange: String, CaseIterable {
    case budget = "budget"
    case moderate = "moderate"
    case premium = "premium"
    case luxury = "luxury"
    var displayName: String {
        switch self {
        case .budget: return "Budget Friendly"
        case .moderate: return "Moderate"
        case .premium: return "Premium"
        case .luxury: return "Luxury"
        }
    }
    var description: String {
        switch self {
        case .budget: return "$30-50/month"
        case .moderate: return "$50-80/month"
        case .premium: return "$80-120/month"
        case .luxury: return "$120+/month"
        }
    }
    var range: ClosedRange<Double> {
        switch self {
        case .budget: return 30...50
        case .moderate: return 50...80
        case .premium: return 80...120
        case .luxury: return 120...200
        }
    }
}
#Preview {
    NutritionPlanCreationView(pet: Pet.samplePet)
}
