//
//  NutritionPlannerView.swift
//  PetCapsule
//
//  World-Class Nutrition Planning Interface
//  ✅ Modern Design with Glass Morphism & Animations
//

import SwiftUI

struct NutritionPlannerView: View {
    @StateObject private var nutritionService = NutritionPlannerService.shared
    @EnvironmentObject var petService: RealDataService
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @State private var selectedPet: Pet?
    @State private var showingPlanCreation = false
    @State private var showingMealSchedule = false
    @State private var selectedTab = 0
    @State private var animateCards = false
    @State private var showDetails = false
    
    private let tabs = ["Overview", "Meals", "Progress", "Database"]
    
    var body: some View {
        NavigationView {
            ZStack {
                // Animated Background
                animatedBackground
                
                ScrollView {
                    LazyVStack(spacing: 20) {
                        if !petService.pets.isEmpty {
                            // Pet Selector Header
                            petSelectorHeader
                            
                            // Hero Nutrition Card
                            heroNutritionCard
                            
                            // Tab Navigation
                            tabNavigation
                            
                            // Tab Content
                            tabContent
                        } else {
                            emptyStateView
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    if selectedPet != nil {
                        Button("New Plan") {
                            showingPlanCreation = true
                        }
                        .foregroundColor(.blue)
                    }
                }
            }
            .sheet(isPresented: $showingPlanCreation) {
                if let pet = selectedPet {
                    NutritionPlanCreationView(pet: pet)
                }
            }
            .onAppear {
                if selectedPet == nil && !petService.pets.isEmpty {
                    selectedPet = petService.pets.first
                }
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateCards = true
                }
            }
        }
    }
    
    // MARK: - Animated Background
    private var animatedBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    colorScheme == .dark ? Color.black : Color(.systemGroupedBackground),
                    colorScheme == .dark ? Color.gray.opacity(0.1) : Color.blue.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Floating elements
            ForEach(0..<3) { index in
                Circle()
                    .fill(Color.blue.opacity(0.1))
                    .frame(width: 100 + CGFloat(index * 50))
                    .offset(
                        x: CGFloat(index * 100) - 150,
                        y: CGFloat(index * 80) - 200
                    )
                    .blur(radius: 20)
            }
        }
        .ignoresSafeArea()
    }
    
    // MARK: - Pet Selector Header
    private var petSelectorHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Nutrition Planner")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("Personalized meal planning for your pets")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
                
                Image(systemName: "fork.knife.circle.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(.blue)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(petService.pets, id: \.id) { pet in
                        petSelectionCard(pet: pet)
                    }
                }
                .padding(.horizontal, 4)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    private func petSelectionCard(pet: Pet) -> some View {
        Button {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                selectedPet = pet
            }
        } label: {
            VStack(spacing: 8) {
                Circle()
                    .fill(selectedPet?.id == pet.id ? 
                          LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing) :
                          LinearGradient(colors: [.gray.opacity(0.3), .gray.opacity(0.1)], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Text(String(pet.name.prefix(1)).uppercased())
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(selectedPet?.id == pet.id ? .white : .primary)
                    )
                    .scaleEffect(selectedPet?.id == pet.id ? 1.1 : 1.0)
                
                Text(pet.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedPet?.id == pet.id ? .blue : .primary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Hero Nutrition Card
    private var heroNutritionCard: some View {
        VStack(spacing: 20) {
            if let pet = selectedPet {
                // Pet Info & Nutrition Score
                HStack(spacing: 16) {
                    // Pet Avatar
                    Circle()
                        .fill(LinearGradient(colors: [.blue, .cyan], startPoint: .topLeading, endPoint: .bottomTrailing))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Text(String(pet.name.prefix(1)).uppercased())
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(pet.name)
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("\(pet.species ?? "Unknown") • \(pet.breed ?? "Unknown")")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("\(pet.age) years old • \(String(format: "%.1f", pet.weight ?? 0.0)) kg")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // Nutrition Score
                    nutritionScoreIndicator(score: 85)
                }
                
                // Daily Nutrition Summary
                VStack(alignment: .leading, spacing: 12) {
                    Text("Today's Nutrition")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 20) {
                        nutritionMetric(
                            icon: "flame.fill",
                            value: "1,200",
                            unit: "kcal",
                            color: .orange
                        )
                        
                        nutritionMetric(
                            icon: "drop.fill",
                            value: "250",
                            unit: "ml",
                            color: .blue
                        )
                        
                        nutritionMetric(
                            icon: "leaf.fill",
                            value: "45",
                            unit: "g",
                            color: .green
                        )
                    }
                }
            } else {
                Text("Select a pet to view nutrition details")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)
    }
    
    private func nutritionMetric(icon: String, value: String, unit: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
            
            Text(unit)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func nutritionScoreIndicator(score: Int) -> some View {
        VStack(spacing: 4) {
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 4)
                    .frame(width: 50, height: 50)
                
                Circle()
                    .trim(from: 0, to: CGFloat(score) / 100)
                    .stroke(
                        LinearGradient(colors: [.green, .blue], startPoint: .leading, endPoint: .trailing),
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 50, height: 50)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: score)
                
                Text("\(score)")
                    .font(.caption)
                    .fontWeight(.bold)
            }
            
            Text("Score")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Tab Navigation
    private var tabNavigation: some View {
        HStack(spacing: 0) {
            ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                Button {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        selectedTab = index
                    }
                } label: {
                    Text(tab)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(selectedTab == index ? Color.blue : Color.clear)
                        )
                        .foregroundColor(selectedTab == index ? .white : .primary)
                }
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateCards)
    }
    
    // MARK: - Tab Content
    private var tabContent: some View {
        Group {
            switch selectedTab {
            case 0:
                overviewTab
            case 1:
                mealsTab
            case 2:
                progressTab
            case 3:
                databaseTab
            default:
                overviewTab
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateCards)
    }
    
    // MARK: - Overview Tab
    private var overviewTab: some View {
        VStack(spacing: 16) {
            // Quick Actions
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                quickActionCard(
                    icon: "plus.circle.fill",
                    title: "New Meal Plan",
                    subtitle: "Create personalized plan",
                    color: .green,
                    action: { showingPlanCreation = true }
                )
                
                quickActionCard(
                    icon: "clock.fill",
                    title: "Meal Schedule",
                    subtitle: "View daily schedule",
                    color: .blue,
                    action: { showingMealSchedule = true }
                )
            }
            
            // Nutrition Insights
            nutritionInsightsCard
            
            // Recent Meals
            recentMealsCard
        }
    }
    
    private func quickActionCard(icon: String, title: String, subtitle: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var nutritionInsightsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                Text("Nutrition Insights")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                insightRow(
                    icon: "checkmark.circle.fill",
                    text: "Protein intake is optimal for muscle development",
                    color: .green
                )
                
                insightRow(
                    icon: "exclamationmark.triangle.fill",
                    text: "Consider increasing fiber for better digestion",
                    color: .orange
                )
                
                insightRow(
                    icon: "info.circle.fill",
                    text: "Hydration levels are excellent",
                    color: .blue
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func insightRow(icon: String, text: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
    
    private var recentMealsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "clock.fill")
                    .foregroundColor(.blue)
                Text("Recent Meals")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                ForEach(1...3, id: \.self) { index in
                    mealRow(
                        time: index == 1 ? "8:00 AM" : index == 2 ? "12:00 PM" : "6:00 PM",
                        meal: index == 1 ? "Breakfast" : index == 2 ? "Lunch" : "Dinner",
                        food: index == 1 ? "Premium kibble + fresh vegetables" : 
                              index == 2 ? "Wet food + supplements" : "Homemade meal",
                        calories: index == 1 ? "450 kcal" : index == 2 ? "380 kcal" : "520 kcal"
                    )
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func mealRow(time: String, meal: String, food: String, calories: String) -> some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(time)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(meal)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .frame(width: 80, alignment: .leading)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(food)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Text(calories)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
        }
        .padding(.vertical, 4)
    }
    
    // MARK: - Meals Tab
    private var mealsTab: some View {
        VStack(spacing: 16) {
            Text("Meal Schedule")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for meal schedule
            VStack(spacing: 20) {
                Image(systemName: "fork.knife.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.blue)
                
                Text("Meal Schedule Coming Soon")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Text("Detailed meal planning and scheduling features will be available here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Progress Tab
    private var progressTab: some View {
        VStack(spacing: 16) {
            Text("Nutrition Progress")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for progress charts
            VStack(spacing: 20) {
                Image(systemName: "chart.line.uptrend.xyaxis.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.green)
                
                Text("Progress Tracking Coming Soon")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Text("Track your pet's nutrition progress over time with detailed analytics")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Database Tab
    private var databaseTab: some View {
        VStack(spacing: 16) {
            Text("Food Database")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for food database
            VStack(spacing: 20) {
                Image(systemName: "magnifyingglass.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.purple)
                
                Text("Food Database Coming Soon")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Text("Browse and search through a comprehensive database of pet foods and ingredients")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "pawprint.circle")
                .font(.system(size: 64))
                .foregroundColor(.blue)
            
            Text("No Pets Found")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Add a pet to start planning their nutrition")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
}

// MARK: - Tab Button Component
struct TabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isSelected ? Color.blue : Color.clear)
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
    }
}

#Preview {
    NutritionPlannerView()
        .environmentObject(RealDataService())
}
