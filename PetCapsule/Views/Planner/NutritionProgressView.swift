//
//  NutritionProgressView.swift
//  PetCapsule
//
//  Track nutrition progress and analytics
//

import SwiftUI
import Charts

struct NutritionProgressView: View {
    let petId: String
    @StateObject private var nutritionService = NutritionPlannerService.shared
    @State private var selectedTimeframe: Timeframe = .week
    @State private var showingDetailedAnalytics = false
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Progress Overview
                progressOverviewCard
                
                // Streak and Goals
                streakAndGoalsCard
                
                // Weekly Progress Chart
                weeklyProgressChart
                
                // Macro Trends
                macroTrendsCard
                
                // Cost Analysis
                costAnalysisCard
                
                // Detailed Analytics Button
                detailedAnalyticsButton
            }
        }
        .sheet(isPresented: $showingDetailedAnalytics) {
            DetailedAnalyticsView(petId: petId)
        }
    }
    
    // MARK: - Progress Overview Card
    
    private var progressOverviewCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Progress Overview")
                .font(.headline)
                .fontWeight(.semibold)
            
            let progress = nutritionService.getNutritionProgress(for: petId)
            
            HStack(spacing: 20) {
                // Completion Rate
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(Color(.systemGray5), lineWidth: 8)
                            .frame(width: 80, height: 80)
                        
                        Circle()
                            .trim(from: 0, to: progress.completionRate)
                            .stroke(Color.blue, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                            .frame(width: 80, height: 80)
                            .rotationEffect(.degrees(-90))
                        
                        Text("\(Int(progress.completionRate * 100))%")
                            .font(.headline)
                            .fontWeight(.bold)
                    }
                    
                    Text("Completion Rate")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Stats
                VStack(alignment: .leading, spacing: 12) {
                    ProgressStat(
                        title: "Meals Completed",
                        value: "\(progress.completedMeals)/\(progress.totalMeals)",
                        icon: "fork.knife",
                        color: .green
                    )
                    
                    ProgressStat(
                        title: "Current Streak",
                        value: progress.streakText,
                        icon: "flame.fill",
                        color: .orange
                    )
                    
                    ProgressStat(
                        title: "Weekly Goal",
                        value: "\(Int(progress.weeklyGoalProgress * 100))%",
                        icon: "target",
                        color: .blue
                    )
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Streak and Goals Card
    
    private var streakAndGoalsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Goals Progress")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let plan = nutritionService.nutritionPlans.first(where: { $0.petId == petId }) {
                ForEach(plan.goals) { goal in
                    GoalProgressRow(goal: goal)
                }
            } else {
                Text("No goals set")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Weekly Progress Chart
    
    private var weeklyProgressChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Weekly Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Picker("Timeframe", selection: $selectedTimeframe) {
                    ForEach(Timeframe.allCases, id: \.self) { timeframe in
                        Text(timeframe.displayName).tag(timeframe)
                    }
                }
                .pickerStyle(.segmented)
                .frame(width: 200)
            }
            
            // Chart placeholder (would use real Charts framework)
            VStack(spacing: 8) {
                HStack {
                    ForEach(0..<7) { day in
                        VStack(spacing: 4) {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.blue)
                                .frame(width: 30, height: CGFloat.random(in: 20...80))
                            
                            Text(dayAbbreviation(for: day))
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Text("Meal completion rate by day")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(height: 120)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Macro Trends Card
    
    private var macroTrendsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Macro Trends")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 16) {
                MacroTrendView(
                    name: "Protein",
                    percentage: 28,
                    trend: .up,
                    color: .blue
                )
                
                MacroTrendView(
                    name: "Fat",
                    percentage: 16,
                    trend: .stable,
                    color: .orange
                )
                
                MacroTrendView(
                    name: "Carbs",
                    percentage: 45,
                    trend: .down,
                    color: .green
                )
                
                MacroTrendView(
                    name: "Fiber",
                    percentage: 4,
                    trend: .up,
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Cost Analysis Card
    
    private var costAnalysisCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Cost Analysis")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 20) {
                CostMetric(
                    title: "This Week",
                    value: "$24.50",
                    subtitle: "vs $22.30 last week",
                    trend: .up
                )
                
                CostMetric(
                    title: "Monthly Projection",
                    value: "$98.00",
                    subtitle: "Within budget",
                    trend: .stable
                )
                
                CostMetric(
                    title: "Cost per Calorie",
                    value: "$0.12",
                    subtitle: "Industry average",
                    trend: .stable
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Detailed Analytics Button
    
    private var detailedAnalyticsButton: some View {
        Button("View Detailed Analytics") {
            showingDetailedAnalytics = true
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.blue)
        .foregroundColor(.white)
        .cornerRadius(12)
        .fontWeight(.medium)
    }
    
    // MARK: - Helper Methods
    
    private func dayAbbreviation(for index: Int) -> String {
        let days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
        return days[index % days.count]
    }
}

// MARK: - Supporting Views

struct ProgressStat: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 16)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
        }
    }
}

struct GoalProgressRow: View {
    let goal: NutritionGoal
    
    var body: some View {
        HStack {
            Image(systemName: goal.type.icon)
                .foregroundColor(goal.type.color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(goal.type.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                ProgressView(value: goal.progress)
                    .tint(goal.type.color)
            }
            
            Spacer()
            
            Text("\(Int(goal.progress * 100))%")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(goal.type.color)
        }
    }
}

struct MacroTrendView: View {
    let name: String
    let percentage: Int
    let trend: Trend
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(name)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(percentage)%")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            HStack(spacing: 2) {
                Image(systemName: trend.icon)
                    .font(.caption2)
                    .foregroundColor(trend.color)
                
                Text(trend.displayName)
                    .font(.caption2)
                    .foregroundColor(trend.color)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

struct CostMetric: View {
    let title: String
    let value: String
    let subtitle: String
    let trend: Trend
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            HStack(spacing: 4) {
                Image(systemName: trend.icon)
                    .font(.caption2)
                    .foregroundColor(trend.color)
                
                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

// MARK: - Supporting Enums

enum Timeframe: String, CaseIterable {
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    
    var displayName: String {
        switch self {
        case .week: return "Week"
        case .month: return "Month"
        case .quarter: return "Quarter"
        }
    }
}

enum Trend {
    case up
    case down
    case stable
    
    var icon: String {
        switch self {
        case .up: return "arrow.up"
        case .down: return "arrow.down"
        case .stable: return "minus"
        }
    }
    
    var color: Color {
        switch self {
        case .up: return .green
        case .down: return .red
        case .stable: return .gray
        }
    }
    
    var displayName: String {
        switch self {
        case .up: return "Up"
        case .down: return "Down"
        case .stable: return "Stable"
        }
    }
}

// MARK: - Placeholder Views

struct DetailedAnalyticsView: View {
    let petId: String
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Detailed analytics would go here")
                        .font(.headline)
                    
                    Text("This would include comprehensive charts, trends, and insights about your pet's nutrition progress.")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
            }
            .navigationTitle("Detailed Analytics")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    NutritionProgressView(petId: "sample-pet-id")
}
