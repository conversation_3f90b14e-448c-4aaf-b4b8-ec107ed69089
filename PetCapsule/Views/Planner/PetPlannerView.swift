//
//  PetPlannerView.swift
//  PetCapsule
//
//  Smart Walk Planner - Focused walk planning interface
//  ✅ Updated to match main page UX/UI design patterns
//

import SwiftUI
import UserNotifications
import CoreLocation

struct PetPlannerView: View {
    @StateObject private var plannerService = PetPlannerService.shared
    @State private var animateCards = false
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        if #available(iOS 18.0, *) {
            EnhancedWalkPlannerView()
                .environmentObject(plannerService)
                .onAppear {
                    withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                        animateCards = true
                    }
                    plannerService.refreshAllData()
                }
                .refreshable {
                    plannerService.refreshAllData()
                }
        } else {
            NavigationView {
                SmartWalkPlannerView()
                    .environmentObject(plannerService)
                    .background(colorScheme == .dark ? Color.black : Color(.systemGroupedBackground))
                    .navigationTitle("Perfect Adventures")
                    .navigationBarTitleDisplayMode(.inline)
                    .onAppear {
                        withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                            animateCards = true
                        }
                        plannerService.refreshAllData()
                    }
                    .refreshable {
                        plannerService.refreshAllData()
                    }
            }
        }
    }
}

// MARK: - Smart Walk Planner View (Updated Design)
struct SmartWalkPlannerView: View {
    @EnvironmentObject var plannerService: PetPlannerService
    @State private var showDetails = false
    @State private var showWalkTracker = false
    @State private var notificationsEnabled = false
    @State private var animateCards = false
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Clean Header
                cleanHeader
                
                // Hero Recommendation Card
                heroRecommendationCard
                
                // Primary Actions Grid
                primaryActionsGrid
                
                // Quick Stats Section
                quickStatsSection
                
                // Notification Settings
                notificationSettingsCard
                
                // Detailed Weather (Expandable)
                if showDetails {
                    detailedWeatherSection
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 100)
        }
        .sheet(isPresented: $showWalkTracker) {
            WalkTrackerView { walkData in
                handleCompletedWalk(walkData)
            }
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                animateCards = true
            }
            checkNotificationPermissions()
        }
    }

    // MARK: - Clean Header (Following Main Page Pattern)
    
    private var cleanHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("AI-powered walk recommendations based on weather & environment")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
                
                Image(systemName: "figure.walk.circle.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(.blue)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    // MARK: - Hero Recommendation Card (Updated Design)
    
    private var heroRecommendationCard: some View {
        VStack(spacing: 20) {
            // Weather Visual Section
            HStack(spacing: 16) {
                // Weather Icon
                if #available(iOS 18.0, *) {
                    Image(systemName: weatherIcon)
                        .font(.system(size: 48, weight: .medium))
                        .foregroundStyle(weatherIconColor)
                        .symbolEffect(.bounce, options: .repeating)
                } else {
                    Image(systemName: weatherIcon)
                        .font(.system(size: 48, weight: .medium))
                        .foregroundStyle(weatherIconColor)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(plannerService.currentWeather.temperature)°")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                    
                    Text(plannerService.currentWeather.condition)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                walkScoreIndicator(score: Int(plannerService.walkRecommendation.score * 10))
            }
            
            // Walk Recommendation
            VStack(alignment: .leading, spacing: 12) {
                Text(walkRecommendationTitle)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(walkRecommendationDescription)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(walkRecommendationBorderColor.opacity(0.3), lineWidth: 1)
                )
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }
    
    // MARK: - Primary Actions Grid (Following Main Page Pattern)
    
    private var primaryActionsGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 12),
            GridItem(.flexible(), spacing: 12)
        ], spacing: 12) {
            // Start Walk Button (Spans full width)
            walkActionCard(
                icon: "figure.walk",
                title: "Start Walk Now",
                subtitle: "Begin tracking your walk",
                color: .green,
                isFullWidth: true,
                action: startWalk
            )
            
            detailsActionCard(
                icon: showDetails ? "eye.slash.fill" : "eye.fill",
                title: showDetails ? "Hide Details" : "Show Details",
                subtitle: "Weather & environment info",
                color: .blue,
                action: { withAnimation(.spring()) { showDetails.toggle() } }
            )
            
            bestTimesActionCard(
                icon: "clock.fill",
                title: "Best Times",
                subtitle: "Optimal walk schedule",
                color: .orange,
                action: { /* Show best times */ }
            )
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)
    }
    
    // MARK: - Quick Stats Section (Updated Grid Design)
    
    private var quickStatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Current Conditions")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12),
                GridItem(.flexible(), spacing: 12)
            ], spacing: 12) {
                statCard(
                    icon: "thermometer",
                    title: "Temperature",
                    value: "\(plannerService.currentWeather.temperature)°",
                    color: .orange
                )
                
                statCard(
                    icon: "humidity",
                    title: "Humidity",
                    value: "\(plannerService.currentWeather.humidity)%",
                    color: .blue
                )
                
                statCard(
                    icon: "wind",
                    title: "Air Quality",
                    value: "\(plannerService.airQuality.index)",
                    color: .green
                )
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateCards)
    }
    
    // MARK: - Notification Settings Card (Updated Design)
    
    private var notificationSettingsCard: some View {
        HStack(spacing: 16) {
            Image(systemName: "bell.fill")
                .font(.title2)
                .foregroundColor(.orange)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Smart Walk Alerts")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("Get notified about optimal walk times")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $notificationsEnabled)
                .toggleStyle(SwitchToggleStyle(tint: .orange))
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateCards)
    }
    
    // MARK: - Helper Components (Updated)
    
    private func walkActionCard(icon: String, title: String, subtitle: String, color: Color, isFullWidth: Bool = false, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.black)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.black.opacity(0.8))
                }
                
                Spacer()
            }
            .padding(16)
            .background(
                LinearGradient(
                    colors: [color, color.opacity(0.8)],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
        }
        .buttonStyle(PlainButtonStyle())
        .shadow(color: color.opacity(0.3), radius: 8, x: 0, y: 4)
        .gridCellColumns(isFullWidth ? 2 : 1)
    }
    
    private func detailsActionCard(icon: String, title: String, subtitle: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(height: 24)
                
                VStack(spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16, style: .continuous)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16, style: .continuous)
                            .stroke(.quaternary, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func bestTimesActionCard(icon: String, title: String, subtitle: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(height: 24)
                
                VStack(spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16, style: .continuous)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16, style: .continuous)
                            .stroke(.quaternary, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func statCard(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(height: 20)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
    }
    
    private func walkScoreIndicator(score: Int) -> some View {
        VStack(spacing: 4) {
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 3)
                    .frame(width: 50, height: 50)
                
                Circle()
                    .trim(from: 0, to: CGFloat(score) / 100)
                    .stroke(walkScoreColor(score), lineWidth: 3)
                    .frame(width: 50, height: 50)
                    .rotationEffect(.degrees(-90))
                
                Text("\(score)")
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(walkScoreColor(score))
            }
            
            Text("Walk Score")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Computed Properties
    
    private var weatherIcon: String {
        switch plannerService.currentWeather.condition.lowercased() {
        case let condition where condition.contains("sunny") || condition.contains("clear"):
            return "sun.max.fill"
        case let condition where condition.contains("cloud"):
            return "cloud.fill"
        case let condition where condition.contains("rain"):
            return "cloud.rain.fill"
        case let condition where condition.contains("snow"):
            return "cloud.snow.fill"
        default:
            return "cloud.sun.fill"
        }
    }
    
    private var weatherIconColor: Color {
        switch plannerService.currentWeather.condition.lowercased() {
        case let condition where condition.contains("sunny") || condition.contains("clear"):
            return .orange
        case let condition where condition.contains("cloud"):
            return .gray
        case let condition where condition.contains("rain"):
            return .blue
        case let condition where condition.contains("snow"):
            return .cyan
        default:
            return .yellow
        }
    }
    
    private var walkRecommendationTitle: String {
        let score = Int(plannerService.walkRecommendation.score * 10)
        
        if score >= 80 {
            return "Perfect for walking! 🐾"
        } else if score >= 60 {
            return "Good walking conditions"
        } else if score >= 40 {
            return "Okay for a short walk"
        } else {
            return "Consider indoor activities"
        }
    }
    
    private var walkRecommendationDescription: String {
        return plannerService.walkRecommendation.reasoning
    }
    
    private var walkRecommendationBorderColor: Color {
        return walkScoreColor(Int(plannerService.walkRecommendation.score * 10))
    }
    
    private func walkScoreColor(_ score: Int) -> Color {
        if score >= 80 {
            return .green
        } else if score >= 60 {
            return .yellow
        } else if score >= 40 {
            return .orange
        } else {
            return .red
        }
    }
    
    // MARK: - Actions
    
    private func startWalk() {
        showWalkTracker = true
    }
    
    private func checkNotificationPermissions() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                notificationsEnabled = settings.authorizationStatus == .authorized
            }
        }
    }
    
    private func handleCompletedWalk(_ walkData: Any) {
        // Handle walk completion
        plannerService.refreshAllData()
    }
    
    // MARK: - Detailed Weather Sections
    
    private var detailedWeatherSection: some View {
        VStack(spacing: 20) {
            // Environmental Alerts
            environmentalAlertsSection
            
            // Hourly Forecast
            hourlyForecastSection
            
            // Weekly Forecast
            weeklyForecastSection
        }
    }
    
    private var environmentalAlertsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.orange)

                Text("Environmental Alerts")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Spacer()

                if plannerService.environmentalAlerts.isEmpty {
                    HStack(spacing: 4) {
                        Circle()
                            .fill(.green)
                            .frame(width: 8, height: 8)
                        Text("All Clear")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }
            }

            if plannerService.environmentalAlerts.isEmpty {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("No environmental concerns for your pet today!")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 8)
            } else {
                VStack(spacing: 12) {
                    ForEach(plannerService.environmentalAlerts, id: \.id) { alert in
                        alertCard(alert)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
    }
    
    private var hourlyForecastSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Today's Forecast")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(plannerService.hourlyForecast, id: \.hour) { forecast in
                        hourlyForecastItem(forecast)
                    }
                }
                .padding(.horizontal, 4)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
    }
    
    private var weeklyForecastSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("7-Day Forecast")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                ForEach(plannerService.weeklyForecast, id: \.day) { forecast in
                    weeklyForecastItem(forecast)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(.quaternary, lineWidth: 1)
                )
        )
    }
    
    private func hourlyForecastItem(_ forecast: HourlyForecast) -> some View {
        VStack(spacing: 8) {
            Text(forecast.hour)
                .font(.caption)
                .foregroundColor(.secondary)

            Image(systemName: forecast.icon)
                .font(.system(size: 20))
                .foregroundColor(forecast.color)

            Text("\(forecast.temperature)°")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)

            Circle()
                .fill(forecast.walkQuality.color)
                .frame(width: 8, height: 8)
        }
        .padding(.vertical, 8)
        .frame(width: 60)
    }

    private func weeklyForecastItem(_ forecast: WeeklyForecast) -> some View {
        HStack {
            Text(forecast.day)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary)
                .frame(width: 60, alignment: .leading)

            Image(systemName: forecast.icon)
                .font(.system(size: 16))
                .foregroundColor(forecast.color)
                .frame(width: 30)

            Spacer()

            HStack(spacing: 4) {
                Circle()
                    .fill(forecast.walkQuality.color)
                    .frame(width: 8, height: 8)

                Text(forecast.walkQuality.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Text("\(forecast.highTemp)°/\(forecast.lowTemp)°")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.primary)
                .frame(width: 60, alignment: .trailing)
        }
        .padding(.vertical, 4)
    }
    
    private func alertCard(_ alert: EnvironmentalAlertUI) -> some View {
        HStack(spacing: 12) {
            Image(systemName: alert.icon)
                .font(.title2)
                .foregroundColor(alert.severity.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(alert.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(alert.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(alert.severity.color.opacity(0.1))
        )
    }
}

// MARK: - Supporting Types

// WalkData for walk tracking functionality
struct WalkData {
    let id = UUID()
    let startTime: Date
    let endTime: Date
    let durationMinutes: Int
    let distanceMeters: Double
    let startLocation: CLLocationCoordinate2D
    let endLocation: CLLocationCoordinate2D
    let routeCoordinates: [CLLocationCoordinate2D]
    let averageSpeed: Double
    let maxSpeed: Double
    let elevationGain: Double
    
    init(startTime: Date, endTime: Date, durationMinutes: Int, distanceMeters: Double, startLocation: CLLocationCoordinate2D, endLocation: CLLocationCoordinate2D, routeCoordinates: [CLLocationCoordinate2D] = [], averageSpeed: Double = 0, maxSpeed: Double = 0, elevationGain: Double = 0) {
        self.startTime = startTime
        self.endTime = endTime
        self.durationMinutes = durationMinutes
        self.distanceMeters = distanceMeters
        self.startLocation = startLocation
        self.endLocation = endLocation
        self.routeCoordinates = routeCoordinates
        self.averageSpeed = averageSpeed
        self.maxSpeed = maxSpeed
        self.elevationGain = elevationGain
    }
}

// OptimalWalkTime for notification scheduling
struct OptimalWalkTime {
    let notificationTime: Date
    let walkTime: Date
    let score: Double
    let weather: HourlyForecast?
    let reason: String
}

#Preview {
    PetPlannerView()
}


