//
//  RouteOptionsSheet.swift
//  PetCapsule
//
//  Route planning options for enhanced MapKit integration
//

import SwiftUI
import MapKit
import CoreLocation

struct RouteOptionsSheet: View {
    let destination: PetFriendlyLocation
    let onRouteSelected: (WalkRoute) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @StateObject private var mapService = AppleMapService.shared
    @StateObject private var locationManager = LocationManager()
    
    @State private var selectedPreference: WalkingPreference = .scenic
    @State private var calculatedRoutes: [WalkRoute] = []
    @State private var isCalculatingRoutes = false
    @State private var selectedRoute: WalkRoute?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Route Preferences
                preferencesSection
                
                // Route Options
                if isCalculatingRoutes {
                    loadingSection
                } else if calculatedRoutes.isEmpty {
                    emptyStateSection
                } else {
                    routesList
                }
                
                Spacer()
                
                // Action Button
                if let route = selectedRoute {
                    actionButton(for: route)
                }
            }
            .navigationTitle("Route Options")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            calculateRoutes()
        }
        .onChange(of: selectedPreference) { _, _ in
            calculateRoutes()
        }
    }
    
    // MARK: - UI Components
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: destination.type.icon)
                    .font(.title2)
                    .foregroundColor(categoryColor)
                    .padding(12)
                    .background(categoryColor.opacity(0.1))
                    .cornerRadius(10)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(destination.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(destination.address)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
            }
            
            if destination.distance > 0 {
                HStack {
                    Image(systemName: "figure.walk")
                        .foregroundColor(.secondary)
                    Text(formatDistance(destination.distance))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
    
    private var preferencesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Walking Preference")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 12) {
                ForEach([WalkingPreference.fastest, .scenic, .safest], id: \.self) { preference in
                    preferenceButton(preference)
                }
            }
        }
        .padding()
    }
    
    private func preferenceButton(_ preference: WalkingPreference) -> some View {
        Button(action: {
            selectedPreference = preference
        }) {
            VStack(spacing: 8) {
                Image(systemName: preferenceIcon(preference))
                    .font(.title2)
                    .foregroundColor(selectedPreference == preference ? .white : .primary)
                
                Text(preferenceTitle(preference))
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedPreference == preference ? .white : .primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(selectedPreference == preference ? .blue : Color(.systemGray5))
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var loadingSection: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Calculating routes...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "location.slash")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("Unable to calculate routes")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("Please check your location settings and try again.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Retry") {
                calculateRoutes()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    private var routesList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(Array(calculatedRoutes.enumerated()), id: \.offset) { index, route in
                    RouteCard(
                        route: route,
                        routeNumber: index + 1,
                        isSelected: selectedRoute?.id == route.id,
                        onSelect: {
                            selectedRoute = route
                        }
                    )
                }
            }
            .padding()
        }
    }
    
    private func actionButton(for route: WalkRoute) -> some View {
        Button(action: {
            onRouteSelected(route)
            dismiss()
        }) {
            HStack {
                Image(systemName: "arrow.triangle.turn.up.right.diamond")
                Text("Start Walking")
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .padding()
    }
    
    // MARK: - Helper Methods
    
    private var categoryColor: Color {
        switch destination.type {
        case .park: return .green
        case .veterinary: return .red
        case .store: return .orange
        case .restaurant: return .purple
        case .trail: return .brown
        case .beach: return .cyan
        case .hotel: return .purple
        }
    }
    
    private func preferenceIcon(_ preference: WalkingPreference) -> String {
        switch preference {
        case .fastest:
            return "timer"
        case .scenic:
            return "camera"
        case .safest:
            return "shield"
        }
    }
    
    private func preferenceTitle(_ preference: WalkingPreference) -> String {
        switch preference {
        case .fastest:
            return "Fastest"
        case .scenic:
            return "Scenic"
        case .safest:
            return "Safest"
        }
    }
    
    private func formatDistance(_ distance: Double) -> String {
        let formatter = MKDistanceFormatter()
        formatter.unitStyle = .abbreviated
        return formatter.string(fromDistance: distance)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration) ?? ""
    }
    
    private func calculateRoutes() {
        guard let userLocation = locationManager.currentLocation else {
            print("❌ User location not available")
            return
        }
        
        isCalculatingRoutes = true
        calculatedRoutes = []
        selectedRoute = nil
        
        Task {
            do {
                // For now, use a default coordinate since PetFriendlyLocation doesn't have coordinate
                let destinationCoordinate = CLLocationCoordinate2D(latitude: 40.7829, longitude: -73.9654)
                let route = try await mapService.planWalkRoute(
                    from: userLocation.coordinate,
                    to: destinationCoordinate,
                    preference: selectedPreference
                )
                
                await MainActor.run {
                    self.calculatedRoutes = [route]
                    self.selectedRoute = route
                    self.isCalculatingRoutes = false
                }
            } catch {
                print("❌ Failed to calculate route: \(error)")
                await MainActor.run {
                    self.isCalculatingRoutes = false
                }
            }
        }
    }
}

// MARK: - Route Card

struct RouteCard: View {
    let route: WalkRoute
    let routeNumber: Int
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    Text("Route \(routeNumber)")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.blue)
                            .font(.title3)
                    }
                }
                
                // Route Details
                HStack(spacing: 20) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Distance")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(formatDistance(route.distance))
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Time")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(formatDuration(route.estimatedTime))
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Difficulty")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(route.difficulty.rawValue)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(route.difficulty.color)
                    }
                    
                    Spacer()
                }
                
                // Scores
                HStack(spacing: 16) {
                    ScoreIndicator(title: "Scenic", score: route.scenicScore, color: .green)
                    ScoreIndicator(title: "Safety", score: route.safetyScore, color: .blue)
                }
            }
            .padding()
            .background(isSelected ? .blue.opacity(0.1) : Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? .blue : .clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatDistance(_ distance: Double) -> String {
        let formatter = MKDistanceFormatter()
        formatter.unitStyle = .abbreviated
        return formatter.string(fromDistance: distance)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration) ?? ""
    }
}

struct ScoreIndicator: View {
    let title: String
    let score: Double
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(spacing: 4) {
                ForEach(0..<5) { index in
                    Circle()
                        .fill(index < Int(score * 5) ? color : Color(.systemGray4))
                        .frame(width: 6, height: 6)
                }
            }
        }
    }
}

// MARK: - Extensions

extension WalkRoute {
    var id: String {
        return "\(distance)-\(estimatedTime)-\(difficulty.rawValue)"
    }
}

#Preview {
    RouteOptionsSheet(
        destination: PetFriendlyLocation(
            name: "Central Dog Park",
            type: .park,
            rating: 4.5,
            distance: 1200,
            address: "123 Park Avenue, New York, NY",
            imageURL: "https://example.com/park.jpg",
            amenities: ["Off-leash area", "Water fountains", "Waste bags"],
            isOpen: true,
            coordinate: CLLocationCoordinate2D(latitude: 40.7829, longitude: -73.9654),
            phoneNumber: "(*************",
            website: "https://www.centralparknyc.org",
            hours: "6:00 AM - 1:00 AM"
        ),
        onRouteSelected: { _ in }
    )
}
