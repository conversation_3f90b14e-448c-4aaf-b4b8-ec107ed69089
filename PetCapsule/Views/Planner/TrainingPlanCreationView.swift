//
//  TrainingPlanCreationView.swift
//  PetCapsule
//
//  Create personalized training plans for pets
//
import SwiftUI
struct TrainingPlanCreationView: View {
    let pet: Pet
    @Environment(\.dismiss) private var dismiss
    @StateObject private var trainingService = TrainingPlannerService.shared
    @State private var selectedGoals: Set<TrainingGoal> = [.basicObedience]
    @State private var experienceLevel: ExperienceLevel = .beginner
    @State private var availableTimePerDay = 15 // minutes
    @State private var preferredTime: TimeOfDay = .morning
    @State private var selectedDays: Set<Weekday> = [.monday, .wednesday, .friday]
    @State private var isCreating = false
    @State private var currentStep = 0
    private let totalSteps = 4
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Header
                progressHeader
                // Content
                TabView(selection: $currentStep) {
                    goalsSelectionStep.tag(0)
                    experienceLevelStep.tag(1)
                    scheduleStep.tag(2)
                    reviewStep.tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                // Navigation Buttons
                navigationButtons
            }
            .navigationTitle("Create Training Plan")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    private var progressHeader: some View {
        VStack(spacing: 12) {
            HStack {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 50, height: 50)
                    .overlay(
                        Text(String(pet.name.prefix(1)))
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    )
                VStack(alignment: .leading) {
                    Text("Training Plan for \(pet.name)")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Text("Step \(currentStep + 1) of \(totalSteps)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
            ProgressView(value: Double(currentStep + 1) / Double(totalSteps))
                .tint(.blue)
        }
        .padding()
        .background(Color(.systemGray6))
    }
    private var goalsSelectionStep: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Training Goals")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("What would you like \(pet.name) to learn? Select all that apply.")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                    ForEach(TrainingGoal.allCases, id: \.self) { goal in
                        GoalSelectionCard(
                            goal: goal,
                            isSelected: selectedGoals.contains(goal)
                        ) {
                            if selectedGoals.contains(goal) {
                                selectedGoals.remove(goal)
                            } else {
                                selectedGoals
                            }
                        }
                    }
                }
            }
            .padding()
        }
    }
    private var experienceLevelStep: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Experience Level")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("What's your experience with pet training?")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                VStack(spacing: 12) {
                    ForEach(ExperienceLevel.allCases, id: \.self) { level in
                        ExperienceLevelCard(
                            level: level,
                            isSelected: experienceLevel == level
                        ) {
                            experienceLevel = level
                        }
                    }
                }
            }
            .padding()
        }
    }
    private var scheduleStep: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Training Schedule")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("When can you dedicate time to training \(pet.name)?")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                VStack(alignment: .leading, spacing: 20) {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Minutes per session")
                            .font(.headline)
                            .fontWeight(.semibold)
                        HStack {
                            Slider(value: Binding(
                                get: { Double(availableTimePerDay) },
                                set: { availableTimePerDay = Int($0) }
                            ), in: 5...60, step: 5)
                            Text("\(availableTimePerDay) min")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .frame(width: 60)
                        }
                    }
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Preferred time of day")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Picker("Time of Day", selection: $preferredTime) {
                            ForEach(TimeOfDay.allCases, id: \.self) { time in
                                Text(time.displayName).tag(time)
                            }
                        }
                        .pickerStyle(.segmented)
                    }
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Training days")
                            .font(.headline)
                            .fontWeight(.semibold)
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                            ForEach(Weekday.allCases, id: \.self) { day in
                                DaySelectionButton(
                                    day: day,
                                    isSelected: selectedDays.contains(day)
                                ) {
                                    if selectedDays.contains(day) {
                                        selectedDays.remove(day)
                                    } else {
                                        selectedDays
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .padding()
        }
    }
    private var reviewStep: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Review Plan")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("Review your training plan details before creating.")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                VStack(spacing: 16) {
                    ReviewSectionCard(title: "Goals", content: selectedGoalsText)
                    ReviewSectionCard(title: "Experience Level", content: experienceLevel.displayName)
                    ReviewSectionCard(title: "Schedule", content: scheduleText)
                }
            }
            .padding()
        }
    }
    private var navigationButtons: some View {
        HStack {
            if currentStep > 0 {
                Button("Previous") {
                    withAnimation {
                        currentStep -= 1
                    }
                }
                .buttonStyle(.bordered)
            }
            Spacer()
            if currentStep < totalSteps - 1 {
                Button("Next") {
                    withAnimation {
                        currentStep += 1
                    }
                }
                .buttonStyle(.borderedProminent)
                .disabled(!canProceedToNextStep)
            } else {
                Button(isCreating ? "Creating..." : "Create Plan") {
                    createPlan()
                }
                .buttonStyle(.borderedProminent)
                .disabled(isCreating || selectedGoals.isEmpty)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    private var canProceedToNextStep: Bool {
        switch currentStep {
        case 0: return !selectedGoals.isEmpty
        case 2: return !selectedDays.isEmpty
        default: return true
        }
    }
    private var selectedGoalsText: String {
        Array(selectedGoals).map { $0.displayName }.joined(separator: ", ")
    }
    private var scheduleText: String {
        let daysText = Array(selectedDays).map { $0.displayName }.joined(separator: ", ")
        return "\(availableTimePerDay) min sessions, \(selectedDays.count)x/week (\(daysText)), \(preferredTime.displayName)"
    }
    private func createPlan() {
        isCreating = true
        Task {
            let plan = await trainingService.generateTrainingPlan(
                for: pet,
                goals: selectedGoals,
                experience: experienceLevel
            )
            await MainActor.run {
                isCreating = false
                dismiss()
            }
        }
    }
}
struct GoalSelectionCard: View {
    let goal: TrainingGoal
    let isSelected: Bool
    let action: () -> Void
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: goal.icon)
                        .foregroundColor(goal.color)
                        .font(.title2)
                    Spacer()
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.blue)
                    }
                }
                VStack(alignment: .leading, spacing: 4) {
                    Text(goal.displayName)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    Text(goal.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                Spacer()
            }
            .padding()
            .frame(height: 120)
            .background(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
struct ExperienceLevelCard: View {
    let level: ExperienceLevel
    let isSelected: Bool
    let action: () -> Void
    var body: some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(level.displayName)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    Text(level.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title3)
                }
            }
            .padding()
            .background(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
struct DaySelectionButton: View {
    let day: Weekday
    let isSelected: Bool
    let action: () -> Void
    var body: some View {
        Button(action: action) {
            Text(String(day.displayName.prefix(1)))
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(isSelected ? .white : .primary)
                .frame(width: 40, height: 40)
                .background(isSelected ? Color.blue : Color(.systemGray6))
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
struct ReviewSectionCard: View {
    let title: String
    let content: String
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
            Text(content)
                .font(.subheadline)
                .foregroundColor(.primary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}
#Preview {
    TrainingPlanCreationView(pet: Pet(
        id: "1",
        name: "Buddy",
        species: "Dog",
        breed: "Golden Retriever",
        age: 2,
        weight: 30.0,
        profileImageURL: nil,
        dateOfBirth: Date(),
        sex: "Male"
    ))
}
