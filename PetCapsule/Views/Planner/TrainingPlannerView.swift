//
//  TrainingPlannerView.swift
//  PetCapsule
//
//  World-Class Training Planning Interface
//  ✅ Modern Design with Glass Morphism & Animations
//

import SwiftUI

struct TrainingPlannerView: View {
    @StateObject private var trainingService = TrainingPlannerService.shared
    @EnvironmentObject var petService: RealDataService
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @State private var selectedPet: Pet?
    @State private var showingPlanCreation = false
    @State private var showingSessionDetail = false
    @State private var selectedTab = 0
    @State private var animateCards = false
    @State private var showDetails = false
    
    private let tabs = ["Overview", "Sessions", "Programs", "Progress"]
    
    var body: some View {
        NavigationView {
            ZStack {
                // Animated Background
                animatedBackground
                
                ScrollView {
                    LazyVStack(spacing: 20) {
                        if !petService.pets.isEmpty {
                            // Pet Selector Header
                            petSelectorHeader
                            
                            // Hero Training Card
                            heroTrainingCard
                            
                            // Tab Navigation
                            tabNavigation
                            
                            // Tab Content
                            tabContent
                        } else {
                            emptyStateView
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    if selectedPet != nil {
                        Button("New Plan") {
                            showingPlanCreation = true
                        }
                        .foregroundColor(.blue)
                    }
                }
            }
            .sheet(isPresented: $showingPlanCreation) {
                if let pet = selectedPet {
                    TrainingPlanCreationView(pet: pet)
                }
            }
            .onAppear {
                if selectedPet == nil && !petService.pets.isEmpty {
                    selectedPet = petService.pets.first
                }
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateCards = true
                }
            }
        }
    }
    
    // MARK: - Animated Background
    private var animatedBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    colorScheme == .dark ? Color.black : Color(.systemGroupedBackground),
                    colorScheme == .dark ? Color.gray.opacity(0.1) : Color.green.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Floating elements
            ForEach(0..<3) { index in
                Circle()
                    .fill(Color.green.opacity(0.1))
                    .frame(width: 100 + CGFloat(index * 50))
                    .offset(
                        x: CGFloat(index * 100) - 150,
                        y: CGFloat(index * 80) - 200
                    )
                    .blur(radius: 20)
            }
        }
        .ignoresSafeArea()
    }
    
    // MARK: - Pet Selector Header
    private var petSelectorHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Training Planner")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("Smart training programs for your pets")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
                
                Image(systemName: "brain.head.profile.circle.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(.green)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(petService.pets, id: \.id) { pet in
                        petSelectionCard(pet: pet)
                    }
                }
                .padding(.horizontal, 4)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    private func petSelectionCard(pet: Pet) -> some View {
        Button {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                selectedPet = pet
            }
        } label: {
            VStack(spacing: 8) {
                Circle()
                    .fill(selectedPet?.id == pet.id ? 
                          LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing) :
                          LinearGradient(colors: [.gray.opacity(0.3), .gray.opacity(0.1)], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Text(String(pet.name.prefix(1)).uppercased())
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(selectedPet?.id == pet.id ? .white : .primary)
                    )
                    .scaleEffect(selectedPet?.id == pet.id ? 1.1 : 1.0)
                
                Text(pet.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedPet?.id == pet.id ? .green : .primary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Hero Training Card
    private var heroTrainingCard: some View {
        VStack(spacing: 20) {
            if let pet = selectedPet {
                // Pet Info & Training Score
                HStack(spacing: 16) {
                    // Pet Avatar
                    Circle()
                        .fill(LinearGradient(colors: [.green, .mint], startPoint: .topLeading, endPoint: .bottomTrailing))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Text(String(pet.name.prefix(1)).uppercased())
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(pet.name)
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("\(pet.species ?? "Unknown") • \(pet.breed ?? "Unknown")")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("\(pet.age) years old • \(String(format: "%.1f", pet.weight ?? 0.0)) kg")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // Training Score
                    trainingScoreIndicator(score: 78)
                }
                
                // Training Progress Summary
                VStack(alignment: .leading, spacing: 12) {
                    Text("Training Progress")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 20) {
                        trainingMetric(
                            icon: "target",
                            value: "12",
                            unit: "Skills",
                            color: .blue
                        )
                        
                        trainingMetric(
                            icon: "clock.fill",
                            value: "45",
                            unit: "min",
                            color: .orange
                        )
                        
                        trainingMetric(
                            icon: "star.fill",
                            value: "8.5",
                            unit: "Rating",
                            color: .yellow
                        )
                    }
                }
            } else {
                Text("Select a pet to view training details")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)
    }
    
    private func trainingMetric(icon: String, value: String, unit: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
            
            Text(unit)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func trainingScoreIndicator(score: Int) -> some View {
        VStack(spacing: 4) {
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 4)
                    .frame(width: 50, height: 50)
                
                Circle()
                    .trim(from: 0, to: CGFloat(score) / 100)
                    .stroke(
                        LinearGradient(colors: [.green, .mint], startPoint: .leading, endPoint: .trailing),
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 50, height: 50)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: score)
                
                Text("\(score)")
                    .font(.caption)
                    .fontWeight(.bold)
            }
            
            Text("Score")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Tab Navigation
    private var tabNavigation: some View {
        HStack(spacing: 0) {
            ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                Button {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        selectedTab = index
                    }
                } label: {
                    Text(tab)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(selectedTab == index ? Color.green : Color.clear)
                        )
                        .foregroundColor(selectedTab == index ? .white : .primary)
                }
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateCards)
    }
    
    // MARK: - Tab Content
    private var tabContent: some View {
        Group {
            switch selectedTab {
            case 0:
                overviewTab
            case 1:
                sessionsTab
            case 2:
                programsTab
            case 3:
                progressTab
            default:
                overviewTab
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateCards)
    }
    
    // MARK: - Overview Tab
    private var overviewTab: some View {
        VStack(spacing: 16) {
            // Quick Actions
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                quickActionCard(
                    icon: "plus.circle.fill",
                    title: "New Training Plan",
                    subtitle: "Create personalized program",
                    color: .green,
                    action: { showingPlanCreation = true }
                )
                
                quickActionCard(
                    icon: "play.circle.fill",
                    title: "Start Session",
                    subtitle: "Begin training now",
                    color: .blue,
                    action: { showingSessionDetail = true }
                )
            }
            
            // Training Insights
            trainingInsightsCard
            
            // Recent Sessions
            recentSessionsCard
        }
    }
    
    private func quickActionCard(icon: String, title: String, subtitle: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var trainingInsightsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                Text("Training Insights")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                insightRow(
                    icon: "checkmark.circle.fill",
                    text: "Basic commands mastered - ready for advanced training",
                    color: .green
                )
                
                insightRow(
                    icon: "exclamationmark.triangle.fill",
                    text: "Focus on recall training for better off-leash control",
                    color: .orange
                )
                
                insightRow(
                    icon: "info.circle.fill",
                    text: "Consistent training schedule improving behavior",
                    color: .blue
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func insightRow(icon: String, text: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
    
    private var recentSessionsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "clock.fill")
                    .foregroundColor(.green)
                Text("Recent Sessions")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                ForEach(1...3, id: \.self) { index in
                    sessionRow(
                        date: index == 1 ? "Today" : index == 2 ? "Yesterday" : "2 days ago",
                        skill: index == 1 ? "Sit & Stay" : index == 2 ? "Recall Training" : "Leash Walking",
                        duration: index == 1 ? "30 min" : index == 2 ? "45 min" : "25 min",
                        rating: index == 1 ? "9/10" : index == 2 ? "8/10" : "7/10"
                    )
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func sessionRow(date: String, skill: String, duration: String, rating: String) -> some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(date)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(skill)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .frame(width: 100, alignment: .leading)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(duration)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Text("Duration")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(rating)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.green)
                
                Text("Rating")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
    
    // MARK: - Sessions Tab
    private var sessionsTab: some View {
        VStack(spacing: 16) {
            Text("Training Sessions")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for training sessions
            VStack(spacing: 20) {
                Image(systemName: "brain.head.profile.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.green)
                
                Text("Training Sessions Coming Soon")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Text("Track and manage individual training sessions with detailed progress")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Programs Tab
    private var programsTab: some View {
        VStack(spacing: 16) {
            Text("Training Programs")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for training programs
            VStack(spacing: 20) {
                Image(systemName: "list.bullet.clipboard.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.blue)
                
                Text("Training Programs Coming Soon")
                .font(.title3)
                .fontWeight(.medium)
                
                Text("Access structured training programs designed for different skill levels")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Progress Tab
    private var progressTab: some View {
        VStack(spacing: 16) {
            Text("Training Progress")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for progress charts
            VStack(spacing: 20) {
                Image(systemName: "chart.line.uptrend.xyaxis.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.green)
                
                Text("Progress Tracking Coming Soon")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Text("Monitor your pet's training progress with detailed analytics and milestones")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "pawprint.circle")
                .font(.system(size: 64))
                .foregroundColor(.green)
            
            Text("No Pets Found")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Add a pet to start their training journey")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
}

#Preview {
    TrainingPlannerView()
        .environmentObject(RealDataService())
}
