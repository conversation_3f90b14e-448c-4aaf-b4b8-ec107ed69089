//
//  UnifiedPlannerDashboard.swift
//  PetCapsule
//
//  Unified planner dashboard that integrates all planner features seamlessly
//

import SwiftUI

struct UnifiedPlannerDashboard: View {
    @StateObject private var plannerService = UnifiedPlannerService.shared
    @EnvironmentObject var petService: RealDataService
    @State private var selectedPet: Pet?
    @State private var showingEventDetails = false
    @State private var selectedEvent: PlannerEvent?
    @State private var showingPlannerSettings = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Pet Selector
                    if !petService.pets.isEmpty {
                        petSelectorSection
                    }
                    
                    // Today's Plan Overview
                    if let todaysPlan = plannerService.todaysPlan {
                        todaysPlanSection(todaysPlan)
                    }
                    
                    // Active Reminders
                    if !plannerService.activeReminders.isEmpty {
                        activeRemindersSection
                    }
                    
                    // Upcoming Events
                    upcomingEventsSection
                    
                    // Planner Categories
                    plannerCategoriesSection
                    
                    // Insights
                    if !plannerService.plannerInsights.isEmpty {
                        plannerInsightsSection
                    }
                }
                .padding()
            }
            .navigationTitle("Perfect Adventures")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { showingPlannerSettings = true }) {
                        Image(systemName: "gear")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: refreshPlanner) {
                        Image(systemName: plannerService.isLoading ? "arrow.clockwise" : "arrow.clockwise")
                            .rotationEffect(.degrees(plannerService.isLoading ? 360 : 0))
                            .animation(.linear(duration: 1).repeatCount(plannerService.isLoading ? 10 : 0), value: plannerService.isLoading)
                    }
                }
            }
            .sheet(isPresented: $showingEventDetails) {
                if let event = selectedEvent {
                    PlannerEventDetailsView(event: event)
                }
            }
            .sheet(isPresented: $showingPlannerSettings) {
                PlannerSettingsView()
            }
            .onAppear {
                if selectedPet == nil && !petService.pets.isEmpty {
                    selectedPet = petService.pets.first
                }
            }
        }
    }
    
    // MARK: - Pet Selector Section
    
    private var petSelectorSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Pet")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(petService.pets, id: \.id) { pet in
                        Button {
                            selectedPet = pet
                        } label: {
                            VStack(spacing: 8) {
                                Circle()
                                    .fill(selectedPet?.id == pet.id ? Color.blue : Color.gray.opacity(0.3))
                                    .frame(width: 60, height: 60)
                                    .overlay {
                                        Text(String(pet.name.prefix(1)).uppercased())
                                            .font(.title2)
                                            .fontWeight(.bold)
                                            .foregroundColor(selectedPet?.id == pet.id ? .white : .primary)
                                    }
                                
                                Text(pet.name)
                                    .font(.caption)
                                    .foregroundColor(selectedPet?.id == pet.id ? .blue : .primary)
                            }
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Today's Plan Section
    
    private func todaysPlanSection(_ plan: DailyPlan) -> some View {
        VStack(spacing: 16) {
            // Plan Overview Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Today's Plan")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(plan.date.formatted(.dateTime.weekday(.wide).month().day()))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Completion Score
                VStack(spacing: 4) {
                    ZStack {
                        Circle()
                            .stroke(Color(.systemGray5), lineWidth: 6)
                            .frame(width: 60, height: 60)
                        
                        Circle()
                            .trim(from: 0, to: plan.completionScore)
                            .stroke(getCompletionColor(plan.completionScore), style: StrokeStyle(lineWidth: 6, lineCap: .round))
                            .frame(width: 60, height: 60)
                            .rotationEffect(.degrees(-90))
                            .animation(.easeInOut(duration: 1), value: plan.completionScore)
                        
                        Text("\(Int(plan.completionScore * 100))%")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(getCompletionColor(plan.completionScore))
                    }
                    
                    Text("Complete")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Task Summary
            HStack(spacing: 16) {
                TaskSummaryCard(
                    title: "Total Tasks",
                    count: plan.totalTasks,
                    icon: "list.bullet",
                    color: .blue
                )
                
                TaskSummaryCard(
                    title: "Completed",
                    count: plan.completedTasks,
                    icon: "checkmark.circle.fill",
                    color: .green
                )
                
                TaskSummaryCard(
                    title: "Pending",
                    count: plan.pendingTasks,
                    icon: "clock.fill",
                    color: .orange
                )
            }
            
            // Environmental Recommendations
            if !plan.environmentalRecommendations.isEmpty {
                environmentalRecommendationsView(plan.environmentalRecommendations)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Active Reminders Section
    
    private var activeRemindersSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Active Reminders")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(plannerService.activeReminders.count)")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.2))
                    .foregroundColor(.red)
                    .cornerRadius(8)
            }
            
            ForEach(plannerService.activeReminders.prefix(3)) { reminder in
                ReminderCard(reminder: reminder) {
                    plannerService.dismissReminder(reminder)
                }
            }
            
            if plannerService.activeReminders.count > 3 {
                Button("View All Reminders") {
                    // Navigate to full reminders view
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
    }
    
    // MARK: - Upcoming Events Section
    
    private var upcomingEventsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Upcoming Events")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if let lastSync = plannerService.lastSyncTime {
                    Text("Updated \(lastSync.formatted(.relative(presentation: .named)))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            if plannerService.upcomingEvents.isEmpty {
                EmptyEventsView()
            } else {
                ForEach(plannerService.upcomingEvents.prefix(5)) { event in
                    PlannerEventCard(event: event) {
                        selectedEvent = event
                        showingEventDetails = true
                    } completeAction: {
                        Task {
                            await plannerService.markEventCompleted(event.id)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Planner Categories Section
    
    private var plannerCategoriesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Planner Categories")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                PlannerCategoryCard(
                    title: "Smart Walk",
                    icon: "figure.walk",
                    color: .blue,
                    description: "Plan optimal walks"
                ) {
                    // Navigate to walk planner
                }
                
                PlannerCategoryCard(
                    title: "Nutrition",
                    icon: "fork.knife",
                    color: .orange,
                    description: "Meal planning & feeding"
                ) {
                    // Navigate to nutrition planner
                }
                
                PlannerCategoryCard(
                    title: "Training",
                    icon: "graduationcap",
                    color: .purple,
                    description: "Skills & behavior training"
                ) {
                    // Navigate to training planner
                }
                
                PlannerCategoryCard(
                    title: "Health",
                    icon: "heart.fill",
                    color: .red,
                    description: "Health monitoring & care"
                ) {
                    // Navigate to health monitoring
                }
                
                PlannerCategoryCard(
                    title: "Vaccinations",
                    icon: "syringe",
                    color: .green,
                    description: "Vaccination schedule"
                ) {
                    // Navigate to vaccination planner
                }
                
                PlannerCategoryCard(
                    title: "Emergency",
                    icon: "exclamationmark.triangle.fill",
                    color: .red,
                    description: "Emergency preparedness"
                ) {
                    // Navigate to emergency planning
                }
            }
        }
    }
    
    // MARK: - Planner Insights Section
    
    private var plannerInsightsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Planner Insights")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(plannerService.plannerInsights) { insight in
                InsightCard(insight: insight)
            }
        }
    }
    
    // MARK: - Supporting Views
    
    private func environmentalRecommendationsView(_ recommendations: [EnvironmentalRecommendation]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Environmental Recommendations")
                .font(.subheadline)
                .fontWeight(.medium)
            
            ForEach(Array(recommendations.prefix(2)), id: \.id) { recommendation in
                HStack(spacing: 8) {
                    Image(systemName: "leaf.fill")
                        .font(.caption)
                        .foregroundColor(recommendation.priority.color)
                    
                    Text(recommendation.message)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.green.opacity(0.1))
        )
    }
    
    // MARK: - Helper Methods
    
    private func refreshPlanner() {
        Task {
            await plannerService.refreshAllPlanners()
        }
    }
    
    private func getCompletionColor(_ score: Double) -> Color {
        switch score {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .blue
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
}

// MARK: - Supporting Views

struct TaskSummaryCard: View {
    let title: String
    let count: Int
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text("\(count)")
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
        )
    }
}

struct ReminderCard: View {
    let reminder: PlannerReminder
    let dismissAction: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: reminder.type.icon)
                .font(.title2)
                .foregroundColor(reminder.priority.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(reminder.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(reminder.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            Button("Dismiss", action: dismissAction)
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.gray.opacity(0.2))
                .foregroundColor(.gray)
                .cornerRadius(6)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(reminder.priority.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(reminder.priority.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct PlannerEventCard: View {
    let event: PlannerEvent
    let action: () -> Void
    let completeAction: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: event.type.icon)
                    .font(.title2)
                    .foregroundColor(event.type.color)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(event.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(event.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    Text(event.scheduledTime.formatted(.dateTime.hour().minute()))
                        .font(.caption)
                        .foregroundColor(event.isOverdue ? .red : .blue)
                }
                
                Spacer()
                
                VStack(spacing: 4) {
                    Button("Complete", action: completeAction)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.green.opacity(0.2))
                        .foregroundColor(.green)
                        .cornerRadius(6)
                    
                    Text(event.priority.displayName)
                        .font(.caption2)
                        .foregroundColor(event.priority.color)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct PlannerCategoryCard: View {
    let title: String
    let icon: String
    let color: Color
    let description: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct InsightCard: View {
    let insight: PlannerInsight
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: insight.type.icon)
                .font(.title2)
                .foregroundColor(insight.type.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(insight.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(insight.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            if insight.actionable {
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(insight.type.color.opacity(0.1))
        )
    }
}

struct EmptyEventsView: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "calendar")
                .font(.system(size: 32))
                .foregroundColor(.gray)
            
            Text("No Upcoming Events")
                .font(.subheadline)
                .fontWeight(.medium)
            
            Text("Your schedule is clear! Consider planning some activities for your pet.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 20)
    }
}

// MARK: - Placeholder Detail Views

struct PlannerEventDetailsView: View {
    let event: PlannerEvent
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Event Details")
                    .font(.headline)
                
                Text("Event details would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle(event.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct PlannerSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Planner Settings")
                    .font(.headline)
                
                Text("Planner settings would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    UnifiedPlannerDashboard()
}
