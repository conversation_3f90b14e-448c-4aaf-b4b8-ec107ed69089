//
//  VaccinationPlannerView.swift
//  PetCapsule
//
//  World-Class Vaccination Planning Interface
//  ✅ Modern Design with Glass Morphism & Animations
//

import SwiftUI

struct VaccinationPlannerView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var petService: RealDataService
    @Environment(\.colorScheme) private var colorScheme
    @StateObject private var vaccinationService = VaccinationPlannerService.shared
    @State private var selectedPet: Pet?
    @State private var showAddVaccination = false
    @State private var showScheduleCreation = false
    @State private var selectedTab = 0
    @State private var vaccinations: [String] = []
    @State private var upcomingVaccinations: [String] = []
    @State private var animateCards = false
    @State private var showDetails = false
    
    private let tabs = ["Overview", "Schedule", "Vaccines", "Records"]
    
    var body: some View {
        NavigationView {
            ZStack {
                // Animated Background
                animatedBackground
                
                ScrollView {
                    LazyVStack(spacing: 20) {
                        if !petService.pets.isEmpty {
                            // Pet Selector Header
                            petSelectorHeader
                            
                            // Hero Vaccination Card
                            heroVaccinationCard
                            
                            // Tab Navigation
                            tabNavigation
                            
                            // Tab Content
                            tabContent
                        } else {
                            emptyStateView
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    if selectedPet != nil {
                        Button(action: { showAddVaccination = true }) {
                            Image(systemName: "plus")
                                .font(.title3)
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
            // TODO: Add vaccination and schedule creation sheets when views are implemented
            // .sheet(isPresented: $showAddVaccination) {
            //     AddVaccinationView(pet: selectedPet)
            // }
            // .sheet(isPresented: $showScheduleCreation) {
            //     if let pet = selectedPet {
            //         VaccinationScheduleCreationView(pet: pet)
            //     }
            // }
            .onAppear {
                if selectedPet == nil && !petService.pets.isEmpty {
                    selectedPet = petService.pets.first
                }
                loadVaccinations()
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateCards = true
                }
            }
        }
    }
    
    // MARK: - Animated Background
    private var animatedBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    colorScheme == .dark ? Color.black : Color(.systemGroupedBackground),
                    colorScheme == .dark ? Color.gray.opacity(0.1) : Color.red.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Floating elements
            ForEach(0..<3) { index in
                Circle()
                    .fill(Color.red.opacity(0.1))
                    .frame(width: 100 + CGFloat(index * 50))
                    .offset(
                        x: CGFloat(index * 100) - 150,
                        y: CGFloat(index * 80) - 200
                    )
                    .blur(radius: 20)
            }
        }
        .ignoresSafeArea()
    }
    
    // MARK: - Pet Selector Header
    private var petSelectorHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Vaccination Planner")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("Keep your pets healthy and protected")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
                
                Image(systemName: "cross.circle.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(.red)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(petService.pets, id: \.id) { pet in
                        petSelectionCard(pet: pet)
                    }
                }
                .padding(.horizontal, 4)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    private func petSelectionCard(pet: Pet) -> some View {
        Button {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                selectedPet = pet
            }
        } label: {
            VStack(spacing: 8) {
                Circle()
                    .fill(selectedPet?.id == pet.id ? 
                          LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing) :
                          LinearGradient(colors: [.gray.opacity(0.3), .gray.opacity(0.1)], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Text(String(pet.name.prefix(1)).uppercased())
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(selectedPet?.id == pet.id ? .white : .primary)
                    )
                    .scaleEffect(selectedPet?.id == pet.id ? 1.1 : 1.0)
                
                Text(pet.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedPet?.id == pet.id ? .red : .primary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Hero Vaccination Card
    private var heroVaccinationCard: some View {
        VStack(spacing: 20) {
            if let pet = selectedPet {
                // Pet Info & Vaccination Status
                HStack(spacing: 16) {
                    // Pet Avatar
                    Circle()
                        .fill(LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Text(String(pet.name.prefix(1)).uppercased())
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(pet.name)
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("\(pet.species ?? "Unknown") • \(pet.breed ?? "Unknown")")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("\(pet.age) years old • \(String(format: "%.1f", pet.weight ?? 0.0)) kg")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // Vaccination Status
                    vaccinationStatusIndicator(status: "Up to Date")
                }
                
                // Vaccination Summary
                VStack(alignment: .leading, spacing: 12) {
                    Text("Vaccination Status")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 20) {
                        vaccinationMetric(
                            icon: "checkmark.shield.fill",
                            value: "8",
                            unit: "Completed",
                            color: .green
                        )
                        
                        vaccinationMetric(
                            icon: "clock.fill",
                            value: "2",
                            unit: "Upcoming",
                            color: .orange
                        )
                        
                        vaccinationMetric(
                            icon: "exclamationmark.triangle.fill",
                            value: "0",
                            unit: "Overdue",
                            color: .red
                        )
                    }
                }
            } else {
                Text("Select a pet to view vaccination details")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                )
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)
    }
    
    private func vaccinationMetric(icon: String, value: String, unit: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
            
            Text(unit)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func vaccinationStatusIndicator(status: String) -> some View {
        VStack(spacing: 4) {
            ZStack {
                Circle()
                    .fill(Color.green.opacity(0.2))
                    .frame(width: 50, height: 50)
                
                Image(systemName: "checkmark.shield.fill")
                    .font(.title2)
                    .foregroundColor(.green)
            }
            
            Text(status)
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(.green)
        }
    }
    
    // MARK: - Tab Navigation
    private var tabNavigation: some View {
        HStack(spacing: 0) {
            ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                Button {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        selectedTab = index
                    }
                } label: {
                    Text(tab)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(selectedTab == index ? Color.red : Color.clear)
                        )
                        .foregroundColor(selectedTab == index ? .white : .primary)
                }
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateCards)
    }
    
    // MARK: - Tab Content
    private var tabContent: some View {
        Group {
            switch selectedTab {
            case 0:
                overviewTab
            case 1:
                scheduleTab
            case 2:
                vaccinesTab
            case 3:
                recordsTab
            default:
                overviewTab
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateCards)
    }
    
    // MARK: - Overview Tab
    private var overviewTab: some View {
        VStack(spacing: 16) {
            // Quick Actions
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                quickActionCard(
                    icon: "plus.circle.fill",
                    title: "Add Vaccination",
                    subtitle: "Record new vaccination",
                    color: .red,
                    action: { showAddVaccination = true }
                )
                
                quickActionCard(
                    icon: "calendar.circle.fill",
                    title: "Create Schedule",
                    subtitle: "Set up vaccination plan",
                    color: .blue,
                    action: { showScheduleCreation = true }
                )
            }
            
            // Vaccination Insights
            vaccinationInsightsCard
            
            // Upcoming Vaccinations
            upcomingVaccinationsCard
        }
    }
    
    private func quickActionCard(icon: String, title: String, subtitle: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var vaccinationInsightsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                Text("Vaccination Insights")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                insightRow(
                    icon: "checkmark.circle.fill",
                    text: "All core vaccinations are up to date",
                    color: .green
                )
                
                insightRow(
                    icon: "exclamationmark.triangle.fill",
                    text: "Rabies booster due in 3 months",
                    color: .orange
                )
                
                insightRow(
                    icon: "info.circle.fill",
                    text: "Consider annual wellness check",
                    color: .blue
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func insightRow(icon: String, text: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
    
    private var upcomingVaccinationsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "calendar.fill")
                    .foregroundColor(.red)
                Text("Upcoming Vaccinations")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            VStack(spacing: 12) {
                ForEach(1...3, id: \.self) { index in
                    vaccinationRow(
                        date: index == 1 ? "Next Week" : index == 2 ? "Next Month" : "3 months",
                        vaccine: index == 1 ? "Rabies Booster" : index == 2 ? "DHPP" : "Bordetella",
                        status: index == 1 ? "Scheduled" : index == 2 ? "Due Soon" : "Upcoming",
                        priority: index == 1 ? .high : index == 2 ? .medium : .low
                    )
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func vaccinationRow(date: String, vaccine: String, status: String, priority: Priority) -> some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(date)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(vaccine)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .frame(width: 100, alignment: .leading)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(status)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Text("Status")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Circle()
                    .fill(priority.color)
                    .frame(width: 12, height: 12)
                
                Text(priority.text)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
    
    enum Priority {
        case high, medium, low
        
        var color: Color {
            switch self {
            case .high: return .red
            case .medium: return .orange
            case .low: return .green
            }
        }
        
        var text: String {
            switch self {
            case .high: return "High"
            case .medium: return "Medium"
            case .low: return "Low"
            }
        }
    }
    
    // MARK: - Schedule Tab
    private var scheduleTab: some View {
        VStack(spacing: 16) {
            Text("Vaccination Schedule")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for vaccination schedule
            VStack(spacing: 20) {
                Image(systemName: "calendar.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.red)
                
                Text("Vaccination Schedule Coming Soon")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Text("View and manage your pet's vaccination schedule with reminders")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Vaccines Tab
    private var vaccinesTab: some View {
        VStack(spacing: 16) {
            Text("Available Vaccines")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for vaccine database
            VStack(spacing: 20) {
                Image(systemName: "cross.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.blue)
                
                Text("Vaccine Database Coming Soon")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Text("Browse comprehensive information about different vaccines and their requirements")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Records Tab
    private var recordsTab: some View {
        VStack(spacing: 16) {
            Text("Vaccination Records")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Placeholder for vaccination records
            VStack(spacing: 20) {
                Image(systemName: "doc.text.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.green)
                
                Text("Vaccination Records Coming Soon")
                    .font(.title3)
                    .fontWeight(.medium)
                
                Text("Access complete vaccination history and medical records")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "pawprint.circle")
                .font(.system(size: 64))
                .foregroundColor(.red)
            
            Text("No Pets Found")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Add a pet to start tracking their vaccinations")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .scaleEffect(animateCards ? 1.0 : 0.8)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)
    }
    
    // MARK: - Helper Methods
    private func loadVaccinations() {
        // Load vaccination data
        vaccinations = []
        upcomingVaccinations = []
    }
}

#Preview {
    VaccinationPlannerView()
        .environmentObject(RealDataService())
}
