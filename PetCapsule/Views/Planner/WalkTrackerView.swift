//
//  WalkTrackerView.swift
//  PetCapsule
//
//  Simple walk tracker for capturing walk data with environmental context
//

import SwiftUI
import CoreLocation
import MapKit

struct WalkTrackerView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var locationManager = LocationManager()
    
    let onWalkCompleted: (WalkData) -> Void
    
    @State private var isTracking = false
    @State private var startTime: Date?
    @State private var startLocation: CLLocationCoordinate2D?
    @State private var currentLocation: CLLocationCoordinate2D?
    @State private var routeCoordinates: [CLLocationCoordinate2D] = []
    @State private var totalDistance: Double = 0
    @State private var elapsedTime: TimeInterval = 0
    
    @State private var timer: Timer?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Map View
                mapView
                
                // Stats
                statsView
                
                // Controls
                controlsView
                
                Spacer()
            }
            .padding()
            .navigationTitle("Adventure Journal")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        stopTracking()
                        dismiss()
                    }
                }
            }
            .onAppear {
                setupLocationTracking()
            }
            .onDisappear {
                stopTracking()
            }
        }
    }
    
    // MARK: - UI Components
    
    private var mapView: some View {
        Map(bounds: MapCameraBounds(minimumDistance: 100, maximumDistance: 2000)) {
            // Start location
            if let startLocation = startLocation {
                Annotation("Start", coordinate: startLocation) {
                    Image(systemName: "play.circle.fill")
                        .foregroundColor(.green)
                        .font(.title2)
                }
            }
            
            // Current location
            if let currentLocation = currentLocation {
                Annotation("Current", coordinate: currentLocation) {
                    Image(systemName: "location.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title2)
                }
            }
            
            // Route
            if routeCoordinates.count > 1 {
                MapPolyline(coordinates: routeCoordinates)
                    .stroke(.blue, style: StrokeStyle(lineWidth: 4, lineCap: .round))
            }
        }
        .frame(height: 300)
        .cornerRadius(12)
    }
    
    private var statsView: some View {
        HStack(spacing: 20) {
            StatCard(
                title: "Time",
                value: formatTime(elapsedTime),
                icon: "clock",
                color: .blue
            )
            
            StatCard(
                title: "Distance",
                value: formatDistance(totalDistance),
                icon: "figure.walk",
                color: .green
            )
            
            StatCard(
                title: "Pace",
                value: formatPace(distance: totalDistance, time: elapsedTime),
                icon: "speedometer",
                color: .orange
            )
        }
    }
    
    private var controlsView: some View {
        VStack(spacing: 16) {
            if !isTracking {
                Button(action: startTracking) {
                    HStack {
                        Image(systemName: "play.fill")
                        Text("Start Walk")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(.green)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
            } else {
                HStack(spacing: 12) {
                    Button(action: pauseTracking) {
                        HStack {
                            Image(systemName: "pause.fill")
                            Text("Pause")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.orange)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    
                    Button(action: finishWalk) {
                        HStack {
                            Image(systemName: "stop.fill")
                            Text("Finish")
                                .fontWeight(.semibold)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(.red)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                }
            }
        }
    }
    
    // MARK: - Methods
    
    private func setupLocationTracking() {
        locationManager.requestLocationPermission()
    }
    
    private func startTracking() {
        guard let location = currentLocation else {
            print("❌ Location not available")
            return
        }
        
        isTracking = true
        startTime = Date()
        startLocation = location
        currentLocation = location
        routeCoordinates = [location]
        totalDistance = 0
        elapsedTime = 0
        
        // Start timer
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            updateElapsedTime()
        }
        
        // Start location updates
        startLocationUpdates()
    }
    
    private func pauseTracking() {
        isTracking = false
        timer?.invalidate()
        timer = nil
    }
    
    private func finishWalk() {
        guard let startLocation = startLocation,
              let currentLocation = currentLocation,
              let startTime = startTime else {
            return
        }
        
        stopTracking()
        
        let walkData = WalkData(
            startTime: startTime,
            endTime: Date(),
            durationMinutes: Int(elapsedTime / 60),
            distanceMeters: totalDistance,
            startLocation: startLocation,
            endLocation: currentLocation,
            routeCoordinates: routeCoordinates
        )
        
        onWalkCompleted(walkData)
        dismiss()
    }
    
    private func stopTracking() {
        isTracking = false
        timer?.invalidate()
        timer = nil
    }
    
    private func updateElapsedTime() {
        guard let startTime = startTime else { return }
        elapsedTime = Date().timeIntervalSince(startTime)
    }
    
    private func startLocationUpdates() {
        // Simplified location updates - in production would use proper location manager delegate
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { timer in
            guard isTracking else {
                timer.invalidate()
                return
            }
            
            if let newLocation = currentLocation {
                updateLocation(newLocation)
            }
        }
    }
    
    private func updateLocation(_ newLocation: CLLocationCoordinate2D) {
        guard let lastLocation = routeCoordinates.last else { return }
        
        // Calculate distance from last point
        let lastCLLocation = CLLocation(latitude: lastLocation.latitude, longitude: lastLocation.longitude)
        let newCLLocation = CLLocation(latitude: newLocation.latitude, longitude: newLocation.longitude)
        let distance = lastCLLocation.distance(from: newCLLocation)
        
        // Only add if moved significantly (> 5 meters)
        if distance > 5 {
            routeCoordinates.append(newLocation)
            totalDistance += distance
            currentLocation = newLocation
        }
    }
    
    // MARK: - Formatting Methods
    
    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        let seconds = Int(timeInterval) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
    
    private func formatDistance(_ meters: Double) -> String {
        let miles = meters / 1609.34
        if miles < 0.1 {
            return String(format: "%.0f ft", meters * 3.28084)
        } else {
            return String(format: "%.2f mi", miles)
        }
    }
    
    private func formatPace(distance: Double, time: TimeInterval) -> String {
        guard distance > 0 && time > 0 else { return "--" }
        
        let milesPerHour = (distance / 1609.34) / (time / 3600)
        return String(format: "%.1f mph", milesPerHour)
    }
}

// MARK: - Stat Card

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title2)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    WalkTrackerView { _ in }
}
