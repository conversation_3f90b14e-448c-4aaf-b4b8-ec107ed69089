//
//  ProfileView.swift
//  PetCapsule
//
//  User profile with premium features and investor access
//

import SwiftUI

struct ProfileView: View {
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var analyticsService = AnalyticsService.shared
    @State private var showingSubscription = false
    @State private var showingInvestorDashboard = false
    @State private var showingSettings = false
    @State private var showingRevenueDashboard = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.xl) {
                    // Profile Header
                    ProfileHeaderSection()
                    
                    // Subscription Status
                    SubscriptionStatusCard()
                    
                    // Revenue Dashboard (for business users)
                    if subscriptionService.hasFeature(.businessTools) {
                        BusinessRevenueCard()
                    }
                    
                    // Quick Stats
                    QuickStatsSection()
                    
                    // Menu Options
                    ProfileMenuSection(
                        showingSubscription: $showingSubscription,
                        showingInvestorDashboard: $showingInvestorDashboard,
                        showingSettings: $showingSettings,
                        showingRevenueDashboard: $showingRevenueDashboard
                    )
                    
                    // App Info
                    AppInfoSection()
                }
                .padding(.horizontal, Spacing.lg)
                .padding(.top, Spacing.md)
            }
            .navigationTitle("About You")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingSubscription) {
                SubscriptionView()
            }
            .sheet(isPresented: $showingInvestorDashboard) {
                InvestorDashboardView()
            }
            .sheet(isPresented: $showingRevenueDashboard) {
                BusinessRevenueDashboard()
            }
            .sheet(isPresented: $showingSettings) {
                SettingsView()
            }
        }
    }
}

struct ProfileHeaderSection: View {
    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Profile Image
            Circle()
                .fill(
                    LinearGradient(
                        colors: [Color.petAccent, Color.blue],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 100, height: 100)
                .overlay(
                    Text("👤")
                        .font(.system(size: 50))
                )
            
            // User Info
            VStack(spacing: Spacing.sm) {
                Text("Pet Parent")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text("Preserving precious memories since 2024")
                    .font(.petCallout)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
}

struct SubscriptionStatusCard: View {
    @StateObject private var subscriptionService = SubscriptionService.shared
    
    var body: some View {
        VStack(spacing: Spacing.md) {
            HStack {
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("Subscription Status")
                        .font(.petHeadline)
                        .foregroundColor(.primary)
                    
                    HStack {
                                            Image(systemName: subscriptionService.subscriptionStatus == .pawStarter ? "circle" : "checkmark.circle.fill")
                        .foregroundColor(subscriptionService.subscriptionStatus == .pawStarter ? .orange : .green)
                        
                        Text(subscriptionService.currentPlan?.name ?? "Free Plan")
                            .font(.petCallout)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                    }
                }
                
                Spacer()
                
                if subscriptionService.subscriptionStatus == .pawStarter {
                    Button("Upgrade") {
                        // Show subscription view
                    }
                    .petButtonStyle(.primary)
                    .controlSize(.small)
                }
            }
            
            if let plan = subscriptionService.currentPlan, plan.price > 0 {
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("Premium Features Active")
                        .font(.petCaption)
                        .foregroundColor(.green)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: Spacing.xs) {
                            ForEach(plan.features.prefix(3), id: \.self) { feature in
                                Text(feature)
                                    .font(.petCaption)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, Spacing.sm)
                                    .padding(.vertical, Spacing.xs)
                                    .background(Color.green)
                                    .cornerRadius(CornerRadius.sm)
                            }
                        }
                    }
                }
            }
        }
        .padding(Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.lg)
                .fill(subscriptionService.subscriptionStatus == .pawStarter ? Color.orange.opacity(0.1) : Color.green.opacity(0.1))
        )
    }
}

struct BusinessRevenueCard: View {
    @StateObject private var analyticsService = AnalyticsService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                Text("Your Business Revenue")
                    .font(.petHeadline)
                    .foregroundColor(.primary)
                Spacer()
                Button("View Details") {
                    // Show revenue dashboard
                }
                .font(.petCaption)
                .foregroundColor(.blue)
            }
            
            HStack(spacing: Spacing.xl) {
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("This Month")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                    
                    Text("$\(analyticsService.revenueMetrics.totalMonthlyRevenue * 0.1, specifier: "%.0f")")
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }
                
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text("Growth")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                    
                    Text("+\(analyticsService.revenueMetrics.revenueGrowthRate, specifier: "%.1f")%")
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                }
                
                Spacer()
            }
        }
        .padding(Spacing.lg)
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(CornerRadius.lg)
    }
}

struct QuickStatsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Your Stats")
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: Spacing.md) {
                StatCard(title: "Memories", value: "247", icon: "photo.fill", color: .blue)
                StatCard(title: "Videos", value: "12", icon: "video.fill", color: .purple)
                StatCard(title: "Friends", value: "89", icon: "person.2.fill", color: .green)
                StatCard(title: "Tributes", value: "5", icon: "heart.fill", color: .pink)
            }
        }
    }
}

// StatCard moved to WalkTrackerView.swift to avoid duplication

struct ProfileMenuSection: View {
    @Binding var showingSubscription: Bool
    @Binding var showingInvestorDashboard: Bool
    @Binding var showingSettings: Bool
    @Binding var showingRevenueDashboard: Bool
    
    @StateObject private var subscriptionService = SubscriptionService.shared
    
    var body: some View {
        VStack(spacing: Spacing.sm) {
            if subscriptionService.subscriptionStatus == .pawStarter {
                ProfileMenuRow(
                    icon: "crown.fill",
                    title: "Go Premium",
                    subtitle: "Unlock AI features & unlimited storage",
                    action: { showingSubscription = true },
                    showChevron: true,
                    accentColor: .yellow
                )
            }
            
            ProfileMenuRow(
                icon: "chart.line.uptrend.xyaxis",
                title: "Investor Dashboard",
                subtitle: "View revenue metrics & projections",
                action: { showingInvestorDashboard = true },
                showChevron: true,
                accentColor: .green
            )
            
            if subscriptionService.hasFeature(.businessTools) {
                ProfileMenuRow(
                    icon: "dollarsign.circle.fill",
                    title: "Revenue Dashboard",
                    subtitle: "Track your business earnings",
                    action: { showingRevenueDashboard = true },
                    showChevron: true,
                    accentColor: .blue
                )
            }
            
            ProfileMenuRow(
                icon: "gear",
                title: "Settings",
                subtitle: "App preferences & account",
                action: { showingSettings = true },
                showChevron: true
            )
            
            ProfileMenuRow(
                icon: "questionmark.circle",
                title: "Help & Support",
                subtitle: "Get help with the app",
                action: { },
                showChevron: true
            )
            
            ProfileMenuRow(
                icon: "star.fill",
                title: "Rate PetCapsule",
                subtitle: "Share your experience",
                action: { },
                showChevron: true,
                accentColor: .yellow
            )
        }
    }
}

struct ProfileMenuRow: View {
    let icon: String
    let title: String
    var subtitle: String? = nil
    let action: () -> Void
    var showChevron: Bool = false
    var accentColor: Color = .petAccent
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: Spacing.md) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(accentColor)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(title)
                        .font(.petCallout)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if showChevron {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(Spacing.md)
            .background(Color.white)
            .cornerRadius(CornerRadius.md)
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AppInfoSection: View {
    var body: some View {
        VStack(spacing: Spacing.md) {
            Text("PetCapsule v1.0")
                .font(.petCallout)
                .foregroundColor(.secondary)
            
            Text("Preserving precious pet memories with AI")
                .font(.petCaption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            HStack(spacing: Spacing.lg) {
                Button("Privacy Policy") { }
                    .font(.petCaption)
                    .foregroundColor(.blue)
                
                Button("Terms of Service") { }
                    .font(.petCaption)
                    .foregroundColor(.blue)
            }
        }
        .padding(.top, Spacing.xl)
    }
}

// Placeholder views
struct BusinessRevenueDashboard: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Business Revenue Dashboard")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Settings")
                    .font(.petTitle2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#Preview {
    ProfileView()
}
