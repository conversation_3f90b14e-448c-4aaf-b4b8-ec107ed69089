//
//  NotificationSettingsView.swift
//  PetCapsule
//
//  Notification settings management with environmental alert preferences
//

import SwiftUI

// Using AlertSeverity directly to avoid redeclaration

struct NotificationSettingsView: View {
    @StateObject private var notificationService = EnvironmentalNotificationService.shared
    @State private var settings = NotificationSettings.defaultSettings()
    @State private var isLoading = false
    @State private var showingPermissionAlert = false
    @State private var showingTestNotification = false
    
    var body: some View {
        NavigationView {
            Form {
                // Permission Status Section
                permissionStatusSection
                
                // Environmental Alerts Section
                environmentalAlertsSection
                
                // Alert Types Section
                alertTypesSection
                
                // Quiet Hours Section
                quietHoursSection
                
                // Alert Frequency Section
                alertFrequencySection
                
                // Test & Manage Section
                testAndManageSection
            }
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            }
            .onAppear {
                loadSettings()
                checkNotificationPermissionStatus()
            }
            .alert("Notification Permission Required", isPresented: $showingPermissionAlert) {
                Button("Settings") {
                    openAppSettings()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("Please enable notifications in Settings to receive environmental alerts for your pet's safety.")
            }
            .alert("Test Notification Sent", isPresented: $showingTestNotification) {
                Button("OK") { }
            } message: {
                Text("A test notification has been sent to verify your settings.")
            }
        }
    }
    
    // MARK: - Permission Status Section
    
    private var permissionStatusSection: some View {
        Section {
            HStack {
                Image(systemName: notificationService.authorizationStatus == .authorized ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                    .foregroundColor(notificationService.authorizationStatus == .authorized ? .green : .orange)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Notification Permission")
                        .font(.headline)
                    
                    Text(permissionStatusText)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if notificationService.authorizationStatus != .authorized {
                    Button("Enable") {
                        requestPermission()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                }
            }
            .padding(.vertical, 4)
        } header: {
            Text("Permission Status")
        } footer: {
            Text("Notifications are essential for receiving timely environmental alerts that could affect your pet's health and safety.")
        }
    }
    
    // MARK: - Environmental Alerts Section
    
    private var environmentalAlertsSection: some View {
        Section {
            Toggle("Environmental Alerts", isOn: $settings.environmentalAlertsEnabled)
                .onChange(of: settings.environmentalAlertsEnabled) { _ in
                    saveSettings()
                }
            
            Toggle("Critical Alerts", isOn: $settings.criticalAlertsEnabled)
                .onChange(of: settings.criticalAlertsEnabled) { _ in
                    saveSettings()
                }
        } header: {
            Text("Environmental Monitoring")
        } footer: {
            Text("Environmental alerts notify you when air quality, temperature, or other conditions may affect your pet. Critical alerts are always delivered regardless of other settings.")
        }
    }
    
    // MARK: - Alert Types Section
    
    private var alertTypesSection: some View {
        Section {
            Toggle("Weather Updates", isOn: $settings.weatherUpdatesEnabled)
                .onChange(of: settings.weatherUpdatesEnabled) { _ in
                    saveSettings()
                }
            
            Toggle("Pollen Alerts", isOn: $settings.pollenAlertsEnabled)
                .onChange(of: settings.pollenAlertsEnabled) { _ in
                    saveSettings()
                }
        } header: {
            Text("Alert Types")
        } footer: {
            Text("Choose which types of environmental alerts you want to receive.")
        }
    }
    
    // MARK: - Quiet Hours Section
    
    private var quietHoursSection: some View {
        Section {
            Toggle("Enable Quiet Hours", isOn: $settings.quietHoursEnabled)
                .onChange(of: settings.quietHoursEnabled) { _ in
                    saveSettings()
                }
            
            if settings.quietHoursEnabled {
                HStack {
                    Text("Start Time")
                    Spacer()
                    Text(settings.quietHoursStart, style: .time)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("End Time")
                    Spacer()
                    Text(settings.quietHoursEnd, style: .time)
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("Quiet Hours")
        } footer: {
            Text("During quiet hours, only critical environmental alerts will be delivered. Regular alerts will be delayed until quiet hours end.")
        }
    }
    
    // MARK: - Alert Frequency Section
    
    private var alertFrequencySection: some View {
        Section {
            Picker("Alert Frequency", selection: $settings.alertFrequency) {
                ForEach(AlertFrequency.allCases, id: \.self) { frequency in
                    Text(frequency.displayName)
                        .tag(frequency)
                }
            }
            .onChange(of: settings.alertFrequency) { _ in
                saveSettings()
            }
        } header: {
            Text("Alert Frequency")
        } footer: {
            Text("Controls how often you receive similar environmental alerts. Critical alerts are always delivered immediately.")
        }
    }
    
    // MARK: - Test & Manage Section
    
    private var testAndManageSection: some View {
        Section {
            Button("Send Test Notification") {
                sendTestNotification()
            }
            .disabled(notificationService.authorizationStatus != .authorized)
            
            Button("View Notification History") {
                // Navigate to notification history
            }
            
            Button("Clear All Notifications") {
                clearAllNotifications()
            }
            .foregroundColor(.red)
        } header: {
            Text("Test & Manage")
        }
    }
    
    // MARK: - Helper Methods
    
    private var permissionStatusText: String {
        switch notificationService.authorizationStatus {
        case .authorized:
            return "Notifications enabled"
        case .denied:
            return "Notifications disabled"
        case .notDetermined:
            return "Permission not requested"
        case .provisional:
            return "Provisional authorization"
        case .ephemeral:
            return "Ephemeral authorization"
        @unknown default:
            return "Unknown status"
        }
    }
    
    private func loadSettings() {
        if let currentSettings = notificationService.notificationSettings {
            settings = currentSettings
        } else {
            // Load from service
            Task {
                // Settings will be loaded automatically by the service
                await MainActor.run {
                    if let loadedSettings = notificationService.notificationSettings {
                        settings = loadedSettings
                    }
                }
            }
        }
    }
    
    private func saveSettings() {
        isLoading = true
        
        Task {
            await notificationService.updateNotificationSettings(settings)
            
            await MainActor.run {
                isLoading = false
            }
        }
    }
    
    private func requestPermission() {
        Task {
            let granted = await notificationService.requestNotificationPermission()
            
            await MainActor.run {
                if granted {
                    // Register for remote notifications
                    notificationService.registerForRemoteNotifications()
                } else {
                    showingPermissionAlert = true
                }
            }
        }
    }
    
    private func sendTestNotification() {
        Task {
            // Send test notification using UNUserNotificationCenter
            let content = UNMutableNotificationContent()
            content.title = "Test Alert"
            content.body = "This is a test notification to verify your environmental alert settings are working correctly."
            content.sound = .default
            
            let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
            let request = UNNotificationRequest(identifier: "test_alert", content: content, trigger: trigger)
            
            try? await UNUserNotificationCenter.current().add(request)
            
            await MainActor.run {
                showingTestNotification = true
            }
        }
    }
    
    private func clearAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
        
        // Reset badge count
        UIApplication.shared.applicationIconBadgeNumber = 0
    }
    
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    private func checkNotificationPermissionStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { notificationSettings in
            Task { @MainActor in
                notificationService.authorizationStatus = notificationSettings.authorizationStatus
                
                // If settings are enabled but permissions denied, show alert
                if (settings.environmentalAlertsEnabled || settings.criticalAlertsEnabled) && 
                   notificationSettings.authorizationStatus == .denied {
                    showingPermissionAlert = true
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct NotificationHistoryView: View {
    @StateObject private var notificationService = EnvironmentalNotificationService.shared
    
    var body: some View {
        NavigationView {
            List {
                ForEach(notificationService.notificationHistory, id: \.id) { notification in
                    NotificationHistoryRow(notification: notification)
                }
            }
            .navigationTitle("Notification History")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Clear All") {
                        notificationService.clearNotificationHistory()
                    }
                }
            }
        }
    }
}

struct NotificationHistoryRow: View {
    let notification: NotificationHistoryItem
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(notification.title)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text(notification.sentAt.formatted(.relative(presentation: .named)))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(notification.message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
            
            HStack {
                NotificationSeverityBadge(severity: notification.severity)
                
                Spacer()
                
                if !notification.isRead {
                    Circle()
                        .fill(Color.blue)
                        .frame(width: 8, height: 8)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

struct NotificationSeverityBadge: View {
    let severity: AlertSeverity
    
    var body: some View {
        Text(severity.rawValue.capitalized)
            .font(.caption2)
            .fontWeight(.semibold)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(severity.color.opacity(0.2))
            .foregroundColor(severity.color)
            .cornerRadius(4)
    }
}

#Preview {
    NotificationSettingsView()
}
