//
//  PrivacySettingsView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//
import SwiftUI
import LocalAuthentication
/// Privacy and security settings view following Apple's privacy guidelines
struct PrivacySettingsView: View {
    @StateObject private var biometricService = BiometricAuthenticationService()
    @StateObject private var authService = AuthenticationService()
    @State private var biometricEnabled = false
    @State private var dataCollectionEnabled = true
    @State private var analyticsEnabled = false
    @State private var crashReportingEnabled = true
    @State private var locationSharingEnabled = false
    @State private var showingDataExport = false
    @State private var showingDataDeletion = false
    @State private var showingPrivacyPolicy = false
    @State private var showingChangePassword = false
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    var body: some View {
        NavigationView {
            List {
                // Biometric Security Section
                biometricSecuritySection
                // Data Privacy Section
                dataPrivacySection
                // Analytics and Diagnostics Section
                analyticsSection
                // Data Management Section
                dataManagementSection
                // Privacy Information Section
                privacyInformationSection
            }
            .navigationTitle("Privacy & Security")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadCurrentSettings()
            }
            .sheet(isPresented: $showingDataExport) {
                DataExportView()
            }
            .sheet(isPresented: $showingDataDeletion) {
                DataDeletionView()
            }
            .sheet(isPresented: $showingPrivacyPolicy) {
                PrivacyPolicyDetailView()
            }
            .sheet(isPresented: $showingChangePassword) {
                ChangePasswordView()
                    .environmentObject(authService)
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("OK") {}
            } message: {
                Text(alertMessage)
            }
        }
    }
    // MARK: - Biometric Security Section
    private var biometricSecuritySection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: biometricService.biometricType.icon)
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(biometricService.biometricType.displayName) Authentication")
                            .font(.body)
                            .fontWeight(.medium)
                        Text("Use \(biometricService.biometricType.displayName) to secure your pet data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    Toggle("", isOn: $biometricEnabled)
                        .disabled(!biometricService.isAvailable)
                        .onChange(of: biometricEnabled) { _, newValue in
                            updateBiometricSetting(newValue)
                        }
                }
                if !biometricService.isAvailable {
                    Text(biometricService.getSetupInstructions())
                        .font(.caption2)
                        .foregroundColor(.orange)
                        .padding(.leading, 32)
                }
            }
            .padding(.vertical, 4)
            // Change Password Button
            Button(action: { showingChangePassword = true }) {
                HStack {
                    Image(systemName: "key.fill")
                        .foregroundColor(.orange)
                        .frame(width: 24)
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Change Password")
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        Text("Update your account password")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 4)
            }
            .buttonStyle(PlainButtonStyle())
        } header: {
            Text("Security")
        } footer: {
            Text("Your biometric data never leaves your device and is processed entirely within Apple's Secure Enclave.")
                .font(.caption2)
        }
    }
    // MARK: - Data Privacy Section
    private var dataPrivacySection: some View {
        Section {
            PrivacyToggleRow(
                icon: "icloud.fill",
                title: "Data Sync",
                subtitle: "Sync your pet data across devices",
                isEnabled: $dataCollectionEnabled,
                iconColor: .blue
            )
            .onChange(of: dataCollectionEnabled) { _, newValue in
                updateDataSyncSetting(newValue)
            }
            PrivacyToggleRow(
                icon: "location.fill",
                title: "Location Services",
                subtitle: "Add location to memories and photos",
                isEnabled: $locationSharingEnabled,
                iconColor: .green
            )
            .onChange(of: locationSharingEnabled) { _, newValue in
                updateLocationSetting(newValue)
            }
        } header: {
            Text("Data Collection")
        } footer: {
            Text("You can control what data is collected and how it's used. All data is encrypted and stored securely.")
                .font(.caption2)
        }
    }
    // MARK: - Analytics Section
    private var analyticsSection: some View {
        Section {
            PrivacyToggleRow(
                icon: "chart.bar.fill",
                title: "Analytics",
                subtitle: "Help improve the app with usage data",
                isEnabled: $analyticsEnabled,
                iconColor: .purple
            )
            .onChange(of: analyticsEnabled) { _, newValue in
                updateAnalyticsSetting(newValue)
            }
            PrivacyToggleRow(
                icon: "exclamationmark.triangle.fill",
                title: "Crash Reporting",
                subtitle: "Send crash reports to help fix bugs",
                isEnabled: $crashReportingEnabled,
                iconColor: .orange
            )
            .onChange(of: crashReportingEnabled) { _, newValue in
                updateCrashReportingSetting(newValue)
            }
        } header: {
            Text("Analytics & Diagnostics")
        } footer: {
            Text("Analytics data is anonymized and cannot be used to identify you or your pets.")
                .font(.caption2)
        }
    }
    // MARK: - Data Management Section
    private var dataManagementSection: some View {
        Section {
            Button(action: { showingDataExport = true }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Export My Data")
                            .foregroundColor(.primary)
                        Text("Download a copy of your data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            Button(action: { showingDataDeletion = true }) {
                HStack {
                    Image(systemName: "trash.fill")
                        .foregroundColor(.red)
                        .frame(width: 24)
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Delete My Data")
                            .foregroundColor(.red)
                        Text("Permanently delete all your data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("Data Management")
        } footer: {
            Text("You have full control over your data. Export or delete it at any time.")
                .font(.caption2)
        }
    }
    // MARK: - Privacy Information Section
    private var privacyInformationSection: some View {
        Section {
            Button(action: { showingPrivacyPolicy = true }) {
                HStack {
                    Image(systemName: "doc.text.fill")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    Text("Privacy Policy")
                        .foregroundColor(.primary)
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            HStack {
                Image(systemName: "shield.fill")
                    .foregroundColor(.green)
                    .frame(width: 24)
                VStack(alignment: .leading, spacing: 2) {
                    Text("Data Protection")
                        .foregroundColor(.primary)
                    Text("End-to-end encryption, zero-knowledge architecture")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("Privacy Information")
        }
    }
    // MARK: - Helper Methods
    private func loadCurrentSettings() {
        // Load biometric settings
        Task {
            biometricEnabled = await biometricService.isBiometricAuthenticationEnabled()
        }
        // Load stored preferences with defaults
        if UserDefaults.standard.object(forKey: "privacy.dataSync") == nil {
            UserDefaults.standard.set(true, forKey: "privacy.dataSync")
            UserDefaults.standard.set(false, forKey: "privacy.locationSharing")
            UserDefaults.standard.set(false, forKey: "privacy.analytics")
            UserDefaults.standard.set(true, forKey: "privacy.crashReporting")
        }
        dataCollectionEnabled = UserDefaults.standard.bool(forKey: "privacy.dataSync")
        locationSharingEnabled = UserDefaults.standard.bool(forKey: "privacy.locationSharing")
        analyticsEnabled = UserDefaults.standard.bool(forKey: "privacy.analytics")
        crashReportingEnabled = UserDefaults.standard.bool(forKey: "privacy.crashReporting")
    }
    private func updateBiometricSetting(_ enabled: Bool) {
        if enabled && biometricService.isAvailable {
            Task {
                let success = await biometricService.authenticateUser(
                    reason: "Enable biometric authentication for secure access to your pet data"
                )
                await MainActor.run {
                    if success {
                        biometricService.setBiometricAuthenticationEnabled(true)
                        biometricEnabled = true
                        showAlert("Biometric Authentication Enabled", "Your pet data is now secured with biometric authentication.")
                    } else {
                        biometricEnabled = false
                        showAlert("Authentication Failed", "Failed to enable biometric authentication. Please try again.")
                    }
                }
            }
        } else if !enabled {
            biometricService.setBiometricAuthenticationEnabled(false)
            biometricEnabled = false
            showAlert("Biometric Authentication Disabled", "Biometric authentication has been turned off.")
        } else {
            biometricEnabled = false
            showAlert("Biometric Authentication Unavailable", biometricService.getSetupInstructions())
        }
    }
    private func updateDataSyncSetting(_ enabled: Bool) {
        UserDefaults.standard.set(enabled, forKey: "privacy.dataSync")
        if enabled {
            // Enable data synchronization
            Task {
                await MainActor.run {
                    showAlert("Data Sync Enabled", "Your pet data will now sync across all your devices.")
                }
            }
        } else {
            showAlert("Data Sync Disabled", "Your data will only be stored locally on this device.")
        }
    }
    private func updateLocationSetting(_ enabled: Bool) {
        UserDefaults.standard.set(enabled, forKey: "privacy.locationSharing")
        if enabled {
            // Request location permission
            showAlert("Location Services Enabled", "Location will be added to new memories and photos.")
        } else {
            showAlert("Location Services Disabled", "Location will not be added to new memories and photos.")
        }
    }
    private func updateAnalyticsSetting(_ enabled: Bool) {
        UserDefaults.standard.set(enabled, forKey: "privacy.analytics")
        if enabled {
            showAlert("Analytics Enabled", "Anonymous usage data will help improve the app experience.")
        } else {
            showAlert("Analytics Disabled", "No usage data will be collected.")
        }
    }
    private func updateCrashReportingSetting(_ enabled: Bool) {
        UserDefaults.standard.set(enabled, forKey: "privacy.crashReporting")
        if enabled {
            showAlert("Crash Reporting Enabled", "Crash reports will help us fix bugs faster.")
        } else {
            showAlert("Crash Reporting Disabled", "Crash reports will not be sent.")
        }
    }
    private func showAlert(_ title: String, _ message: String) {
        alertTitle = title
        alertMessage = message
        showingAlert = true
    }
}
// MARK: - Privacy Toggle Row Component
struct PrivacyToggleRow: View {
    let icon: String
    let title: String
    let subtitle: String
    @Binding var isEnabled: Bool
    let iconColor: Color
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .frame(width: 24)
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            Spacer()
            Toggle("", isOn: $isEnabled)
        }
        .padding(.vertical, 2)
    }
}
// MARK: - Supporting Views
struct DataExportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isExporting = false
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Image(systemName: "square.and.arrow.up.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                VStack(spacing: 8) {
                    Text("Export Your Data")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("Download a complete copy of all your pet data including photos, memories, and health records.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                Button(action: startExport) {
                    HStack {
                        if isExporting {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        }
                        Text("Start Export")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(isExporting)
                Spacer()
            }
            .padding()
            .navigationTitle("Export Data")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    private func startExport() {
        isExporting = true
        Task {
            do {
                let userData = try await exportUserData()
                // Create and share the export file
                await MainActor.run {
                    shareExportedData(userData)
                    isExporting = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    print("❌ Failed to export data: \(error)")
                    isExporting = false
                }
            }
        }
    }
    private func exportUserData() async throws -> Data {
        // Create a comprehensive data export
        let exportData: [String: Any] = [
            "export_date": ISO8601DateFormatter().string(from: Date()),
            "app_version": "1.0.0",
            "user_profile": [
                "email": "<EMAIL>", // Would get from auth service
                "export_note": "Complete PetCapsule data export"
            ],
            "pets": [], // Would fetch actual pet data
            "memories": [], // Would fetch actual memory data
            "settings": [
                "biometric_enabled": false,
                "data_sync_enabled": UserDefaults.standard.bool(forKey: "privacy.dataSync"),
                "location_enabled": UserDefaults.standard.bool(forKey: "privacy.locationSharing"),
                "analytics_enabled": UserDefaults.standard.bool(forKey: "privacy.analytics"),
                "crash_reporting_enabled": UserDefaults.standard.bool(forKey: "privacy.crashReporting")
            ]
        ]
        return try JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted)
    }
    private func shareExportedData(_ data: Data) {
        let fileName = "PetCapsule_Export_\(DateFormatter.yyyyMMdd_HHmmss.string(from: Date())).json"
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        do {
            try data.write(to: tempURL)
            DispatchQueue.main.async {
                // Use iOS native share sheet properly
                let activityViewController = UIActivityViewController(
                    activityItems: [tempURL],
                    applicationActivities: nil
                )
                // Configure for iPad
                if let popover = activityViewController.popoverPresentationController {
                    popover.sourceView = UIApplication.shared.windows.first
                    popover.sourceRect = CGRect(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2, width: 0, height: 0)
                    popover.permittedArrowDirections = []
                }
                // Present safely
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let window = windowScene.windows.first,
                   let rootViewController = window.rootViewController {
                    // Find the topmost view controller
                    var topViewController = rootViewController
                    while let presentedViewController = topViewController.presentedViewController {
                        topViewController = presentedViewController
                    }
                    // Only present if nothing else is being presented
                    if topViewController.presentedViewController == nil {
                        topViewController.present(activityViewController, animated: true) {
                            // Cleanup temp file after sharing
                            DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                                try? FileManager.default.removeItem(at: tempURL)
                            }
                        }
                    } else {
                        print("❌ Cannot present share sheet - another view is already being presented")
                        // Cleanup temp file
                        try? FileManager.default.removeItem(at: tempURL)
                    }
                } else {
                    print("❌ Could not find root view controller for sharing")
                    // Cleanup temp file
                    try? FileManager.default.removeItem(at: tempURL)
                }
            }
        } catch {
            print("❌ Failed to save export file: \(error)")
        }
    }
}
// MARK: - Helper Extensions
private extension DateFormatter {
    static let yyyyMMdd_HHmmss: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        return formatter
    }()
}
struct DataDeletionView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authService = AuthenticationService()
    @State private var confirmationText = ""
    @State private var isDeleting = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    private let requiredText = "DELETE MY DATA"
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Image(systemName: "trash.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.red)
                VStack(spacing: 8) {
                    Text("Delete All Data")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                    Text("This action cannot be undone. All your pet data, photos, and memories will be permanently deleted.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                VStack(spacing: 16) {
                    Text("Type '\(requiredText)' to confirm:")
                        .font(.body)
                        .fontWeight(.medium)
                    TextField("Confirmation", text: $confirmationText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .autocapitalization(.allCharacters)
                }
                Button(action: deleteData) {
                    HStack {
                        if isDeleting {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.white)
                        }
                        Text("Delete All Data")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(confirmationText == requiredText ? Color.red : Color.gray.opacity(0.3))
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(confirmationText != requiredText || isDeleting)
                Spacer()
            }
            .padding()
            .navigationTitle("Delete Data")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("Data Deletion Result", isPresented: $showingAlert) {
                Button("OK") {
                    dismiss()
                }
            } message: {
                Text(alertMessage)
            }
        }
    }
    private func deleteData() {
        isDeleting = true
        Task {
            do {
                let success = try await authService.deleteUserAccount()
                await MainActor.run {
                    isDeleting = false
                    if success {
                        dismiss()
                        // User deletion successful - app should handle sign out
                    } else {
                        // Show error message
                        alertMessage = "Failed to delete user account"
                        showingAlert = true
                    }
                }
            } catch {
                await MainActor.run {
                    isDeleting = false
                    print("Error deleting account: \(error)")
                }
            }
        }
    }
}
struct PrivacyPolicyDetailView: View {
    @Environment(\.dismiss) private var dismiss
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Privacy Policy")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    privacyContent
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    private var privacyContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Your Privacy Matters")
                .font(.title2)
                .fontWeight(.semibold)
            Text("PetTime Capsule is designed with privacy at its core. We believe your pet data should remain private and secure.")
                .font(.body)
            Text("Data Collection")
                .font(.headline)
                .padding(.top)
            Text("We only collect data necessary to provide our services. This includes pet information you provide, photos you upload, and basic usage analytics to improve the app.")
                .font(.body)
            Text("Data Security")
                .font(.headline)
                .padding(.top)
            Text("All data is encrypted in transit and at rest. We use industry-standard security measures to protect your information.")
                .font(.body)
            Text("Your Rights")
                .font(.headline)
                .padding(.top)
            Text("You have the right to access, export, or delete your data at any time. You can manage these settings in the Privacy & Security section.")
                .font(.body)
        }
    }
}
#Preview {
    PrivacySettingsView()
}
