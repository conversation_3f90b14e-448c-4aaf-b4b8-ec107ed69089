//
//  SocialDashboardView.swift
//  PetCapsule
//
//  Social dashboard with playdates, community posts, and social connections
//

import SwiftUI

struct SocialDashboardView: View {
    @StateObject private var socialService = SocialInteractionService.shared
    @StateObject private var communityEventsService = CommunityEventsService.shared
    @State private var selectedTab = 0
    @State private var showingCreatePlaydate = false
    @State private var showingCreatePost = false
    @State private var showingNearbyPets = false
    @State private var showingSocialProfile = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Social Stats Header
                socialStatsHeader
                
                // Tab Navigation
                tabNavigation
                
                // Content
                TabView(selection: $selectedTab) {
                    // Social Feed Tab
                    socialFeedTab
                        .tag(0)
                    
                    // Playdates Tab
                    playdatesTab
                        .tag(1)
                    
                    // Friends Tab
                    friendsTab
                        .tag(2)
                    
                    // Discover Tab
                    discoverTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Pet Community")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { showingSocialProfile = true }) {
                        Image(systemName: "person.circle")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Create Post") { showingCreatePost = true }
                        Button("Create Playdate") { showingCreatePlaydate = true }
                        Button("Find Nearby Pets") { showingNearbyPets = true }
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingCreatePlaydate) {
                CreatePlaydateView()
            }
            .sheet(isPresented: $showingCreatePost) {
                CreatePostView()
            }
            .sheet(isPresented: $showingNearbyPets) {
                NearbyPetsView()
            }
            .sheet(isPresented: $showingSocialProfile) {
                SocialProfileView()
            }
            .onAppear {
                Task {
                    await socialService.discoverNearbyPets()
                    await socialService.generateSocialRecommendations()
                }
            }
        }
    }
    
    // MARK: - Social Stats Header
    
    private var socialStatsHeader: some View {
        VStack(spacing: 12) {
            if let metrics = socialService.socialMetrics {
                HStack(spacing: 20) {
                    SocialStatCard(
                        title: "Friends",
                        value: "\(metrics.friendsCount)",
                        icon: "person.2.fill",
                        color: .blue
                    )
                    
                    SocialStatCard(
                        title: "Playdates",
                        value: "\(metrics.playdatesCount)",
                        icon: "gamecontroller.fill",
                        color: .green
                    )
                    
                    SocialStatCard(
                        title: "Posts",
                        value: "\(metrics.postsCount)",
                        icon: "square.and.pencil",
                        color: .orange
                    )
                    
                    SocialStatCard(
                        title: "Engagement",
                        value: "\(Int(metrics.engagementScore * 100))%",
                        icon: "heart.fill",
                        color: .red
                    )
                }
            }
            
            // Social Recommendations
            if !socialService.socialRecommendations.isEmpty {
                socialRecommendationsSection
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal)
    }
    
    // MARK: - Social Recommendations Section
    
    private var socialRecommendationsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Recommendations")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(socialService.socialRecommendations.prefix(3)) { recommendation in
                        SocialRecommendationCard(recommendation: recommendation) {
                            handleRecommendationAction(recommendation)
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Tab Navigation
    
    private var tabNavigation: some View {
        HStack(spacing: 0) {
            Button("Feed") {
                selectedTab = 0
            }
            .foregroundColor(selectedTab == 0 ? .blue : .primary)
            .frame(maxWidth: .infinity)
            
            Button("Playdates") {
                selectedTab = 1
            }
            .foregroundColor(selectedTab == 1 ? .blue : .primary)
            .frame(maxWidth: .infinity)
            
            Button("Friends") {
                selectedTab = 2
            }
            .foregroundColor(selectedTab == 2 ? .blue : .primary)
            .frame(maxWidth: .infinity)
            
            Button("Discover") {
                selectedTab = 3
            }
            .foregroundColor(selectedTab == 3 ? .blue : .primary)
            .frame(maxWidth: .infinity)
        }
        .background(Color(.systemBackground))
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color(.systemGray4)),
            alignment: .bottom
        )
    }
    
    // MARK: - Social Feed Tab
    
    private var socialFeedTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if socialService.communityPosts.isEmpty {
                    emptySocialFeedView
                } else {
                    ForEach(socialService.communityPosts) { post in
                        // Convert CommunityPost to SharedCommunityPost for compatibility
                        let sharedPost = SharedCommunityPost(
                            id: post.id,
                            authorId: post.authorId,
                            authorName: post.authorName,
                            content: post.content,
                            createdAt: post.createdAt,
                            likesCount: post.likesCount,
                            commentsCount: post.commentsCount
                        )
                        CommunityPostCard(post: sharedPost) { action in
                            handlePostAction(sharedPost, action: action)
                        }
                    }
                }
            }
            .padding()
        }
        .refreshable {
            // Refresh social feed
        }
    }
    
    // MARK: - Playdates Tab
    
    private var playdatesTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Upcoming Playdates
                let upcomingPlaydates = socialService.getUpcomingPlaydates()
                if !upcomingPlaydates.isEmpty {
                    upcomingPlaydatesSection(upcomingPlaydates)
                }
                
                // All Playdates
                if socialService.playdates.isEmpty {
                    emptyPlaydatesView
                } else {
                    allPlaydatesSection
                }
            }
            .padding()
        }
    }
    
    // MARK: - Friends Tab
    
    private var friendsTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Friend Requests
                let pendingRequests = socialService.getPendingFriendRequests()
                if !pendingRequests.isEmpty {
                    friendRequestsSection(pendingRequests)
                }
                
                // Friends List
                let friends = socialService.getFriends()
                if friends.isEmpty {
                    emptyFriendsView
                } else {
                    friendsListSection(friends)
                }
            }
            .padding()
        }
    }
    
    // MARK: - Discover Tab
    
    private var discoverTab: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Nearby Pets
                if !socialService.nearbyPets.isEmpty {
                    nearbyPetsSection
                }
                
                // Community Events
                communityEventsSection
                
                // Social Activities
                recentActivitiesSection
            }
            .padding()
        }
    }
    
    // MARK: - Section Views
    
    private func upcomingPlaydatesSection(_ playdates: [SharedPlaydate]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Upcoming Playdates")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Temporarily disabled - build issues
            Text("Playdates feature temporarily unavailable")
                .foregroundColor(.secondary)
        }
    }
    
    private var allPlaydatesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("All Playdates")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Temporarily disabled - build issues
            Text("All playdates feature temporarily unavailable")
                .foregroundColor(.secondary)
        }
    }
    
    private func friendRequestsSection(_ requests: [SharedSharedSharedSocialConnection]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Friend Requests")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(requests) { request in
                FriendRequestCard(request: request) { action in
                    handleFriendRequestAction(request, action: action)
                }
            }
        }
    }
    
    private func friendsListSection(_ friends: [SharedSharedSharedSocialConnection]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Friends")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Temporarily disabled - build issues
            Text("Friends feature temporarily unavailable")
                .foregroundColor(.secondary)
        }
    }
    
    private var nearbyPetsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Nearby Pets")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(socialService.nearbyPets.prefix(5)) { pet in
                        NearbyPetCard(pet: pet) {
                            // Send friend request or invite to playdate
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    private var communityEventsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Community Events")
                .font(.headline)
                .fontWeight(.semibold)
            
            let upcomingEvents = communityEventsService.getUpcomingEvents(limit: 3)
            
            if upcomingEvents.isEmpty {
                Text("No upcoming community events")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            } else {
                ForEach(upcomingEvents) { event in
                    CommunityEventCard(event: event) {
                        // Navigate to event details
                    }
                }
            }
        }
    }
    
    private var recentActivitiesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Activities")
                .font(.headline)
                .fontWeight(.semibold)
            
            let recentActivities = socialService.getRecentSocialActivities(limit: 5)
            
            if recentActivities.isEmpty {
                Text("No recent social activities")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            } else {
                ForEach(recentActivities) { activity in
                    SocialActivityRow(activity: activity)
                }
            }
        }
    }
    
    // MARK: - Empty State Views
    
    private var emptySocialFeedView: some View {
        SocialEmptyStateView(
            icon: "square.and.pencil",
            title: "No Posts Yet",
            message: "Share your pet's adventures with the community!",
            actionTitle: "Create Post"
        ) {
            showingCreatePost = true
        }
    }
    
    private var emptyPlaydatesView: some View {
        SocialEmptyStateView(
            icon: "gamecontroller",
            title: "No Playdates",
            message: "Organize playdates to help your pet socialize and have fun!",
            actionTitle: "Create Playdate"
        ) {
            showingCreatePlaydate = true
        }
    }
    
    private var emptyFriendsView: some View {
        SocialEmptyStateView(
            icon: "person.2",
            title: "No Friends Yet",
            message: "Connect with other pet owners in your area!",
            actionTitle: "Find Nearby Pets"
        ) {
            showingNearbyPets = true
        }
    }
    
    // MARK: - Action Handlers
    
    private func handleRecommendationAction(_ recommendation: SocialRecommendation) {
        switch recommendation.type {
        case .activity:
            // Navigate to activity
            break
        case .health:
            // Navigate to health
            break
        case .social:
            showingNearbyPets = true
        case .environmental:
            // Navigate to environmental
            break
        case .nutrition:
            // Navigate to nutrition
            break
        case .training:
            // Navigate to training
            break
        case .playdate:
            showingCreatePlaydate = true
        case .communityEvent:
            // Navigate to community events
            break
        case .socialConnection:
            showingNearbyPets = true
        case .petMatch:
            // Navigate to pet matching
            break
        case .groupActivity:
            // Navigate to group activities
            break
        }
    }
    
    private func handlePostAction(_ post: SharedCommunityPost, action: PostAction) {
        Task {
            switch action {
            case .like:
                try? await socialService.likePost(post.id)
            case .comment(let content):
                try? await socialService.addComment(to: post.id, content: content)
            case .share:
                // Handle share action
                break
            }
        }
    }
    
    private func handlePlaydateAction(_ playdate: SharedPlaydate, action: SharedPlaydateAction) {
        Task {
            switch action {
            case .join:
                // Create a simple pet info for the join request
                let petInfo = SharedPlaydatePetInfo(
                    id: "default-pet-id",
                    name: "My Pet",
                    breed: "Unknown",
                    age: 1,
                    size: "Medium",
                    temperament: "Friendly",
                    isVaccinated: true,
                    ownerName: "Pet Owner",
                    profileImageUrl: nil as String?
                )
                try? await socialService.joinPlaydate(playdate.id, petInfo: petInfo)
            case .leave:
                try? await socialService.leavePlaydate(playdate.id)
            case .cancel:
                try? await socialService.cancelPlaydate(playdate.id)
            case .complete:
                try? await socialService.completePlaydate(playdate.id)
            }
        }
    }
    
    private func handleFriendRequestAction(_ request: SharedSharedSharedSocialConnection, action: FriendRequestAction) {
        Task {
            switch action {
            case .accept:
                try? await socialService.acceptFriendRequest(request.id)
            case .decline:
                try? await socialService.declineFriendRequest(request.id)
            }
        }
    }
}

// MARK: - Supporting Views

struct SocialStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct SocialRecommendationCard: View {
    let recommendation: SocialRecommendation
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: recommendation.type.icon)
                        .foregroundColor(recommendation.priority.color)
                    
                    Spacer()
                    
                    Text(recommendation.priority.displayName)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(recommendation.priority.color.opacity(0.2))
                        .foregroundColor(recommendation.priority.color)
                        .cornerRadius(4)
                }
                
                Text(recommendation.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            .padding()
            .frame(width: 160)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Action Enums

enum PostAction {
    case like
    case comment(String)
    case share
}

enum FriendRequestAction {
    case accept
    case decline
}

// MARK: - Placeholder Detail Views

struct CreatePlaydateView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Create Playdate")
                    .font(.headline)
                
                Text("Playdate creation form would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("New Playdate")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct CreatePostView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Create Post")
                    .font(.headline)
                
                Text("Post creation form would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("New Post")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct NearbyPetsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Nearby Pets")
                    .font(.headline)
                
                Text("Nearby pets discovery would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Discover")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct SocialProfileView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Social Profile")
                    .font(.headline)
                
                Text("Social profile would go here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

// MARK: - Missing View Components (Placeholders)

struct CommunityPostCard: View {
    let post: SharedCommunityPost
    let onAction: (PostAction) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(post.authorName)
                    .font(.headline)
                Spacer()
                Text(post.createdAt, style: .relative)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Text(post.content)
                .font(.body)

            HStack {
                Button("Like") { onAction(.like) }
                Button("Comment") { onAction(.comment("")) }
                Button("Share") { onAction(.share) }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

struct PlaydateCard: View {
    let playdate: SharedPlaydate
    let onAction: (SharedPlaydateAction) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(playdate.title)
                .font(.headline)
            Text(playdate.description)
                .font(.subheadline)
                .foregroundColor(.secondary)

            Button("Join") { onAction(.join) }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

struct FriendRequestCard: View {
    let request: SharedSharedSharedSocialConnection
    let onAction: (FriendRequestAction) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(request.connectedUserName)
                .font(.headline)

            HStack {
                Button("Accept") { onAction(.accept) }
                Button("Decline") { onAction(.decline) }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

struct FriendCard: View {
    let friend: SharedSharedSharedSocialConnection
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                Text(friend.connectedUserName)
                    .font(.headline)
                Text("\(friend.mutualConnections) mutual connections")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
}

struct NearbyPetCard: View {
    let pet: NearbyPet
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                Text(pet.name)
                    .font(.headline)
                Text(pet.breed)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
}

struct CommunityEventCard: View {
    let event: CommunityEvent
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                Text(event.title)
                    .font(.headline)
                Text(event.description ?? "No description")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
}

struct SocialActivityRow: View {
    let activity: SocialActivity

    var body: some View {
        HStack {
            Text(activity.description)
                .font(.subheadline)
            Spacer()
            Text(activity.timestamp, style: .relative)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Empty State View

struct SocialEmptyStateView: View {
    let icon: String
    let title: String
    let message: String
    let actionTitle: String
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            Button(action: action) {
                Text(actionTitle)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

// MARK: - Previews

#if DEBUG
struct SocialDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        SocialDashboardView()
    }
}
#endif
