//
//  SubscriptionView.swift
//  PetCapsule
//
//  Premium subscription interface for $2M/month revenue
//

import SwiftUI

struct SubscriptionView: View {
    @StateObject private var premiumService = ComprehensivePremiumService.shared
    @StateObject private var subscriptionService = SubscriptionService.shared // Keep for plan data
    @State private var selectedPlan: SubscriptionPlan?
    @State private var showingPurchase = false
    @State private var showingSuccess = false
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.xl) {
                    if !premiumService.isSubscribed {
                        // Show upgrade flow for free users
                        PremiumHeroSection()
                        
                        SubscriptionPlansSection(
                            plans: filteredPlans,
                            selectedPlan: $selectedPlan,
                            onPlanSelected: { plan in
                                selectedPlan = plan
                                showingPurchase = true
                            }
                        )
                        
                        SocialProofSection()
                        FAQSection()
                    } else {
                        // Show subscription management for premium users
                        CurrentSubscriptionSection()
                        BillingHistorySection()
                        SubscriptionSettingsSection()
                        
                        // Revenue Metrics (for demo)
                        if premiumService.currentSubscription == .premiumPro {
                            RevenueMetricsCard()
                        }
                        
                        // Upgrade options for premium users
                        if premiumService.currentSubscription != .premiumPro {
                            VStack(alignment: .leading, spacing: Spacing.md) {
                                Text("Upgrade Your Plan")
                                    .font(.petTitle2)
                                    .fontWeight(.bold)
                                
                                SubscriptionPlansSection(
                                    plans: filteredPlans.filter { plan in
                                        // Only show plans higher than current
                                        return getSubscriptionTierOrder(plan.id) > getSubscriptionTierOrder(premiumService.currentSubscription.rawValue)
                                    },
                                    selectedPlan: $selectedPlan,
                                    onPlanSelected: { plan in
                                        selectedPlan = plan
                                        showingPurchase = true
                                    }
                                )
                            }
                        }
                    }
                }
                .padding(.horizontal, Spacing.lg)
                .padding(.top, Spacing.md)
            }
            .navigationTitle(subscriptionService.subscriptionStatus == .pawStarter ? "Unlock More Love" : "Subscription & Billing")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") { dismiss() }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Restore") {
                        Task {
                            await premiumService.restorePurchases()
                        }
                    }
                    .font(.petCallout)
                }
            }
            .sheet(isPresented: $showingPurchase) {
                if let plan = selectedPlan {
                    PurchaseConfirmationView(plan: plan) { success in
                        showingPurchase = false
                        if success {
                            showingSuccess = true
                        }
                    }
                }
            }
            .sheet(isPresented: $showingSuccess) {
                PurchaseSuccessView()
            }
        }
    }
    
    // Filter out lifetime plan
    private var filteredPlans: [SubscriptionPlan] {
        subscriptionService.availablePlans.filter { !$0.isLifetime }
    }
    
    // Helper function to determine subscription tier order
    private func getSubscriptionTierOrder(_ tierId: String) -> Int {
        switch tierId {
        case "paw_starter": return 0
        case "growing_bond": return 1
        case "family_circle": return 2
        case "premium_pro": return 3
        default: return 0
        }
    }
}

struct PremiumHeroSection: View {
    var body: some View {
        VStack(spacing: Spacing.lg) {
            // Premium Badge
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.petAccent)
                Text("AI-POWERED PET CARE")
                    .font(.petCaption)
                    .fontWeight(.bold)
                    .foregroundColor(.petAccent)
            }
            .padding(.horizontal, Spacing.md)
            .padding(.vertical, Spacing.xs)
            .background(Color.petAccent.opacity(0.15))
            .cornerRadius(CornerRadius.sm)

            // Main Headline
            VStack(spacing: Spacing.sm) {
                Text("4 Specialized AI Agents")
                    .font(.petTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                Text("From emergency protocols to nutrition optimization - get expert pet care guidance with Apple Intelligence privacy protection.")
                    .font(.petBody)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            // Key AI Agents Preview (actual implemented agents)
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: Spacing.md) {
                AIAgentPill(icon: "crown.fill", title: "Pet Master", subtitle: "Ultimate AI companion")
                AIAgentPill(icon: "heart.fill", title: "Health Guardian", subtitle: "Emergency & medical")
                AIAgentPill(icon: "leaf.fill", title: "Dr. Nutrition", subtitle: "Diet optimization")
                AIAgentPill(icon: "figure.walk.circle.fill", title: "Trainer Pro", subtitle: "Training & behavior")
            }
            
            // Apple Ecosystem Integration (only implemented features)
            HStack(spacing: Spacing.lg) {
                BenefitPill(icon: "applewatch", text: "Apple Watch")
                BenefitPill(icon: "iphone", text: "Apple Intelligence")
                BenefitPill(icon: "icloud.fill", text: "iCloud Sync")
            }
        }
        .padding(Spacing.xl)
        .background(
            LinearGradient(
                colors: [Color.petAccent.opacity(0.1), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(CornerRadius.lg)
    }
}

struct BenefitPill: View {
    let icon: String
    let text: String

    var body: some View {
        VStack(spacing: Spacing.xs) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.petAccent)

            Text(text)
                .font(.petCaption)
                .foregroundColor(.primary)
        }
    }
}

struct RevenueMetricsCard: View {
    @StateObject private var subscriptionService = SubscriptionService.shared

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                Text("Revenue Dashboard")
                    .font(.petHeadline)
                    .foregroundColor(.primary)
                Spacer()
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: Spacing.md) {
                SubscriptionMetricCard(
                    title: "Monthly Revenue",
                    value: String(format: "$%.0f", subscriptionService.revenue.monthlyRevenue),
                    change: String(format: "+%.1f%%", subscriptionService.revenue.revenueGrowth),
                    color: .green
                )

                SubscriptionMetricCard(
                    title: "Active Users",
                    value: "\(subscriptionService.revenue.activeSubscribers)",
                    change: "+12.5%",
                    color: .blue
                )

                SubscriptionMetricCard(
                    title: "ARPU",
                    value: String(format: "$%.0f", subscriptionService.revenue.averageRevenuePerUser),
                    change: "+8.3%",
                    color: .purple
                )

                SubscriptionMetricCard(
                    title: "Churn Rate",
                    value: String(format: "%.1f%%", subscriptionService.revenue.churnRate),
                    change: "-2.1%",
                    color: .orange
                )
            }
        }
        .padding(Spacing.lg)
        .background(Color.white)
        .cornerRadius(CornerRadius.lg)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct SubscriptionMetricCard: View {
    let title: String
    let value: String
    let change: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.xs) {
            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)

            Text(value)
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(change)
                .font(.petCaption)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(Spacing.md)
        .background(color.opacity(0.1))
        .cornerRadius(CornerRadius.md)
    }
}

struct SubscriptionPlansSection: View {
    let plans: [SubscriptionPlan]
    @Binding var selectedPlan: SubscriptionPlan?
    let onPlanSelected: (SubscriptionPlan) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("Choose Your Plan")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: Spacing.md) {
                ForEach(plans) { plan in
                    SubscriptionPlanCard(
                        plan: plan,
                        isSelected: selectedPlan?.id == plan.id,
                        onTap: { 
                            if plan.price > 0 {
                                onPlanSelected(plan) 
                            }
                        }
                    )
                }
            }
        }
    }
}

struct SubscriptionPlanCard: View {
    let plan: SubscriptionPlan
    let isSelected: Bool
    let onTap: () -> Void
    @State private var isExpanded = false
    @State private var isAIExpanded = false

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: Spacing.md) {
                // Header with emoji and name
                HStack {
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        HStack {
                            Text(planEmoji)
                                .font(.title2)
                            
                            Text(plan.name)
                                .font(.petTitle3)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)

                            if plan.popularBadge {
                                Text("MOST POPULAR")
                                    .font(.caption2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, Spacing.sm)
                                    .padding(.vertical, Spacing.xs)
                                    .background(Color.petAccent)
                                    .cornerRadius(CornerRadius.sm)
                            }
                            
                            if plan.isUltimate {
                                Text("ULTIMATE")
                                    .font(.caption2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, Spacing.sm)
                                    .padding(.vertical, Spacing.xs)
                                    .background(Color.purple)
                                    .cornerRadius(CornerRadius.sm)
                            }
                        }

                        Text(plan.formattedPrice)
                            .font(.petTitle2)
                            .fontWeight(.bold)
                            .foregroundColor(.petAccent)
                            
                        if let subtitle = plan.subtitle {
                            Text(subtitle)
                                .font(.petCaption)
                                .foregroundColor(.secondary)
                        }
                        
                        // Pet limit and storage info
                        HStack(spacing: Spacing.md) {
                            HStack(spacing: Spacing.xs) {
                                Image(systemName: "pawprint.fill")
                                    .font(.caption)
                                    .foregroundColor(.petAccent)
                                Text("\(plan.maxPets) pet\(plan.maxPets > 1 ? "s" : "")")
                                    .font(.petCaption)
                                    .foregroundColor(.secondary)
                            }
                            
                            if let storageInfo = getStorageInfo() {
                                HStack(spacing: Spacing.xs) {
                                    Image(systemName: "icloud.fill")
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                    Text(storageInfo)
                                        .font(.petCaption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }

                    Spacer()

                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.petAccent)
                    }
                }

                // AI Features Section
                if !plan.aiFeatures.isEmpty {
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        HStack {
                            Image(systemName: "brain.head.profile")
                                .font(.caption)
                                .foregroundColor(.purple)
                            Text("AI Features")
                                .font(.petCaption)
                                .fontWeight(.semibold)
                                .foregroundColor(.purple)
                        }
                        
                        ForEach(isAIExpanded ? plan.aiFeatures : Array(plan.aiFeatures.prefix(3)), id: \.self) { feature in
                            HStack(spacing: Spacing.sm) {
                                Circle()
                                    .fill(Color.purple.opacity(0.3))
                                    .frame(width: 4, height: 4)

                                Text(feature)
                                    .font(.petCallout)
                                    .foregroundColor(.primary)

                                Spacer()
                            }
                        }
                        
                        if plan.aiFeatures.count > 3 {
                            Button(action: { 
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    isAIExpanded.toggle()
                                }
                            }) {
                                HStack(spacing: Spacing.xs) {
                                    Text(isAIExpanded ? "Show less" : "+ \(plan.aiFeatures.count - 3) more AI features")
                                        .font(.petCaption)
                                        .foregroundColor(.purple)
                                    
                                    Image(systemName: isAIExpanded ? "chevron.up" : "chevron.down")
                                        .font(.caption2)
                                        .foregroundColor(.purple)
                                }
                            }
                        }
                    }
                    .padding(Spacing.sm)
                    .background(Color.purple.opacity(0.05))
                    .cornerRadius(CornerRadius.sm)
                }

                // Core Features
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    ForEach(isExpanded ? plan.features : Array(plan.features.prefix(4)), id: \.self) { feature in
                        HStack(spacing: Spacing.sm) {
                            Image(systemName: "checkmark")
                                .font(.caption)
                                .foregroundColor(.green)

                            Text(feature)
                                .font(.petCallout)
                                .foregroundColor(.primary)

                            Spacer()
                        }
                    }

                    if plan.features.count > 4 {
                        Button(action: { 
                            withAnimation(.easeInOut(duration: 0.3)) {
                                isExpanded.toggle()
                            }
                        }) {
                            HStack(spacing: Spacing.xs) {
                                Text(isExpanded ? "Show less" : "+ \(plan.features.count - 4) more features")
                                    .font(.petCaption)
                                    .foregroundColor(.blue)
                                
                                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                    .font(.caption2)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }

                // Value proposition for each tier
                if let valueProps = getValueProposition() {
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        Text("Perfect For")
                            .font(.petCaption)
                            .fontWeight(.semibold)
                            .foregroundColor(.petAccent)

                        Text(valueProps)
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }
                    .padding(Spacing.sm)
                    .background(Color.petAccent.opacity(0.05))
                    .cornerRadius(CornerRadius.sm)
                }
            }
            .padding(Spacing.lg)
            .background(Color.white)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.lg)
                    .stroke(isSelected ? Color.petAccent : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
            )
            .cornerRadius(CornerRadius.lg)
            .shadow(color: .black.opacity(isSelected ? 0.2 : 0.1), radius: isSelected ? 8 : 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var planEmoji: String {
        switch plan.id {
        case "paw_starter":
            return "🐾"
        case "growing_bond":
            return "🎯"
        case "family_circle":
            return "👨‍👩‍👧‍👦"
        case "alpha_pack":
            return "🦮"
        case "lifetime_legacy":
            return "💎"
        default:
            return "🐕"
        }
    }
    
    private func getStorageInfo() -> String? {
        switch plan.id {
        case "paw_starter":
            return "100MB"
        case "growing_bond":
            return "2GB"
        case "family_circle":
            return "10GB"
        case "alpha_pack":
            return "50GB"
        case "lifetime_legacy":
            return "Unlimited"
        default:
            return nil
        }
    }
    
    private func getValueProposition() -> String? {
        switch plan.id {
        case "paw_starter":
            return "First-time pet parents exploring basic digital pet care"
        case "growing_bond":
            return "Pet parents wanting AI-powered health insights and emergency preparedness"
        case "family_circle":
            return "Multi-pet households needing comprehensive family coordination and advanced features"
        case "alpha_pack":
            return "Large pet families, breeders, or pet enthusiasts wanting ultimate AI capabilities"
        case "lifetime_legacy":
            return "Pet parents wanting lifetime protection and exclusive access to all future features"
        default:
            return nil
        }
    }
}





struct SocialProofSection: View {
    var body: some View {
        VStack(spacing: Spacing.lg) {
            Text("Trusted by Pet Parents Worldwide")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: Spacing.md) {
                TestimonialCard(
                    name: "Sarah M.",
                    text: "PetCapsule's AI helped me create the most beautiful memorial video for my beloved Max. Worth every penny.",
                    rating: 5
                )

                TestimonialCard(
                    name: "Dr. Johnson, DVM",
                    text: "As a veterinarian, I recommend PetCapsule to all my clients. The health tracking features are incredible.",
                    rating: 5
                )

                TestimonialCard(
                    name: "Happy Paws Rescue",
                    text: "We've increased our adoption rates by 40% using PetCapsule's professional features.",
                    rating: 5
                )
            }
        }
    }
}

struct TestimonialCard: View {
    let name: String
    let text: String
    let rating: Int

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            HStack {
                ForEach(0..<rating, id: \.self) { _ in
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.caption)
                }
            }

            Text(text)
                .font(.petCallout)
                .foregroundColor(.primary)
                .italic()

            Text("- \(name)")
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct FAQSection: View {
    @State private var expandedFAQ: Int?

    private let faqs = [
        FAQ(question: "How does the AI memory curation work?", answer: "Our advanced AI analyzes your pet's photos, videos, and activities to automatically detect milestones, suggest memory prompts, and create personalized content."),
        FAQ(question: "Can I cancel anytime?", answer: "Yes, you can cancel your subscription at any time. You'll continue to have access to premium features until the end of your billing period."),
        FAQ(question: "What's included in the Professional plan?", answer: "The Professional plan includes everything in Family plus business tools, client management, breeding records, API access, and white-label options perfect for veterinarians, breeders, and pet businesses."),
        FAQ(question: "Is my data secure?", answer: "Absolutely. We use enterprise-grade encryption and security measures to protect your precious memories. Your data is backed up across multiple secure servers.")
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("Frequently Asked Questions")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(spacing: Spacing.sm) {
                ForEach(Array(faqs.enumerated()), id: \.offset) { index, faq in
                    FAQRow(
                        faq: faq,
                        isExpanded: expandedFAQ == index,
                        onTap: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                expandedFAQ = expandedFAQ == index ? nil : index
                            }
                        }
                    )
                }
            }
        }
    }
}

struct FAQ {
    let question: String
    let answer: String
}

struct FAQRow: View {
    let faq: FAQ
    let isExpanded: Bool
    let onTap: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Button(action: onTap) {
                HStack {
                    Text(faq.question)
                        .font(.petCallout)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            if isExpanded {
                Text(faq.answer)
                    .font(.petCallout)
                    .foregroundColor(.secondary)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.md)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct AIAgentPill: View {
    let icon: String
    let title: String
    let subtitle: String

    var body: some View {
        VStack(spacing: Spacing.xs) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.petAccent)

            Text(title)
                .font(.petCaption)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
}

// MARK: - Subscription Management Sections for Premium Users

struct CurrentSubscriptionSection: View {
    @StateObject private var subscriptionService = SubscriptionService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("Current Subscription")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: Spacing.md) {
                HStack {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 12, height: 12)
                    
                    Text("Active")
                        .font(.petCallout)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                    
                    Spacer()
                    
                    Text(getCurrentPlanName())
                        .font(.petHeadline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                }
                
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    HStack {
                        Text("Plan:")
                            .foregroundColor(.secondary)
                        Spacer()
                        Text(getCurrentPlanPrice())
                            .fontWeight(.semibold)
                    }
                    
                    HStack {
                        Text("Next billing:")
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("December 17, 2024")
                            .fontWeight(.semibold)
                    }
                    
                    HStack {
                        Text("Pet limit:")
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("\(getCurrentPetLimit()) pets")
                            .fontWeight(.semibold)
                    }
                }
                .font(.petCallout)
            }
            .padding(Spacing.lg)
            .background(Color.green.opacity(0.05))
            .cornerRadius(CornerRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.md)
                    .stroke(Color.green.opacity(0.3), lineWidth: 1)
            )
        }
    }
    
    private func getCurrentPlanName() -> String {
        switch subscriptionService.subscriptionStatus {
        case .pawStarter: return "Paw Starter"
        case .growingBond: return "Growing Bond"
        case .familyCircle: return "Family Circle"
        case .premiumPro: return "Premium Pro"
        case .expired: return "Expired"
        }
    }
    
    private func getCurrentPlanPrice() -> String {
        switch subscriptionService.subscriptionStatus {
        case .pawStarter: return "Free"
        case .growingBond: return "$9.99/month"
        case .familyCircle: return "$14.99/month"
        case .premiumPro: return "$19.99/month"
        case .expired: return "Expired"
        }
    }
    
    private func getCurrentPetLimit() -> Int {
        switch subscriptionService.subscriptionStatus {
        case .pawStarter: return 1
        case .growingBond: return 2
        case .familyCircle: return 5
        case .premiumPro: return 10
        case .expired: return 1
        }
    }
}

struct BillingHistorySection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("Billing History")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: Spacing.sm) {
                BillingHistoryRow(
                    date: "Nov 17, 2024",
                    amount: "$14.99",
                    status: "Paid",
                    plan: "Family Circle"
                )
                
                BillingHistoryRow(
                    date: "Oct 17, 2024",
                    amount: "$14.99",
                    status: "Paid",
                    plan: "Family Circle"
                )
                
                BillingHistoryRow(
                    date: "Sep 17, 2024",
                    amount: "$9.99",
                    status: "Paid",
                    plan: "Growing Bond"
                )
            }
            
            Button("View All Billing History") {
                // Open detailed billing history
            }
            .font(.petCallout)
            .foregroundColor(.blue)
        }
    }
}

struct BillingHistoryRow: View {
    let date: String
    let amount: String
    let status: String
    let plan: String
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(date)
                    .font(.petCallout)
                    .fontWeight(.medium)
                
                Text(plan)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: Spacing.xs) {
                Text(amount)
                    .font(.petCallout)
                    .fontWeight(.semibold)
                
                HStack(spacing: Spacing.xs) {
                    Circle()
                        .fill(status == "Paid" ? Color.green : Color.orange)
                        .frame(width: 6, height: 6)
                    
                    Text(status)
                        .font(.petCaption)
                        .foregroundColor(status == "Paid" ? .green : .orange)
                }
            }
        }
        .padding(Spacing.md)
        .background(Color.white)
        .cornerRadius(CornerRadius.sm)
        .overlay(
            RoundedRectangle(cornerRadius: CornerRadius.sm)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
}

struct SubscriptionSettingsSection: View {
    @State private var showingCancelAlert = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.lg) {
            Text("Subscription Settings")
                .font(.petTitle2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            VStack(spacing: Spacing.sm) {
                SubscriptionSettingRow(
                    icon: "creditcard.fill",
                    title: "Update Payment Method",
                    subtitle: "Change your credit card or payment method",
                    action: {
                        // Update payment method
                    }
                )
                
                SubscriptionSettingRow(
                    icon: "envelope.fill",
                    title: "Update Billing Email",
                    subtitle: "Change the email for billing notifications",
                    action: {
                        // Update billing email
                    }
                )
                
                SubscriptionSettingRow(
                    icon: "doc.text.fill",
                    title: "Download Invoices",
                    subtitle: "Get PDF copies of your payment receipts",
                    action: {
                        // Download invoices
                    }
                )
                
                SubscriptionSettingRow(
                    icon: "xmark.circle.fill",
                    title: "Cancel Subscription",
                    subtitle: "Cancel your subscription anytime",
                    action: { showingCancelAlert = true },
                    isDestructive: true
                )
            }
        }
        .alert("Cancel Subscription", isPresented: $showingCancelAlert) {
            Button("Keep Subscription", role: .cancel) { }
            Button("Cancel", role: .destructive) {
                // Handle cancellation
            }
        } message: {
            Text("Are you sure you want to cancel your subscription? You'll lose access to premium features at the end of your billing period.")
        }
    }
}

struct SubscriptionSettingRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    var isDestructive: Bool = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: Spacing.md) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(isDestructive ? .red : .blue)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: Spacing.xs) {
                    Text(title)
                        .font(.petCallout)
                        .fontWeight(.medium)
                        .foregroundColor(isDestructive ? .red : .primary)
                    
                    Text(subtitle)
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(Spacing.md)
            .background(Color.white)
            .cornerRadius(CornerRadius.sm)
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.sm)
                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    SubscriptionView()
}
