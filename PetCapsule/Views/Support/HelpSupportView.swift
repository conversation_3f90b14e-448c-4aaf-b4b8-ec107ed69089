//
//  HelpSupportView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
#if canImport(MessageUI)
import MessageUI
#endif

struct HelpSupportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var selectedCategory: HelpCategory = .general
    @State private var showContactForm = false
    @State private var showEmailComposer = false
    @State private var animateContent = false

    private let helpCategories: [HelpCategory] = [
        .general, .aiAgents, .petManagement, .memories, .premium, .technical
    ]

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Search Bar
                    searchSection

                    // Email Support Section
                    emailSupportSection

                    // Categories
                    categoriesSection

                    // FAQ Section
                    faqSection

                    // Contact Form Section
                    contactFormSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Help & Support")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animateContent = true
                }
            }
        }
        .sheet(isPresented: $showContactForm) {
            ContactSupportView()
        }
        .sheet(isPresented: $showEmailComposer) {
            if MFMailComposeViewController.canSendMail() {
                MailComposeView()
            } else {
                Text("Mail not configured")
                    .padding()
            }
        }
    }

    // MARK: - Search Section

    private var searchSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search help articles...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button("Clear") {
                        searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateContent)
    }

    // MARK: - Email Support Section

    private var emailSupportSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Get Help")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
            }

            VStack(spacing: 12) {
                supportOption(
                    icon: "envelope.fill",
                    title: "Email Support",
                    subtitle: "Get personalized help via email",
                    color: .blue
                ) {
                    showEmailComposer = true
                }

                supportOption(
                    icon: "doc.text.fill",
                    title: "Contact Form",
                    subtitle: "Send us a detailed message",
                    color: .green
                ) {
                    showContactForm = true
                }
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateContent)
    }

    // MARK: - Categories

    private var categoriesSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Help Categories")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(helpCategories, id: \.self) { category in
                        categoryChip(category)
                    }
                }
                .padding(.horizontal)
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateContent)
    }

    // MARK: - FAQ Section

    private var faqSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Frequently Asked Questions")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
            }

            VStack(spacing: 12) {
                ForEach(getFAQsForCategory(selectedCategory), id: \.question) { faq in
                    HelpFAQRow(faq: faq)
                }
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateContent)
    }

    // MARK: - Contact Form Section

    private var contactFormSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Need More Help?")
                    .font(.headline)
                    .fontWeight(.bold)
                Spacer()
            }

            Text("Can't find what you're looking for? Our support team is here to help!")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button(action: { showContactForm = true }) {
                HStack {
                    Image(systemName: "paperplane.fill")
                        .font(.headline)
                    Text("Contact Support Team")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.9)
        .opacity(animateContent ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.5), value: animateContent)
    }

    // MARK: - Helper Views

    private func categoryChip(_ category: HelpCategory) -> some View {
        Button(action: { selectedCategory = category }) {
            Text(category.displayName)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(selectedCategory == category ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(selectedCategory == category ? Color.blue : Color(.systemGray6))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func supportOption(
        icon: String,
        title: String,
        subtitle: String,
        color: Color,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 16) {
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(color.opacity(0.15))
                        .frame(width: 32, height: 32)

                    Image(systemName: icon)
                        .font(.system(size: 14))
                        .foregroundColor(color)
                }

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Helper Methods

    private func getFAQsForCategory(_ category: HelpCategory) -> [HelpFAQ] {
        switch category {
        case .general:
            return [
                HelpFAQ(question: "How do I get started with PetCapsule?", answer: "Download the app, create an account, and add your first pet profile to begin capturing memories and accessing AI support."),
                HelpFAQ(question: "Is PetCapsule free to use?", answer: "PetCapsule offers both free and premium features. Basic pet profiles and memory storage are free, while advanced AI features and unlimited storage require a subscription."),
                HelpFAQ(question: "How secure is my pet data?", answer: "We use enterprise-grade encryption and secure cloud storage to protect all your pet data and memories. Apple Intelligence processing happens locally on your device for complete privacy."),
                HelpFAQ(question: "What makes PetCapsule different?", answer: "PetCapsule combines AI-powered pet care with comprehensive memory preservation, featuring 4 specialized AI agents, Apple Intelligence integration, and seamless Apple ecosystem support.")
            ]
        case .aiAgents:
            return [
                HelpFAQ(question: "What are the 8 AI agents?", answer: "Pet Master (👑) - Ultimate AI companion, Health Guardian (🏥) - Emergency & medical care, Dr. Nutrition (🥗) - Diet optimization, Trainer Pro (🎾) - Training & behavior, Style Guru (✂️) - Grooming & hygiene, Shopping Assistant (🛍️) - Product recommendations, Insurance Advisor (🛡️) - Insurance guidance, and Wellness Coach (🏠) - Holistic wellness."),
                HelpFAQ(question: "How do I chat with AI agents?", answer: "Go to Pet Support tab, tap 'Ask Pet AI Support Agents', select your preferred agent, and start chatting. Each agent specializes in different aspects of pet care."),
                HelpFAQ(question: "Do AI agents know about my pets?", answer: "Yes! All AI agents have access to your pet profiles, health records, and memories to provide personalized advice based on your specific pets."),
                HelpFAQ(question: "Are AI conversations private?", answer: "Absolutely. All AI processing happens locally on your device using Apple Intelligence. Your conversations are stored securely and never shared."),
                HelpFAQ(question: "Can I use voice with AI agents?", answer: "Yes! You can talk to AI agents using Siri shortcuts like 'Hey Siri, ask PetCapsule about my pet's health' for hands-free assistance.")
            ]
        case .petManagement:
            return [
                HelpFAQ(question: "How many pets can I add?", answer: "Free users can add up to 1 pet. Growing Bond allows 2 pets, Family Circle supports 5 pets, and Premium Pro allows up to 10 pets."),
                HelpFAQ(question: "What information should I include in pet profiles?", answer: "Add your pet's name, breed, age, weight, photos, health records, vaccination history, and any special notes. More details help AI agents provide better advice."),
                HelpFAQ(question: "How do I track my pet's health?", answer: "Use the health section in your pet's profile to log symptoms, medications, vet visits, and vaccinations. The app calculates a health score based on this data."),
                HelpFAQ(question: "Can I share pet profiles with family?", answer: "Yes! Premium users can share pet profiles and memories with family members for collaborative pet care."),
                HelpFAQ(question: "How do I update pet information?", answer: "Go to your pet's profile and tap 'Edit' to update their information, add new photos, or record health updates.")
            ]
        case .memories:
            return [
                HelpFAQ(question: "What types of memories can I save?", answer: "You can save photos, videos, voice notes, text memories, milestones, and walk memories. Each memory can be tagged and organized for easy searching."),
                HelpFAQ(question: "Is there a limit to memory storage?", answer: "Storage varies by plan: Paw Starter (100MB), Growing Bond (2GB), Family Circle (10GB), Premium Pro (50GB). Premium plans include cloud backup."),
                HelpFAQ(question: "How do I organize my memories?", answer: "Use tags, mark favorites, create memory collections, and filter by type (photos, videos, milestones). The search function helps you find specific memories quickly."),
                HelpFAQ(question: "Can I export my memories?", answer: "Yes, you can export your memories from Settings > Data & Storage > Export Data to back up your precious pet memories."),
                HelpFAQ(question: "What are walk memories?", answer: "Walk memories automatically capture your pet's walking routes, duration, and can include photos/notes from your walks. Find them in the Memories section filtered by 'Walks'.")
            ]
        case .premium:
            return [
                HelpFAQ(question: "What's included in Premium plans?", answer: "Premium includes unlimited AI agent access, advanced health analytics, family sharing, priority support, increased storage, and access to all premium features."),
                HelpFAQ(question: "What are the subscription tiers?", answer: "Paw Starter (Free), Growing Bond ($9.99/month), Family Circle ($14.99/month), and Premium Pro ($19.99/month). Each tier offers more pets, storage, and features."),
                HelpFAQ(question: "How do I upgrade my subscription?", answer: "Go to More > Manage Subscription to see all plans and upgrade options. Changes take effect immediately."),
                HelpFAQ(question: "Can I cancel my subscription?", answer: "Yes, you can cancel anytime from your device's subscription settings. You'll retain premium features until the end of your current billing period."),
                HelpFAQ(question: "Do I need Premium for AI agents?", answer: "Basic AI agent access is included in all plans. Premium provides unlimited conversations, faster responses, and access to advanced AI features.")
            ]
        case .technical:
            return [
                HelpFAQ(question: "The app is running slowly", answer: "Try closing and reopening the app, or restart your device. Ensure you have the latest iOS version and app update. Clear some device storage if needed."),
                HelpFAQ(question: "My photos aren't uploading", answer: "Check your internet connection and ensure you have sufficient storage space in your plan. Try uploading one photo at a time and verify camera permissions."),
                HelpFAQ(question: "I'm not receiving notifications", answer: "Check your device notification settings and ensure PetCapsule notifications are enabled. Go to Settings > Notifications > PetCapsule to adjust preferences."),
                HelpFAQ(question: "Siri shortcuts aren't working", answer: "Go to Settings > Siri & Search > PetCapsule and enable 'Use with Siri'. You may need to re-record your custom phrases."),
                HelpFAQ(question: "Apple Intelligence features missing", answer: "Apple Intelligence requires iOS 18+ and compatible devices (iPhone 15 Pro or later). Check your device compatibility and iOS version."),
                HelpFAQ(question: "Sync issues between devices", answer: "Ensure you're signed into the same account on all devices and have internet connectivity. Manual sync can be triggered by pulling down to refresh.")
            ]
        }
    }
}

// MARK: - Supporting Types

enum HelpCategory: CaseIterable {
    case general, aiAgents, petManagement, memories, premium, technical

    var displayName: String {
        switch self {
        case .general: return "General"
        case .aiAgents: return "AI Agents"
        case .petManagement: return "Pet Management"
        case .memories: return "Memories"
        case .premium: return "Premium"
        case .technical: return "Technical"
        }
    }
}

struct HelpFAQ {
    let question: String
    let answer: String
}

struct HelpFAQRow: View {
    let faq: HelpFAQ
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Button(action: { isExpanded.toggle() }) {
                HStack {
                    Text(faq.question)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
            }
            .buttonStyle(PlainButtonStyle())

            if isExpanded {
                Text(faq.answer)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                    .padding(.bottom)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
        .animation(.easeInOut(duration: 0.3), value: isExpanded)
    }
}

#Preview {
    HelpSupportView()
}
