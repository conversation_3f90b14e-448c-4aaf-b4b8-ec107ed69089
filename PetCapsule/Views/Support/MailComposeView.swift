//
//  MailComposeView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
#if canImport(MessageUI)
import MessageUI
#endif
#if canImport(UIKit)
import UIKit
#endif

struct MailComposeView: UIViewControllerRepresentable {
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> MFMailComposeViewController {
        let mailComposer = MFMailComposeViewController()
        mailComposer.mailComposeDelegate = context.coordinator
        
        // Pre-configure email
        mailComposer.setToRecipients(["<EMAIL>"])
        mailComposer.setSubject("PetCapsule Support Request")
        
        // Add device information
        let deviceInfo = getDeviceInfo()
        let messageBody = """
        
        
        ---
        Device Information:
        \(deviceInfo)
        """
        
        mailComposer.setMessageBody(messageBody, isHTML: false)
        
        return mailComposer
    }
    
    func updateUIViewController(_ uiViewController: M<PERSON>ailComposeViewController, context: Context) {
        // No updates needed
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, MFMailComposeViewControllerDelegate {
        let parent: MailComposeView
        
        init(_ parent: MailComposeView) {
            self.parent = parent
        }
        
        func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
            parent.dismiss()
        }
    }
    
    private func getDeviceInfo() -> String {
        let device = UIDevice.current
        let app = Bundle.main
        
        return """
        App Version: \(app.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown")
        Build: \(app.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown")
        Device: \(device.model)
        iOS Version: \(device.systemVersion)
        Device ID: \(device.identifierForVendor?.uuidString ?? "Unknown")
        """
    }
}

#Preview {
    MailComposeView()
}
