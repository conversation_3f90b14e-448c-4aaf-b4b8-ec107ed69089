//
//  TimelineView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import SwiftData

@available(iOS 18.0, *)
struct TimelineView: View {
    @StateObject private var aiService = AIService.shared

    @State private var selectedPet: Pet?
    @State private var memories: [Memory] = []
    @State private var showingMemoryPrompts = false

    @State private var showingPetSelector = false
    @State private var memoryPrompts: [MemoryPrompt] = []
    @State private var selectedTimeFilter: TimeFilter = .all
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Pet Selector Header
                PetSelectorHeader(
                    selectedPet: $selectedPet,
                    showingPetSelector: $showingPetSelector
                )
                
                // Quick Actions Bar
                QuickActionsBar(
                    showingMemoryPrompts: $showingMemoryPrompts,
                    selectedPet: selectedPet
                )
                
                // Time Filter
                TimeFilterPicker(selectedFilter: $selectedTimeFilter)
                
                // Timeline Content
                if memories.isEmpty {
                    EmptyTimelineView(
                        selectedPet: selectedPet,
                        showingMemoryPrompts: $showingMemoryPrompts
                    )
                } else {
                    TimelineScrollView(
                        memories: filteredMemories,
                        selectedPet: selectedPet
                    )
                }
            }
            .navigationTitle("Story of Us")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadMemories()
            }
            .sheet(isPresented: $showingPetSelector) {
                PetSelectorSheet(selectedPet: $selectedPet)
            }
            .sheet(isPresented: $showingMemoryPrompts) {
                MemoryPromptsView(
                    pet: selectedPet,
                    prompts: memoryPrompts
                )
            }

        }
    }
    
    private var filteredMemories: [Memory] {
        switch selectedTimeFilter {
        case .all:
            return memories
        case .thisWeek:
            return memories.filter { Calendar.current.isDate($0.createdAt, equalTo: Date(), toGranularity: .weekOfYear) }
        case .thisMonth:
            return memories.filter { Calendar.current.isDate($0.createdAt, equalTo: Date(), toGranularity: .month) }
        case .thisYear:
            return memories.filter { Calendar.current.isDate($0.createdAt, equalTo: Date(), toGranularity: .year) }
        }
    }
    
    private func loadMemories() {
        // Mock memories for demo
        memories = generateMockMemories()
        
        // Load AI prompts if pet is selected
        if let pet = selectedPet {
            Task {
                do {
                    memoryPrompts = try await aiService.generateMemoryPrompts(for: pet)
                } catch {
                    print("Error loading memory prompts: \(error)")
                }
            }
        }
    }
    
    private func generateMockMemories() -> [Memory] {
        return [
            Memory(
                title: "First Day Home",
                content: "Buddy's first day in his new home. He was so curious about everything!",
                type: .photo,
                milestone: "Adoption Day",
                sentiment: "joyful",
                tags: ["adoption", "home", "first day", "curious"]
            ),
            Memory(
                title: "Learning to Sit",
                content: "Teaching Buddy his first command. He's such a smart boy!",
                type: .video,
                milestone: "First Command",
                sentiment: "proud",
                tags: ["training", "sit", "smart", "learning"]
            ),
            Memory(
                title: "Beach Adventure",
                content: "Buddy's first time at the beach. He loved the sand but was scared of the waves at first.",
                type: .photo,
                milestone: "First Beach Visit",
                sentiment: "adventurous",
                tags: ["beach", "sand", "waves", "adventure", "first time"]
            )
        ]
    }
}

struct PetSelectorHeader: View {
    @Binding var selectedPet: Pet?
    @Binding var showingPetSelector: Bool
    
    var body: some View {
        HStack {
            Button(action: {
                showingPetSelector = true
            }) {
                HStack(spacing: 10) {
                    if let pet = selectedPet {
                        AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Circle()
                                .fill(Color.gray.opacity(0.2))
                                .overlay(
                                    Text("🐕")
                                        .font(.title2)
                                )
                        }
                        .frame(width: 40, height: 40)
                        .clipShape(Circle())
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(pet.name)
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text(pet.breed ?? "Mixed Breed")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } else {
                        Circle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: 40, height: 40)
                            .overlay(
                                Image(systemName: "plus")
                                    .foregroundColor(.secondary)
                            )
                        
                        Text("Select Pet")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    
                    Image(systemName: "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
}

struct QuickActionsBar: View {
    @Binding var showingMemoryPrompts: Bool
    let selectedPet: Pet?
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack {
                TimelineQuickActionButton(
                    icon: "lightbulb.fill",
                    title: "Memory Ideas",
                    color: .yellow
                ) {
                    showingMemoryPrompts = true
                }
                .disabled(selectedPet == nil)
                
                TimelineQuickActionButton(
                    icon: "wand.and.stars",
                    title: "Create Magic",
                    color: .purple
                ) {
                    // Create magic memory
                }
                .disabled(selectedPet == nil)
                
                TimelineQuickActionButton(
                    icon: "brain.head.profile",
                    title: "AI Insights",
                    color: .blue
                ) {
                    // Show AI insights
                }
                .disabled(selectedPet == nil)

                TimelineQuickActionButton(
                    icon: "heart.text.square",
                    title: "Milestones",
                    color: .pink
                ) {
                    // Show milestones
                }
                .disabled(selectedPet == nil)

                TimelineQuickActionButton(
                    icon: "calendar.badge.clock",
                    title: "Time Vault",
                    color: .green
                ) {
                    // Create time vault
                }
                .disabled(selectedPet == nil)
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }
}

struct TimelineQuickActionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 50, height: 50)
                    .background(color.opacity(0.15))
                    .clipShape(Circle())
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct TimeFilterPicker: View {
    @Binding var selectedFilter: TimeFilter
    
    var body: some View {
        Picker("Filter", selection: $selectedFilter) {
            ForEach(TimeFilter.allCases) { filter in
                Text(filter.rawValue).tag(filter)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding(.horizontal)
    }
}

enum TimeFilter: String, CaseIterable, Identifiable {
    case all = "All"
    case thisWeek = "This Week"
    case thisMonth = "This Month"
    case thisYear = "This Year"
    
    var id: String { self.rawValue }
}

struct TimelineScrollView: View {
    let memories: [Memory]
    let selectedPet: Pet?
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(memories) { memory in
                    MemoryCard(memory: memory)
                }
            }
            .padding()
        }
    }
}

struct EmptyTimelineView: View {
    let selectedPet: Pet?
    @Binding var showingMemoryPrompts: Bool
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            VStack(spacing: 16) {
                Image(systemName: "camera.macro")
                    .font(.system(size: 60))
                    .foregroundColor(.secondary)
                
                VStack(spacing: 8) {
                    if let pet = selectedPet {
                        Text("Start \(pet.name)'s Timeline")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                        
                        Text("Capture precious moments and create lasting memories together.")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    } else {
                        Text("Select a Pet")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                        
                        Text("Choose a pet to start building their timeline of memories.")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }
            }
            
            if selectedPet != nil {
                VStack(spacing: 12) {
                    Button("Get Memory Ideas") {
                        showingMemoryPrompts = true
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Button("Add First Memory") {
                        // Navigate to memory creation
                    }
                    .buttonStyle(.bordered)
                }
            }
            
            Spacer()
        }
        .padding(32)
    }
}

struct MemoryCard: View {
    let memory: Memory
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            if let urlString = memory.mediaURL, let url = URL(string: urlString) {
                AsyncImage(url: url) { image in
                    image.resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 200)
                        .clipped()
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 200)
                }
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(memory.title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(memory.content)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                
                HStack {
                    Text(memory.createdAt, style: .date)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    Spacer()
                    
                    if let sentiment = memory.sentiment {
                        Text(sentiment)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.1))
                            .clipShape(Capsule())
                    }
                }
            }
            .padding()
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 4)
    }
}

// Placeholder views
struct PetSelectorSheet: View {
    @Binding var selectedPet: Pet?
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Select Pet")
                    .font(.title2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct MemoryPromptsView: View {
    let pet: Pet?
    let prompts: [MemoryPrompt]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Memory Ideas")
                    .font(.title2)
                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

// MARK: - Previews

#if DEBUG
@available(iOS 18.0, *)
struct TimelineView_Previews: PreviewProvider {
    static var previews: some View {
        if #available(iOS 18.0, *) {
            TimelineView()
        } else {
            Text("iOS 18.0+ Required")
        }
    }
}
#endif

