//
//  RealTimeWeatherWidget.swift
//  PetCapsule
//
//  Real-time weather widget with live updates and environmental alerts
//

import SwiftUI

struct RealTimeWeatherWidget: View {
    @StateObject private var weatherService = RealTimeWeatherService.shared
    @State private var showingDetailedWeather = false
    @State private var isRefreshing = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with refresh button
            HStack {
                Text("Current Conditions")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: refreshWeather) {
                    Image(systemName: "arrow.clockwise")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                        .rotationEffect(.degrees(isRefreshing ? 360 : 0))
                        .animation(.linear(duration: 1).repeatCount(isRefreshing ? 10 : 0), value: isRefreshing)
                }
                
                But<PERSON>(action: { showingDetailedWeather = true }) {
                    Image(systemName: "info.circle")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)
            
            // Weather content
            if let weather = weatherService.currentWeather,
               let airQuality = weatherService.airQuality {
                
                VStack(spacing: 16) {
                    // Main weather display
                    mainWeatherDisplay(weather: weather, airQuality: airQuality)
                    
                    // Environmental metrics
                    environmentalMetrics(weather: weather, airQuality: airQuality)
                    
                    // Active alerts
                    if !weatherService.weatherAlerts.isEmpty {
                        activeAlertsSection
                    }
                    
                    // Walk recommendation
                    walkRecommendationBanner
                    
                    // Last update time
                    lastUpdateInfo
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
                
            } else {
                // Loading state
                loadingState
                    .padding(16)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .onAppear {
            if weatherService.currentWeather == nil {
                Task {
                    await weatherService.updateWeatherData()
                }
            }
        }
        .sheet(isPresented: $showingDetailedWeather) {
            DetailedWeatherView()
        }
    }
    
    // MARK: - Main Weather Display
    
    private func mainWeatherDisplay(weather: WeatherData, airQuality: AirQualityData) -> some View {
        HStack(spacing: 20) {
            // Weather icon and condition
            VStack(spacing: 8) {
                Image(systemName: weather.icon)
                    .font(.system(size: 40))
                    .foregroundColor(weatherIconColor(for: weather.condition))
                    .symbolEffect(.bounce, value: weather.temperature)
                
                Text(weather.condition)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }
            
            // Temperature
            VStack(spacing: 4) {
                Text("\(weather.temperature)°")
                    .font(.system(size: 36, weight: .light, design: .rounded))
                    .foregroundColor(.primary)
                
                Text("Feels like \(weather.temperature + 2)°")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Air quality indicator
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(airQuality.color.opacity(0.2))
                        .frame(width: 50, height: 50)
                    
                    VStack(spacing: 2) {
                        Text("\(airQuality.index)")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(airQuality.color)
                        
                        Text("AQI")
                            .font(.caption2)
                            .foregroundColor(airQuality.color)
                    }
                }
                
                Text(airQuality.description)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(airQuality.color)
            }
        }
    }
    
    // MARK: - Environmental Metrics
    
    private func environmentalMetrics(weather: WeatherData, airQuality: AirQualityData) -> some View {
        HStack(spacing: 16) {
            MetricCard(
                icon: "humidity.fill",
                title: "Humidity",
                value: "\(weather.humidity)%",
                color: .cyan
            )
            
            MetricCard(
                icon: "wind",
                title: "Wind",
                value: String(format: "%.1f mph", weather.windSpeed),
                color: .gray
            )
            
            if let pollen = weatherService.pollenData {
                MetricCard(
                    icon: "leaf.fill",
                    title: "Pollen",
                    value: pollenLevelText(pollen),
                    color: pollenColor(pollen)
                )
            }
        }
    }
    
    // MARK: - Active Alerts Section
    
    private var activeAlertsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Active Alerts")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            ForEach(weatherService.weatherAlerts.filter { $0.isActive }.prefix(2)) { alert in
                AlertCard(alert: alert)
            }
        }
    }
    
    // MARK: - Walk Recommendation Banner
    
    private var walkRecommendationBanner: some View {
        HStack(spacing: 12) {
            Image(systemName: weatherService.isGoodWalkingWeather() ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                .font(.title3)
                .foregroundColor(weatherService.isGoodWalkingWeather() ? .green : .orange)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(weatherService.isGoodWalkingWeather() ? "Great for walking!" : "Check conditions")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(walkRecommendationText)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("Plan Walk") {
                // Navigate to walk planner
            }
            .font(.caption)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Last Update Info
    
    private var lastUpdateInfo: some View {
        HStack {
            if weatherService.isMonitoring {
                HStack(spacing: 4) {
                    Circle()
                        .fill(.green)
                        .frame(width: 6, height: 6)
                        .scaleEffect(1.0)
                        .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: weatherService.isMonitoring)
                    
                    Text("Live updates")
                        .font(.caption2)
                        .foregroundColor(.green)
                }
            }
            
            Spacer()
            
            if let lastUpdate = weatherService.lastUpdateTime {
                Text("Updated \(lastUpdate.formatted(.relative(presentation: .named)))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Loading State
    
    private var loadingState: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading weather data...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(height: 120)
    }
    
    // MARK: - Helper Methods
    
    private func refreshWeather() {
        isRefreshing = true
        
        Task {
            await weatherService.forceWeatherUpdate()
            
            await MainActor.run {
                withAnimation(.easeOut(duration: 0.5)) {
                    isRefreshing = false
                }
            }
        }
    }
    
    private func weatherIconColor(for condition: String) -> Color {
        switch condition.lowercased() {
        case let c where c.contains("sun") || c.contains("clear"):
            return .orange
        case let c where c.contains("cloud"):
            return .gray
        case let c where c.contains("rain"):
            return .blue
        case let c where c.contains("snow"):
            return .cyan
        case let c where c.contains("storm"):
            return .purple
        default:
            return .primary
        }
    }
    
    private func pollenLevelText(_ pollen: PollenData) -> String {
        let total = pollen.treeIndex + pollen.grassIndex + pollen.weedIndex
        switch total {
        case 0...5: return "Low"
        case 6...12: return "Moderate"
        case 13...20: return "High"
        default: return "Very High"
        }
    }
    
    private func pollenColor(_ pollen: PollenData) -> Color {
        let total = pollen.treeIndex + pollen.grassIndex + pollen.weedIndex
        switch total {
        case 0...5: return .green
        case 6...12: return .yellow
        case 13...20: return .orange
        default: return .red
        }
    }
    
    private var walkRecommendationText: String {
        guard let weather = weatherService.currentWeather,
              let airQuality = weatherService.airQuality else {
            return "Weather data loading..."
        }
        
        if weatherService.isGoodWalkingWeather() {
            return "Perfect conditions for outdoor activities"
        } else {
            var issues: [String] = []
            
            if weather.temperature > 85 {
                issues.append("high temperature")
            } else if weather.temperature < 45 {
                issues.append("cold temperature")
            }
            
            if airQuality.index > 100 {
                issues.append("poor air quality")
            }
            
            return "Consider \(issues.joined(separator: " and "))"
        }
    }
}

// MARK: - Supporting Views

struct MetricCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
        )
    }
}

struct AlertCard: View {
    let alert: WeatherAlert
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: alert.type.icon)
                .font(.caption)
                .foregroundColor(alert.severity.color)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(alert.title)
                    .font(.caption)
                    .fontWeight(.medium)
                
                Text(alert.message)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(alert.severity.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(alert.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Detailed Weather View

struct DetailedWeatherView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var weatherService = RealTimeWeatherService.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Detailed weather view would go here")
                        .font(.headline)
                    
                    Text("This would include hourly forecasts, extended forecasts, radar maps, and detailed environmental data.")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
            }
            .navigationTitle("Weather Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    RealTimeWeatherWidget()
        .padding()
}
