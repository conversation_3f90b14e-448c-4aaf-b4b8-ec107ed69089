//
//  iOS18FeaturesView.swift
//  PetCapsule
//
//  Showcase and control iOS 18 features for Apple Award consideration
//  Demonstrates comprehensive integration of latest iOS capabilities
//

import SwiftUI
import AppIntents

@available(iOS 18.0, *)
struct iOS18FeaturesView: View {
    @StateObject private var appleIntelligence = AppleIntelligenceService.shared
    @StateObject private var passkeyService = PasskeyAuthenticationService.shared
    @StateObject private var iconManager = AppIconManager.shared
    @StateObject private var mlService = EnhancedMLService.shared
    @StateObject private var activityManager = PetLiveActivityManager.shared
    
    @State private var selectedTab = 0
    @State private var showingFeatureDemo = false
    
    var body: some View {
        NavigationView {
            TabView(selection: $selectedTab) {
                // Apple Intelligence Tab
                AppleIntelligenceTab()
                    .tabItem {
                        Label("AI Features", systemImage: "brain.head.profile")
                    }
                    .tag(0)

                // Visual Intelligence Tab
                VisualIntelligenceDemoView()
                    .tabItem {
                        Label("Visual Intelligence", systemImage: "eye.circle")
                    }
                    .tag(1)

                // Controls & Widgets Tab
                ControlsWidgetsTab()
                    .tabItem {
                        Label("Controls", systemImage: "switch.2")
                    }
                    .tag(2)

                // Security & Authentication Tab
                SecurityTab()
                    .tabItem {
                        Label("Security", systemImage: "faceid")
                    }
                    .tag(3)

                // Customization Tab
                CustomizationTab()
                    .tabItem {
                        Label("Customize", systemImage: "paintbrush")
                    }
                    .tag(4)

                // Live Activities Tab
                LiveActivitiesTab()
                    .tabItem {
                        Label("Live Activities", systemImage: "bell.badge")
                    }
                    .tag(5)
            }
            .navigationTitle("iOS 18 Features")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Demo All") {
                        showingFeatureDemo = true
                    }
                }
            }
        }
        .sheet(isPresented: $showingFeatureDemo) {
            iOS18FeatureDemoView()
        }
    }
}

// MARK: - Apple Intelligence Tab

@available(iOS 18.0, *)
struct AppleIntelligenceTab: View {
    @StateObject private var appleIntelligence = AppleIntelligenceService.shared
    @State private var testText = "My dog loves playing in the park and chasing squirrels."
    @State private var enhancedText = ""
    @State private var isEnhancing = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Feature Status
                FeatureStatusCard(
                    title: "Apple Intelligence",
                    isAvailable: appleIntelligence.isWritingToolsAvailable,
                    description: "Advanced AI capabilities for enhanced pet care"
                )
                
                // Writing Tools Demo
                VStack(alignment: .leading, spacing: 16) {
                    Text("Writing Tools Demo")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Original Text:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        TextEditor(text: $testText)
                            .frame(height: 80)
                            .padding(8)
                            .background(.gray.opacity(0.1))
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                        
                        Button(action: enhanceText) {
                            HStack {
                                if isEnhancing {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "wand.and.stars")
                                }
                                Text("Enhance with Apple Intelligence")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(.blue)
                            .foregroundStyle(.white)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                        }
                        .disabled(isEnhancing || !appleIntelligence.isWritingToolsAvailable)
                        
                        if !enhancedText.isEmpty {
                            Text("Enhanced Text:")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Text(enhancedText)
                                .padding(12)
                                .background(.green.opacity(0.1))
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                        }
                    }
                }
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 16))
                
                // Image Playground Demo
                ImagePlaygroundDemo()
                
                // Genmoji Demo
                GenmojiDemo()
            }
            .padding()
        }
    }
    
    private func enhanceText() {
        isEnhancing = true
        appleIntelligence.enhanceMemoryDescription(testText) { enhanced in
            enhancedText = enhanced
            isEnhancing = false
        }
    }
}

// MARK: - Controls & Widgets Tab

@available(iOS 18.0, *)
struct ControlsWidgetsTab: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Controls Section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Control Center & Lock Screen")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        ControlPreviewCard(
                            title: "Emergency Vet",
                            icon: "phone.fill",
                            color: .red,
                            description: "Quick access to emergency contacts"
                        )
                        
                        ControlPreviewCard(
                            title: "Add Memory",
                            icon: "camera.fill",
                            color: .blue,
                            description: "Instantly capture pet moments"
                        )
                        
                        ControlPreviewCard(
                            title: "Vaccination",
                            icon: "syringe.fill",
                            color: .green,
                            description: "Toggle vaccination reminders"
                        )
                        
                        ControlPreviewCard(
                            title: "Walk Planner",
                            icon: "figure.walk",
                            color: .orange,
                            description: "Check weather for walks"
                        )
                    }
                }
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 16))
                
                // Widgets Section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Enhanced Widgets")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Text("Interactive widgets with iOS 18 features:")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                    
                    VStack(spacing: 12) {
                        WidgetFeatureRow(
                            icon: "rectangle.3.group.bubble",
                            title: "Pet Dashboard",
                            description: "Interactive pet overview with quick actions"
                        )
                        
                        WidgetFeatureRow(
                            icon: "syringe",
                            title: "Vaccination Tracker",
                            description: "Real-time health monitoring"
                        )
                        
                        WidgetFeatureRow(
                            icon: "camera.viewfinder",
                            title: "Memory Timeline",
                            description: "Recent memories with tap-to-view"
                        )
                        
                        WidgetFeatureRow(
                            icon: "cloud.sun",
                            title: "Walk Planner",
                            description: "Weather-based walk recommendations"
                        )
                    }
                }
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 16))
            }
            .padding()
        }
    }
}

// MARK: - Security Tab

@available(iOS 18.0, *)
struct SecurityTab: View {
    @StateObject private var passkeyService = PasskeyAuthenticationService.shared
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Passkey Status
                FeatureStatusCard(
                    title: "Passkeys",
                    isAvailable: passkeyService.isPasskeyAvailable,
                    description: "Secure, passwordless authentication"
                )
                
                // Authentication Options
                VStack(spacing: 16) {
                    if passkeyService.isAuthenticated {
                        AuthenticatedView()
                    } else {
                        PasskeyAuthenticationView()
                    }
                }
                .padding()
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 16))
                
                // Security Features
                SecurityFeaturesView()
            }
            .padding()
        }
    }
}

// MARK: - Customization Tab

@available(iOS 18.0, *)
struct CustomizationTab: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // App Icon Selection
                AppIconSelectionView()
                
                // Theme Customization
                ThemeCustomizationView()
            }
            .padding()
        }
    }
}

// MARK: - Live Activities Tab

@available(iOS 18.0, *)
struct LiveActivitiesTab: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Live Activity Control
                LiveActivityControlView()
            }
            .padding()
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct FeatureStatusCard: View {
    let title: String
    let isAvailable: Bool
    let description: String
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.bold)
                
                Text(description)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Spacer()
            
            HStack {
                Circle()
                    .fill(isAvailable ? .green : .red)
                    .frame(width: 8, height: 8)
                
                Text(isAvailable ? "Available" : "Unavailable")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundStyle(isAvailable ? .green : .red)
            }
        }
        .padding()
        .background(isAvailable ? .green.opacity(0.1) : .red.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

@available(iOS 18.0, *)
struct ControlPreviewCard: View {
    let title: String
    let icon: String
    let color: Color
    let description: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title)
                .foregroundStyle(color)
            
            Text(title)
                .font(.caption)
                .fontWeight(.semibold)
            
            Text(description)
                .font(.caption2)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(color.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

@available(iOS 18.0, *)
struct WidgetFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundStyle(.blue)
                .font(.title3)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

@available(iOS 18.0, *)
struct ImagePlaygroundDemo: View {
    @StateObject private var appleIntelligence = AppleIntelligenceService.shared
    @State private var generatedImage: UIImage?
    @State private var isGenerating = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Image Playground")
                .font(.headline)
                .fontWeight(.bold)
            
            Button(action: generateImage) {
                HStack {
                    if isGenerating {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "photo.artframe")
                    }
                    Text("Generate Pet Image")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(.purple)
                .foregroundStyle(.white)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .disabled(isGenerating || !appleIntelligence.isImagePlaygroundAvailable)
            
            if let image = generatedImage {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(height: 100)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    
    private func generateImage() {
        isGenerating = true
        appleIntelligence.createPetMemoryImage(concept: "happy dog playing") { image in
            generatedImage = image
            isGenerating = false
        }
    }
}

@available(iOS 18.0, *)
struct GenmojiDemo: View {
    @StateObject private var appleIntelligence = AppleIntelligenceService.shared
    @State private var generatedEmoji: String?
    @State private var isGenerating = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Genmoji")
                .font(.headline)
                .fontWeight(.bold)
            
            Button(action: generateEmoji) {
                HStack {
                    if isGenerating {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "face.smiling")
                    }
                    Text("Generate Pet Emoji")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(.pink)
                .foregroundStyle(.white)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .disabled(isGenerating)
            
            if let emoji = generatedEmoji {
                Text(emoji)
                    .font(.largeTitle)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(.pink.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
    
    private func generateEmoji() {
        isGenerating = true
        appleIntelligence.createPetEmoji(petName: "Buddy", emotion: "happy") { emoji in
            generatedEmoji = emoji
            isGenerating = false
        }
    }
}

@available(iOS 18.0, *)
struct AuthenticatedView: View {
    @StateObject private var passkeyService = PasskeyAuthenticationService.shared
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.shield.fill")
                .font(.largeTitle)
                .foregroundStyle(.green)
            
            Text("Authenticated with Passkey")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let user = passkeyService.currentUser {
                Text("User: \(user.name.isEmpty ? user.id : user.name)")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Button("Sign Out") {
                passkeyService.signOut()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 8)
            .background(.red.opacity(0.2))
            .foregroundStyle(.red)
            .clipShape(Capsule())
        }
    }
}

@available(iOS 18.0, *)
struct SecurityFeaturesView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Security Features")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                SecurityFeatureRow(
                    icon: "key.fill",
                    title: "Passkey Authentication",
                    description: "Secure, passwordless sign-in"
                )
                
                SecurityFeatureRow(
                    icon: "lock.shield",
                    title: "Vault Protection",
                    description: "Biometric access to sensitive data"
                )
                
                SecurityFeatureRow(
                    icon: "faceid",
                    title: "Biometric Fallback",
                    description: "Face ID and Touch ID support"
                )
                
                SecurityFeatureRow(
                    icon: "icloud.and.arrow.up",
                    title: "iCloud Keychain",
                    description: "Sync across all your devices"
                )
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
}

@available(iOS 18.0, *)
struct SecurityFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundStyle(.blue)
                .font(.title3)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

@available(iOS 18.0, *)
struct ThemeCustomizationView: View {
    @AppStorage("app_theme") private var selectedTheme = "auto"
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Theme Customization")
                .font(.headline)
                .fontWeight(.bold)
            
            Picker("Theme", selection: $selectedTheme) {
                Text("Automatic").tag("auto")
                Text("Light").tag("light")
                Text("Dark").tag("dark")
            }
            .pickerStyle(.segmented)
            
            Text("Choose how PetCapsule appears on your device")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
}

@available(iOS 18.0, *)
struct iOS18FeatureDemoView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    Text("🏆 iOS 18 Features Demo")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)
                    
                    Text("PetCapsule showcases the latest iOS 18 capabilities for an award-worthy experience")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.center)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        FeatureDemoCard(
                            icon: "brain.head.profile",
                            title: "Apple Intelligence",
                            description: "Writing Tools, Image Playground, Genmoji"
                        )
                        
                        FeatureDemoCard(
                            icon: "switch.2",
                            title: "Controls",
                            description: "Control Center & Lock Screen widgets"
                        )
                        
                        FeatureDemoCard(
                            icon: "faceid",
                            title: "Passkeys",
                            description: "Secure passwordless authentication"
                        )
                        
                        FeatureDemoCard(
                            icon: "paintbrush",
                            title: "Customization",
                            description: "Tinted icons & themes"
                        )
                        
                        FeatureDemoCard(
                            icon: "bell.badge",
                            title: "Live Activities",
                            description: "Dynamic Island integration"
                        )
                        
                        FeatureDemoCard(
                            icon: "cpu",
                            title: "Enhanced ML",
                            description: "Core ML, Vision, Translation"
                        )
                    }
                }
                .padding()
            }
            .navigationTitle("Feature Demo")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

@available(iOS 18.0, *)
struct FeatureDemoCard: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.largeTitle)
                .foregroundStyle(.blue)
            
            Text(title)
                .font(.headline)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            Text(description)
                .font(.caption)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(3)
        }
        .padding()
        .frame(maxWidth: .infinity, minHeight: 120)
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
}
