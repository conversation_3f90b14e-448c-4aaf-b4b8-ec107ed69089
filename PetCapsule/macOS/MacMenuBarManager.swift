//
//  MacMenuBarManager.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 12/24/24.
//

#if targetEnvironment(macCatalyst)
import Foundation
import SwiftUI
import AppKit

@MainActor
class MacMenuBarManager: ObservableObject {
    static let shared = MacMenuBarManager()
    
    @Published var isMenuBarVisible = true
    @Published var currentPet: Pet?
    @Published var recentMemories: [Memory] = []
    @Published var upcomingReminders: [VaccinationRecord] = []
    
    private var statusItem: NSStatusItem?
    private var popover: NSPopover?
    
    private init() {
        setupMenuBar()
    }
    
    // MARK: - Menu Bar Setup
    
    func setupMenuBar() {
        guard let statusBar = NSStatusBar.system.statusBar.statusItem(withLength: NSStatusItem.variableLength) else {
            return
        }
        
        statusItem = statusBar
        
        // Set menu bar icon
        if let button = statusItem?.button {
            button.image = NSImage(systemSymbolName: "pawprint.fill", accessibilityDescription: "PetCapsule")
            button.action = #selector(togglePopover)
            button.target = self
        }
        
        setupPopover()
    }
    
    private func setupPopover() {
        popover = NSPopover()
        popover?.contentSize = NSSize(width: 320, height: 480)
        popover?.behavior = .transient
        popover?.contentViewController = NSHostingController(
            rootView: MenuBarPopoverView()
                .environmentObject(self)
        )
    }
    
    @objc private func togglePopover() {
        guard let button = statusItem?.button else { return }
        
        if let popover = popover {
            if popover.isShown {
                popover.performClose(nil)
            } else {
                popover.show(relativeTo: button.bounds, of: button, preferredEdge: .minY)
            }
        }
    }
    
    // MARK: - Data Updates
    
    func updateCurrentPet(_ pet: Pet?) {
        currentPet = pet
        updateMenuBarTitle()
    }
    
    func updateRecentMemories(_ memories: [Memory]) {
        recentMemories = Array(memories.prefix(5))
    }
    
    func updateUpcomingReminders(_ reminders: [VaccinationRecord]) {
        upcomingReminders = Array(reminders.prefix(3))
    }
    
    private func updateMenuBarTitle() {
        if let pet = currentPet {
            statusItem?.button?.toolTip = "PetCapsule - \(pet.name)"
        } else {
            statusItem?.button?.toolTip = "PetCapsule"
        }
    }
    
    // MARK: - Quick Actions
    
    func addQuickMemory() {
        // Open main app to memory creation
        openMainApp(to: .addMemory)
    }
    
    func viewPetHealth() {
        openMainApp(to: .health)
    }
    
    func emergencyCall() {
        EmergencyCallService.shared.callEmergencyVet(for: currentPet)
    }
    
    private func openMainApp(to destination: AppDestination) {
        // Send notification to main app to navigate
        NotificationCenter.default.post(
            name: .navigateToDestination,
            object: destination
        )
        
        // Bring main app to foreground
        NSApp.activate(ignoringOtherApps: true)
    }
}

// MARK: - Menu Bar Popover View

struct MenuBarPopoverView: View {
    @EnvironmentObject private var menuBarManager: MacMenuBarManager
    @StateObject private var healthManager = WatchHealthManager.shared
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            MenuBarHeader()
            
            Divider()
            
            ScrollView {
                VStack(spacing: 16) {
                    // Pet Status
                    if let pet = menuBarManager.currentPet {
                        PetStatusSection(pet: pet)
                    } else {
                        NoPetSection()
                    }
                    
                    // Quick Actions
                    QuickActionsSection()
                    
                    // Recent Memories
                    if !menuBarManager.recentMemories.isEmpty {
                        RecentMemoriesSection()
                    }
                    
                    // Upcoming Reminders
                    if !menuBarManager.upcomingReminders.isEmpty {
                        UpcomingRemindersSection()
                    }
                }
                .padding()
            }
            
            Divider()
            
            // Footer
            MenuBarFooter()
        }
        .background(Color(.controlBackgroundColor))
    }
}

// MARK: - Menu Bar Components

struct MenuBarHeader: View {
    var body: some View {
        HStack {
            Image(systemName: "pawprint.fill")
                .foregroundColor(.blue)
                .font(.title2)
            
            Text("PetCapsule")
                .font(.headline)
                .fontWeight(.semibold)
            
            Spacer()
            
            Button(action: {
                NSApp.activate(ignoringOtherApps: true)
            }) {
                Image(systemName: "arrow.up.right.square")
                    .foregroundColor(.secondary)
            }
            .buttonStyle(.plain)
        }
        .padding()
    }
}

struct PetStatusSection: View {
    let pet: Pet
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(pet.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Circle()
                    .fill(Color.green)
                    .frame(width: 8, height: 8)
                
                Text("Healthy")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Age")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(pet.age) years")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Weight")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(pet.weight, specifier: "%.1f") lbs")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(8)
    }
}

struct NoPetSection: View {
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: "pawprint")
                .font(.largeTitle)
                .foregroundColor(.secondary)
            
            Text("No Pet Added")
                .font(.headline)
            
            Text("Add your first pet to get started")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Add Pet") {
                MacMenuBarManager.shared.openMainApp(to: .addPet)
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.small)
        }
        .padding()
    }
}

struct QuickActionsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Quick Actions")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                QuickActionButton(
                    title: "Add Memory",
                    icon: "camera.fill",
                    color: .blue
                ) {
                    MacMenuBarManager.shared.addQuickMemory()
                }
                
                QuickActionButton(
                    title: "Health",
                    icon: "heart.fill",
                    color: .red
                ) {
                    MacMenuBarManager.shared.viewPetHealth()
                }
                
                QuickActionButton(
                    title: "Emergency",
                    icon: "phone.fill",
                    color: .orange
                ) {
                    MacMenuBarManager.shared.emergencyCall()
                }
                
                QuickActionButton(
                    title: "AI Chat",
                    icon: "brain.head.profile",
                    color: .purple
                ) {
                    MacMenuBarManager.shared.openMainApp(to: .aiChat)
                }
            }
        }
    }
}

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .background(Color(.controlBackgroundColor))
            .cornerRadius(6)
        }
        .buttonStyle(.plain)
    }
}

struct RecentMemoriesSection: View {
    @EnvironmentObject private var menuBarManager: MacMenuBarManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Recent Memories")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            ForEach(menuBarManager.recentMemories.prefix(3)) { memory in
                MemoryRowView(memory: memory)
            }
        }
    }
}

struct MemoryRowView: View {
    let memory: Memory
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: memory.type.systemImage)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(memory.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text(memory.createdAt, style: .relative)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct UpcomingRemindersSection: View {
    @EnvironmentObject private var menuBarManager: MacMenuBarManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Upcoming Reminders")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            ForEach(menuBarManager.upcomingReminders.prefix(2)) { reminder in
                ReminderRowView(reminder: reminder)
            }
        }
    }
}

struct ReminderRowView: View {
    let reminder: VaccinationRecord
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: "calendar")
                .foregroundColor(.orange)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(reminder.vaccineName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                if let dueDate = reminder.nextDueDate {
                    Text("Due \(dueDate, style: .relative)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct MenuBarFooter: View {
    var body: some View {
        HStack {
            Button("Preferences") {
                MacMenuBarManager.shared.openMainApp(to: .settings)
            }
            .buttonStyle(.plain)
            .font(.caption)
            
            Spacer()
            
            Button("Quit") {
                NSApp.terminate(nil)
            }
            .buttonStyle(.plain)
            .font(.caption)
        }
        .padding()
    }
}

// MARK: - Supporting Types

enum AppDestination {
    case addPet
    case addMemory
    case health
    case aiChat
    case settings
}

extension Notification.Name {
    static let navigateToDestination = Notification.Name("navigateToDestination")
}

#endif
