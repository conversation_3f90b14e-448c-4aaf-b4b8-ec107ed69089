//
//  AIChatTests.swift
//  PetCapsuleTests
//
//  Comprehensive tests for AI Chat functionality
//  Tests conversation history, attachments, and agent-specific responses
//

import XCTest
import Swift<PERSON>
@testable import PetCapsule

@available(iOS 18.0, *)
final class AIChatTests: XCTestCase {
    
    var aiService: EnhancedAIAgentService!
    var appleIntelligenceService: EnhancedAppleIntelligenceService!
    var testPet: Pet!
    var testAgent: AIAgent!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // Initialize services
        aiService = EnhancedAIAgentService.shared
        appleIntelligenceService = EnhancedAppleIntelligenceService.shared
        
        // Create test pet
        testPet = Pet(
            id: UUID(),
            name: "Buddy",
            species: "Dog",
            breed: "Golden Retriever",
            age: 3,
            weight: 65.0,
            activityLevel: "high",
            personalityTraits: ["friendly", "energetic", "loyal"],
            vaccinations: ["rabies", "distemper"],
            aiRecommendations: [],
            lastCheckupDate: Date(),
            chronicConditions: [],
            foodAllergies: [],
            healthScore: 0.9,
            dailyCalories: 1200,
            lastAIAnalysis: nil,
            storedMemoryCount: 0,
            subscriptionTier: "paw_starter",
            gender: "Male",
            vetName: "Dr. Smith",
            vetContact: "555-0123",
            currentFood: "Premium Dog Food",
            foodBrand: "HealthyPaws",
            createdAt: Date(),
            updatedAt: Date(),
            friendsCount: 0,
            bio: "A happy and healthy Golden Retriever",
            achievementBadges: [],
            isFavorite: true,
            bondedWith: nil,
            isIndoor: false,
            careGroup: "Active Dogs",
            waterIntakeML: 1000,
            dietaryRestrictions: [],
            exerciseMinutesDaily: 60,
            walkingFrequency: "twice daily"
        )
        
        // Create test agent
        testAgent = AIAgent(
            name: "Dr. Nutrition",
            iconName: "🥗",
            description: "Nutrition specialist for diet planning and meal schedules",
            specialty: "Nutrition & Diet",
            specialties: ["Diet Planning", "Meal Scheduling", "Weight Management", "Nutritional Analysis", "Food Safety"],
            gradientColors: ["#4CAF50", "#8BC34A"],
            isPremium: false,
            systemPrompt: "You are Dr. Nutrition, a specialized pet nutrition expert. Provide comprehensive dietary guidance, meal planning, weight management strategies, and nutritional analysis for optimal pet health.",
            conversationStarters: [
                "What's the best diet for my pet?",
                "Help me create a meal plan",
                "My pet needs to lose weight",
                "Is this food safe for my pet?"
            ],
            responseConfig: AIResponseConfig(
                maxTokens: 800,
                temperature: 0.7,
                tone: "caring",
                responseStyle: "detailed",
                expertise: "intermediate"
            )
        )
    }
    
    override func tearDownWithError() throws {
        // Clean up conversation history
        aiService.clearConversationHistory(for: testAgent.id)
        try super.tearDownWithError()
    }
    
    // MARK: - Conversation History Tests
    
    func testConversationHistoryStorage() async throws {
        // Test that conversation history is properly stored
        let message1 = "What should I feed my dog?"
        let message2 = "How much should I feed him?"
        
        // Send first message
        let response1 = await aiService.sendMessage(to: testAgent, message: message1, pet: testPet)
        XCTAssertFalse(response1.isEmpty, "First response should not be empty")
        
        // Send second message
        let response2 = await aiService.sendMessage(to: testAgent, message: message2, pet: testPet)
        XCTAssertFalse(response2.isEmpty, "Second response should not be empty")
        
        // Verify conversation history is stored
        let history = aiService.conversationHistory[testAgent.id.uuidString] ?? []
        XCTAssertGreaterThanOrEqual(history.count, 4, "Should have at least 4 messages (2 user + 2 AI)")
        
        // Verify message content
        let userMessages = history.filter { $0.isFromUser }
        let aiMessages = history.filter { !$0.isFromUser }
        
        XCTAssertEqual(userMessages.count, 2, "Should have 2 user messages")
        XCTAssertEqual(aiMessages.count, 2, "Should have 2 AI messages")
        
        // Verify message content matches
        XCTAssertTrue(userMessages.contains { $0.content.contains("feed my dog") }, "Should contain first user message")
        XCTAssertTrue(userMessages.contains { $0.content.contains("How much should I feed him") }, "Should contain second user message")
    }
    
    func testConversationHistoryPersistence() async throws {
        // Test that conversation history persists across service instances
        let message = "What's the best food for my Golden Retriever?"
        
        // Send message with first service instance
        let response1 = await aiService.sendMessage(to: testAgent, message: message, pet: testPet)
        XCTAssertFalse(response1.isEmpty, "Response should not be empty")
        
        // Create new service instance
        let newAIService = EnhancedAIAgentService.shared
        
        // Verify conversation history is accessible
        let history = newAIService.conversationHistory[testAgent.id.uuidString] ?? []
        XCTAssertGreaterThanOrEqual(history.count, 2, "Should have at least 2 messages")
        
        // Verify the message is in history
        let userMessages = history.filter { $0.isFromUser }
        XCTAssertTrue(userMessages.contains { $0.content.contains("Golden Retriever") }, "Should contain user message")
    }
    
    func testClearConversationHistory() async throws {
        // Test clearing conversation history
        let message = "What should I feed my dog?"
        
        // Send message
        let response = await aiService.sendMessage(to: testAgent, message: message, pet: testPet)
        XCTAssertFalse(response.isEmpty, "Response should not be empty")
        
        // Verify history exists
        var history = aiService.conversationHistory[testAgent.id.uuidString] ?? []
        XCTAssertGreaterThanOrEqual(history.count, 2, "Should have at least 2 messages")
        
        // Clear history
        aiService.clearConversationHistory(for: testAgent.id)
        
        // Verify history is cleared
        history = aiService.conversationHistory[testAgent.id.uuidString] ?? []
        XCTAssertEqual(history.count, 0, "History should be empty after clearing")
    }
    
    // MARK: - Agent-Specific Response Tests
    
    func testNutritionAgentResponse() async throws {
        // Test that Dr. Nutrition provides nutrition-specific responses
        let nutritionAgent = AIAgent(
            name: "Dr. Nutrition",
            iconName: "🥗",
            description: "Nutrition specialist",
            specialty: "Nutrition & Diet",
            specialties: ["Diet Planning", "Meal Scheduling", "Weight Management"],
            gradientColors: ["#4CAF50", "#8BC34A"],
            isPremium: false,
            systemPrompt: "You are Dr. Nutrition, a specialized pet nutrition expert.",
            conversationStarters: ["What's the best diet for my pet?"],
            responseConfig: AIResponseConfig(temperature: 0.7, tone: "caring", responseStyle: "detailed", expertise: "intermediate")
        )
        
        let message = "What should I feed my dog?"
        let response = await aiService.sendMessage(to: nutritionAgent, message: message, pet: testPet)
        
        // Verify response is nutrition-focused
        XCTAssertTrue(response.lowercased().contains("nutrition") || 
                     response.lowercased().contains("diet") || 
                     response.lowercased().contains("food") || 
                     response.lowercased().contains("feeding") ||
                     response.lowercased().contains("calories") ||
                     response.lowercased().contains("meal"),
                     "Response should be nutrition-focused")
        
        // Verify response mentions the pet's name
        XCTAssertTrue(response.contains(testPet.name), "Response should mention pet's name")
    }
    
    func testHealthAgentResponse() async throws {
        // Test that Health Guardian provides health-specific responses
        let healthAgent = AIAgent(
            name: "Health Guardian",
            iconName: "🏥",
            description: "Health specialist",
            specialty: "Health & Emergency",
            specialties: ["Health Monitoring", "Emergency Protocols", "Symptom Analysis"],
            gradientColors: ["#F44336", "#E91E63"],
            isPremium: false,
            systemPrompt: "You are Health Guardian, a specialized medical AI assistant.",
            conversationStarters: ["Check my pet's symptoms"],
            responseConfig: AIResponseConfig(temperature: 0.4, tone: "professional", responseStyle: "step-by-step", expertise: "advanced")
        )
        
        let message = "My dog seems lethargic"
        let response = await aiService.sendMessage(to: healthAgent, message: message, pet: testPet)
        
        // Verify response is health-focused
        XCTAssertTrue(response.lowercased().contains("health") || 
                     response.lowercased().contains("symptom") || 
                     response.lowercased().contains("vet") || 
                     response.lowercased().contains("medical") ||
                     response.lowercased().contains("lethargic") ||
                     response.lowercased().contains("consult"),
                     "Response should be health-focused")
    }
    
    func testTrainingAgentResponse() async throws {
        // Test that Trainer Pro provides training-specific responses
        let trainingAgent = AIAgent(
            name: "Trainer Pro",
            iconName: "🎾",
            description: "Training specialist",
            specialty: "Training & Behavior",
            specialties: ["Behavior Training", "Obedience", "Problem Solving"],
            gradientColors: ["#2196F3", "#03DAC5"],
            isPremium: false,
            systemPrompt: "You are Trainer Pro, a professional pet trainer.",
            conversationStarters: ["What behavior would you like to work on?"],
            responseConfig: AIResponseConfig(temperature: 0.5, tone: "encouraging", responseStyle: "step-by-step", expertise: "intermediate")
        )
        
        let message = "My dog won't sit when I ask"
        let response = await aiService.sendMessage(to: trainingAgent, message: message, pet: testPet)
        
        // Verify response is training-focused
        XCTAssertTrue(response.lowercased().contains("training") || 
                     response.lowercased().contains("behavior") || 
                     response.lowercased().contains("sit") || 
                     response.lowercased().contains("command") ||
                     response.lowercased().contains("positive") ||
                     response.lowercased().contains("reinforcement"),
                     "Response should be training-focused")
    }
    
    // MARK: - Pet Context Tests
    
    func testPetContextIntegration() async throws {
        // Test that responses include pet-specific information
        let message = "What's the best diet for my pet?"
        let response = await aiService.sendMessage(to: testAgent, message: message, pet: testPet)
        
        // Verify response includes pet-specific information
        XCTAssertTrue(response.contains(testPet.name), "Response should mention pet's name")
        XCTAssertTrue(response.contains(testPet.species), "Response should mention pet's species")
        XCTAssertTrue(response.contains(testPet.breed), "Response should mention pet's breed")
        
        // Verify response considers pet's characteristics
        XCTAssertTrue(response.lowercased().contains("golden retriever") || 
                     response.lowercased().contains("dog") ||
                     response.lowercased().contains("high activity") ||
                     response.lowercased().contains("65"),
                     "Response should consider pet's characteristics")
    }
    
    func testPetContextWithoutPet() async throws {
        // Test response when no pet is provided
        let message = "What's the best diet for my pet?"
        let response = await aiService.sendMessage(to: testAgent, message: message, pet: nil)
        
        // Verify response asks for pet selection
        XCTAssertTrue(response.lowercased().contains("pet") || 
                     response.lowercased().contains("select") ||
                     response.lowercased().contains("which"),
                     "Response should ask for pet selection when no pet is provided")
    }
    
    // MARK: - Apple Intelligence Integration Tests
    
    func testAppleIntelligenceAvailability() {
        // Test Apple Intelligence service availability
        XCTAssertNotNil(appleIntelligenceService, "Apple Intelligence service should be available")
        XCTAssertTrue(appleIntelligenceService.isWritingToolsAvailable, "Writing tools should be available")
        XCTAssertTrue(appleIntelligenceService.isImagePlaygroundAvailable, "Image playground should be available")
        XCTAssertTrue(appleIntelligenceService.isVisualIntelligenceAvailable, "Visual intelligence should be available")
    }
    
    func testAppleIntelligenceResponseEnhancement() async throws {
        // Test that Apple Intelligence enhances responses
        let message = "What should I feed my dog?"
        let response = await aiService.sendMessage(to: testAgent, message: message, pet: testPet)
        
        // Verify response includes Apple Intelligence features
        XCTAssertTrue(response.contains("Apple Intelligence") || 
                     response.contains("🍎") ||
                     response.contains("Hey Siri") ||
                     response.contains("local processing") ||
                     response.contains("privacy"),
                     "Response should include Apple Intelligence features")
    }
    
    // MARK: - Performance Tests
    
    func testResponseTime() async throws {
        // Test that responses are generated quickly
        let message = "What should I feed my dog?"
        
        let startTime = Date()
        let response = await aiService.sendMessage(to: testAgent, message: message, pet: testPet)
        let endTime = Date()
        
        let responseTime = endTime.timeIntervalSince(startTime)
        
        XCTAssertFalse(response.isEmpty, "Response should not be empty")
        XCTAssertLessThan(responseTime, 2.0, "Response should be generated in less than 2 seconds")
    }
    
    func testConcurrentRequests() async throws {
        // Test handling multiple concurrent requests
        let messages = [
            "What should I feed my dog?",
            "How much exercise does he need?",
            "What's the best training approach?"
        ]
        
        let startTime = Date()
        
        // Send concurrent requests
        async let response1 = aiService.sendMessage(to: testAgent, message: messages[0], pet: testPet)
        async let response2 = aiService.sendMessage(to: testAgent, message: messages[1], pet: testPet)
        async let response3 = aiService.sendMessage(to: testAgent, message: messages[2], pet: testPet)
        
        let responses = await [response1, response2, response3]
        let endTime = Date()
        
        let totalTime = endTime.timeIntervalSince(startTime)
        
        // Verify all responses are generated
        for (index, response) in responses.enumerated() {
            XCTAssertFalse(response.isEmpty, "Response \(index + 1) should not be empty")
        }
        
        // Verify reasonable performance for concurrent requests
        XCTAssertLessThan(totalTime, 5.0, "Concurrent requests should complete in reasonable time")
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorHandling() async throws {
        // Test error handling with invalid input
        let emptyMessage = ""
        let response = await aiService.sendMessage(to: testAgent, message: emptyMessage, pet: testPet)
        
        // Should handle empty message gracefully
        XCTAssertFalse(response.isEmpty, "Should provide a response even for empty message")
    }
    
    func testInvalidAgentHandling() async throws {
        // Test handling of invalid agent
        let invalidAgent = AIAgent(
            name: "Invalid Agent",
            iconName: "❓",
            description: "Invalid agent for testing",
            specialty: "Invalid",
            specialties: ["Invalid"],
            gradientColors: ["#000000"],
            isPremium: false,
            systemPrompt: "Invalid agent",
            conversationStarters: ["Invalid"],
            responseConfig: AIResponseConfig(temperature: 0.0, tone: "neutral", responseStyle: "concise", expertise: "beginner")
        )
        
        let message = "Test message"
        let response = await aiService.sendMessage(to: invalidAgent, message: message, pet: testPet)
        
        // Should handle invalid agent gracefully
        XCTAssertFalse(response.isEmpty, "Should provide a response even for invalid agent")
    }
} 