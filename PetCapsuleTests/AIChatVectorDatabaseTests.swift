//
//  AIChatVectorDatabaseTests.swift
//  PetCapsuleTests
//
//  Comprehensive tests for AI Chat Vector Database functionality
//  Tests cross-agent chat history, semantic search, and pet context
//

import XCTest
import SwiftUI
@testable import PetCapsule

@available(iOS 18.0, *)
final class AIChatVectorDatabaseTests: XCTestCase {
    
    var aiService: EnhancedAIAgentService!
    var vectorService: LocalVectorDatabaseService!
    var testPet: Pet!
    var testAgent: AIAgent!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // Initialize services
        aiService = EnhancedAIAgentService.shared
        vectorService = LocalVectorDatabaseService.shared
        
        // Create test pet
        testPet = Pet(
            name: "<PERSON>",
            species: "Dog",
            breed: "Golden Retriever",
            age: 3.0,
            weight: 65.0,
            specialInstructions: "Allergic to chicken"
        )
        
        // Get test agent
        testAgent = aiService.availableAgents.first { $0.name == "Health Guardian" }!
    }
    
    override func tearDownWithError() throws {
        // Clean up test data
        aiService.clearConversationHistory(for: testAgent.id)
        try super.tearDownWithError()
    }
    
    // MARK: - Vector Database Tests
    
    func testVectorDatabaseInitialization() {
        XCTAssertNotNil(vectorService, "Vector database service should be initialized")
    }
    
    func testChatMessageVectorStorage() {
        // Test storing chat messages in vector database
        let testMessage = "My dog has been coughing for the past week"
        let agentId = testAgent.id
        let petId = testPet.id
        
        vectorService.addChatMessage(
            agentId: agentId,
            petId: petId,
            content: testMessage,
            messageType: "user",
            timestamp: Date()
        )
        
        // Verify message was stored
        let searchResults = vectorService.searchChatHistory(
            query: "coughing",
            agentId: agentId,
            petId: petId,
            limit: 5
        )
        
        XCTAssertFalse(searchResults.isEmpty, "Should find stored message")
        XCTAssertEqual(searchResults.first?.content, testMessage, "Should return correct message content")
    }
    
    func testSemanticSearchFunctionality() {
        // Test semantic search across different phrasings
        let originalMessage = "My dog has been coughing for the past week"
        let searchQuery = "pet respiratory issues"
        
        vectorService.addChatMessage(
            agentId: testAgent.id,
            petId: testPet.id,
            content: originalMessage,
            messageType: "user",
            timestamp: Date()
        )
        
        let searchResults = vectorService.searchChatHistory(
            query: searchQuery,
            agentId: testAgent.id,
            petId: testPet.id,
            limit: 5
        )
        
        XCTAssertFalse(searchResults.isEmpty, "Semantic search should find relevant results")
        XCTAssertGreaterThan(searchResults.first?.relevanceScore ?? 0, 0.1, "Should have reasonable relevance score")
    }
    
    func testPetContextVectorStorage() {
        // Test storing pet context information
        let contextContent = "Buddy is allergic to chicken and needs grain-free food"
        let contextType = "health"
        
        vectorService.addPetContext(
            petId: testPet.id,
            petName: testPet.name,
            content: contextContent,
            contextType: contextType
        )
        
        // Verify context was stored
        let searchResults = vectorService.searchPetContext(
            query: "allergies",
            petId: testPet.id,
            contextType: contextType,
            limit: 5
        )
        
        XCTAssertFalse(searchResults.isEmpty, "Should find stored pet context")
        XCTAssertEqual(searchResults.first?.content, contextContent, "Should return correct context content")
    }
    
    // MARK: - Cross-Agent Chat History Tests
    
    func testCrossAgentHistoryAccess() async {
        // Test Pet Master accessing chat history from other agents
        
        // Add messages to different agents
        let healthAgent = aiService.availableAgents.first { $0.name == "Health Guardian" }!
        let nutritionAgent = aiService.availableAgents.first { $0.name == "Dr. Nutrition" }!
        
        // Add test messages
        vectorService.addChatMessage(
            agentId: healthAgent.id,
            petId: testPet.id,
            content: "Buddy has been coughing",
            messageType: "user",
            timestamp: Date()
        )
        
        vectorService.addChatMessage(
            agentId: nutritionAgent.id,
            petId: testPet.id,
            content: "Buddy needs grain-free food",
            messageType: "user",
            timestamp: Date()
        )
        
        // Test cross-agent search
        let crossAgentResults = vectorService.searchCrossAgentHistory(
            query: "Buddy health issues",
            petId: testPet.id,
            limit: 10
        )
        
        XCTAssertFalse(crossAgentResults.isEmpty, "Should find messages from multiple agents")
        XCTAssertGreaterThan(crossAgentResults.count, 1, "Should find messages from different agents")
    }
    
    func testPetSegregatedHistory() {
        // Test that chat history is properly segregated by pet
        
        let pet2 = Pet(
            name: "Luna",
            species: "Cat",
            breed: "Siamese",
            age: 2.0,
            weight: 8.0
        )
        
        // Add messages for different pets
        vectorService.addChatMessage(
            agentId: testAgent.id,
            petId: testPet.id,
            content: "Buddy's health update",
            messageType: "user",
            timestamp: Date()
        )
        
        vectorService.addChatMessage(
            agentId: testAgent.id,
            petId: pet2.id,
            content: "Luna's health update",
            messageType: "user",
            timestamp: Date()
        )
        
        // Search for Buddy's messages only
        let buddyResults = vectorService.searchChatHistory(
            query: "health update",
            agentId: testAgent.id,
            petId: testPet.id,
            limit: 5
        )
        
        // Search for Luna's messages only
        let lunaResults = vectorService.searchChatHistory(
            query: "health update",
            agentId: testAgent.id,
            petId: pet2.id,
            limit: 5
        )
        
        XCTAssertFalse(buddyResults.isEmpty, "Should find Buddy's messages")
        XCTAssertFalse(lunaResults.isEmpty, "Should find Luna's messages")
        XCTAssertNotEqual(buddyResults.first?.content, lunaResults.first?.content, "Messages should be different")
    }
    
    // MARK: - AI Service Integration Tests
    
    func testAIServiceWithVectorDatabase() async throws {
        // Test that AI service properly uses vector database
        
        // Send a message through AI service
        let userMessage = "What should I do about Buddy's coughing?"
        let response = try await aiService.sendMessage(
            to: testAgent,
            message: userMessage,
            pet: testPet
        )
        
        XCTAssertFalse(response.isEmpty, "Should get a response from AI service")
        
        // Verify message was stored in vector database
        let searchResults = vectorService.searchChatHistory(
            query: "coughing",
            agentId: testAgent.id,
            petId: testPet.id,
            limit: 5
        )
        
        XCTAssertFalse(searchResults.isEmpty, "AI service should store messages in vector database")
    }
    
    func testPetMasterCrossAgentAccess() async throws {
        // Test Pet Master's ability to access other agents' chat history
        
        let petMaster = aiService.availableAgents.first { $0.name == "Pet Master" }!
        let healthAgent = aiService.availableAgents.first { $0.name == "Health Guardian" }!
        
        // Add some history to Health Guardian
        vectorService.addChatMessage(
            agentId: healthAgent.id,
            petId: testPet.id,
            content: "Buddy has been coughing and sneezing",
            messageType: "user",
            timestamp: Date()
        )
        
        vectorService.addChatMessage(
            agentId: healthAgent.id,
            petId: testPet.id,
            content: "This could be allergies or respiratory infection",
            messageType: "ai",
            timestamp: Date()
        )
        
        // Ask Pet Master about the same issue
        let petMasterQuery = "What's wrong with Buddy and what should I do?"
        let response = try await aiService.sendMessage(
            to: petMaster,
            message: petMasterQuery,
            pet: testPet
        )
        
        XCTAssertFalse(response.isEmpty, "Pet Master should provide a response")
        // Pet Master should have access to Health Guardian's previous conversation
    }
    
    // MARK: - Performance Tests
    
    func testVectorSearchPerformance() {
        // Test performance with multiple messages
        
        // Add multiple test messages
        for i in 1...50 {
            vectorService.addChatMessage(
                agentId: testAgent.id,
                petId: testPet.id,
                content: "Test message \(i) about Buddy's health",
                messageType: "user",
                timestamp: Date()
            )
        }
        
        // Measure search performance
        let startTime = CFAbsoluteTimeGetCurrent()
        let searchResults = vectorService.searchChatHistory(
            query: "Buddy health",
            agentId: testAgent.id,
            petId: testPet.id,
            limit: 10
        )
        let endTime = CFAbsoluteTimeGetCurrent()
        
        let searchTime = endTime - startTime
        
        XCTAssertFalse(searchResults.isEmpty, "Should find results")
        XCTAssertLessThan(searchTime, 1.0, "Search should complete within 1 second")
    }
    
    // MARK: - Relevance Scoring Tests
    
    func testRelevanceScoring() {
        // Test that relevance scoring works correctly
        
        let specificMessage = "Buddy the Golden Retriever has been coughing"
        let generalMessage = "My dog has been coughing"
        
        vectorService.addChatMessage(
            agentId: testAgent.id,
            petId: testPet.id,
            content: specificMessage,
            messageType: "user",
            timestamp: Date()
        )
        
        vectorService.addChatMessage(
            agentId: testAgent.id,
            petId: testPet.id,
            content: generalMessage,
            messageType: "user",
            timestamp: Date()
        )
        
        // Search with specific query
        let specificResults = vectorService.searchChatHistory(
            query: "Buddy Golden Retriever coughing",
            agentId: testAgent.id,
            petId: testPet.id,
            limit: 5
        )
        
        XCTAssertFalse(specificResults.isEmpty, "Should find results")
        if specificResults.count >= 2 {
            // The more specific message should have higher relevance
            let specificScore = specificResults.first { $0.content == specificMessage }?.relevanceScore ?? 0
            let generalScore = specificResults.first { $0.content == generalMessage }?.relevanceScore ?? 0
            
            XCTAssertGreaterThanOrEqual(specificScore, generalScore, "More specific message should have higher relevance")
        }
    }
} 