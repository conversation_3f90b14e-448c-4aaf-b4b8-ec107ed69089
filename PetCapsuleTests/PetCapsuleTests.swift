//
//  PetCapsuleTests.swift
//  PetCapsuleTests
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import XCTest
@testable import PetCapsule

final class PetCapsuleTests: XCTestCase {

    override func setUpWithError() throws {
        // Put setup code here. This method is called before the invocation of each test method in the class.
    }

    override func tearDownWithError() throws {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
    }

    func testPetModelCreation() throws {
        // Test Pet model creation with proper models
        let pet = Pet(
            name: "<PERSON>",
            species: "<PERSON>",
            age: 24
        )
        
        XCTAssertEqual(pet.name, "<PERSON>")
        XCTAssertEqual(pet.species, "<PERSON>")
        XCTAssertEqual(pet.age, 24)
    }

    func testMemoryModelCreation() throws {
        // Test Memory model creation with proper models
        let memory = Memory(
            title: "First Walk",
            content: "Buddy's first walk in the park",
            type: .text
        )
        
        XCTAssertEqual(memory.title, "First Walk")
        XCTAssertEqual(memory.content, "Buddy's first walk in the park")
        XCTAssertEqual(memory.type, .text)
    }

    func testPerformanceExample() throws {
        // This is an example of a performance test case.
        self.measure {
            // Put the code you want to measure the time of here.
        }
    }
}
