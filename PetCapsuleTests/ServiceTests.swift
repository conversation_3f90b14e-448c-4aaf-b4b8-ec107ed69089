//
//  ServiceTests.swift
//  PetCapsuleTests
//
//  Comprehensive unit tests for service layer
//
import XCTest
@testable import PetCapsule
@MainActor
final class ServiceTests: XCTestCase {
    var optimizedServiceManager: OptimizedServiceManager!
    var performanceMonitor: PerformanceMonitoringService!
    override func setUpWithError() throws {
        optimizedServiceManager = OptimizedServiceManager.shared
        performanceMonitor = PerformanceMonitoringService.shared
    }
    override func tearDownWithError() throws {
        optimizedServiceManager = nil
        performanceMonitor = nil
    }
    // MARK: - Service Manager Tests
    func testOptimizedServiceManagerInitialization() throws {
        XCTAssertTrue(optimizedServiceManager.isInitialized, "Service manager should be initialized")
        XCTAssertNotNil(optimizedServiceManager.coreServices, "Core services should be available")
    }
    func testCoreServicesAvailability() throws {
        let coreServices = optimizedServiceManager.coreServices!
        XCTAssertNotNil(coreServices.supabase, "Supabase service should be available")
        XCTAssertNotNil(coreServices.authentication, "Authentication service should be available")
        XCTAssertNotNil(coreServices.theme, "Theme manager should be available")
        XCTAssertNotNil(coreServices.performance, "Performance manager should be available")
    }
    func testLazyServiceLoading() throws {
        // Test that services are loaded on demand
        let aiServices = optimizedServiceManager.aiServices
        XCTAssertNotNil(aiServices, "AI services should be loaded on demand")
        let dataServices = optimizedServiceManager.dataServices
        XCTAssertNotNil(dataServices, "Data services should be loaded on demand")
        let healthServices = optimizedServiceManager.healthServices
        XCTAssertNotNil(healthServices, "Health services should be loaded on demand")
    }
    func testServiceGroupTypes() throws {
        let allTypes = ServiceGroupType.allCases
        XCTAssertEqual(allTypes.count, 7, "Should have 7 service group types")
        XCTAssertTrue(allTypes.contains(.core), "Should contain core service type")
        XCTAssertTrue(allTypes.contains(.ai), "Should contain AI service type")
        XCTAssertTrue(allTypes.contains(.data), "Should contain data service type")
    }
    // MARK: - Performance Monitoring Tests
    func testPerformanceMonitoringInitialization() throws {
        XCTAssertNotNil(performanceMonitor, "Performance monitor should be initialized")
        XCTAssertGreaterThan(performanceMonitor.currentFPS, 0, "FPS should be greater than 0")
    }
    func testPerformanceMetrics() throws {
        // Test that performance metrics are within reasonable ranges
        XCTAssertGreaterThanOrEqual(performanceMonitor.currentFPS, 0, "FPS should be non-negative")
        XCTAssertLessThanOrEqual(performanceMonitor.currentFPS, 120, "FPS should be reasonable")
        XCTAssertGreaterThanOrEqual(performanceMonitor.memoryUsage, 0, "Memory usage should be non-negative")
        XCTAssertGreaterThanOrEqual(performanceMonitor.cpuUsage, 0, "CPU usage should be non-negative")
        XCTAssertLessThanOrEqual(performanceMonitor.cpuUsage, 100, "CPU usage should not exceed 100%")
    }
    func testPerformanceAlerts() throws {
        let initialAlertCount = performanceMonitor.performanceAlerts.count
        // Test adding a performance alert
        let testAlert = PerformanceAlert.lowFPS(30.0)
        performanceMonitor.performanceAlerts.append(testAlert)
        XCTAssertEqual(performanceMonitor.performanceAlerts.count, initialAlertCount + 1, "Alert should be added")
        XCTAssertEqual(performanceMonitor.performanceAlerts.last?.type, .lowFPS(30.0), "Alert type should match")
    }
    // MARK: - Data Model Tests
    func testPetModelCreation() throws {
        let pet = Pet(
            id: UUID().uuidString,
            name: "Test Pet",
            species: "Dog",
            breed: "Golden Retriever",
            age: 3,
            weight: 65.0
        )
        XCTAssertEqual(pet.name, "Test Pet", "Pet name should match")
        XCTAssertEqual(pet.species, "Dog", "Pet species should match")
        XCTAssertEqual(pet.breed, "Golden Retriever", "Pet breed should match")
        XCTAssertEqual(pet.age, 3, "Pet age should match")
        XCTAssertEqual(pet.weight, 65.0, "Pet weight should match")
    }
    func testMemoryModelCreation() throws {
        let memory = Memory(
            id: UUID(),
            title: "Test Memory",
            content: "A wonderful day at the park",
            mediaURL: nil,
            petId: UUID().uuidString,
            userId: UUID().uuidString
        )
        XCTAssertEqual(memory.title, "Test Memory", "Memory title should match")
        XCTAssertEqual(memory.content, "A wonderful day at the park", "Memory content should match")
        XCTAssertNotNil(memory.createdAt, "Memory should have creation date")
    }
    // MARK: - AI Service Tests
    @available(iOS 18.0, *)
    func testAIServiceInitialization() throws {
        let aiServices = optimizedServiceManager.aiServices
        XCTAssertNotNil(aiServices.appleIntelligence, "Apple Intelligence service should be available")
        XCTAssertNotNil(aiServices.conversation, "Conversation service should be available")
        XCTAssertNotNil(aiServices.chatService, "Chat service should be available")
        XCTAssertNotNil(aiServices.supportService, "Support service should be available")
    }
    // MARK: - Authentication Tests
    func testAuthenticationServiceInitialization() throws {
        let authService = optimizedServiceManager.coreServices.authentication
        XCTAssertNotNil(authService, "Authentication service should be initialized")
        XCTAssertFalse(authService.isAuthenticated, "Should not be authenticated initially")
    }
    // MARK: - Theme Manager Tests
    func testThemeManagerInitialization() throws {
        let themeManager = optimizedServiceManager.coreServices.theme
        XCTAssertNotNil(themeManager, "Theme manager should be initialized")
        XCTAssertNotNil(themeManager.currentTheme, "Should have a current theme")
    }
    // MARK: - Performance Tests
    func testServiceLoadingPerformance() throws {
        measure {
            // Test the performance of loading all service groups
            _ = optimizedServiceManager.aiServices
            _ = optimizedServiceManager.dataServices
            _ = optimizedServiceManager.healthServices
            _ = optimizedServiceManager.plannerServices
            _ = optimizedServiceManager.memoryServices
            _ = optimizedServiceManager.securityServices
        }
    }
    func testMemoryUsageOptimization() throws {
        // Test that memory usage is reasonable
        let initialMemory = performanceMonitor.memoryUsage
        // Load all services
        _ = optimizedServiceManager.aiServices
        _ = optimizedServiceManager.dataServices
        _ = optimizedServiceManager.healthServices
        let finalMemory = performanceMonitor.memoryUsage
        let memoryIncrease = finalMemory - initialMemory
        // Memory increase should be reasonable (less than 50MB for all services)
        XCTAssertLessThan(memoryIncrease, 50.0, "Memory increase should be reasonable")
    }
    // MARK: - Integration Tests
    func testServiceIntegration() throws {
        // Test that services can work together
        let dataServices = optimizedServiceManager.dataServices
        let aiServices = optimizedServiceManager.aiServices
        XCTAssertNotNil(dataServices.realData, "Real data service should be available")
        XCTAssertNotNil(aiServices.conversation, "Conversation service should be available")
        // Test that services can reference each other without circular dependencies
        XCTAssertNoThrow(dataServices.realData.pets, "Should be able to access pets data")
    }
    // MARK: - Error Handling Tests
    func testServiceErrorHandling() throws {
        // Test that services handle errors gracefully
        let dataServices = optimizedServiceManager.dataServices
        // Test with invalid data
        XCTAssertNoThrow(dataServices.realData.errorMessage, "Should handle errors gracefully")
    }
    // MARK: - Concurrency Tests
    func testConcurrentServiceAccess() throws {
        let expectation = XCTestExpectation(description: "Concurrent service access")
        expectation.expectedFulfillmentCount = 3
        // Test concurrent access to different service groups
        DispatchQueue.global().async {
            _ = self.optimizedServiceManager.aiServices
            expectation.fulfill()
        }
        DispatchQueue.global().async {
            _ = self.optimizedServiceManager.dataServices
            expectation.fulfill()
        }
        DispatchQueue.global().async {
            _ = self.optimizedServiceManager.healthServices
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 5.0)
    }
}
// MARK: - Mock Objects for Testing
class MockPetDataService: ObservableObject {
    @Published var pets: [Pet] = []
    @Published var isLoading = false
    func loadPets() {
        isLoading = true
        // Simulate loading
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.pets = [
                Pet(id: UUID().uuidString, name: "Test Dog", species: "Dog", breed: "Labrador", age: 2, weight: 30.0),
                Pet(id: UUID().uuidString, name: "Test Cat", species: "Cat", breed: "Persian", age: 1, weight: 8.0)
            ]
            self.isLoading = false
        }
    }
}
