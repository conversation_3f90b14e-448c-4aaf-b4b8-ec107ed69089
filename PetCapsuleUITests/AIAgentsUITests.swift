//
//  AIAgentsUITests.swift
//  PetCapsuleUITests
//
//  Specialized UI Tests for AI Agents System
//  Tests Pet Master, Knowledge Base, and Agent Interactions
//

import XCTest

final class AIAgentsUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launchArguments = ["UI_TESTING", "AI_AGENTS_TESTING"]
        app.launch()
        
        // Wait for app to fully load and skip splash screen
        sleep(5) // Allow splash screen to complete
    }

    override func tearDownWithError() throws {
        app.terminate()
        app = nil
    }
    
    // MARK: - Pet Support Navigation Tests
    
    @MainActor
    func testPetSupportAccess() throws {
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.waitForExistence(timeout: 10), "Tab bar should exist")
        
        // Navigate to Pet Support tab (index 2 based on MainAppView)
        if tabBar.buttons.count > 2 {
            let petSupportTab = tabBar.buttons.element(boundBy: 2)
            petSupportTab.tap()
            sleep(3)
            
            // Take screenshot of Pet Support interface
            let petSupportScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: petSupportScreenshot)
            attachment.name = "Pet Support Main Interface"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            print("✅ Pet Support accessed successfully")
        } else {
            XCTFail("Pet Support tab not available")
        }
    }
    
    @MainActor
    func testAIAgentsHubAccess() throws {
        // Navigate to Pet Support
        navigateToPetSupport()
        
        // Look for AI Agents Hub or related elements
        let aiAgentsElements = [
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'AI Agents'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Agents Hub'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Pet Master'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Dr.'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Agent'"))
        ]
        
        var agentElementsFound = 0
        for elementQuery in aiAgentsElements {
            agentElementsFound += elementQuery.count
        }
        
        print("✅ Found \(agentElementsFound) AI agent-related elements")
        
        // Take screenshot showing available AI agents
        let agentsScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: agentsScreenshot)
        attachment.name = "AI Agents Available"
        attachment.lifetime = .keepAlways
        add(attachment)
        
        // Test interaction with first available agent
        for elementQuery in aiAgentsElements {
            if elementQuery.count > 0 {
                let firstAgent = elementQuery.firstMatch
                if firstAgent.exists {
                    firstAgent.tap()
                    sleep(2)
                    
                    let agentDetailScreenshot = app.screenshot()
                    let agentDetailAttachment = XCTAttachment(screenshot: agentDetailScreenshot)
                    agentDetailAttachment.name = "AI Agent Detail Interface"
                    agentDetailAttachment.lifetime = .keepAlways
                    add(agentDetailAttachment)
                    
                    print("✅ Successfully interacted with AI agent")
                    break
                }
            }
        }
    }
    
    @MainActor
    func testPetMasterAgentFunctionality() throws {
        navigateToPetSupport()
        
        // Look for Pet Master specifically
        let petMasterSelectors = [
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Pet Master'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] '🎯'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Master'")),
            app.staticTexts.matching(NSPredicate(format: "label CONTAINS[c] 'Pet Master'"))
        ]
        
        var petMasterFound = false
        for selector in petMasterSelectors {
            if selector.count > 0 {
                let element = selector.firstMatch
                if element.exists {
                    element.tap()
                    sleep(3)
                    petMasterFound = true
                    
                    // Take screenshot of Pet Master interface
                    let petMasterScreenshot = app.screenshot()
                    let attachment = XCTAttachment(screenshot: petMasterScreenshot)
                    attachment.name = "Pet Master Interface"
                    attachment.lifetime = .keepAlways
                    add(attachment)
                    
                    print("✅ Pet Master agent accessed")
                    break
                }
            }
        }
        
        if petMasterFound {
            // Test chat interface elements
            testChatInterface()
        } else {
            print("❌ Pet Master agent not found - looking for alternative AI elements")
            // Fallback: test any available AI chat interface
            testAnyAvailableAIInterface()
        }
    }
    
    @MainActor
    func testSpecializedAgents() throws {
        // Navigate to Pet Support tab
        navigateToPetSupport()
        
        // Wait for enhanced Pet Support page to load
        let petSupportView = app.otherElements.containing(.staticText, identifier: "AI Agents Hub").firstMatch
        XCTAssertTrue(petSupportView.waitForExistence(timeout: 5), "Pet Support view should load")
        
        // Test Pet Master accessibility - using the new accessibility identifier
        let petMasterButton = app.buttons["🎯 Pet Master"]
        XCTAssertTrue(petMasterButton.waitForExistence(timeout: 3), "Pet Master button should be visible")
        
        // Test quick action buttons
        let quickActionButtons = [
            "Emergency", "Health Check", "Nutrition", "Training", "Grooming"
        ]
        
        for actionTitle in quickActionButtons {
            let actionButton = app.buttons.containing(.staticText, identifier: actionTitle).firstMatch
            if actionButton.exists {
                print("✅ Quick action '\(actionTitle)' found and accessible")
            }
        }
        
        // Test category selector
        let categoryButtons = [
            "All Agents", "Health & Care", "Lifestyle & Care", "Specialized", "Emergency"
        ]
        
        var categoriesFound = 0
        for category in categoryButtons {
            let categoryButton = app.buttons.containing(.staticText, identifier: category).firstMatch
            if categoryButton.exists {
                categoriesFound += 1
                print("✅ Category '\(category)' found")
            }
        }
        
        XCTAssertGreaterThan(categoriesFound, 0, "At least one category should be visible")
        
        // Test individual agent cards by looking for agent names
        let agentNames = [
            "Dr. Nutrition", "Health and Emergency", "Style Guru", 
            "Trainer Pro", "Shopping Assistant", "Pet Insurance Advisor"
        ]
        
        var agentsFound = 0
        for agentName in agentNames {
            let agentElements = app.staticTexts.matching(NSPredicate(format: "label CONTAINS[c] %@", agentName))
            if agentElements.count > 0 {
                agentsFound += 1
                print("✅ Agent '\(agentName)' found in UI")
            }
        }
        
        print("📊 Found \(agentsFound) out of \(agentNames.count) expected agents")
        XCTAssertGreaterThan(agentsFound, 3, "Should find at least 4 specialized agents")
        
        // Test agent stats
        let statsElements = [
            "Available Agents", "Response Time", "Uptime"
        ]
        
        for stat in statsElements {
            let statElement = app.staticTexts.containing(.staticText, identifier: stat).firstMatch
            if statElement.exists {
                print("✅ Stat '\(stat)' displayed")
            }
        }
        
        // Take screenshot of enhanced Pet Support page
        let enhancedScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: enhancedScreenshot)
        attachment.name = "Enhanced Pet Support - AI Agents Hub"
        attachment.lifetime = .keepAlways
        add(attachment)
        
        print("✅ Enhanced Pet Support UI tests completed successfully")
    }
    
    @MainActor
    func testConversationHistory() throws {
        navigateToPetSupport()
        
        // Look for conversation history elements
        let historyElements = [
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'history'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'conversation'")),
            app.scrollViews.matching(NSPredicate(format: "identifier CONTAINS[c] 'conversation'")),
            app.scrollViews.matching(NSPredicate(format: "identifier CONTAINS[c] 'chat'"))
        ]
        
        var historyFound = false
        for elementQuery in historyElements {
            if elementQuery.count > 0 {
                historyFound = true
                break
            }
        }
        
        if historyFound {
            print("✅ Conversation history interface elements found")
        } else {
            print("ℹ️ No specific conversation history elements found - this may be integrated within agent interfaces")
        }
        
        // Take screenshot of current state
        let historyScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: historyScreenshot)
        attachment.name = "Conversation History State"
        attachment.lifetime = .keepAlways
        add(attachment)
    }
    
    @MainActor
    func testKnowledgeBaseIntegration() throws {
        // Try accessing Knowledge Base from More section
        let tabBar = app.tabBars.firstMatch
        if tabBar.buttons.count > 4 {
            let moreTab = tabBar.buttons.element(boundBy: 4)
            moreTab.tap()
            sleep(2)
            
            // Look for Knowledge Base
            let knowledgeBaseElements = [
                app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Knowledge Base'")),
                app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Knowledge'")),
                app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'Help'")),
                app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'FAQ'"))
            ]
            
            var knowledgeBaseFound = false
            for query in knowledgeBaseElements {
                if query.count > 0 {
                    let element = query.firstMatch
                    if element.exists {
                        element.tap()
                        sleep(2)
                        knowledgeBaseFound = true
                        
                        // Take screenshot of Knowledge Base
                        let kbScreenshot = app.screenshot()
                        let attachment = XCTAttachment(screenshot: kbScreenshot)
                        attachment.name = "Knowledge Base Interface"
                        attachment.lifetime = .keepAlways
                        add(attachment)
                        
                        print("✅ Knowledge Base accessed")
                        break
                    }
                }
            }
            
            if !knowledgeBaseFound {
                print("ℹ️ Knowledge Base not found in More section - may be integrated elsewhere")
            }
        }
    }
    
    @MainActor
    func testVoiceCapabilities() throws {
        navigateToPetSupport()
        
        // Look for voice-related elements
        let voiceElements = [
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'voice'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'speak'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'microphone'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'record'"))
        ]
        
        var voiceElementsFound = 0
        for query in voiceElements {
            voiceElementsFound += query.count
        }
        
        print("✅ Found \(voiceElementsFound) voice-related elements")
        
        // Take screenshot showing voice capabilities
        let voiceScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: voiceScreenshot)
        attachment.name = "Voice Capabilities"
        attachment.lifetime = .keepAlways
        add(attachment)
        
        // Test voice interaction if available
        for query in voiceElements {
            if query.count > 0 {
                let voiceElement = query.firstMatch
                if voiceElement.exists {
                    voiceElement.tap()
                    sleep(2)
                    
                    let voiceInterfaceScreenshot = app.screenshot()
                    let voiceInterfaceAttachment = XCTAttachment(screenshot: voiceInterfaceScreenshot)
                    voiceInterfaceAttachment.name = "Voice Interface Active"
                    voiceInterfaceAttachment.lifetime = .keepAlways
                    add(voiceInterfaceAttachment)
                    
                    print("✅ Voice interface activated")
                    break
                }
            }
        }
    }
    
    @MainActor
    func testAgentAvailabilityStatus() throws {
        navigateToPetSupport()
        
        // Test overall agent system availability
        let allElements = app.descendants(matching: .any)
        let buttonCount = app.buttons.count
        let textElementCount = app.staticTexts.count
        let scrollViewCount = app.scrollViews.count
        
        print("✅ Pet Support loaded with:")
        print("   - \(buttonCount) interactive buttons")
        print("   - \(textElementCount) text elements")
        print("   - \(scrollViewCount) scroll views")
        print("   - \(allElements.count) total UI elements")
        
        // Take comprehensive screenshot
        let statusScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: statusScreenshot)
        attachment.name = "Agent System Status"
        attachment.lifetime = .keepAlways
        add(attachment)
        
        // Verify minimum viable interface
        XCTAssertGreaterThan(buttonCount, 0, "Pet Support should have interactive elements")
        XCTAssertGreaterThan(textElementCount, 0, "Pet Support should have text content")
    }
    
    // MARK: - Helper Methods
    
    private func navigateToPetSupport() {
        let tabBar = app.tabBars.firstMatch
        if tabBar.buttons.count > 2 {
            let petSupportTab = tabBar.buttons.element(boundBy: 2)
            petSupportTab.tap()
            sleep(2)
        }
    }
    
    private func goBackToPetSupport() {
        // Try different ways to go back
        let backButtons = [
            app.navigationBars.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'back'")),
            app.navigationBars.buttons.matching(NSPredicate(format: "label CONTAINS[c] '<'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'close'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'done'"))
        ]
        
        for buttonQuery in backButtons {
            if buttonQuery.count > 0 {
                let backButton = buttonQuery.firstMatch
                if backButton.exists {
                    backButton.tap()
                    sleep(1)
                    return
                }
            }
        }
        
        // Fallback: navigate back to Pet Support tab
        navigateToPetSupport()
    }
    
    private func testChatInterface() {
        // Look for chat interface elements
        let chatElements = [
            app.textFields,
            app.textViews,
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'send'")),
            app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'message'"))
        ]
        
        var chatInterfaceFound = false
        for elementQuery in chatElements {
            if elementQuery.count > 0 {
                chatInterfaceFound = true
                break
            }
        }
        
        if chatInterfaceFound {
            print("✅ Chat interface elements found")
        } else {
            print("ℹ️ No traditional chat interface found - may use different interaction model")
        }
    }
    
    private func testAnyAvailableAIInterface() {
        // Look for any AI-related interactive elements
        let aiElements = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'AI' OR label CONTAINS[c] 'agent' OR label CONTAINS[c] 'assistant'"))
        
        if aiElements.count > 0 {
            let firstAI = aiElements.firstMatch
            if firstAI.exists {
                firstAI.tap()
                sleep(2)
                
                let aiScreenshot = app.screenshot()
                let attachment = XCTAttachment(screenshot: aiScreenshot)
                attachment.name = "Alternative AI Interface"
                attachment.lifetime = .keepAlways
                add(attachment)
                
                print("✅ Alternative AI interface accessed")
            }
        }
    }
    
    private func testBasicChatFunctionality(agentName: String) {
        // Test basic interaction with the agent interface
        let textFields = app.textFields
        let textViews = app.textViews
        let sendButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'send'"))
        
        if textFields.count > 0 || textViews.count > 0 {
            print("✅ \(agentName) has text input capability")
        }
        
        if sendButtons.count > 0 {
            print("✅ \(agentName) has send functionality")
        }
        
        // Test any available quick action buttons
        let quickActions = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'help' OR label CONTAINS[c] 'ask' OR label CONTAINS[c] 'question'"))
        if quickActions.count > 0 {
            print("✅ \(agentName) has \(quickActions.count) quick action options")
        }
    }
}
