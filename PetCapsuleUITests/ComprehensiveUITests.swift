//
//  ComprehensiveUITests.swift
//  PetCapsuleUITests
//
//  Comprehensive UI Testing Suite for PetCapsule App
//  Tests all major features including AI Agents, Memory Management, and Core Functionality
//

import XCTest

final class ComprehensiveUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        
        // Set launch arguments for testing
        app.launchArguments = ["UI_TESTING"]
        app.launch()
        
        // Wait for app to fully load and skip splash screen
        sleep(5) // Allow splash screen to complete
    }

    override func tearDownWithError() throws {
        app.terminate()
        app = nil
    }
    
    // MARK: - App Launch and Basic Navigation Tests
    
    @MainActor
    func testAppLaunchAndBasicNavigation() throws {
        // Test app launches successfully
        XCTAssertTrue(app.state == .runningForeground, "App should be running in foreground")
        
        // Take screenshot of launch state
        let launchScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: launchScreenshot)
        attachment.name = "App Launch State"
        attachment.lifetime = .keepAlways
        add(attachment)
        
        // Wait for main interface to load
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.waitForExistence(timeout: 10), "Tab bar should exist after loading")
        
        print("✅ App launched successfully with tab bar visible")
    }
    
    @MainActor
    func testTabNavigation() throws {
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.waitForExistence(timeout: 10), "Tab bar should exist")
        
        // Current app tabs based on MainAppView.swift
        let expectedTabs = ["Dashboard", "My Pets", "Pet Support", "Memories", "More"]
        
        for (index, tabName) in expectedTabs.enumerated() {
            // Try different ways to find the tab
            var tabButton = tabBar.buttons[tabName]
            
            if !tabButton.exists {
                // Try by accessibility identifier
                tabButton = tabBar.buttons.element(boundBy: index)
            }
            
            if tabButton.exists {
                tabButton.tap()
                sleep(2) // Allow time for navigation
                
                // Take screenshot of each tab
                let tabScreenshot = app.screenshot()
                let attachment = XCTAttachment(screenshot: tabScreenshot)
                attachment.name = "\(tabName) Tab"
                attachment.lifetime = .keepAlways
                add(attachment)
                
                print("✅ Successfully navigated to \(tabName)")
            } else {
                print("❌ \(tabName) tab not accessible")
            }
        }
    }
    
    // MARK: - Dashboard Tests
    
    @MainActor
    func testDashboardFunctionality() throws {
        let tabBar = app.tabBars.firstMatch
        
        // Navigate to Dashboard (should be default)
        if let dashboardTab = tabBar.buttons.allElementsBoundByIndex.first {
            dashboardTab.tap()
            sleep(2)
        }
        
        // Take screenshot of dashboard
        let dashboardScreenshot = app.screenshot()
        let attachment = XCTAttachment(screenshot: dashboardScreenshot)
        attachment.name = "Dashboard View"
        attachment.lifetime = .keepAlways
        add(attachment)
        
        // Look for dashboard elements
        let scrollViews = app.scrollViews
        let buttons = app.buttons
        let staticTexts = app.staticTexts
        
        print("✅ Dashboard loaded with \(scrollViews.count) scroll views, \(buttons.count) buttons, \(staticTexts.count) text elements")
    }
    
    // MARK: - Pet Support/AI Agents Testing
    
    @MainActor
    func testPetSupportAccess() throws {
        let tabBar = app.tabBars.firstMatch
        
        // Navigate to Pet Support tab (index 2)
        if tabBar.buttons.count > 2 {
            let petSupportTab = tabBar.buttons.element(boundBy: 2)
            petSupportTab.tap()
            sleep(3)
            
            // Take screenshot of Pet Support
            let petSupportScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: petSupportScreenshot)
            attachment.name = "Pet Support View"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            // Look for AI-related elements
            let aiElements = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'AI' OR label CONTAINS[c] 'agent' OR label CONTAINS[c] 'master' OR label CONTAINS[c] 'pet'"))
            
            print("✅ Pet Support accessed with \(aiElements.count) AI-related elements")
            
            // Test interaction with first available element
            if aiElements.count > 0 {
                let firstElement = aiElements.firstMatch
                if firstElement.exists {
                    firstElement.tap()
                    sleep(2)
                    
                    let aiDetailScreenshot = app.screenshot()
                    let aiDetailAttachment = XCTAttachment(screenshot: aiDetailScreenshot)
                    aiDetailAttachment.name = "AI Agent Interface"
                    aiDetailAttachment.lifetime = .keepAlways
                    add(aiDetailAttachment)
                    
                    print("✅ AI element interaction successful")
                }
            }
        }
    }
    
    // MARK: - Memory Management Testing
    
    @MainActor
    func testMemorySection() throws {
        let tabBar = app.tabBars.firstMatch
        
        // Navigate to Memories tab (index 3)
        if tabBar.buttons.count > 3 {
            let memoriesTab = tabBar.buttons.element(boundBy: 3)
            memoriesTab.tap()
            sleep(3)
            
            // Take screenshot of Memories section
            let memoryScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: memoryScreenshot)
            attachment.name = "Memories Section"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            // Look for memory-related buttons
            let addButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'add' OR label CONTAINS[c] '+'"))
            let memoryButtons = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'memory' OR label CONTAINS[c] 'photo' OR label CONTAINS[c] 'vault'"))
            
            print("✅ Memories section loaded with \(addButtons.count) add buttons and \(memoryButtons.count) memory-related elements")
            
            // Test memory functionality if available
            if memoryButtons.count > 0 {
                let firstMemoryButton = memoryButtons.firstMatch
                if firstMemoryButton.exists {
                    firstMemoryButton.tap()
                    sleep(2)
                    
                    let memoryDetailScreenshot = app.screenshot()
                    let memoryDetailAttachment = XCTAttachment(screenshot: memoryDetailScreenshot)
                    memoryDetailAttachment.name = "Memory Detail View"
                    memoryDetailAttachment.lifetime = .keepAlways
                    add(memoryDetailAttachment)
                }
            }
        }
    }
    
    // MARK: - My Pets Testing
    
    @MainActor
    func testMyPetsSection() throws {
        let tabBar = app.tabBars.firstMatch
        
        // Navigate to My Pets tab (index 1)
        if tabBar.buttons.count > 1 {
            let myPetsTab = tabBar.buttons.element(boundBy: 1)
            myPetsTab.tap()
            sleep(3)
            
            // Take screenshot of My Pets section
            let myPetsScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: myPetsScreenshot)
            attachment.name = "My Pets Section"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            // Look for pet-related elements
            let petElements = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'pet' OR label CONTAINS[c] 'add'"))
            let cards = app.scrollViews.descendants(matching: .any)
            
            print("✅ My Pets section loaded with \(petElements.count) pet elements and \(cards.count) UI components")
        }
    }
    
    // MARK: - More Section Testing
    
    @MainActor
    func testMoreSection() throws {
        let tabBar = app.tabBars.firstMatch
        
        // Navigate to More tab (index 4)
        if tabBar.buttons.count > 4 {
            let moreTab = tabBar.buttons.element(boundBy: 4)
            moreTab.tap()
            sleep(3)
            
            // Take screenshot of More section
            let moreScreenshot = app.screenshot()
            let attachment = XCTAttachment(screenshot: moreScreenshot)
            attachment.name = "More Section"
            attachment.lifetime = .keepAlways
            add(attachment)
            
            // Look for settings and other options
            let settingsElements = app.buttons.matching(NSPredicate(format: "label CONTAINS[c] 'settings' OR label CONTAINS[c] 'premium' OR label CONTAINS[c] 'health'"))
            
            print("✅ More section loaded with \(settingsElements.count) menu options")
            
            // Test interaction with first available setting
            if settingsElements.count > 0 {
                let firstSetting = settingsElements.firstMatch
                if firstSetting.exists {
                    firstSetting.tap()
                    sleep(2)
                    
                    let settingDetailScreenshot = app.screenshot()
                    let settingDetailAttachment = XCTAttachment(screenshot: settingDetailScreenshot)
                    settingDetailAttachment.name = "Settings Detail View"
                    settingDetailAttachment.lifetime = .keepAlways
                    add(settingDetailAttachment)
                }
            }
        }
    }
    
    // MARK: - Performance Tests
    
    @MainActor
    func testNavigationPerformance() throws {
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.waitForExistence(timeout: 5), "Tab bar should exist")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // Test rapid navigation between tabs
        for index in 0..<min(5, tabBar.buttons.count) {
            let tab = tabBar.buttons.element(boundBy: index)
            if tab.exists {
                tab.tap()
                sleep(1) // Minimal wait time
            }
        }
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let navigationTime = endTime - startTime
        
        // Navigation should complete in reasonable time
        XCTAssertLessThan(navigationTime, 15.0, "Navigation between all tabs should complete quickly")
        
        print("✅ Navigation performance test completed in \(navigationTime) seconds")
    }
    
    @MainActor
    func testLaunchPerformance() throws {
        if #available(macOS 10.15, iOS 13.0, tvOS 13.0, watchOS 7.0, *) {
            // This measures how long it takes to launch your application.
            measure(metrics: [XCTApplicationLaunchMetric()]) {
                XCUIApplication().launch()
            }
        }
    }
}

// MARK: - Helper Extensions

extension XCUIElement {
    func waitForStableState(timeout: TimeInterval = 5.0) -> Bool {
        let startTime = Date()
        var lastFrame = self.frame
        
        while Date().timeIntervalSince(startTime) < timeout {
            usleep(100000) // 0.1 second
            let currentFrame = self.frame
            if currentFrame == lastFrame {
                return true
            }
            lastFrame = currentFrame
        }
        return false
    }
}
