//
//  PetCapsuleWidgetsBundle.swift
//  PetCapsuleWidgets
//
//  Widget Extension Bundle for iOS 18 Enhanced Widgets
//

import WidgetKit
import Swift<PERSON>

@main
struct PetCapsuleWidgetsBundle: WidgetBundle {
    var body: some Widget {
        if #available(iOS 18.0, *) {
            PetDashboardWidget()
            VaccinationTrackerWidget()
            MemoryTimelineWidget()
            WalkPlannerWidget()
            PetHealthWidget()
            EmergencyContactWidget()
            PetCareActivityWidget()
        } else {
            // Fallback widgets for older iOS versions
            LegacyPetDashboardWidget()
        }
    }
}

// MARK: - Legacy Widget for iOS 17 and below

struct LegacyPetDashboardWidget: Widget {
    let kind: String = "LegacyPetDashboardWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: LegacyPetDashboardProvider()) { entry in
            LegacyPetDashboardView(entry: entry)
        }
        .configurationDisplayName("Pet Dashboard")
        .description("Basic pet information and status")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

struct LegacyPetDashboardProvider: TimelineProvider {
    func placeholder(in context: Context) -> LegacyPetDashboardEntry {
        LegacyPetDashboardEntry(date: Date(), petName: "Buddy", petType: "Dog")
    }
    
    func getSnapshot(in context: Context, completion: @escaping (LegacyPetDashboardEntry) -> ()) {
        let entry = LegacyPetDashboardEntry(date: Date(), petName: "Buddy", petType: "Dog")
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<LegacyPetDashboardEntry>) -> ()) {
        let entries: [LegacyPetDashboardEntry] = [
            LegacyPetDashboardEntry(date: Date(), petName: "Buddy", petType: "Dog")
        ]
        
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct LegacyPetDashboardEntry: TimelineEntry {
    let date: Date
    let petName: String
    let petType: String
}

struct LegacyPetDashboardView: View {
    let entry: LegacyPetDashboardEntry
    
    var body: some View {
        VStack {
            Image(systemName: "pawprint.circle.fill")
                .foregroundColor(.blue)
                .font(.largeTitle)
            
            Text(entry.petName)
                .font(.headline)
            
            Text(entry.petType)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
}
